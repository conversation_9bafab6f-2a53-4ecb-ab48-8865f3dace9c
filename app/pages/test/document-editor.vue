<script setup lang="ts">
import type { DocumentEditorData } from '~/composables/useBookWritingViews'
import type { ChatMessage } from '~/types/chat'
import { useBookWritingViews } from '~/composables/useBookWritingViews'

definePageMeta({
  title: 'DocumentEditor Test',
  description: 'Test page for DocumentEditorView component',
})

const { createDocumentEditorMessage, isBookWritingView } = useBookWritingViews()

// State
const showChatIntegration = ref(false)
const chatDocType = ref('chapter')
const chatMessage = ref<ChatMessage | null>(null)

// Document type options
const docTypeOptions = [
  { label: 'Chapter', value: 'chapter' },
  { label: 'Scene', value: 'scene' },
  { label: 'Notes', value: 'notes' },
  { label: 'Outline', value: 'outline' },
]

// Sample document data for different types
const sampleDocuments = {
  chapter: {
    documentId: 'test-chapter-1',
    title: 'Chapter 1: The Beginning',
    content: `# Chapter 1: The Beginning

The morning sun cast long shadows across the cobblestone streets of **Millbrook**, a small village nestled between rolling hills and ancient forests. *<PERSON><PERSON>* had lived here all her nineteen years, but today would be the day that changed everything.

She stood at her bedroom window, clutching the mysterious letter that had arrived with the dawn. The parchment was thick and yellowed, sealed with red wax bearing an unfamiliar symbol—a dragon coiled around a crown.

## The Letter's Contents

*"Dear Lyra,"* it began in elegant script, *"Your grandfather's time has come to pass on his greatest secret. Meet me at the old oak tree by midnight. Come alone, and bring the compass he gave you. Your destiny awaits."*

The letter was unsigned, but somehow Lyra knew it was genuine. Her grandfather had always hinted at mysteries beyond their simple village life, speaking in riddles about ancient powers and forgotten kingdoms.

**Today, she would finally learn the truth.**`,
    type: 'chapter' as const,
    chapterNumber: 1,
  },

  scene: {
    documentId: 'test-scene-1',
    title: 'The Mysterious Encounter',
    content: `## Scene: The Mysterious Encounter

**Location:** The old oak tree, edge of Whispering Woods  
**Time:** Just before midnight  
**Characters:** Lyra, The Stranger

The ancient oak stood silhouetted against the star-filled sky, its gnarled branches reaching toward the heavens like desperate fingers. Lyra approached cautiously, her grandfather's brass compass clutched tightly in her hand.

*A figure stepped from the shadows.*

"You came," said a voice, neither young nor old, neither male nor female—something in between, something... otherworldly.

"Who are you?" Lyra asked, trying to keep the tremor from her voice.

The stranger smiled, and in the moonlight, their eyes seemed to glow with an inner fire. "I am someone who knew your grandfather well. Someone who has been waiting a very long time for this moment."

**Key Details:**
- The stranger wears a cloak that seems to shift in the moonlight
- They know about the compass before Lyra shows it
- There's an otherworldly quality to their presence
- The oak tree responds to their proximity (leaves rustling without wind)`,
    type: 'scene' as const,
    sceneId: 'scene-mysterious-encounter',
  },

  notes: {
    documentId: 'test-notes-1',
    title: 'World Building Notes',
    content: `# World Building Notes

## The Kingdom of Eldermoor

### Geography
- **Capital:** Dragonspire City (built around ancient dragon lair)
- **Major Regions:**
  - The Sunward Plains (agricultural heartland)
  - The Whispering Woods (mysterious, magical forest)
  - The Northlands (harsh, mountainous region)
  - The Shadowed Valleys (dangerous, monster-infested)

### Magic System
Magic in Eldermoor is tied to ancient artifacts called **Dragon Stones**. These crystalline formations contain the concentrated essence of dragons from ages past.

**Types of Magic:**
- **Elemental:** Fire, water, earth, air
- **Life:** Healing, growth, communication with nature
- **Shadow:** Illusion, stealth, manipulation of darkness
- **Time:** Rarely seen, extremely powerful, mostly theoretical

### Political Structure
- **The Dragon Crown:** Artifact that grants the right to rule
- **The Council of Mages:** Advisors to the ruler
- **Regional Lords:** Govern individual territories
- **The Common Folk:** Farmers, merchants, craftsmen

### Current Conflicts
1. **The Shadow Lord's Rise:** Ancient evil returning to power
2. **Missing Dragon Stones:** Magical artifacts disappearing
3. **Political Unrest:** Regional lords questioning central authority
4. **Mysterious Disappearances:** People vanishing near the Whispering Woods

### Important NPCs
- **Grandfather Aldric:** Former royal mage, keeper of secrets
- **The Shadow Lord:** Primary antagonist, Lyra's corrupted father
- **Kael:** Mysterious scholar with hidden agenda
- **Master Finn:** Village blacksmith, secretly knows ancient lore`,
    type: 'notes' as const,
  },

  outline: {
    documentId: 'test-outline-1',
    title: 'Story Outline: The Dragon Crown Chronicles',
    content: `# Story Outline: The Dragon Crown Chronicles

## Book 1: The Awakening

### Act I: Discovery (Chapters 1-3)
**Chapter 1: The Letter**
- Introduce Lyra in her village
- Mysterious letter arrives
- Decision to meet the stranger

**Chapter 2: The Revelation**
- Meeting at the oak tree
- Learn about the Dragon Crown
- Grandfather's true identity revealed

**Chapter 3: The Journey Begins**
- Village attacked by shadow creatures
- Lyra forced to flee
- First use of latent magical abilities

### Act II: Adventure (Chapters 4-6)
**Chapter 4: The Ancient Library**
- Seek knowledge about the Crown
- Meet Kael, the scholar
- Discover the prophecy

**Chapter 5: The Trials**
- Three magical trials to prove worthiness
- Each trial reveals more about Lyra's heritage
- Growing relationship with Kael

**Chapter 6: The Betrayal**
- Kael revealed as agent of Shadow Lord
- Crown stolen during moment of trust
- Lyra barely escapes with her life

### Act III: Confrontation (Chapters 7-9)
**Chapter 7: Gathering Allies**
- Return to village, find survivors
- Rally the remaining mages
- Prepare for final battle

**Chapter 8: The Shadow Citadel**
- Assault on Shadow Lord's fortress
- Kael's change of heart
- Epic magical battle

**Chapter 9: The New Dawn**
- Shadow Lord defeated (but not destroyed)
- Lyra becomes the new Guardian
- Setup for future books

## Character Arcs

### Lyra Stormwind
- **Beginning:** Naive village girl with big dreams
- **Middle:** Learns harsh realities, faces betrayal
- **End:** Mature leader ready for greater challenges

### Kael
- **Beginning:** Charming scholar with hidden agenda
- **Middle:** Conflicted between duty and growing feelings
- **End:** Redeemed ally who chooses love over loyalty

### The Shadow Lord
- **Beginning:** Mysterious ancient evil
- **Middle:** Revealed as Lyra's corrupted father
- **End:** Temporarily defeated, but his corruption lingers

## Themes
- **Coming of Age:** Growing from innocence to experience
- **Trust and Betrayal:** Learning who to believe in
- **Legacy:** The weight of family history
- **Power and Responsibility:** What comes with great abilities
- **Redemption:** The possibility of changing one's path`,
    type: 'outline' as const,
  },
}

// Create chat message
function createChatMessage() {
  const sampleData = sampleDocuments[chatDocType.value as keyof typeof sampleDocuments]

  const documentData: DocumentEditorData = {
    ...sampleData,
    lastModified: new Date(),
    wordCount: sampleData.content.split(/\s+/).length,
    characterCount: sampleData.content.length,
    readingTime: Math.ceil(sampleData.content.split(/\s+/).length / 200 * 60),
    collaborators: [
      {
        id: 'test-user-1',
        name: 'Test User',
        avatar: 'https://i.pravatar.cc/40?img=3',
        isOnline: true,
      },
    ],
    projectId: 'test-project',
  }

  chatMessage.value = createDocumentEditorMessage(documentData)
}

// Reset demo
function resetDemo() {
  chatMessage.value = null
  showChatIntegration.value = false
  chatDocType.value = 'chapter'
}

// Initialize
onMounted(() => {
  // Auto-create a sample message
  createChatMessage()
})
</script>

<template>
  <div class="min-h-screen bg-muted-50 dark:bg-muted-900">
    <!-- Page Header -->
    <div class="bg-white dark:bg-muted-800 border-b border-muted-200 dark:border-muted-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                DocumentEditor Test
              </h1>
              <p class="mt-2 text-gray-600 dark:text-gray-300 max-w-2xl">
                Interactive testing environment for the DocumentEditorView component with live demos and examples.
              </p>
            </div>
            <div class="flex gap-3">
              <BaseButton
                :color="showChatIntegration ? 'primary' : 'default'"
                size="sm"
                @click="showChatIntegration = !showChatIntegration"
              >
                Chat Integration
              </BaseButton>
              <BaseButton
                color="default"
                size="sm"
                @click="resetDemo"
              >
                Reset Demo
              </BaseButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="space-y-8">
        <!-- Chat Integration Demo -->
        <div v-if="showChatIntegration" class="space-y-6">
          <BaseCard class="p-6">
            <BaseHeading as="h2" size="xl" class="mb-4">
              Chat Integration Demo
            </BaseHeading>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              This demonstrates how the DocumentEditorView integrates with the chat system using the useBookWritingViews composable.
            </p>

            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <BaseHeading as="h3" size="lg" class="mb-3">
                  Create Chat Message
                </BaseHeading>
                <div class="space-y-4">
                  <div>
                    <BaseText size="sm" class="font-medium mb-2">
                      Document Type:
                    </BaseText>
                    <BaseListbox
                      v-model="chatDocType"
                      :items="docTypeOptions"
                      class="w-full"
                    />
                  </div>

                  <BaseButton
                    color="primary"
                    class="w-full"
                    @click="createChatMessage"
                  >
                    Create Document Editor Message
                  </BaseButton>
                </div>
              </div>

              <div>
                <BaseHeading as="h3" size="lg" class="mb-3">
                  Generated Message
                </BaseHeading>
                <div v-if="chatMessage" class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div class="space-y-2 text-sm">
                    <div><strong>ID:</strong> {{ chatMessage.id }}</div>
                    <div><strong>Role:</strong> {{ chatMessage.role }}</div>
                    <div><strong>Content:</strong> {{ chatMessage.content }}</div>
                    <div><strong>View Type:</strong> {{ chatMessage.metadata?.view?.type }}</div>
                    <div><strong>Timestamp:</strong> {{ chatMessage.timestamp.toLocaleString() }}</div>
                  </div>
                </div>
                <div v-else class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg text-center text-gray-500 dark:text-gray-400">
                  No message created yet
                </div>
              </div>
            </div>
          </BaseCard>
        </div>

        <!-- Component Demo -->
        <DocumentEditorDemo />

        <!-- Technical Information -->
        <BaseCard class="p-6">
          <BaseHeading as="h2" size="xl" class="mb-4">
            Technical Information
          </BaseHeading>
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <BaseHeading as="h3" size="lg" class="mb-3">
                Component Features
              </BaseHeading>
              <div class="space-y-2 text-sm">
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Rich text editing with formatting toolbar</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Live word/character count and reading time</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Auto-save functionality (every 30 seconds)</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>AI-powered improvement suggestions</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Preview mode with markdown-like rendering</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Real-time collaboration presence indicators</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Event emission for external integration</span>
                </div>
              </div>
            </div>

            <div>
              <BaseHeading as="h3" size="lg" class="mb-3">
                Integration Status
              </BaseHeading>
              <div class="space-y-2 text-sm">
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Component created in views/DocumentEditorView.vue</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Added to chat canvas registry.enhanced.ts</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Integrated with useBookWritingViews composable</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Exported from views/index.ts</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>TypeScript interfaces defined</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Lazy loading configured</span>
                </div>
                <div class="flex items-center gap-2">
                  <Icon name="lucide:check" class="text-green-500 w-4 h-4" />
                  <span>Demo components created</span>
                </div>
              </div>
            </div>
          </div>
        </BaseCard>

        <!-- Usage Examples -->
        <BaseCard class="p-6">
          <BaseHeading as="h2" size="xl" class="mb-4">
            Usage Examples
          </BaseHeading>
          <div class="space-y-6">
            <div>
              <BaseHeading as="h3" size="lg" class="mb-3">
                Direct Component Usage
              </BaseHeading>
              <BaseText size="sm" class="mb-3 text-gray-600 dark:text-gray-300">
                Use the component directly in your template:
              </BaseText>
              <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto"><code>&lt;DocumentEditorView
  document-id="chapter-1"
  title="The Hero's Journey"
  content="Once upon a time..."
  type="chapter"
  :chapter-number="1"
  @save="handleSave"
  @content-change="handleContentChange"
  @ai-suggestion="handleAISuggestion"
/&gt;</code></pre>
            </div>

            <div>
              <BaseHeading as="h3" size="lg" class="mb-3">
                Chat Integration Usage
              </BaseHeading>
              <BaseText size="sm" class="mb-3 text-gray-600 dark:text-gray-300">
                Create a chat message with the document editor view:
              </BaseText>
              <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto"><code>const { createDocumentEditorMessage } = useBookWritingViews()

const message = createDocumentEditorMessage({
  documentId: 'chapter-1',
  title: 'The Hero\\'s Journey',
  content: 'Once upon a time...',
  type: 'chapter',
  chapterNumber: 1,
  wordCount: 1250,
  collaborators: [
    { id: '1', name: 'John Doe', isOnline: true }
  ]
})

// Message will render inline in chat with the editor</code></pre>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
