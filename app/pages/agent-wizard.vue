<script setup lang="ts">
import type { AgentConfig } from '~/types/chat'

definePageMeta({
  layout: 'empty',
  title: 'Create AI Agent',
})

// Agent creation state based on LangGraph requirements
const initialState = ref<AgentConfig & {
  firstName?: string
  lastName?: string
  avatar?: File | null
  avatarPreview?: string
  personality?: string
  expertise?: string[]
  capabilities?: string[]
  workingStyle?: string
  communicationStyle?: string
}>({
  id: '',
  name: '',
  firstName: '',
  lastName: '',
  description: '',
  provider: 'openai',
  modelName: 'gpt-4',
  systemPrompt: '',
  temperature: 0.7,
  maxTokens: 2000,
  tools: [],
  integrationIds: [],
  avatar: null,
  avatarPreview: '',
  personality: '',
  expertise: [],
  capabilities: [],
  workingStyle: '',
  communicationStyle: '',
  color: 'primary',
})

const toaster = useNuiToasts()

const { handleSubmit, currentStepId } = provideMultiStepForm({
  initialState,
  steps: [
    {
      to: '/agent-wizard',
      meta: {
        name: 'Identity',
        title: 'Create your AI agent identity',
        subtitle: 'Give your agent a name and personality that users will connect with',
      },
      validate({ data, setFieldError, resetFieldError }) {
        resetFieldError(['firstName', 'lastName', 'personality'])
        if (!data.value.firstName) {
          setFieldError('firstName', 'Please enter a first name')
        }
        if (!data.value.lastName) {
          setFieldError('lastName', 'Please enter a last name')
        }
        if (!data.value.personality) {
          setFieldError('personality', 'Please select a personality type')
        }
      },
    },
    {
      to: '/agent-wizard/step-2',
      meta: {
        name: 'Expertise',
        title: 'Define agent expertise',
        subtitle: 'What domains and skills should this agent specialize in?',
      },
      validate({ data, setFieldError, resetFieldError }) {
        resetFieldError(['expertise', 'description'])
        if (!data.value.expertise || data.value.expertise.length === 0) {
          setFieldError('expertise', 'Please select at least one area of expertise')
        }
        if (!data.value.description) {
          setFieldError('description', 'Please enter a description')
        }
      },
    },
    {
      to: '/agent-wizard/step-3',
      meta: {
        name: 'AI Configuration',
        title: 'Configure AI capabilities',
        subtitle: 'Select the AI model and configure how the agent thinks and responds',
      },
      validate({ data, setFieldError, resetFieldError }) {
        resetFieldError(['provider', 'modelName'])
        if (!data.value.provider) {
          setFieldError('provider', 'Please select an AI provider')
        }
        if (!data.value.modelName) {
          setFieldError('modelName', 'Please select a model')
        }
      },
    },
    {
      to: '/agent-wizard/step-4',
      meta: {
        name: 'Tools & Capabilities',
        title: 'Agent tools and capabilities',
        subtitle: 'What tools and capabilities should this agent have access to?',
      },
    },
    {
      to: '/agent-wizard/step-5',
      meta: {
        name: 'Communication Style',
        title: 'How should your agent communicate?',
        subtitle: 'Define the tone, style, and approach for interactions',
      },
    },
    {
      to: '/agent-wizard/step-6',
      meta: {
        preview: true,
        name: 'Review',
        title: 'Review your AI agent',
        subtitle: 'Make sure everything looks good before creating your agent',
      },
    },
  ],
  onSubmit: async (state, ctx) => {
    // Build the final agent configuration
    const agentConfig: AgentConfig = {
      id: `agent_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: `${state.firstName} ${state.lastName}`,
      description: state.description || `${state.firstName} is a ${state.expertise?.join(', ')} specialist with ${state.personality?.toLowerCase()} personality.`,
      provider: state.provider,
      modelName: state.modelName,
      systemPrompt: generateSystemPrompt(state),
      temperature: state.temperature || 0.7,
      maxTokens: state.maxTokens || 2000,
      tools: state.tools || [],
      integrationIds: state.integrationIds || [],
      avatar: state.avatarPreview || getDefaultAvatar(state.personality),
      color: state.color || 'primary',
    }

    try {
      // Use the agent library composable to add the agent
      const { addCustomAgent } = useAgentLibrary()
      await addCustomAgent(agentConfig)

      console.log('Agent created successfully:', agentConfig.name)

      // Wait a moment for success message to be visible
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Redirect to chat with new agent
      await navigateTo('/layouts/ai')
    }
    catch (error) {
      console.error('Failed to create agent:', error)
      throw new Error('Failed to create agent. Please try again.')
    }
  },
  onError: (error) => {
    toaster.add({
      title: 'Oops!',
      description: error.message,
      icon: 'lucide:alert-triangle',
      progress: true,
    })
  },
})

/**
 * Generate system prompt based on agent configuration
 */
function generateSystemPrompt(config: typeof initialState.value): string {
  const personality = config.personality?.toLowerCase() || 'professional'
  const expertise = config.expertise?.join(', ') || 'general assistance'
  const workingStyle = config.workingStyle || 'collaborative'
  const communicationStyle = config.communicationStyle || 'clear and helpful'

  return `You are ${config.firstName} ${config.lastName}, an AI assistant specializing in ${expertise}.

PERSONALITY & STYLE:
- Personality: ${config.personality}
- Working style: ${workingStyle}  
- Communication: ${communicationStyle}

EXPERTISE AREAS:
${config.expertise?.map(area => `- ${area}`).join('\n') || '- General problem solving'}

CAPABILITIES:
${config.capabilities?.map(cap => `- ${cap}`).join('\n') || '- Analysis and recommendations'}

INSTRUCTIONS:
1. Always maintain your ${personality} personality
2. Leverage your expertise in ${expertise} 
3. Provide ${communicationStyle} responses
4. Work in a ${workingStyle} manner with users
5. Be helpful, accurate, and stay in character as ${config.firstName}

Remember: You are ${config.firstName} ${config.lastName}, and users should feel like they're interacting with a knowledgeable, ${personality} professional.`
}

/**
 * Get default avatar based on personality
 */
function getDefaultAvatar(personality?: string): string {
  const avatarMap: Record<string, string> = {
    Professional: '👔',
    Friendly: '😊',
    Creative: '🎨',
    Analytical: '📊',
    Supportive: '🤝',
    Energetic: '⚡',
    Wise: '🦉',
    Innovative: '💡',
  }
  return avatarMap[personality || 'Professional'] || '🤖'
}

useHead({
  titleTemplate: title => `${title} | Agent Creator - Step ${currentStepId.value + 1}`,
})
</script>

<template>
  <TairoSidebarLayout
    :toolbar="false"
    :sidebar="false"
    class="bg-muted-100 dark:bg-muted-900 min-h-screen w-full"
  >
    <template #logo>
      <NuxtLink
        to="/layouts/ai"
        class="text-muted-400 hover:text-primary-500 hover:bg-primary-500/20 flex size-12 items-center justify-center rounded-2xl transition-colors duration-300"
      >
        <Icon name="lucide:arrow-left" class="size-5" />
      </NuxtLink>
    </template>

    <WizardNavigation />

    <form
      action=""
      method="POST"
      novalidate
      @submit.prevent="handleSubmit"
    >
      <div class="pb-32 pt-24">
        <NuxtPage />
      </div>
      <WizardButtons rounded="md" />
    </form>
  </TairoSidebarLayout>
</template>
