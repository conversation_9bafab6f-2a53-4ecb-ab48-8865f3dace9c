<script setup lang="ts">
import type { Project, ProjectStepData } from '~/types'

definePageMeta({
  title: 'Wizard — Step 7',
  preview: {
    title: 'Wizard — Step 7',
    description: 'For onboarding and step forms',
    categories: ['dashboards', 'wizards', 'forms'],
    src: '/img/screens/wizard-7.png',
    srcDark: '/img/screens/wizard-7-dark.png',
    order: 36,
  },
})
useHead({
  title: 'Submit project',
})

const {
  data: project,
  complete,
  getStep,
  checkPreviousSteps,
} = useMultiStepForm<Project, ProjectStepData>()

onBeforeMount(checkPreviousSteps)

const avatarPreview = useNuiFilePreview(() => project.value.avatar)

// Helper function for date formatting
function formatDate(date: Date | string) {
  if (!date)
    return '--'
  return new Date(date).toLocaleDateString()
}

// Widget data
const descriptionCardData = computed(() => ({
  title: 'Project description',
  content: project.value.description,
  contentType: 'text',
  emptyMessage: 'No description was provided for this project',
  editButton: {
    to: getStep(1)?.to || '#',
  },
}))

const typeCardData = computed(() => ({
  label: 'Project type',
  value: project.value.type === undefined ? undefined : project.value.type,
  fallback: 'No type selected',
  iconConditions: [
    {
      condition: project.value.type === undefined,
      icon: 'solar:suitcase-lines-linear',
      iconClass: 'bg-primary-500/10 text-primary-600',
    },
    {
      condition: project.value.type === 'design',
      icon: 'solar:structure-linear',
      iconClass: 'bg-orange-500/10 text-orange-600',
    },
    {
      condition: project.value.type === 'development',
      icon: 'solar:structure-linear',
      iconClass: 'bg-emerald-500/10 text-emerald-600',
    },
    {
      condition: project.value.type === 'marketing',
      icon: 'solar:structure-linear',
      iconClass: 'bg-yellow-500/10 text-yellow-500',
    },
  ],
  editButton: {
    to: getStep(0)?.to || '#',
  },
}))

const customerCardData = computed(() => ({
  label: 'Customer',
  value: project.value.customer?.name,
  fallback: 'No customer selected',
  avatarSrc: project.value.customer?.logo || '/img/avatars/placeholder-file.png',
  editButton: {
    to: getStep(2)?.to || '#',
  },
}))

const budgetCardData = computed(() => ({
  label: 'Estimate budget',
  value: project.value.budget,
  valueSize: 'lg',
  editButton: {
    to: getStep(2)?.to || '#',
  },
}))

const dueDateCardData = computed(() => ({
  label: 'Due date',
  value: formatDate(project.value.endDate),
  fallback: '--',
  cardClass: 'group relative h-full p-6',
  heightClass: 'h-full',
  valueClass: 'text-sm',
  labelClass: 'mb-3',
  editButton: {
    to: getStep(2)?.to || '#',
  },
}))

const filesCardData = computed(() => ({
  label: 'Attached files',
  value: project.value.files?.length || 0,
  valueSize: 'lg',
  editButton: {
    to: getStep(3)?.to || '#',
  },
}))

const teamCardData = computed(() => ({
  title: 'Project team',
  contentType: 'list',
  listItems: project.value.team && project.value.team.length > 0 ? project.value.team : undefined,
  emptyMessage: 'No team members invited',
  editButton: {
    to: getStep(4)?.to || '#',
  },
}))

const toolsCardData = computed(() => ({
  title: 'Project tools',
  contentType: 'list',
  listItems: project.value.tools && project.value.tools.length > 0 ? project.value.tools : undefined,
  emptyMessage: 'No tools selected',
  editButton: {
    to: getStep(5)?.to || '#',
  },
}))
</script>

<template>
  <div>
    <div v-if="!complete">
      <WizardStepTitle />

      <div class="flex flex-col px-4">
        <div
          class="group relative mx-auto mb-2 flex w-16 items-center justify-center"
        >
          <BaseAvatar
            v-if="avatarPreview"
            size="lg"
            :src="avatarPreview"
            class="dark:bg-muted-700/60 bg-white"
          />
          <BaseAvatar
            v-else
            size="lg"
            text="P"
            class="bg-pink-500/10 text-pink-600"
          />
          <!-- Edit -->
          <div class="absolute bottom-0 end-0 z-10">
            <BaseButton
              size="icon-sm"
              rounded="full"
              class="hover:border-primary-500 hover:text-primary-500 dark:hover:border-primary-500 dark:hover:text-primary-500 pointer-events-none opacity-0 group-hover:pointer-events-auto group-hover:opacity-100"
              :to="getStep(1)?.to"
            >
              <Icon name="lucide:edit-2" class="pointer-events-none size-3" />
            </BaseButton>
          </div>
        </div>
        <div class="mx-auto flex w-full max-w-xl flex-col gap-4">
          <!-- Title -->
          <h3
            class="text-muted-800 dark:text-muted-100 text-center font-sans text-xl font-medium"
          >
            {{ project.name === '' ? 'Project title goes here' : project.name }}
          </h3>

          <div class="grid grid-cols-12 gap-4">
            <!-- Description -->
            <div class="col-span-12">
              <WidgetsWizardAgentContentCard :data="descriptionCardData" />
            </div>
            <!-- Type -->
            <div class="col-span-12 sm:col-span-6">
              <WidgetsWizardAgentInfoCard :data="typeCardData" />
            </div>
            <!-- Customer -->
            <div class="col-span-12 sm:col-span-6">
              <WidgetsWizardAgentInfoCard :data="customerCardData" />
            </div>
            <!-- Budget -->
            <div class="col-span-12 sm:col-span-4">
              <WidgetsWizardAgentMetricCard :data="budgetCardData" />
            </div>
            <!-- Due Date -->
            <div class="col-span-12 sm:col-span-4">
              <WidgetsWizardAgentMetricCard :data="dueDateCardData" />
            </div>
            <!-- Files -->
            <div class="col-span-12 sm:col-span-4">
              <WidgetsWizardAgentMetricCard :data="filesCardData" />
            </div>
            <!-- Team -->
            <div class="col-span-12 sm:col-span-6">
              <WidgetsWizardAgentContentCard :data="teamCardData" />
            </div>
            <!-- Tools -->
            <div class="col-span-12 sm:col-span-6">
              <WidgetsWizardAgentContentCard :data="toolsCardData" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="mx-auto max-w-2xl px-4">
        <div class="mb-10 text-center">
          <BaseHeading
            tag="h1"
            size="2xl"
            class="text-muted-800 dark:text-white"
          >
            <span>Congrats! You're all set</span>
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
            <span>Awesome, you just finished creating this project.</span>
          </BaseParagraph>
        </div>

        <BasePlaceholderPage
          title="Get ready for next steps"
          subtitle="You, and the team members you've added can already start working and creating tasks."
        >
          <template #image>
            <img
              src="/img/illustrations/wizard/finish.svg"
              class="mx-auto max-w-[210px] rounded-full"
              alt="Upload files"
            >
          </template>
          <div class="mt-2 text-center">
            <BaseButton
              to="/dashboards"
              rounded="lg"
              variant="primary"
              class="w-48"
            >
              <span>View Project</span>
            </BaseButton>
          </div>
        </BasePlaceholderPage>
      </div>
    </div>
  </div>
</template>
