<script setup lang="ts">
import type { Project, ProjectStepData, Tool } from '~/types'

definePageMeta({
  title: 'Wizard — Step 6',
  preview: {
    title: 'Wizard — Step 6',
    description: 'For onboarding and step forms',
    categories: ['dashboards', 'wizards', 'forms'],
    src: '/img/screens/wizard-6.png',
    srcDark: '/img/screens/wizard-6-dark.png',
    order: 35,
  },
})
useHead({
  title: 'Project tools',
})

const { data: project, checkPreviousSteps } = useMultiStepForm<Project, ProjectStepData>()

onBeforeMount(checkPreviousSteps)

const tools: Tool[] = [
  {
    name: 'Illustrator',
    description: 'Design Software',
    logo: '/img/logos/tools/illustrator.svg',
  },
  {
    name: 'Photoshop',
    description: 'Design Software',
    logo: '/img/logos/tools/photoshop.svg',
  },
  {
    name: 'Adobe XD',
    description: 'Design Software',
    logo: '/img/logos/tools/xd.svg',
  },
  {
    name: 'Figma',
    description: 'Design Software',
    logo: '/img/logos/tools/xd.svg',
  },
  {
    name: 'Invision',
    description: 'Design Software',
    logo: '/img/logos/tools/invision.svg',
  },
  {
    name: 'Jira',
    description: 'Issue Tracker',
    logo: '/img/logos/tools/jira.svg',
  },
  {
    name: 'Taiga',
    description: 'Scrumboard',
    logo: '/img/logos/tools/taiga.svg',
  },
  {
    name: 'Slack',
    description: 'Messaging App',
    logo: '/img/logos/tools/slack.svg',
  },
  {
    name: 'Asana',
    description: 'Task Manager',
    logo: '/img/logos/tools/asana.svg',
  },
  {
    name: 'Teamwork',
    description: 'Collaborative App',
    logo: '/img/logos/tools/teamwork.svg',
  },
  {
    name: 'GitHub',
    description: 'Code Repository',
    logo: '/img/logos/tools/github.svg',
  },
  {
    name: 'Gitlab',
    description: 'Code Repository',
    logo: '/img/logos/tools/gitlab.svg',
  },
]
</script>

<template>
  <div>
    <WizardStepTitle />

    <CheckboxGroupRoot v-model="project.tools" as="div" class="mx-auto grid max-w-4xl gap-4 px-4 sm:grid-cols-3">
      <!-- Tools -->
      <CheckboxRoot
        v-for="tool in tools"
        :key="tool.name"
        :value="tool"
      >
        <CheckboxIndicator
          force-mount
          class="group"
        >
          <BaseCard
            rounded="lg"
            class="group-data-[state=checked]:border-primary-500 group-data-[state=checked]:shadow-muted-300/30 dark:group-data-[state=checked]:shadow-muted-900/30 p-4 group-data-[state=checked]:shadow-xl"
          >
            <div class="cursor-pointer flex items-center justify-start gap-3">
              <img
                :src="tool.logo"
                class="size-8"
                alt=""
              >
              <div class="grow text-start">
                <div
                  class="text-muted-800 dark:text-muted-100 text-sm font-medium"
                >
                  {{ tool.name }}
                </div>
                <div class="text-muted-400 text-xs">
                  {{ tool.description }}
                </div>
              </div>
            </div>
          </BaseCard>
        </CheckboxIndicator>
      </CheckboxRoot>
    </CheckboxGroupRoot>
  </div>
</template>
