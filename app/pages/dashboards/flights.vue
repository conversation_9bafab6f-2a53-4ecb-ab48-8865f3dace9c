<script setup lang="ts">
import type {
  FlightFiltersData,
  FlightOptionCardData,
  FlightResultCardData,
  FlightSearchHeaderData,
  FlightTravelerProfileData,
} from '~/types/widgets'
import { sub } from '~/utils/bundles/date-fns'

definePageMeta({
  title: 'Flights',
  preview: {
    title: 'Flights dashboard',
    description: 'For travel and booking',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-flights.png',
    srcDark: '/img/screens/dashboards-flights-dark.png',
    order: 10,
  },
})

// Flight search header data
const flightSearchHeader: FlightSearchHeaderData = {
  icon: 'vaadin:airplane',
  route: 'Paris [PAR] - New York [NY]',
  subtitle: '1 adult — Business',
  dateRange: {
    start: sub(new Date(), { days: 14 }),
    end: new Date(),
  },
  illustration: '/img/illustrations/dashboards/travel-plane.svg',
  illustrationAlt: 'Travel plane',
}

// Flight option cards data
const flightOptions: FlightOptionCardData[] = [
  {
    price: '$290',
    label: 'Cheapest',
    duration: '7h32min',
    isPrimary: false,
  },
  {
    price: '$439',
    label: 'Best',
    duration: '7h11min',
    isPrimary: true,
  },
  {
    price: '$549',
    label: 'Fastest',
    duration: '5h36min',
    isPrimary: false,
  },
]

// Price formatting function
function formatPrice(amount: number): string {
  return `$${amount}`
}

// Traveler profile data
const travelerProfile: FlightTravelerProfileData = {
  avatar: '/img/avatars/10.svg',
  name: 'Kendra Wilson',
  subtitle: 'Traveller since 2019',
  stats: [
    { label: 'Trips', value: '29' },
    { label: 'Flights', value: '73' },
    { label: 'Miles', value: '65.3k' },
  ],
}

// Flight filters data
const flightFilters: FlightFiltersData = {
  ctaText: 'Add to Favorites',
  sections: [
    {
      title: 'Stops',
      options: [
        { value: 'stops-1', label: 'All flights' },
        { value: 'stops-2', label: 'No stops' },
        { value: 'stops-3', label: 'One stop' },
        { value: 'stops-4', label: 'Two stops' },
      ],
    },
    {
      title: 'Luggage',
      options: [
        { value: 'luggage-1', label: 'All options' },
        { value: 'luggage-2', label: 'One cabin luggage' },
        { value: 'luggage-3', label: 'Two cabin luggage' },
        { value: 'luggage-4', label: 'No luggage' },
      ],
    },
  ],
}

const results = [
  {
    logo: '/img/logos/companies/flights/1.svg',
    company: 'Business Air',
    stops: 1,
    price: 374,
    departure: {
      time: '10:30 am',
      date: 'Feb 12, 2025',
      city: 'Paris',
      airport: 'ORLY',
    },
    arrival: {
      time: '6:58 pm',
      date: 'Feb 12, 2025',
      city: 'New York',
      airport: 'JFK',
    },
  },
  {
    logo: '/img/logos/companies/flights/2.svg',
    company: 'Fly Business',
    stops: 2,
    price: 347,
    departure: {
      time: '9:30 am',
      date: 'Feb 14, 2025',
      city: 'Paris',
      airport: 'ORLY',
    },
    arrival: {
      time: '5:24 pm',
      date: 'Feb 14, 2025',
      city: 'New York',
      airport: 'JFK',
    },
  },
  {
    logo: '/img/logos/companies/flights/3.svg',
    company: 'Ground Air',
    stops: 0,
    price: 319,
    departure: {
      time: '11:30 am',
      date: 'Feb 14, 2025',
      city: 'Paris',
      airport: 'ORLY',
    },
    arrival: {
      time: '7:24 pm',
      date: 'Feb 14, 2025',
      city: 'New York',
      airport: 'JFK',
    },
  },
  {
    logo: '/img/logos/companies/flights/1.svg',
    company: 'Business Air',
    stops: 0,
    price: 328,
    departure: {
      time: '12:30 am',
      date: 'Feb 15, 2025',
      city: 'Paris',
      airport: 'ORLY',
    },
    arrival: {
      time: '8:42 pm',
      date: 'Feb 15, 2025',
      city: 'New York',
      airport: 'JFK',
    },
  },
  {
    logo: '/img/logos/companies/flights/1.svg',
    company: 'Business Air',
    stops: 0,
    price: 297,
    departure: {
      time: '2:30 pm',
      date: 'Feb 15, 2025',
      city: 'Paris',
      airport: 'CDG',
    },
    arrival: {
      time: '11:24 pm',
      date: 'Feb 15, 2025',
      city: 'New York',
      airport: 'JFK',
    },
  },
]

// Flight results data
const flightResults: FlightResultCardData = {
  results,
  formatPrice,
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Column -->
      <div class="col-span-12 lg:col-span-9">
        <!-- Inner column -->
        <div class="flex flex-col gap-4">
          <!-- Flight Search Header Widget -->
          <WidgetsFlightsSearchHeader :data="flightSearchHeader" />

          <!-- Results -->
          <div class="flex w-full flex-col gap-4">
            <!-- Header -->
            <div class="flex w-full items-center justify-between">
              <BaseHeading
                as="h3"
                weight="medium"
                size="sm"
                class="text-muted-600 dark:text-muted-400"
              >
                <span>Showing 69 results</span>
              </BaseHeading>
              <BaseButton variant="default" rounded="md">
                Change destination
              </BaseButton>
            </div>
            <!-- Flight Option Cards Widget -->
            <WidgetsFlightsOptionCards :options="flightOptions" />
            <!-- Flight Results List Widget -->
            <WidgetsFlightsResultsList :data="flightResults" />
          </div>
        </div>
      </div>
      <!-- Column -->
      <div class="col-span-12 lg:col-span-3">
        <div class="flex flex-col gap-4">
          <!-- Flight Traveler Profile Widget -->
          <WidgetsFlightsTravelerProfile :data="travelerProfile" />

          <!-- Flight Filters Widget -->
          <WidgetsFlightsFilters :data="flightFilters" />
        </div>
      </div>
    </div>
  </div>
</template>
