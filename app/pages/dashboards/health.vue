<script setup lang="ts">
import type {
  HealthChartCardData,
  HealthMetricCardData,
  HealthSummaryHeaderData,
} from '~/types/widgets'
import 'v-calendar/dist/style.css'

definePageMeta({
  title: 'Health',
  preview: {
    title: 'Health dashboard',
    description: 'For health and fitness',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-health.png',
    srcDark: '/img/screens/dashboards-health-dark.png',
    order: 17,
  },
})

// Datepicker
const date = ref(new Date())

// Health widgets data
const summaryHeader: HealthSummaryHeaderData = {
  title: 'Today\'s Summary',
  description: 'Monitor your activity and keep improving your weak points.',
  illustration: '/img/illustrations/dashboards/health/doctor.svg',
  illustrationAlt: 'Doctor illustration',
  metrics: [
    { value: '900 kcal', label: 'Burnt today' },
    { value: '2300 kcal', label: 'Eaten today' },
    { value: '2%', label: 'Fat burnt' },
    { value: '11,424', label: 'Steps walked' },
    { value: '8.4km', label: 'Distance today' },
  ],
}

const metricCards: HealthMetricCardData[] = [
  {
    icon: 'solar:waterdrop-bold-duotone',
    value: '114/90',
    unit: 'Min/Max',
    title: 'Blood quality',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit Illis videtur.',
  },
  {
    icon: 'solar:heart-pulse-2-bold-duotone',
    value: '112',
    unit: 'Bpm',
    title: 'Heart rate',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit Illis videtur.',
  },
  {
    icon: 'solar:hearts-bold-duotone',
    value: '12/14',
    unit: 'Units',
    title: 'Blood pressure',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit Illis videtur.',
  },
  {
    icon: 'solar:dumbbell-large-bold-duotone',
    value: '60.4',
    unit: 'Lbs',
    title: 'Weight',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit Illis videtur.',
  },
]

const chartCards: HealthChartCardData[] = [
  {
    title: 'Overall Condition',
    tooltip: 'Based on last 7 days',
    description: 'Your overall condition is very good. Make it even better.',
    readMoreLink: '#',
    chartComponent: 'ChartAreaCondition',
    chartClass: 'relative -start-5',
  },
  {
    title: 'Energy Level',
    tooltip: 'Based on last 7 days',
    description: 'Your energy seems a bit unstable. You can improve it.',
    readMoreLink: '#',
    chartComponent: 'ChartScatterEnergy',
  },
  {
    title: 'Oxygenation',
    tooltip: 'Based on last 7 days',
    description: 'Your oxygen seems a bit unstable. You can improve it.',
    readMoreLink: '#',
    chartComponent: 'ChartBarOxygen',
    chartClass: 'relative -start-5',
  },
  {
    title: 'Overall Progress',
    tooltip: 'Based on last 7 days',
    description: 'Your overall progress is very good. Make it even better.',
    readMoreLink: '#',
    chartComponent: 'ChartAreaProgress',
    chartClass: 'relative -start-5',
  },
]
</script>

<template>
  <div class="relative px-4 md:px-6 lg:px-8 pb-20">
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-6">
      <!-- Header -->
      <div class="col-span-12">
        <WidgetsHealthSummaryHeader :data="summaryHeader" />
      </div>
      <!-- Metric Cards -->
      <div
        v-for="(card, index) in metricCards"
        :key="index"
        class="col-span-6 sm:col-span-3"
      >
        <WidgetsHealthMetricCard :data="card" />
      </div>
      <!-- Column -->
      <div class="col-span-12">
        <!-- Inner grid -->
        <div class="grid grid-cols-12 gap-4">
          <!-- Inner column -->
          <div class="xl:col-span-8 col-span-12 2xl:col-span-9">
            <!-- Chart subgrid -->
            <div class="grid grid-cols-12 gap-4">
              <!-- Charts -->
              <div
                v-for="(chart, index) in chartCards"
                :key="index"
                class="col-span-12 sm:col-span-6"
              >
                <WidgetsHealthChartCard :data="chart" />
              </div>
            </div>
          </div>
          <!-- Inner column -->
          <div class="xl:col-span-4 col-span-12 2xl:col-span-3">
            <div class="flex flex-col gap-4">
              <!-- Widget -->
              <BaseCard class="p-4 md:p-6" rounded="lg">
                <div class="flex w-full items-center justify-between">
                  <SearchCompact rounded="lg" />
                </div>
              </BaseCard>
              <!-- Widget -->
              <BaseCard rounded="lg" class="p-2">
                <LazyAddonDatepicker v-model="date" locale="en" label="Start date" />
              </BaseCard>
              <!-- Widget -->
              <BaseCard rounded="lg" class="flex h-full flex-col p-4 md:p-6">
                <div class="mb-6 flex items-center justify-between">
                  <BaseHeading
                    as="h3"
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>Personal Score</span>
                  </BaseHeading>
                </div>
                <div class="py-16">
                  <ChartRadialGaugeAlt class="-mt-14" />
                </div>
                <div class="mt-auto text-center">
                  <BaseParagraph size="sm">
                    <span class="text-muted-600 dark:text-muted-400">
                      Your score has been calculated based on the latest metrics
                    </span>
                  </BaseParagraph>
                </div>
              </BaseCard>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
