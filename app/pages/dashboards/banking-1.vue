<script setup lang="ts">
import type {
  HistoryChartData,
  MyCardsData,
  OverallProgressData,
  QuickTransferData,
  SendMoneyData,
  TransactionItem,
} from '~/types/widgets'

definePageMeta({
  title: 'Overview',
  preview: {
    title: 'Banking dashboard v1',
    description: 'For banking and accounts',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-banking-1.png',
    srcDark: '/img/screens/dashboards-banking-1-dark.png',
    order: 7,
  },
})

const banks = [
  {
    id: 1,
    name: 'X Bank',
    text: 'checking **** **** 0499',
    media: '/img/icons/banking/bank-1.svg',
  },
  {
    id: 2,
    name: 'Bankaria',
    text: 'checking **** **** 1548',
    media: '/img/icons/banking/bank-2.svg',
  },
  {
    id: 3,
    name: 'Bankus',
    text: 'checking **** **** 8448',
    media: '/img/icons/banking/bank-3.svg',
  },
]

const transactions: TransactionItem[] = [
  {
    icon: 'solar:chef-hat-bold-duotone',
    iconColor: 'bg-success-500/10 text-success-500',
    title: 'Food delivery',
    subtitle: 'Oct 23, 2022 - 8:46pm',
    amount: -41.49,
  },
  {
    icon: 'solar:shop-bold-duotone',
    iconColor: 'bg-orange-500/10 text-orange-500',
    title: 'Market Earnings',
    subtitle: 'Oct 18, 2022 - 9:11am',
    amount: 263.39,
  },
  {
    icon: 'solar:bag-smile-bold-duotone',
    iconColor: 'bg-indigo-500/10 text-indigo-500',
    title: 'Online order',
    subtitle: 'Oct 16, 2022 - 2:13pm',
    amount: -92.00,
  },
  {
    icon: 'solar:screencast-2-bold-duotone',
    iconColor: 'bg-yellow-400/10 text-yellow-500',
    title: 'Netflix subscription',
    subtitle: 'Oct 5, 2022 - 8:22pm',
    amount: -18.00,
  },
]

// Transactions widget data
const transactionsData = {
  title: 'Transactions',
  transactions,
  viewAllLink: {
    text: 'View All',
    href: '#',
  },
}

const selectedBankTransfert = shallowRef(banks[0])
const selectedBankSendTo = shallowRef(banks[0])
const selectedCurrency = ref('usd')
const amount = ref<number>(0)

// My Cards widget data
const myCardsData: MyCardsData = {
  title: 'My Cards',
  cards: [
    {
      label: 'Card Balance',
      balance: { value: 2834.31, formatted: '$2,834.31' },
      color: 'text-primary-600',
      actions: [
        {
          title: 'Invest',
          description: 'Buys more stocks',
          icon: 'solar:wad-of-money-linear',
          href: '#',
        },
        {
          title: 'Benchmark',
          description: 'Compare other sources',
          icon: 'solar:mirror-left-linear',
          href: '#',
        },
        {
          title: 'Trade',
          description: 'View opportunities',
          icon: 'solar:filters-linear',
          href: '#',
        },
        {
          title: 'Wallet',
          description: 'Manage your wallet',
          icon: 'solar:wallet-2-linear',
          href: '#',
        },
      ],
    },
    {
      label: 'Card Balance',
      balance: { value: 2834.31, formatted: '$2,834.31' },
      color: 'text-success-600',
      actions: [
        {
          title: 'Invest',
          description: 'Buys more stocks',
          icon: 'solar:wad-of-money-linear',
          href: '#',
        },
        {
          title: 'Benchmark',
          description: 'Compare other sources',
          icon: 'solar:mirror-left-linear',
          href: '#',
        },
        {
          title: 'Trade',
          description: 'View opportunities',
          icon: 'solar:filters-linear',
          href: '#',
        },
        {
          title: 'Wallet',
          description: 'Manage your wallet',
          icon: 'solar:wallet-2-linear',
          href: '#',
        },
      ],
    },
  ],
  information: {
    editLink: {
      text: 'Edit',
      href: '#',
    },
    items: [
      {
        label: 'Status',
        value: 'Active',
        status: { color: 'emerald' },
      },
      {
        label: 'Expires in',
        value: '125 days',
      },
      {
        label: 'Type',
        value: 'Credit Card',
        icon: 'cib:visa',
        iconClass: 'text-muted-400 size-10',
        textSize: 'xs',
      },
    ],
  },
}

// Quick Transfer widget data
const quickTransferData: QuickTransferData = {
  title: 'Quick Transfer',
  fromAccounts: banks,
  selectedFrom: banks[0],
  description: 'Select one of your bank accounts to transfer some funds. The transfer cannot exceed 20% of your balance.',
  submitButtonText: 'Confirm and send',
}

// Send Money widget data
const sendMoneyData: SendMoneyData = {
  title: 'Send money to',
  toAccounts: banks,
  selectedTo: banks[0],
  currencies: [
    { value: 'usd', symbol: '$' },
    { value: 'gbp', symbol: '£' },
    { value: 'eur', symbol: '€' },
  ],
  selectedCurrency: 'usd',
  contacts: [
    { src: '/img/avatars/16.svg' },
    { text: 'LT', class: 'bg-yellow-400/20 text-yellow-400' },
    { src: '/img/avatars/3.svg' },
    { text: 'KC', class: 'bg-indigo-400/20 text-indigo-400' },
  ],
  seeAllLink: {
    text: 'See All',
    href: '#',
  },
  contactsLink: {
    text: 'See all contacts',
    href: '#',
  },
  transferNote: 'Funds will reach destination tomorrow.',
  submitButtonText: 'Send Money',
}

// Overall Progress widget data
const overallProgressData: OverallProgressData = {
  title: 'Overall Progress',
  level: 'Lvl. 3',
  progress: 60,
  description: 'This is your Tairo customer level. Reach out higher levels to unlock achievements, special gifts, and more.',
  viewDetailsLink: {
    text: 'View details',
    href: '#',
  },
}

// History Chart widget data
const historyChartData: HistoryChartData = {
  title: 'History',
  viewReportsLink: {
    text: 'View reports',
    href: '#',
  },
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Grid -->
    <div class="grid gap-4 md:grid-cols-12">
      <!-- Grid column -->
      <div class="lg:landscape:col-span-4 col-span-12 xl:landscape:col-span-4">
        <!-- Inner column -->
        <div class="flex flex-col gap-4">
          <WidgetsFinanceMyCards :data="myCardsData" />
          <!-- Transactions widget -->
          <WidgetsFinanceTransactionsList :data="transactionsData" />
        </div>
      </div>
      <!-- Grid column -->
      <div class="lg:landscape:col-span-8 col-span-12 xl:landscape:col-span-8">
        <!-- Sub grid -->
        <div class="grid gap-4 md:grid-cols-12">
          <!-- Sub column -->
          <div
            class="col-span-12 flex flex-col gap-4 lg:col-span-6"
          >
            <!-- Quick Transfer Widget -->
            <WidgetsFinanceQuickTransfer
              :data="quickTransferData"
              @submit="handleQuickTransferSubmit"
            />
            <!-- Overall Progress Widget -->
            <WidgetsFinanceOverallProgress :data="overallProgressData" />
          </div>
          <!-- Send Money Widget -->
          <div class="col-span-12 lg:col-span-6">
            <WidgetsFinanceSendMoney :data="sendMoneyData" />
          </div>
          <!-- History Chart Widget -->
          <div class="col-span-12">
            <WidgetsChartsHistoryChart :data="historyChartData" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
