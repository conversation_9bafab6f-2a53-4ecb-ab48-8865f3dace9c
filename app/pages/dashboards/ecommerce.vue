<script setup lang="ts">
import type {
  AdditionalStatsData,
  EcommerceMetricData,
  PersonalScoreData,
  RevenueOverviewData,
  SalesRevenueData,
  SimpleChartData,
} from '~/types/widgets'
import 'v-calendar/dist/style.css'

definePageMeta({
  title: 'Ecommerce',
  preview: {
    title: 'Ecommerce dashboard',
    description: 'For sales and online stores',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-ecommerce.png',
    srcDark: '/img/screens/dashboards-ecommerce-dark.png',
    order: 21,
  },
})

// Datepicker
const date = ref(new Date())

// E-commerce metric cards data
const ecommerceMetrics: EcommerceMetricData[] = [
  {
    title: 'Sales',
    value: '641.4k',
    icon: 'solar:cart-large-2-bold-duotone',
    iconClass: 'text-primary-500',
    chartComponent: 'ChartLineSparkOne',
  },
  {
    title: 'Income',
    value: '$389.9k',
    icon: 'solar:banknote-bold-duotone',
    iconClass: 'text-success-500',
    chartComponent: 'ChartLineSparkTwo',
  },
  {
    title: 'Orders',
    value: '981',
    icon: 'solar:box-bold-duotone',
    iconClass: 'text-info-500',
    chartComponent: 'ChartLineSparkThree',
  },
  {
    title: 'Abandonned',
    value: '43',
    icon: 'solar:cart-cross-bold-duotone',
    iconClass: 'text-destructive-500',
    chartComponent: 'ChartLineSparkFour',
  },
]

// Revenue overview data (reusing existing widget)
const revenueOverviewData: RevenueOverviewData = {
  title: 'Revenue Overview',
  metrics: [
    { label: 'This month', value: '$75,689' },
    { label: 'Last month', value: '$59,724' },
    { label: 'Average', value: '$66,561' },
  ],
  chartComponent: 'ChartAreaCustomers',
  actionButton: {
    label: 'Details',
  },
}

// Sales Revenue data (reusing existing widget)
const salesRevenueData: SalesRevenueData = {
  title: 'Sales Revenue',
  revenue: '$8,641.26',
  description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Bonum integritas corporis.',
}

// Additional Stats data (reusing existing widget)
const additionalStatsData: AdditionalStatsData = {
  title: 'Additional Stats',
  stats: [
    {
      chartComponent: 'ChartRadialSmallOne',
      value: '278',
      label: 'New Deals',
    },
    {
      chartComponent: 'ChartRadialSmallTwo',
      value: '1,519',
      label: 'Proposals',
    },
    {
      chartComponent: 'ChartRadialSmallThree',
      value: '3,214',
      label: 'Closed deals',
    },
  ],
}

// Personal Score data
const personalScoreData: PersonalScoreData = {
  title: 'Personal Score',
  chartComponent: 'ChartRadialGaugeAlt',
  description: 'Your score has been calculated based on the latest metrics',
}

// Orders Summary data
const ordersSummaryData: SimpleChartData = {
  title: 'Orders Summary',
  chartComponent: 'ChartBarOrders',
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20 overflow-hidden">
    <!-- Header -->
    <div class="mb-8 flex flex-col justify-between md:flex-row md:items-center">
      <div
        class="flex max-w-[425px] flex-col items-center gap-4 text-center md:flex-row md:text-start lg:max-w-full"
      >
        <BaseAvatar src="/img/avatars/10.svg" size="lg" />
        <div>
          <BaseHeading
            as="h2"
            size="xl"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>Welcome back, Kendra</span>
          </BaseHeading>
          <BaseParagraph size="sm">
            <span class="text-muted-600 dark:text-muted-400">
              Happy to see you again on your dashboard.
            </span>
          </BaseParagraph>
        </div>
      </div>
      <div
        class="mt-4 flex items-center justify-center gap-2 md:mt-0 md:justify-start"
      >
        <BaseButton rounded="md">
          <span>View Reports</span>
        </BaseButton>
        <BaseButton rounded="md" variant="primary">
          <span>Manage Store</span>
        </BaseButton>
      </div>
    </div>
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- E-commerce Metric Cards -->
      <div
        v-for="(metric, index) in ecommerceMetrics"
        :key="index"
        class="lg:landscape:col-span-3 relative col-span-12 md:col-span-6 2xl:landscape:col-span-3"
      >
        <WidgetsEcommerceMetricCard :data="metric" />
      </div>
      <!-- Revenue Overview - using existing widget -->
      <div class="relative col-span-12 xl:landscape:col-span-6 2xl:landscape:col-span-7">
        <WidgetsChartsRevenueOverview :data="revenueOverviewData" />
      </div>
      <!-- Column -->
      <div class="relative col-span-12 xl:landscape:col-span-6 2xl:landscape:col-span-5">
        <div class="flex h-full flex-col gap-4">
          <!-- Sales Revenue - using existing widget -->
          <WidgetsChartsSalesRevenue :data="salesRevenueData" />
          <!-- Additional Stats - using existing widget -->
          <WidgetsChartsAdditionalStats :data="additionalStatsData" />
        </div>
      </div>
      <!-- Personal Score -->
      <div class="lg:landscape:col-span-4 relative col-span-12 md:col-span-6 2xl:landscape:col-span-3">
        <WidgetsChartsPersonalScore :data="personalScoreData" />
      </div>
      <!-- Orders Summary -->
      <div class="lg:landscape:col-span-4 relative col-span-12 md:col-span-6 2xl:landscape:col-span-6">
        <WidgetsChartsSimple :data="ordersSummaryData" />
      </div>
      <!-- Column -->
      <BaseCard rounded="md" class="p-2 lg:landscape:col-span-4 relative col-span-12 md:col-span-6 2xl:landscape:col-span-3">
        <LazyAddonDatepicker v-model="date" locale="en" label="Start date" />
      </BaseCard>
    </div>
  </div>
</template>
