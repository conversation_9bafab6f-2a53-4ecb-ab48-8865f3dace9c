<script setup lang="ts">
definePageMeta({
  title: 'Accounts',
  preview: {
    title: 'Banking dashboard v5',
    description: 'For bank accounts management',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-banking-5.png',
    srcDark: '/img/screens/dashboards-banking-5-dark.png',
    order: 10,
    new: true,
  },
})
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="space-y-6">
      <div class="grid grid-cols-12 gap-4">
        <!-- Balance -->
        <div class="col-span-12 lg:col-span-6">
          <WidgetsFinanceAccountSummary
            :config="{
              accountLabel: 'Main Account',
              accountType: 'Checking account',
              accountNumber: '1487 3256 54122 4897',
              actions: [
                { label: 'Transfer Money', to: '/layouts/send', variant: 'primary' },
                { label: 'Link Accounts', to: '/layouts/accounts/linked' },
              ],
              availableLabel: 'Available Funds',
              availableAmount: 9543.12,
              currencyPrefix: '$',
              showLogo: true,
            }"
          />
        </div>
        <!-- Orders -->
        <div class="col-span-12 lg:col-span-6">
          <WidgetsFinanceStandingOrders
            :config="{
              title: 'Standing Orders',
              description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Si longus, levis dictata sunt. Quare attende, quaeso',
              buttonText: 'Manage Orders',
              buttonTo: '/layouts/send',
              bgVariant: 'bg-primary-800 border-primary-800',
            }"
          />
        </div>
        <!-- Tile -->
        <div class="col-span-12 sm:col-span-4">
          <WidgetsFinanceBankTile
            :config="{
              bankName: 'Chase bank',
              maskedNumber: '**** **** 1728',
              amount: 3462.12,
              currencyPrefix: '$',
              logoSrc: '/img/logos/companies/chase-full.svg',
              button: { icon: 'lucide:arrow-right', size: 'icon-sm', rounded: 'lg' },
            }"
          />
        </div>
        <!-- Tile -->
        <div class="col-span-12 sm:col-span-4">
          <WidgetsFinanceBankTile
            :config="{
              bankName: 'Eurasian bank',
              maskedNumber: '**** **** 3291',
              amount: 1763.49,
              currencyPrefix: '$',
              logoSrc: '/img/logos/companies/eurasian-full.svg',
              button: { icon: 'lucide:arrow-right', size: 'sm', rounded: 'lg' },
            }"
          />
        </div>
        <!-- Tile -->
        <div class="col-span-12 sm:col-span-4">
          <WidgetsFinanceBankTile
            :config="{
              bankName: 'Bank of America',
              maskedNumber: '**** **** 5482',
              amount: 6729.87,
              currencyPrefix: '$',
              logoSrc: '/img/logos/companies/bank-of-america-full.svg',
              button: { icon: 'lucide:arrow-right', size: 'sm', rounded: 'lg' },
            }"
          />
        </div>
        <!-- Transactions -->
        <div class="col-span-12">
          <WidgetsTransactionsSummary
            :config="{
              title: 'Transaction History',
              linkText: 'View detailed history',
              linkUrl: '/banking/transactions',
              maxItems: 10,
              showSearch: true,
            }"
          />
        </div>
        <!-- Chart -->
        <div class="col-span-12 lg:col-span-6">
          <WidgetsChartsSummaryCard
            :config="{
              title: 'All Expenses',
              metrics: [
                { label: 'Daily', value: '$276.40' },
                { label: 'Weekly', value: '$1,287.12' },
                { label: 'Monthly', value: '$6,832.41' },
              ],
            }"
          >
            <ChartDonutExpenses />
          </WidgetsChartsSummaryCard>
        </div>
        <!-- Chart -->
        <div class="col-span-12 lg:col-span-6">
          <WidgetsChartsSummaryCard
            :config="{
              title: 'Income',
              metrics: [
                { label: 'Daily', value: '$378.12' },
                { label: 'Weekly', value: '$832.21' },
                { label: 'Monthly', value: '$4214.97' },
              ],
            }"
          >
            <ChartBarMultiIncome />
          </WidgetsChartsSummaryCard>
        </div>
      </div>
    </div>
  </div>
</template>
