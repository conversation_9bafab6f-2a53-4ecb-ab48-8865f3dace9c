<script setup lang="ts">
import type {
  CryptoChartData,
  CryptoContentWidgetData,
  CryptoInfoCardData,
  CryptoPriceHeaderData,
  CryptoStatsTilesData,
} from '~/types/widgets'

definePageMeta({
  title: 'Cryptocurrency',
  preview: {
    title: 'Banking dashboard v3',
    description: 'For banking and accounts',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-banking-3.png',
    srcDark: '/img/screens/dashboards-banking-3-dark.png',
    order: 9,
  },
})

const activePeriod = ref('week')

// Crypto price header data
const priceHeaderData: CryptoPriceHeaderData = {
  currency: {
    name: 'Bitcoin',
    symbol: 'BTC',
  },
  price: {
    value: 10584.3,
    formatted: formatPrice(10584.3),
  },
  change: {
    value: 359.46,
    percentage: 3.4,
    isPositive: true,
    formatted: '+3.4%',
  },
  periods: [
    { label: 'Hour', value: 'hour', active: activePeriod.value === 'hour' },
    { label: 'Day', value: 'day', active: activePeriod.value === 'day' },
    { label: 'Week', value: 'week', active: activePeriod.value === 'week' },
    { label: 'Month', value: 'month', active: activePeriod.value === 'month' },
    { label: 'Year', value: 'year', active: activePeriod.value === 'year' },
  ],
  onPeriodChange: (period: string) => {
    activePeriod.value = period
  },
}

// Crypto stats tiles data
const statsTilesData: CryptoStatsTilesData = {
  stats: [
    {
      title: 'Market Cap',
      value: formatPrice(129992260090),
    },
    {
      title: 'Volume 24h',
      value: `${formatPrice(453178)}K`,
    },
    {
      title: 'Low/high 24h',
      value: `${formatPrice(10212)} - ${formatPrice(10584)}`,
    },
  ],
}

// Main BTC chart data
const mainChartData: CryptoChartData = {
  title: 'BTC Price Chart',
  chartComponent: 'ChartAreaBtcPrice',
}

// BTC Evolution chart data
const evolutionChartData: CryptoChartData = {
  title: 'BTC Evolution',
  chartComponent: 'ChartRadialEvolution',
  actionButton: {
    label: 'Buy BTC',
    variant: 'default',
    size: 'sm',
  },
}

// BTC Popularity chart data
const popularityChartData: CryptoChartData = {
  title: 'BTC Popularity',
  chartComponent: 'ChartRadialPopularity',
  actionButton: {
    label: 'Buy BTC',
    variant: 'default',
    size: 'sm',
  },
}

// Bitcoin info card data
const bitcoinInfoData: CryptoInfoCardData = {
  icon: 'cryptocurrency:btc',
  title: 'Bitcoin',
  description: 'Bitcoin is a cryptocurrency invented in 2008 by an unknown person or group of people using the name Satoshi Nakamoto. It was launched anonymously, with no central bank or single administrator, and is peer-to-peer. Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
  actions: [
    {
      label: 'Compare',
      color: 'default',
      variant: 'default',
    },
    {
      label: 'Buy BTC',
      color: 'primary',
      variant: 'primary',
    },
  ],
}

// Team content widget data
const teamContentData: CryptoContentWidgetData = {
  title: 'My Team',
  viewAllLabel: 'View all',
  contentComponent: 'TeamListCompact',
  contentProps: {
    actions: true,
  },
}

// Popular coins content widget data
const popularCoinsData: CryptoContentWidgetData = {
  title: 'Popular Coins',
  viewAllLabel: 'View all',
  contentComponent: 'PopularCryptos',
}
</script>

<template>
  <div class="relative px-4 md:px-6 lg:px-8 pb-20">
    <!-- Price Header -->
    <WidgetsCryptoPriceHeader :data="priceHeaderData" />
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Column -->
      <div class="col-span-12 gap-4 lg:col-span-8">
        <div class="flex flex-col gap-4">
          <!-- Stats Tiles -->
          <WidgetsCryptoStatsTiles :data="statsTilesData" />
          <!-- Main Chart -->
          <WidgetsCryptoChart :data="mainChartData" />

          <!-- Subgrid Charts -->
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <WidgetsCryptoChart :data="evolutionChartData" />
            <WidgetsCryptoChart :data="popularityChartData" />
          </div>
        </div>
      </div>
      <!-- Column -->
      <div class="col-span-12 lg:col-span-4">
        <div class="flex flex-col gap-4">
          <!-- Bitcoin Info Card -->
          <WidgetsCryptoInfoCard :data="bitcoinInfoData" />

          <!-- Team Content -->
          <WidgetsCryptoContentWidget :data="teamContentData" />

          <!-- Popular Coins Content -->
          <WidgetsCryptoContentWidget :data="popularCoinsData" />
        </div>
      </div>
    </div>
  </div>
</template>
