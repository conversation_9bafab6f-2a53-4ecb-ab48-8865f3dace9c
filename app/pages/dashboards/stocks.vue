<script setup lang="ts">
import type {
  StockCardData,
  StockCategoryNavigationData,
  StockProfitEvolutionData,
  StockTrendingListData,
} from '~/types/widgets'

definePageMeta({
  title: 'Stocks',
  preview: {
    title: 'Stocks dashboard',
    description: 'For stock market analysis',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-stocks.png',
    srcDark: '/img/screens/dashboards-stocks-dark.png',
    order: 5,
  },
})

const stocks = ref([
  {
    id: 'LKN',
    icon: 'fa6-brands:linkedin-in',
    iconColors: 'bg-blue-800 text-white shadow-blue-500/20 dark:shadow-blue-800/20',
    name: 'Linkedin Corp.',
    price: 1478.32,
  },
  {
    id: 'GTB',
    icon: 'fa6-brands:github',
    iconColors: 'bg-muted-900 dark:bg-muted-100 dark:text-muted-900 text-white',
    name: 'Github Inc.',
    price: 978.21,
  },
  {
    id: 'INV',
    icon: 'fa6-brands:invision',
    iconColors: 'bg-rose-500 text-white shadow-rose-500/20 dark:shadow-rose-800/20',
    name: 'Invision Corp.',
    price: 1671.89,
  },
  {
    id: 'TLG',
    icon: 'fa-brands:telegram-plane',
    iconColors: 'bg-sky-700 text-white shadow-sky-500/20 dark:shadow-sky-800/20',
    name: 'Telegram Inc.',
    price: 491.54,
  },
  {
    id: 'KCK',
    icon: 'fa-brands:kickstarter-k',
    iconColors: 'bg-emerald-500 text-white shadow-emerald-500/20 dark:shadow-emerald-800/20',
    name: 'Kickstarter Inc.',
    price: 1115.68,
  },
  {
    id: 'GGL',
    icon: 'fa-brands:google',
    iconColors: 'bg-yellow-500 text-white shadow-yellow-500/20 dark:shadow-yellow-800/20',
    name: 'Google Corp.',
    price: 2514.51,
  },
])

// Datepicker
const date = ref(new Date())

// Common stock actions
const stockActions = [
  {
    title: 'Invest',
    description: 'Buys more stocks',
    icon: 'solar:wad-of-money-linear',
    href: '#',
  },
  {
    title: 'Benchmark',
    description: 'Compare other sources',
    icon: 'solar:mirror-left-linear',
    href: '#',
  },
  {
    title: 'Trade',
    description: 'View opportunities',
    icon: 'solar:filters-linear',
    href: '#',
  },
  {
    title: 'Wallet',
    description: 'Manage your wallet',
    icon: 'solar:wallet-2-linear',
    href: '#',
  },
]

// Category navigation data
const categoryNavigationData: StockCategoryNavigationData = {
  categories: [
    {
      name: 'Energy',
      icon: 'solar:bolt-bold-duotone',
      iconColor: 'text-yellow-400',
      hoverColors: 'group-hover:bg-yellow-400 dark:group-hover:bg-yellow-400',
      href: '#',
    },
    {
      name: 'Estate',
      icon: 'solar:buildings-bold-duotone',
      iconColor: 'text-primary-500',
      hoverColors: 'group-hover:bg-primary-500 dark:group-hover:bg-primary-500',
      href: '#',
    },
    {
      name: 'Agriculture',
      icon: 'solar:earth-bold-duotone',
      iconColor: 'text-success-500',
      hoverColors: 'group-hover:bg-success-500 dark:group-hover:bg-success-500',
      href: '#',
    },
    {
      name: 'Finance',
      icon: 'solar:money-bag-bold-duotone',
      iconColor: 'text-indigo-500',
      hoverColors: 'group-hover:bg-indigo-500 dark:group-hover:bg-indigo-500',
      href: '#',
    },
    {
      name: 'Diamonds',
      icon: 'solar:heart-lock-bold-duotone',
      iconColor: 'text-lime-500',
      hoverColors: 'group-hover:bg-lime-500 dark:group-hover:bg-lime-500',
      href: '#',
    },
    {
      name: 'Research',
      icon: 'solar:atom-bold-duotone',
      iconColor: 'text-sky-500',
      hoverColors: 'group-hover:bg-sky-500 dark:group-hover:bg-sky-500',
      href: '#',
    },
    {
      name: 'Technology',
      icon: 'solar:home-wifi-angle-bold-duotone',
      iconColor: 'text-orange-500',
      hoverColors: 'group-hover:bg-orange-500 dark:group-hover:bg-orange-500',
      href: '#',
    },
    {
      name: 'Healthcare',
      icon: 'solar:pill-bold-duotone',
      iconColor: 'text-rose-500',
      hoverColors: 'group-hover:bg-rose-500 dark:group-hover:bg-rose-500',
      href: '#',
    },
  ],
}

// Stock cards data
const stockCardsData: StockCardData[] = [
  {
    isOpen: true,
    chartComponent: 'VectorChartStockOne',
    company: {
      symbol: 'TSL',
      name: 'Tesla Motors',
      icon: 'cib:tesla',
      iconColors: 'bg-red-500 text-white shadow-xl shadow-red-500/20 dark:shadow-red-800/20',
    },
    price: 3876.21,
    actions: stockActions,
  },
  {
    isOpen: true,
    chartComponent: 'VectorChartStockTwo',
    company: {
      symbol: 'FCB',
      name: 'Facebook Inc',
      icon: 'fa6-brands:facebook-f',
      iconColors: 'bg-blue-800 text-white shadow-xl shadow-blue-500/20 dark:shadow-blue-800/20',
    },
    price: 5214.87,
    actions: stockActions,
  },
  {
    isOpen: false,
    chartComponent: 'VectorChartStockThree',
    company: {
      symbol: 'TWT',
      name: 'X Inc',
      icon: 'fa6-brands:x-twitter',
      iconColors: 'bg-sky-500 text-white shadow-xl shadow-sky-500/20 dark:shadow-sky-800/20',
    },
    price: 1657.12,
    actions: stockActions,
  },
]

// Trending stocks data
const trendingStocksData: StockTrendingListData = {
  title: 'Trending Stocks',
  viewAllLabel: 'View all',
  stocks: stocks.value.map(stock => ({
    id: stock.id,
    name: stock.name,
    company: stock.name,
    icon: stock.icon,
    iconColors: stock.iconColors,
    price: stock.price,
  })),
}

// Profit evolution data
const profitEvolutionData: StockProfitEvolutionData = {
  title: 'Profit Evolution',
  viewAllLabel: 'View all',
  chartComponent: 'ChartBarProfit',
}

// Event handlers
function handleViewAll() {
  console.log('View all clicked')
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Investments -->
    <div
      class="flex justify-around gap-6 overflow-x-auto pb-6 lg:overflow-visible"
    >
      <!-- Item -->
      <NuxtLink to="#" class="group flex flex-1 flex-col text-center">
        <div
          class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90 group-hover:bg-yellow-400 dark:group-hover:bg-yellow-400"
        >
          <div
            class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
          >
            <Icon name="solar:bolt-bold-duotone" class="size-7 text-yellow-400" />
          </div>
        </div>
        <BaseHeading
          as="h5"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
        >
          <span class="font-sans text-sm">Energy</span>
        </BaseHeading>
      </NuxtLink>
      <!-- Item -->
      <NuxtLink to="#" class="group flex flex-1 flex-col text-center">
        <div
          class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 group-hover:bg-primary-500 dark:group-hover:bg-primary-500 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90"
        >
          <div
            class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
          >
            <Icon
              name="solar:buildings-bold-duotone"
              class="text-primary-500 size-7"
            />
          </div>
        </div>
        <BaseHeading
          as="h5"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
        >
          <span class="font-sans text-sm">Estate</span>
        </BaseHeading>
      </NuxtLink>
      <!-- Item -->
      <NuxtLink to="#" class="group flex flex-1 flex-col text-center">
        <div
          class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 group-hover:bg-success-500 dark:group-hover:bg-success-500 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90"
        >
          <div
            class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
          >
            <Icon name="solar:earth-bold-duotone" class="text-success-500 size-7" />
          </div>
        </div>
        <BaseHeading
          as="h5"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
        >
          <span class="font-sans text-sm">Agriculture</span>
        </BaseHeading>
      </NuxtLink>
      <!-- Item -->
      <NuxtLink to="#" class="group flex flex-1 flex-col text-center">
        <div
          class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90 group-hover:bg-indigo-500 dark:group-hover:bg-indigo-500"
        >
          <div
            class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
          >
            <Icon name="solar:money-bag-bold-duotone" class="size-7 text-indigo-500" />
          </div>
        </div>
        <BaseHeading
          as="h5"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
        >
          <span class="font-sans text-sm">Finance</span>
        </BaseHeading>
      </NuxtLink>
      <!-- Item -->
      <NuxtLink to="#" class="group flex flex-1 flex-col text-center">
        <div
          class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90 group-hover:bg-lime-500 dark:group-hover:bg-lime-500"
        >
          <div
            class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
          >
            <Icon name="solar:heart-lock-bold-duotone" class="size-7 text-lime-500" />
          </div>
        </div>
        <BaseHeading
          as="h5"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
        >
          <span class="font-sans text-sm">Diamonds</span>
        </BaseHeading>
      </NuxtLink>
      <!-- Item -->
      <NuxtLink to="#" class="group flex flex-1 flex-col text-center">
        <div
          class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90 group-hover:bg-sky-500 dark:group-hover:bg-sky-500"
        >
          <div
            class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
          >
            <Icon name="solar:atom-bold-duotone" class="size-7 text-sky-500" />
          </div>
        </div>
        <BaseHeading
          as="h5"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
        >
          <span class="font-sans text-sm">Research</span>
        </BaseHeading>
      </NuxtLink>
      <!-- Item -->
      <NuxtLink to="#" class="group flex flex-1 flex-col text-center">
        <div
          class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90 group-hover:bg-orange-500 dark:group-hover:bg-orange-500"
        >
          <div
            class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
          >
            <Icon name="solar:home-wifi-angle-bold-duotone" class="size-7 text-orange-500" />
          </div>
        </div>
        <BaseHeading
          as="h5"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
        >
          <span class="font-sans text-sm">Technology</span>
        </BaseHeading>
      </NuxtLink>
      <!-- Item -->
      <NuxtLink to="#" class="group flex flex-1 flex-col text-center">
        <div
          class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90 group-hover:bg-rose-500 dark:group-hover:bg-rose-500"
        >
          <div
            class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
          >
            <Icon name="solar:pill-bold-duotone" class="size-7 text-rose-500" />
          </div>
        </div>
        <BaseHeading
          as="h5"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
        >
          <span class="font-sans text-sm">Healthcare</span>
        </BaseHeading>
      </NuxtLink>
    </div>
    <!-- Tile grid -->
    <div class="mb-4 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <!-- Stock Cards -->
      <WidgetsStocksStockCard
        v-for="(stockCard, index) in stockCardsData"
        :key="index"
        :data="stockCard"
      />
    </div>
    <!-- Card grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Grid item -->
      <div class="col-span-12 md:col-span-6 lg:col-span-4">
        <WidgetsStocksTrendingList
          :data="trendingStocksData"
          @view-all="handleViewAll"
        />
      </div>
      <!-- Grid item -->
      <div class="col-span-12 md:col-span-6 lg:col-span-4">
        <WidgetsStocksProfitEvolution
          :data="profitEvolutionData"
          @view-all="handleViewAll"
        />
      </div>
      <!-- Grid item -->
      <div class="col-span-12 md:col-span-6 lg:col-span-4">
        <BaseCard class="p-4" rounded="lg">
          <LazyAddonDatepicker v-model="date" locale="en" label="Start date" />
        </BaseCard>
      </div>
    </div>
  </div>
</template>
