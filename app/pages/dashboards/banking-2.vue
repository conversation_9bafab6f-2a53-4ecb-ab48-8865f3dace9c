<script setup lang="ts">
import type {
  BankingCardsOverviewData,
  BankingSummaryData,
} from '~/types/widgets'

definePageMeta({
  title: 'Account expenses',
  preview: {
    title: 'Banking dashboard v2',
    description: 'For banking and accounts',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-banking-2.png',
    srcDark: '/img/screens/dashboards-banking-2-dark.png',
    order: 8,
  },
})

// Banking widgets data
const cardsOverview: BankingCardsOverviewData = {
  title: 'My Cards',
  cards: [
    {
      type: 'visa',
      endingNumber: 4986,
      balance: 6341.14,
    },
    {
      type: 'visa',
      endingNumber: 3619,
      balance: 2211.27,
    },
    {
      type: 'visa',
      endingNumber: 1231,
      balance: 839.49,
    },
  ],
  description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Peccata paria minime esse deterritum.',
  banks: [
    {
      id: 1,
      name: 'X Bank',
      text: 'checking **** **** 0499',
      media: '/img/icons/banking/bank-1.svg',
    },
    {
      id: 2,
      name: 'Bankaria',
      text: 'checking **** **** 1548',
      media: '/img/icons/banking/bank-2.svg',
    },
    {
      id: 3,
      name: 'Bankus',
      text: 'checking **** **** 8448',
      media: '/img/icons/banking/bank-3.svg',
    },
  ],
  currentBalance: 6341.14,
  cardNumber: '**** **** **** 4986',
  income: 2324.12,
  expense: 518.41,
}

const bankingSummary: BankingSummaryData = {
  summaryTitle: 'Monthly Summary',
  summary: {
    income: {
      label: 'Income',
      value: 2324.12,
    },
    expenses: {
      label: 'Expenses',
      value: 1509.27,
    },
  },
  chartComponent: 'ChartAreaExpenses',
  transactionsTitle: 'Transactions',
  transactions: [
    {
      icon: 'solar:chef-hat-bold-duotone',
      iconClass: 'bg-success-500/10 text-success-500',
      title: 'Food delivery',
      timestamp: 'Oct 23, 2022 - 8:46pm',
      amount: 41.49,
      isIncome: false,
    },
    {
      icon: 'solar:shop-bold-duotone',
      iconClass: 'bg-orange-500/10 text-orange-500',
      title: 'Market Earnings',
      timestamp: 'Oct 18, 2022 - 9:11am',
      amount: 263.39,
      isIncome: true,
    },
    {
      icon: 'solar:bag-smile-bold-duotone',
      iconClass: 'bg-indigo-500/10 text-indigo-500',
      title: 'Online order',
      timestamp: 'Oct 16, 2022 - 2:13pm',
      amount: 92.17,
      isIncome: false,
    },
  ],
  actions: {
    settings: 'Settings',
    create: 'Create',
  },
}

// Event handlers
function handleAddCard() {
  // Handle add card action
  console.log('Add card clicked')
}

function handleSettings() {
  // Handle settings action
  console.log('Settings clicked')
}

function handleCreate() {
  // Handle create action
  console.log('Create clicked')
}
</script>

<template>
  <div class="relative px-4 md:px-6 lg:px-8 pb-20">
    <div class="flex flex-col gap-6">
      <!-- Cards Overview Widget -->
      <WidgetsFinanceCardsOverview
        :data="cardsOverview"
        @add-card="handleAddCard"
      />

      <!-- Banking Summary Widget -->
      <WidgetsFinanceBankingSummary
        :data="bankingSummary"
        @settings="handleSettings"
        @create="handleCreate"
      />
    </div>
  </div>
</template>
