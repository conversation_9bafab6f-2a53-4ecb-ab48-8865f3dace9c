<script setup lang="ts">
definePageMeta({
  title: 'Tracking',
  preview: {
    title: 'Banking dashboard v4',
    description: 'For personal account tracking',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-banking-4.png',
    srcDark: '/img/screens/dashboards-banking-4-dark.png',
    order: 10,
    new: true,
  },
})

const accounts = ref([
  {
    id: 1,
    type: 'checking',
    label: '**** 4897',
    number: '1487 3256 54122 4897',
    balance: 9543.12,
  },
  {
    id: 2,
    type: 'checking',
    label: '**** 4869',
    number: '3524 65456 3245 4869',
    balance: 1211.67,
  },
  {
    id: 3,
    type: 'savings',
    label: '**** 6279',
    number: '3524 65456 3245 6279',
    balance: 4653.97,
  },
])

const selectedPerson = ref('<PERSON>')
const selectedAccount = ref(accounts.value[0])
const amount = ref()

const target = ref(null)
const open = ref(false)

function openDropdown() {
  open.value = true
}

onClickOutside(target, () => (open.value = false))

function setAccount(account: any) {
  selectedAccount.value = account
  open.value = false
}
</script>

<template>
  <div class="space-y-4 px-4 md:px-6 lg:px-8 pb-20">
    <div class="grid grid-cols-12 gap-4">
      <!-- Tile -->
      <div class="col-span-12 sm:col-span-6 2xl:landscape:col-span-3">
        <WidgetsStatsProgressTile
          :stat="{
            title: 'Weekly Profit',
            value: '$926.31',
            change: '+2.3%',
            progress: 75,
            icon: 'solar:fire-minimalistic-bold-duotone',
            colorClass: 'text-orange-500',
            circleVariant: 'none',
          }"
        />
      </div>
      <!-- Tile -->
      <div class="col-span-12 sm:col-span-6 2xl:landscape:col-span-3">
        <WidgetsStatsProgressTile
          :stat="{
            title: 'Total Income',
            value: '$2,629.43',
            change: '+1.7%',
            progress: 48,
            icon: 'solar:crown-line-bold-duotone',
            colorClass: 'text-green-500',
            circleVariant: 'none',
          }"
        />
      </div>
      <!-- Tile -->
      <div class="col-span-12 sm:col-span-6 2xl:landscape:col-span-3">
        <WidgetsStatsProgressTile
          :stat="{
            title: 'Total Expenses',
            value: '$1,821.56',
            change: '-2.1%',
            progress: 34,
            icon: 'solar:card-recive-bold-duotone',
            colorClass: 'text-destructive-500',
            circleVariant: 'none',
          }"
        />
      </div>
      <!-- Tile -->
      <div class="col-span-12 sm:col-span-6 2xl:landscape:col-span-3">
        <WidgetsStatsProgressTile
          :stat="{
            title: 'Total Taxes',
            value: '$712.19',
            change: '+1.1%',
            progress: 82,
            icon: 'solar:calculator-minimalistic-bold-duotone',
            colorClass: 'text-primary-500',
            circleVariant: 'primary',
          }"
        />
      </div>
    </div>
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 space-y-4 lg:col-span-8">
        <!-- Account balance widget -->
        <WidgetsFinanceAccountBalance
          :data="{
            title: 'Current Balance',
            balance: 12847.65,
            change: 287.43,
            changeLabel: 'This week',
            changeIcon: 'lucide:arrow-up',
            isIncrease: true,
          }"
        />
        <!-- Transactions widget -->
        <WidgetsFinanceTransactionCompact
          :config="{
            title: 'Recent Transactions',
            linkText: 'View all transactions',
            linkUrl: '/transactions',
            showLink: true,
            emptyTitle: 'No transactions yet',
            emptySubtitle: 'Your recent transactions will appear here once you start making payments.',
            maxItems: 8,
          }"
        />
      </div>
      <div class="col-span-12 space-y-4 lg:col-span-4">
        <WidgetsFinanceQuickTransfer
          :accounts="accounts"
          @submit="() => {}"
        />
        <WidgetsCtaQuickCash />
      </div>
    </div>
  </div>
</template>
