<script setup lang="ts">
import type { GenericChartData } from '~/types/widgets'

definePageMeta({
  title: 'Apex Charts',
  preview: {
    title: 'Chart examples',
    description: 'For data visualization',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-charts.png',
    srcDark: '/img/screens/dashboards-charts-dark.png',
    order: 26,
  },
})

// Chart data array
const chartWidgets: GenericChartData[] = [
  { title: 'Line Chart', chartComponent: 'ChartLine' },
  { title: 'Line Chart', chartComponent: 'ChartLineMulti' },
  { title: 'Stepline Chart', chartComponent: 'ChartLineStep' },
  { title: 'Area Chart', chartComponent: 'ChartArea' },
  { title: 'Multiple Area', chartComponent: 'ChartAreaMulti' },
  { title: 'Bar Chart', chartComponent: 'ChartBar' },
  { title: 'Multiple Bars', chartComponent: 'ChartBarMulti' },
  { title: 'Stacked Bars', chartComponent: 'ChartBarStacked' },
  { title: 'Range Column', chartComponent: 'ChartBarRange' },
  { title: 'Horizontal Bar', chartComponent: 'ChartBarHorizontal' },
  { title: 'Horizontal Multiple', chartComponent: 'ChartBarHorizontalMulti' },
  { title: 'Bubble Chart', chartComponent: 'ChartBubble' },
  { title: 'Timeline Chart', chartComponent: 'ChartTimeline', chartProps: { class: '2xl:col-span-2' } },
  { title: 'Scatter Chart', chartComponent: 'ChartScatter' },
  { title: 'Pie Chart', chartComponent: 'ChartPie' },
  { title: 'Donut Chart', chartComponent: 'ChartDonut' },
  { title: 'Radial Bar', chartComponent: 'ChartRadial' },
  { title: 'Radial Multiple', chartComponent: 'ChartRadialMulti' },
  { title: 'Radial Gauge', chartComponent: 'ChartRadialGauge' },
  { title: 'Gauge Chart', chartComponent: 'ChartRadialGaugeAlt' },
]
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 2xl:grid-cols-3">
      <WidgetsChartsGeneric
        v-for="(chart, index) in chartWidgets"
        :key="index"
        :data="chart"
      />
    </div>
  </div>
</template>
