<script setup lang="ts">
import type {
  PersonalContentListData,
  PersonalHeaderData,
  PromotionalCardData,
  SimpleChartData,
} from '~/types/widgets'
import { computed } from 'vue'
import { useAuth } from '~/composables/auth'

definePageMeta({
  title: 'My Projects',
  preview: {
    title: 'Personal dashboard v2',
    description: 'For personal usage and reports',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-personal-2.png',
    srcDark: '/img/screens/dashboards-personal-2-dark.png',
    order: 2,
  },
})

const { currentProfile } = useAuth()

const avatarSrc = computed(() => currentProfile.value?.avatar_url ?? '/img/avatars/10.svg')

const displayName = computed(() => {
  const p = currentProfile.value
  if (!p)
    return 'there'
  if (p.firstName && p.lastName)
    return `${p.firstName} ${p.lastName.charAt(0)}.`
  return p.display_name || 'there'
})

// Personal header data
const headerData: PersonalHeaderData = {
  avatarSrc: avatarSrc.value,
  badgeSrc: '/img/icons/flags/united-states-of-america.svg',
  greeting: 'Welcome back',
  displayName: displayName.value,
  subtitle: 'It\'s nice to see you again',
  metric: {
    value: 31,
    label: 'Tasks',
    description: 'Are currently pending',
  },
  promotionalCard: {
    text: 'Start using our team and project management tools',
    link: {
      href: '#',
      text: 'Learn More',
    },
    icon: {
      name: 'ph:crown-duotone',
      class: 'text-primary-400/50 size-10',
    },
  },
}

// Content list widgets data
const projectsData: PersonalContentListData = {
  title: 'Current projects',
  viewAllLink: {
    href: '#',
    text: 'View all',
  },
  contentComponent: 'ProjectListCompact',
  contentProps: {}, // Empty props to allow default values
}

const teamData: PersonalContentListData = {
  title: 'My Team',
  viewAllLink: {
    href: '#',
    text: 'View all',
  },
  contentComponent: 'TeamListCompact',
  contentProps: {
    actions: true,
  },
  cardClass: 'p-4 md:p-6',
}

const todoData: PersonalContentListData = {
  title: 'Todo today',
  viewAllLink: {
    href: '#',
    text: 'View all',
  },
  contentComponent: 'TodoListCompact',
  contentProps: {}, // Empty props to allow default values
  cardClass: 'p-6',
}

// Chart widgets data
const taskCompletionChart: SimpleChartData = {
  title: 'Task completion',
  chartComponent: 'ChartAreaTaskCompletion',
}

const teamEfficiencyChart: SimpleChartData = {
  title: 'Team Efficiency',
  chartComponent: 'ChartBarTeamEfficiency',
}

// Promotional card data
const ctaPromotionalCard: PromotionalCardData = {
  title: 'You\'re doing great!',
  description: 'Start using our team and project management tools',
  link: {
    href: '#',
    text: 'Learn More',
  },
  icon: {
    name: 'ph:crown-duotone',
    class: 'text-primary-600/50 size-14',
  },
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Grid column -->
      <div class="col-span-12">
        <!-- Personal Header Widget -->
        <WidgetsPersonalHeader :data="headerData" />
      </div>
      <!-- Grid column -->
      <div class="lg:landscape:col-span-8 col-span-12 xl:landscape:col-span-8">
        <!-- Inner grid -->
        <div class="flex flex-col gap-4">
          <!-- Projects List Widget -->
          <WidgetsPersonalContentList :data="projectsData" />

          <!-- Task Completion Chart Widget -->
          <WidgetsChartsSimple :data="taskCompletionChart" />

          <!-- Team Efficiency Chart Widget -->
          <WidgetsChartsSimple :data="teamEfficiencyChart" />
        </div>
      </div>
      <!-- Grid column -->
      <div class="lg:landscape:col-span-4 col-span-12 xl:landscape:col-span-4">
        <!-- Inner grid -->
        <div class="grid gap-4 lg:flex lg:flex-col">
          <!-- Widget -->
          <ActionText
            title="Upgrade to Pro"
            text="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quid censes in Latino fore? Nam ante Aristippus, et ille melius."
            label="Upgrade Now"
            to="#"
            rounded="md"
          />
          <!-- Team Widget -->
          <WidgetsPersonalContentList :data="teamData" />

          <!-- Todo Widget -->
          <WidgetsPersonalContentList :data="todoData" />

          <!-- CTA Promotional Card -->
          <WidgetsUtilsPromotionalCard :data="ctaPromotionalCard" />
        </div>
      </div>
    </div>
  </div>
</template>
