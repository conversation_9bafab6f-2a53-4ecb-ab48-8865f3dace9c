<script setup lang="ts">
import type {
  CreativeGenericData,
  GoalOverviewData,
  KpiTileData,
  ProfitChartData,
  SalesGrowthData,
} from '~/types/widgets'

// RevenueOverviewData interface for the revenue-overview widget
interface RevenueOverviewData {
  title?: string
  metrics?: {
    label: string
    value: string
  }[]
  chartComponent?: string
  actionButton?: {
    label: string
    onClick?: () => void
  }
}

definePageMeta({
  title: 'Analytics',
  preview: {
    title: 'Analytics dashboard',
    description: 'For heavy stats and kpis',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-analytics.png',
    srcDark: '/img/screens/dashboards-analytics-dark.png',
    order: 4,
  },
})

// Datepicker
const date = ref(new Date())

// KPI tile data
const kpiTiles: KpiTileData[] = [
  {
    title: 'Transactions',
    icon: {
      name: 'solar:chart-square-bold-duotone',
      color: 'bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2',
    },
    value: 7549,
    trend: {
      value: 7.8,
      percentage: 7.8,
      isIncrease: true,
      period: 'since last month',
    },
  },
  {
    title: 'Subscriptions',
    icon: {
      name: 'solar:gamepad-bold-duotone',
      color: 'bg-primary-100 text-primary-500 dark:border-2 dark:border-primary-500 dark:bg-primary-500/20 dark:text-primary-400',
    },
    value: 1611,
    trend: {
      value: -2.7,
      percentage: -2.7,
      isIncrease: false,
      period: 'going down',
    },
  },
  {
    title: 'Referals',
    icon: {
      name: 'solar:home-smile-angle-bold-duotone',
      color: 'bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2',
    },
    value: 862,
    trend: {
      value: 4.5,
      percentage: 4.5,
      isIncrease: true,
      period: 'going up',
    },
  },
]

// Goal Overview widget data
const goalOverviewData: GoalOverviewData = {
  title: 'Goal Overview',
  completed: 1431,
  inProgress: 219,
}

// Sales Growth widget data
const salesGrowthData: SalesGrowthData = {
  title: 'Sales Growth',
  channel: {
    name: 'Shopify',
    icon: 'logos:shopify',
    description: 'Best selling channel',
  },
}

// Profit Chart widget data
const profitChartData: ProfitChartData = {
  title: 'Profit',
}

// Revenue Overview widget data
const revenueOverviewData: RevenueOverviewData = {
  title: 'Revenue',
  metrics: [
    { label: 'This month', value: '$75,689' },
    { label: 'Last month', value: '$59,724' },
  ],
  chartComponent: 'ChartLineRevenue',
  actionButton: {
    label: 'Details',
  },
}

// Sidebar widget configurations
const vcardData: CreativeGenericData = {
  contentComponent: 'VcardRight',
  cardClass: 'p-4 md:p-6',
}

const followersData: CreativeGenericData = {
  contentComponent: 'FollowersCompact',
  cardClass: 'p-4 md:p-6',
}

const notificationsData: CreativeGenericData = {
  contentComponent: 'NotificationsCompact',
  cardClass: 'p-4 md:p-6',
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="grid grid-cols-12 gap-4">
      <!-- Grid column -->
      <div class="col-span-12 2xl:landscape:col-span-9">
        <!-- Inner grid -->
        <div class="grid grid-cols-12 gap-4">
          <!-- KPI Tiles using reusable widget -->
          <div
            v-for="(tile, index) in kpiTiles"
            :key="index"
            class="col-span-12 md:col-span-4"
          >
            <WidgetsStatsKpiTile :data="tile" />
          </div>
          <!-- Chart -->
          <div class="col-span-12 md:col-span-8">
            <WidgetsChartsRevenueOverview :data="revenueOverviewData" />
          </div>
          <!-- Chart -->
          <div class="col-span-12 md:col-span-4">
            <WidgetsChartsGoalOverview :data="goalOverviewData" />
          </div>
          <!-- Chart -->
          <div class="col-span-12 md:col-span-4">
            <WidgetsChartsSalesGrowth :data="salesGrowthData" />
          </div>
          <!-- Chart -->
          <div class="col-span-12 md:col-span-8">
            <WidgetsChartsProfitChart :data="profitChartData" />
          </div>
        </div>
      </div>
      <!-- Grid column -->
      <div class="col-span-12 2xl:landscape:col-span-3">
        <!-- Inner column -->
        <div
          class="lg:portrait:grid-cols-2 lg:landscape:grid-cols-2 grid gap-4 2xl:landscape:flex 2xl:landscape:flex-col"
        >
          <!-- Sidebar Widgets -->
          <WidgetsCreativeGeneric :data="vcardData" />
          <!-- Calendar (special handling for v-model) -->
          <BaseCard rounded="md" class="p-4">
            <AddonDatepicker v-model="date" locale="en" label="Start date" />
          </BaseCard>
          <WidgetsCreativeGeneric :data="followersData" />
          <WidgetsCreativeGeneric :data="notificationsData" />
        </div>
      </div>
    </div>
  </div>
</template>
