<script setup lang="ts">
import { computed } from 'vue'
import { useAuth } from '~/composables/auth'

definePageMeta({
  title: 'Activity',
  preview: {
    title: 'Personal dashboard v1',
    description: 'For personal usage and reports',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-personal-1.png',
    srcDark: '/img/screens/dashboards-personal-1-dark.png',
    order: 1,
  },
})

const { currentProfile } = useAuth()

const avatarSrc = computed(() => currentProfile.value?.avatar_url ?? '/img/avatars/10.svg')

const displayName = computed(() => {
  const p = currentProfile.value
  if (!p)
    return 'there'
  if (p.firstName && p.lastName)
    return `${p.firstName} ${p.lastName.charAt(0)}.`
  return p.display_name || 'there'
})

// Dynamic widget data - demonstrating reusability
const statsTitle = computed(() => `${displayName.value}'s Performance`)

// Personalized achievement based on user
const achievementData = computed(() => ({
  title: `Welcome back, ${displayName.value}!`,
  subtitle: 'You\'ve been crushing your goals this month. Keep up the amazing work!',
  buttonText: 'View Progress',
  buttonLink: '/progress',
  image: '/img/illustrations/widgets/1.svg',
  badgeSmall: '/img/illustrations/widgets/3.svg',
  badgeMedium: '/img/illustrations/widgets/2.svg',
  count: 3,
}))

// Dynamic CTA that could change based on user tier or time
const ctaData = {
  title: 'Ready to scale up?',
  subtitle: 'Unlock advanced features with our premium plan',
  linkText: 'Upgrade Now',
  linkUrl: '/pricing',
  iconName: 'ph:rocket-duotone',
  gradientFrom: 'from-indigo-900',
  gradientTo: 'to-purple-800',
}

// Team data including current user
const teamEfficiencyData = computed(() => ({
  title: 'Your Team Performance',
  members: [
    { src: currentProfile.value?.avatar_url || '/img/avatars/10.svg' },
    { text: 'M', bgClass: 'bg-green-400/10 dark:bg-green-400/20', textClass: 'text-green-600' },
    { src: '/img/avatars/4.svg' },
    { text: 'J', bgClass: 'bg-blue-400/10 dark:bg-blue-400/20', textClass: 'text-blue-600' },
  ],
}))

// Could be configured based on user preferences or date range
const expenseReportData = {
  title: 'Monthly Expense Breakdown',
}

// Configurable transaction widget
const transactionConfig = {
  title: 'Latest Transactions',
  linkText: 'View all transactions',
  linkUrl: '/transactions',
  maxItems: 6,
  showSearch: false,
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <LayoutsDashboardHeader
      :avatar-src="avatarSrc"
      :display-name="displayName"
    />
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Example: Reusable components with dynamic props -->
      <!-- Same components can be used in different contexts with different data -->

      <!-- Area Chart card -->
      <div class="col-span-12 lg:col-span-6 xl:col-span-5">
        <WidgetsCrmCustomers title="New / returning customers" />
      </div>
      <!-- Quick stats -->
      <div class="lg:landscape:col-span-6 col-span-12 2xl:landscape:col-span-4">
        <WidgetsStatsQuick :title="statsTitle" />
      </div>
      <!-- Grid item -->
      <div class="lg:landscape:col-span-6 col-span-12 2xl:landscape:col-span-3">
        <WidgetsInfoAchievements :achievement="achievementData" />
      </div>
      <!-- CTA card -->
      <div
        class="lg:portrait:col-span-6 lg:landscape:col-span-6 col-span-12 2xl:landscape:col-span-3"
      >
        <WidgetsCtaTeam :cta="ctaData" />
      </div>
      <!-- Radial Bar card -->
      <div
        class="lg:portrait:col-span-6 lg:landscape:col-span-5 col-span-12 2xl:landscape:col-span-4"
      >
        <WidgetsChartsTeamEfficiency :team-data="teamEfficiencyData" />
      </div>
      <!-- Bar chart card -->
      <div class="lg:landscape:col-span-7 col-span-12 2xl:landscape:col-span-5">
        <WidgetsChartsExpenseReport :report-data="expenseReportData" />
      </div>
      <div class="col-span-12 md:col-span-12">
        <!-- Transactions widget -->
        <WidgetsTransactionsSummary :config="transactionConfig" />
      </div>
    </div>
  </div>
</template>
