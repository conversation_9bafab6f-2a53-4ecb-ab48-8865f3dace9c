<script setup lang="ts">
import type {
  ListTitledData,
  WriterArticlesSectionData,
  WriterHeaderData,
  WriterMetricTileData,
} from '~/types/widgets'

definePageMeta({
  title: 'Writer',
  preview: {
    title: 'Writer dashboard',
    description: 'For writers and bloggers',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-writer.png',
    srcDark: '/img/screens/dashboards-writer-dark.png',
    order: 18,
  },
})

// Widget data configurations
const headerData: WriterHeaderData = {
  illustration: '/img/illustrations/dashboards/writer/readers.svg',
  illustrationAlt: 'Readers illustration',
  greeting: 'Hello, <PERSON>',
  description: 'Have any ideas for a new article? If not, you should definitely check the feed for some inspiration.',
  ctaIcon: 'lucide:plus',
  ctaText: 'New Article',
}

const metricTiles: WriterMetricTileData[] = [
  {
    label: 'Articles',
    value: 138,
  },
  {
    label: 'Readers',
    value: 921,
  },
  {
    label: 'Comments',
    value: 279,
  },
]

const popularAuthorsData: ListTitledData = {
  title: 'Popular authors',
  listComponent: 'AuthorsListCompact',
}

const latestCommentsData: ListTitledData = {
  title: 'Latest comments',
  listComponent: 'CommentListCompact',
}

const articlesData: WriterArticlesSectionData = {
  title: 'New articles',
  filters: [
    { label: 'Recent', value: 'recent' },
    { label: 'Popular', value: 'popular' },
  ],
  defaultFilter: 'recent',
  articles: [
    {
      id: '1',
      title: 'Learning the modern novel',
      excerpt: 'Some article content and lorem ipsum sit dolor amet as a nice dummy subtitle',
      image: '/img/illustrations/dashboards/writer/post-1.svg',
      imageAlt: 'Post image',
      link: '#',
      category: 'recent',
      author: {
        avatar: '/img/avatars/6.svg',
        initials: 'BT',
        name: 'Mike Janovski',
        role: 'Novel writer',
      },
    },
    {
      id: '2',
      title: '5 writing tips just for you',
      excerpt: 'Some article content and lorem ipsum sit dolor amet as a nice dummy subtitle',
      image: '/img/illustrations/dashboards/writer/post-2.svg',
      imageAlt: 'Post image',
      link: '#',
      category: 'popular',
      author: {
        avatar: '/img/avatars/5.svg',
        initials: 'BT',
        name: 'Clarissa Miller',
        role: 'Novel writer',
      },
    },
  ],
}
</script>

<template>
  <div class="overflow-hidden relative px-4 md:px-6 lg:px-8 pb-20">
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Column -->
      <div class="col-span-12 xl:col-span-8">
        <!-- Inner grid -->
        <div class="grid grid-cols-12 gap-4">
          <!-- Header -->
          <WidgetsWriterHeader :data="headerData" />
          <!-- Content -->
          <div class="col-span-12">
            <!-- Sub grid -->
            <div class="grid grid-cols-12 gap-4">
              <!-- Sub column -->
              <div class="col-span-12 sm:col-span-6">
                <div class="flex flex-col gap-4">
                  <!-- Metric Tiles -->
                  <div class="grid grid-cols-12 gap-4">
                    <template v-for="(metric, index) in metricTiles" :key="index">
                      <div class="col-span-4">
                        <WidgetsWriterMetricTile :data="metric" />
                      </div>
                    </template>
                  </div>
                  <!-- Popular Authors Widget -->
                  <WidgetsListTitled :data="popularAuthorsData" />
                </div>
              </div>
              <!-- Sub column -->
              <div class="col-span-12 sm:col-span-6">
                <!-- Latest Comments Widget -->
                <WidgetsListTitled :data="latestCommentsData" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Column -->
      <div class="col-span-12 xl:col-span-4">
        <WidgetsWriterArticlesSection :data="articlesData" />
      </div>
    </div>
  </div>
</template>
