<script setup lang="ts">
import type {
  CompanyPendingTicketsData,
  CompanyStatsHeaderData,
  CompanyTeamTableData,
  PersonalScoreData,
  SimpleChartWidgetData,
} from '~/types/widgets'

definePageMeta({
  title: 'Company',
  preview: {
    title: 'Company dashboard',
    description: 'For corporate business',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-company.png',
    srcDark: '/img/screens/dashboards-company-dark.png',
    order: 11,
  },
})

// Company widgets data
const statsHeader: CompanyStatsHeaderData = {
  stats: [
    {
      icon: 'solar:users-group-rounded-bold-duotone',
      value: '162',
      label: 'New users',
    },
    {
      icon: 'solar:card-send-bold-duotone',
      value: '$8,579',
      label: 'Daily income',
    },
    {
      icon: 'solar:case-bold-duotone',
      value: '192',
      label: 'Completed projects',
    },
    {
      icon: 'solar:ticker-star-bold-duotone',
      value: '32',
      label: 'Active tickets',
    },
  ],
}

const teamTable: CompanyTeamTableData = {
  team: [
    {
      id: '0',
      src: '/img/avatars/22.svg',
      name: '<PERSON>',
      role: 'UI/UX designer',
      expertise: 'UX Design',
      rate: 49,
      status: 'Available',
    },
    {
      id: '1',
      src: '/img/avatars/3.svg',
      name: 'John Cambell',
      role: 'Sales manager',
      expertise: 'Management',
      rate: 74,
      status: 'Hired',
    },
    {
      id: '2',
      src: '/img/avatars/9.svg',
      name: 'Beth Delanoe',
      role: 'Product designer',
      expertise: 'Product',
      rate: 43,
      status: 'Available',
    },
    {
      id: '3',
      src: '/img/avatars/14.svg',
      name: 'Andrew Higgs',
      role: 'Project manager',
      expertise: 'Project',
      rate: 69,
      status: 'New',
    },
    {
      id: '4',
      src: '/img/avatars/15.svg',
      name: 'Linda Fox',
      role: 'Frontend developer',
      expertise: 'Development',
      rate: 56,
      status: 'Hired',
    },
    {
      id: '5',
      src: '/img/avatars/16.svg',
      name: 'Derek Stone',
      role: 'Backend developer',
      expertise: 'Development',
      rate: 52,
      status: 'Available',
    },
    {
      id: '6',
      src: '/img/avatars/17.svg',
      name: 'Sara Smith',
      role: 'Marketing manager',
      expertise: 'Marketing',
      rate: 47,
      status: 'New',
    },
  ],
  actionLabel: 'View',
}

// Chart widgets
const companyOverviewChart: SimpleChartWidgetData = {
  chartComponent: 'CompanyOverview',
  cardClass: 'h-full p-4 md:p-6',
}

const areaStatsChart: SimpleChartWidgetData = {
  chartComponent: 'ChartAreaStats',
  cardClass: 'relative h-full',
  chartClass: '**:[.apexcharts-svg]:rounded-b-md',
}

const socialChannelsChart: SimpleChartWidgetData = {
  chartComponent: 'ChartBarSocialChannels',
  cardClass: 'relative h-full',
}

const profitChart: SimpleChartWidgetData = {
  title: 'Profit',
  chartComponent: 'ChartBarSalesProfit',
  cardClass: 'relative p-4 md:p-6',
}

// Personal score widget (reuse from health domain)
const personalScore: PersonalScoreData = {
  chartComponent: 'ChartRadialGaugeAlt',
  description: 'Your score has been calculated based on the latest metrics',
}

// Pending tickets widget
const pendingTickets: CompanyPendingTicketsData = {
  title: 'Pending tickets',
  viewAllLink: {
    href: '#',
    label: 'View all',
  },
  contentComponent: 'PendingTickets',
}

// Datepicker
const date = ref(new Date())
</script>

<template>
  <div class="relative overflow-hidden px-4 md:px-6 lg:px-8 pb-20">
    <div class="flex flex-col gap-4">
      <!-- Header -->
      <WidgetsCompanyStatsHeader :data="statsHeader" />
      <!-- Grid -->
      <div class="grid grid-cols-12 gap-4">
        <!-- Company Overview Chart -->
        <div class="lg:portrait:col-span-6 col-span-12 sm:landscape:col-span-4">
          <WidgetsChartsSimpleChart :data="companyOverviewChart" />
        </div>
        <!-- Area Stats Chart -->
        <div class="lg:portrait:col-span-6 col-span-12 sm:landscape:col-span-4">
          <WidgetsChartsSimpleChart :data="areaStatsChart" />
        </div>
        <!-- Social Channels Chart -->
        <div class="lg:portrait:col-span-12 col-span-12 sm:landscape:col-span-4">
          <WidgetsChartsSimpleChart :data="socialChannelsChart" />
        </div>
        <!-- Team Table -->
        <div class="col-span-12">
          <WidgetsCompanyTeamTable :data="teamTable" />
        </div>
        <!-- Personal Score -->
        <div class="lg:portrait:col-span-12 col-span-12 sm:landscape:col-span-3">
          <WidgetsChartsPersonalScore :data="personalScore" />
        </div>
        <!-- Profit Chart -->
        <div class="lg:portrait:col-span-12 col-span-12 sm:landscape:col-span-5 2xl:landscape:col-span-6">
          <WidgetsChartsSimpleChart :data="profitChart" />
        </div>
        <!-- Datepicker Widget -->
        <div class="lg:portrait:col-span-12 col-span-12 sm:landscape:col-span-4 2xl:landscape:col-span-3">
          <BaseCard class="flex h-full flex-col p-2">
            <LazyAddonDatepicker v-model="date" locale="en" label="Start date" />
          </BaseCard>
        </div>
        <!-- Pending Tickets -->
        <div class="col-span-12">
          <WidgetsCompanyPendingTickets :data="pendingTickets" />
        </div>
      </div>
    </div>
  </div>
</template>
