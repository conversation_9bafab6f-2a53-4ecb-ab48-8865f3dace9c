<script setup lang="ts">
import type {
  SoccerAvailableLeaguesData,
  SoccerContestPromoData,
  SoccerLiveMatchData,
  SoccerMatchesTableData,
} from '~/types/widgets'

definePageMeta({
  title: 'Soccer',
  preview: {
    title: 'Soccer dashboard',
    description: 'For soccer & football fans',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-soccer.png',
    srcDark: '/img/screens/dashboards-soccer-dark.png',
    order: 20,
  },
})

// Live match widget data
const liveMatchData: SoccerLiveMatchData = {
  tournament: 'UEFA Champions League',
  group: 'Group C',
  matchDescription: 'Match 3 of 6',
  liveTag: {
    icon: 'lucide:video',
    text: 'Live now',
  },
  match: {
    home: {
      name: 'Barcelona',
      logo: '/img/icons/soccer/teams/barcelona.svg',
      score: 4,
    },
    away: {
      name: 'PSG',
      logo: '/img/icons/soccer/teams/psg.svg',
      score: 0,
    },
  },
  ctaText: 'Watch Now',
}

// Available leagues widget data
const availableLeaguesData: SoccerAvailableLeaguesData = {
  title: 'Available Leagues',
  listComponent: 'LeagueListCompact',
}

// Contest promo widget data
const contestPromoData: SoccerContestPromoData = {
  title: 'Win awesome prizes in our contest',
  description: 'Predict who will win and how a match will end and get a chance to win incredible cash prizes.',
  ctaText: 'Learn More',
  ctaIcon: 'lucide:arrow-right',
  illustration: '/img/illustrations/dashboards/soccer/soccer-player.svg',
  illustrationAlt: 'Soccer Player',
  bgClass: 'bg-primary-800',
}

// Matches table widget data
const matches = [
  {
    id: 1,
    status: 'live' as const,
    time: '11:30',
    stadium: 'Camp Nou',
    match: {
      home: {
        name: 'Barcelona',
        logo: '/img/icons/soccer/teams/barcelona.svg',
        score: 4,
      },
      away: {
        name: 'PSG',
        logo: '/img/icons/soccer/teams/psg.svg',
        score: 0,
      },
    },
  },
  {
    id: 2,
    status: 'scheduled' as const,
    time: '14:30',
    stadium: 'Bernabeu',
    match: {
      home: {
        name: 'Real Madrid',
        logo: '/img/icons/soccer/teams/madrid.svg',
        score: 1,
      },
      away: {
        name: 'Liverpool',
        logo: '/img/icons/soccer/teams/liverpool.svg',
        score: 0,
      },
    },
  },
  {
    id: 3,
    status: 'scheduled' as const,
    time: '16:30',
    stadium: 'Arsenal',
    match: {
      home: {
        name: 'Arsenal',
        logo: '/img/icons/soccer/teams/arsenal.svg',
        score: undefined,
      },
      away: {
        name: 'Chelsea',
        logo: '/img/icons/soccer/teams/chelsea.svg',
        score: undefined,
      },
    },
  },
  {
    id: 4,
    status: 'scheduled' as const,
    time: '18:30',
    stadium: 'Goodison Park',
    match: {
      home: {
        name: 'Everton',
        logo: '/img/icons/soccer/teams/everton.svg',
        score: undefined,
      },
      away: {
        name: 'Liverpool',
        logo: '/img/icons/soccer/teams/liverpool.svg',
        score: undefined,
      },
    },
  },
  {
    id: 5,
    status: 'scheduled' as const,
    time: '20:30',
    stadium: 'José Zorrilla',
    match: {
      home: {
        name: 'Valladolid',
        logo: '/img/icons/soccer/teams/valladolid.svg',
        score: undefined,
      },
      away: {
        name: 'Zagreb',
        logo: '/img/icons/soccer/teams/zagreb.svg',
        score: undefined,
      },
    },
  },
  {
    id: 6,
    status: 'scheduled' as const,
    time: '22:30',
    stadium: 'Parc des Princes',
    match: {
      home: {
        name: 'PSG',
        logo: '/img/icons/soccer/teams/psg.svg',
        score: undefined,
      },
      away: {
        name: 'Chelsea',
        logo: '/img/icons/soccer/teams/chelsea.svg',
        score: undefined,
      },
    },
  },
]

const matchesTableData: SoccerMatchesTableData = {
  matches,
  filters: [
    { label: 'All', value: 'all', active: true },
    { label: 'Live', value: 'live' },
    { label: 'Finished', value: 'finished' },
    { label: 'Scheduled', value: 'scheduled' },
  ],
}
</script>

<template>
  <div class="relative mt-12 px-4 md:px-6 lg:px-8 pb-20">
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Column -->
      <div class="col-span-12 lg:col-span-4">
        <div class="flex flex-col gap-4">
          <!-- Live Match Widget -->
          <WidgetsSoccerLiveMatch :data="liveMatchData" />
          <!-- Available Leagues Widget -->
          <WidgetsSoccerAvailableLeagues :data="availableLeaguesData" />
        </div>
      </div>
      <!-- Column -->
      <div class="col-span-12 lg:col-span-8">
        <div class="flex flex-col gap-4">
          <!-- Contest Promo Header -->
          <WidgetsSoccerContestPromo :data="contestPromoData" />
          <!-- Matches Table Widget -->
          <WidgetsSoccerMatchesTable :data="matchesTableData" />
        </div>
      </div>
    </div>
  </div>
</template>
