<script setup lang="ts">
import type { AdditionalStatsData, SalesRevenueData, SparklineData } from '~/types/widgets'

definePageMeta({
  title: 'Sales',
  preview: {
    title: 'Sales dashboard',
    description: 'For sales and marketing',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-sales.png',
    srcDark: '/img/screens/dashboards-sales-dark.png',
    order: 6,
  },
})

// Datepicker
const date = ref(new Date())

// Sparkline chart data
const sparklineCharts: SparklineData[] = [
  { chartComponent: 'ChartAreaSparkSalesOne' },
  { chartComponent: 'ChartAreaSparkSalesTwo' },
  { chartComponent: 'ChartAreaSparkSalesThree' },
  { chartComponent: 'ChartAreaSparkSalesFour' },
]

// Revenue overview data
const revenueOverviewData = {
  title: 'Revenue Overview',
  metrics: [
    { label: 'This month', value: '$75,689' },
    { label: 'Last month', value: '$59,724' },
    { label: 'Average', value: '$66,561' },
  ],
  chartComponent: 'ChartAreaCustomers',
  actionButton: {
    label: 'Details',
  },
}

// Sales Revenue widget data
const salesRevenueData: SalesRevenueData = {
  title: 'Sales Revenue',
  revenue: '$8,641.26',
  description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Bonum integritas corporis.',
}

// Additional Stats widget data
const additionalStatsData: AdditionalStatsData = {
  title: 'Additional Stats',
  stats: [
    {
      chartComponent: 'ChartRadialSmallOne',
      value: '278',
      label: 'New Deals',
    },
    {
      chartComponent: 'ChartRadialSmallTwo',
      value: '1,519',
      label: 'Proposals',
    },
    {
      chartComponent: 'ChartRadialSmallThree',
      value: '3,214',
      label: 'Closed deals',
    },
  ],
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20 overflow-hidden">
    <!-- Header -->
    <div class="mb-8 flex flex-col justify-between md:flex-row md:items-center">
      <div
        class="lg:landscape:max-w-full flex max-w-[425px] flex-col items-center gap-4 text-center md:flex-row md:text-start 2xl:landscape:max-w-full"
      >
        <BaseAvatar src="/img/avatars/10.svg" size="lg" />
        <div>
          <BaseHeading
            as="h2"
            size="xl"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>Welcome back, Kendra</span>
          </BaseHeading>
          <BaseParagraph size="sm">
            <span class="text-muted-600 dark:text-muted-400">
              Happy to see you again on your dashboard.
            </span>
          </BaseParagraph>
        </div>
      </div>
      <div
        class="mt-4 flex items-center justify-center gap-2 md:mt-0 md:justify-start"
      >
        <BaseButton rounded="md">
          <span>View Reports</span>
        </BaseButton>
        <BaseButton variant="primary" rounded="md">
          <span>Transactions</span>
        </BaseButton>
      </div>
    </div>
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Sparkline widgets using reusable component -->
      <div
        v-for="(sparkline, index) in sparklineCharts"
        :key="index"
        class="lg:landscape:col-span-3 relative col-span-12 md:col-span-6 2xl:landscape:col-span-3"
      >
        <WidgetsChartsSparkline :data="sparkline" />
      </div>
      <!-- Revenue Overview widget -->
      <div class="relative col-span-12 xl:landscape:col-span-6 2xl:landscape:col-span-7">
        <WidgetsChartsRevenueOverview :data="revenueOverviewData" />
      </div>
      <!-- Column -->
      <div class="relative col-span-12 xl:landscape:col-span-6 2xl:landscape:col-span-5">
        <div class="flex h-full flex-col gap-4">
          <!-- Chart -->
          <WidgetsChartsSalesRevenue :data="salesRevenueData" />
          <!-- Charts -->
          <WidgetsChartsAdditionalStats :data="additionalStatsData" />
        </div>
      </div>
      <!-- Column -->
      <BaseCard
        rounded="md"
        class="flex flex-col p-4 md:p-6 lg:landscape:col-span-4 relative col-span-12 md:col-span-6 2xl:landscape:col-span-3"
      >
        <div class="mb-6 flex items-center justify-between">
          <BaseHeading
            as="h3"
            size="md"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>Personal Score</span>
          </BaseHeading>
        </div>
        <div class="py-16">
          <ChartRadialGaugeAlt class="-mt-14" />
        </div>
        <div class="mt-auto text-center">
          <BaseParagraph size="sm">
            <span class="text-muted-500 dark:text-muted-400">
              Your score has been calculated based on the latest metrics
            </span>
          </BaseParagraph>
        </div>
      </BaseCard>
      <!-- Column -->
      <BaseCard
        rounded="md"
        class="lg:landscape:col-span-4 relative p-4 md:p-6 col-span-12 md:col-span-6 2xl:landscape:col-span-6"
      >
        <div class="mb-6">
          <BaseHeading
            as="h3"
            size="md"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>Orders Summary</span>
          </BaseHeading>
        </div>
        <ChartBarOrders />
      </BaseCard>
      <!-- Column -->
      <BaseCard
        class="p-2 lg:landscape:col-span-4 relative col-span-12 md:col-span-6 2xl:landscape:col-span-3"
        rounded="md"
      >
        <LazyAddonDatepicker v-model="date" locale="en" label="Start date" />
      </BaseCard>
    </div>
  </div>
</template>
