<script setup lang="ts">
import type {
  DatePickerWidgetData,
  InterviewCardData,
  InterviewWelcomeData,
  PersonalContentListData,
  PersonalMetricCardData,
} from '~/types/widgets'
import { computed } from 'vue'
import { useAuth } from '~/composables/auth'

definePageMeta({
  title: 'Interviews',
  preview: {
    title: 'Personal dashboard v3',
    description: 'For personal usage and reports',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-personal-3.png',
    srcDark: '/img/screens/dashboards-personal-3-dark.png',
    order: 3,
  },
})

const { currentProfile } = useAuth()

const displayName = computed(() => {
  const p = currentProfile.value
  if (!p)
    return 'there'
  if (p.firstName && p.lastName)
    return `${p.firstName} ${p.lastName.charAt(0)}.`
  return p.display_name || 'there'
})

const candidates = [
  {
    id: 0,
    tooltip: 'Adam Wrangler',
    src: '/img/avatars/15.svg',
    text: 'EC',
    role: 'UI/UX designer',
  },
  {
    id: 1,
    tooltip: '<PERSON>',
    src: '/img/avatars/5.svg',
    text: 'JM',
    role: 'Frontend developer',
  },
  {
    id: 2,
    tooltip: 'Tara Svenson',
    src: '/img/avatars/4.svg',
    text: 'TS',
    role: 'Software architect',
  },
  {
    id: 3,
    tooltip: 'Naomi Liversky',
    src: undefined,
    text: 'NL',
    role: 'UI/UX designer',
  },
]

// Datepicker
const date = ref(new Date())

// Widget data configurations
const welcomeData = computed<InterviewWelcomeData>(() => ({
  displayName: displayName.value,
  description: 'You have 6 interviews to conduct during this week. Your current progress is great. Check your schedule and don\'t miss any activity.',
  emoji: '🎉',
  progressLabel: 'Your Progress',
  progressStatus: 'Outstanding',
  ctaText: 'View Schedule',
}))

const metricsData = ref<PersonalMetricCardData[]>([
  {
    icon: 'solar:alarm-bold-duotone',
    iconClass: 'bg-info-100 text-info-500 dark:bg-info-500/20 dark:text-info-400 dark:border-info-500 dark:border-2',
    value: '62K',
    label: 'Minutes',
  },
  {
    icon: 'solar:home-wifi-angle-bold-duotone',
    iconClass: 'bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2',
    value: '263',
    label: 'Interviews',
  },
  {
    icon: 'solar:verified-check-bold-duotone',
    iconClass: 'bg-lime-100 text-lime-500 dark:border-2 dark:border-lime-500 dark:bg-lime-500/20 dark:text-lime-400',
    value: '49',
    label: 'Approved',
  },
  {
    icon: 'solar:mask-sad-bold-duotone',
    iconClass: 'bg-amber-100 text-amber-500 dark:border-2 dark:border-amber-500 dark:bg-amber-500/20 dark:text-amber-400',
    value: '214',
    label: 'Rejected',
  },
])

const datePickerData: DatePickerWidgetData = {
  locale: 'en',
  label: 'Start date',
}

const trendingSkillsData: PersonalContentListData = {
  title: 'Trending skills',
  viewAllLink: {
    href: '#',
    text: 'View all',
  },
  contentComponent: 'TrendingSkills',
}

const interviewsData = ref<InterviewCardData[]>([
  {
    name: 'Jonathan K.',
    avatar: '/img/avatars/11.svg',
    timeSlot: '8:00 am — 9:00 am',
  },
  {
    name: 'Erwin S.',
    avatar: '/img/avatars/16.svg',
    timeSlot: '10:30 am — 11:30 am',
  },
  {
    name: 'Jennifer M.',
    avatar: '/img/avatars/5.svg',
    timeSlot: '2:00 pm — 3:00 pm',
  },
  {
    name: 'Clark S.',
    avatar: '/img/avatars/3.svg',
    timeSlot: '4:00 pm — 5:00 pm',
  },
])
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div
      class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3"
    >
      <!-- Welcome Header Widget -->
      <div>
        <WidgetsPersonalInterviewWelcome :data="welcomeData" />
      </div>

      <!-- Metrics Grid -->
      <div class="grid grid-cols-2 gap-4">
        <!-- Metric Cards -->
        <WidgetsPersonalMetricCard
          v-for="metric in metricsData"
          :key="metric.label"
          :data="metric"
        />
        <!-- Inner item -->
        <div class="col-span-2">
          <div class="mt-auto flex h-full items-end justify-between pb-4">
            <div>
              <BaseHeading
                as="h2"
                size="md"
                weight="medium"
                lead="tight"
                class="text-muted-900 dark:text-white"
              >
                <span>Total Interviews</span>
              </BaseHeading>
              <BaseParagraph size="xs">
                <span class="text-muted-500 dark:text-muted-400">
                  23 interviews this month
                </span>
              </BaseParagraph>
            </div>
            <div>
              <BaseAvatarGroup
                :avatars="candidates"
                size="sm"
                :limit="3"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- Date Picker Widget -->
      <div>
        <WidgetsUtilsDatePicker v-model="date" :data="datePickerData" />
      </div>

      <!-- Trending Skills Content Widget -->
      <div>
        <WidgetsPersonalContentList :data="trendingSkillsData" />
      </div>

      <!-- Interviews Chart Widget -->
      <div>
        <WidgetsChartsSimple
          title="Interviews"
          chart-component="ChartAreaInterviews"
          action-label="Reports"
          action-href="#"
        />
      </div>

      <!-- Interview Schedule Cards -->
      <div class="flex flex-col gap-4">
        <WidgetsPersonalInterviewCard
          v-for="interview in interviewsData"
          :key="interview.name"
          :data="interview"
        />
      </div>
    </div>
  </div>
</template>
