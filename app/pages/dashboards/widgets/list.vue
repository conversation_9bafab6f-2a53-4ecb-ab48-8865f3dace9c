<script setup lang="ts">
import type { ListSimpleData, ListTitledData } from '~/types/widgets'

definePageMeta({
  title: 'List Widgets',
  preview: {
    title: 'List Widgets',
    description: 'For page compositions',
    categories: ['dashboards', 'widgets'],
    src: '/img/screens/dashboards-widgets-lists.png',
    srcDark: '/img/screens/dashboards-widgets-lists-dark.png',
    order: 29,
  },
})

// Widget data configurations
const titledListWidgets: ListTitledData[] = [
  {
    title: 'Members',
    listComponent: 'UserList',
    rounded: 'lg',
  },
  {
    title: 'Members',
    listComponent: 'UserList',
    rounded: 'full',
  },
  {
    title: 'My Tasks',
    listComponent: 'TodoListCompact',
    color: 'success',
  },
  {
    title: 'My Tasks',
    listComponent: 'TodoListCompact',
    color: 'info',
  },
  {
    title: 'Latest comments',
    listComponent: 'CommentListCompact',
  },
  {
    title: 'Topics',
    listComponent: 'TopicListCompact',
    rounded: 'lg',
  },
  {
    title: 'Topics',
    listComponent: 'TopicListCompact',
    rounded: 'full',
  },
  {
    title: 'Timeline',
    listComponent: 'TimelineCompact',
    rounded: 'sm',
  },
  {
    title: 'Timeline',
    listComponent: 'TimelineCompact',
    rounded: 'lg',
  },
  {
    title: 'Timeline',
    listComponent: 'TimelineCompact',
    rounded: 'full',
  },
]

const simpleListWidgets: ListSimpleData[] = [
  {
    listComponent: 'FileListTabbed',
    rounded: 'full',
  },
  {
    listComponent: 'TodoListTabbed',
    rounded: 'sm',
    color: 'primary',
  },
]
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="grid gap-4 sm:grid-cols-3">
      <!-- Titled List Widgets -->
      <template v-for="(widget, index) in titledListWidgets" :key="`titled-${index}`">
        <div class="relative">
          <WidgetsListTitled :data="widget" />
        </div>
      </template>

      <!-- Simple List Widgets -->
      <template v-for="(widget, index) in simpleListWidgets" :key="`simple-${index}`">
        <div class="relative">
          <WidgetsListSimple :data="widget" />
        </div>
      </template>
    </div>
  </div>
</template>
