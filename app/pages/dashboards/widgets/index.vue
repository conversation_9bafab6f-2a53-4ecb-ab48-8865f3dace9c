<script setup lang="ts">
import type {
  CreativeGenericData,
  PersonalContentListData,
  PersonalScoreData,
  UICalendarData,
  UIIconTextData,
  UIInboxMessageData,
  UIPictureData,
} from '~/types/widgets'

definePageMeta({
  title: 'UI Widgets',
  preview: {
    title: 'UI Widgets',
    description: 'For page compositions',
    categories: ['dashboards', 'widgets'],
    src: '/img/screens/dashboards-widgets-ui.png',
    srcDark: '/img/screens/dashboards-widgets-ui-dark.png',
    order: 27,
  },
})

// Widget data configurations
const personalScoreData: PersonalScoreData = {
  title: 'Personal Score',
  chartComponent: 'ChartRadialGaugeAlt',
  description: 'Your score has been calculated based on the latest metrics',
}

const calendarData: UICalendarData = {
  titlePosition: 'left',
  expanded: true,
  borderless: true,
  transparent: true,
  trimWeeks: true,
  calendarClass: 'max-w-full rounded-xl',
}

const iconTextWidgets: UIIconTextData[] = [
  {
    title: 'Payment',
    icon: 'solar:bell-linear',
    text: 'You have an upcoming payment for your recurring subscription fee due on Sept 20, 2020. Check your billing details.',
    indicator: true,
  },
  {
    title: 'Messages',
    icon: 'lucide:message-square',
    text: 'You currently have more than 10 unread messages in your inbox. It could be a good time to check them out.',
    indicator: true,
  },
]

const pictureData: UIPictureData = {
  src: '/img/illustrations/widgets/mountain-picture.svg',
  alt: 'Picture widget image description',
  rounded: 'lg',
  height: 286,
  width: 212,
  loading: true,
}

const inboxMessageData: UIInboxMessageData = {
  picture: '/img/avatars/10.svg',
  name: 'Kendra W.',
  title: 'Design Project',
  text: 'Where are we in terms of design? We need to review the new screens.',
  time: '28 minutes',
  rounded: 'lg',
}

const popularTopicsData: PersonalContentListData = {
  title: 'Popular topics',
  titleSize: 'sm',
  contentComponent: 'TagListCompact',
  contentProps: { rounded: 'lg' },
  rounded: 'lg',
  cardClass: 'p-4 md:p-6',
}

// Generic content widgets
const genericWidgets: CreativeGenericData[] = [
  { contentComponent: 'FollowersCompact', cardClass: 'p-4 md:p-6' },
  { contentComponent: 'SearchCompact', contentRounded: 'lg', cardClass: 'p-4 md:p-6' },
  { contentComponent: 'VideoCompact', contentRounded: 'lg', cardClass: 'p-3' },
  { contentComponent: 'TagListCompact', contentRounded: 'full', cardClass: 'p-4 md:p-6' },
  { contentComponent: 'IconLinks', contentRounded: 'lg', cardClass: 'p-4 md:p-6' },
  { contentComponent: 'DaysSquare', contentRounded: 'lg', cardClass: 'p-4 md:p-6' },
  { contentComponent: 'MenuIconList', contentRounded: 'lg', cardClass: 'p-4 md:p-6' },
  { contentComponent: 'NotificationsCompact', cardClass: 'p-4 md:p-6' },
]
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="grid gap-4 sm:grid-cols-4">
      <!-- Column -->
      <div class="relative flex flex-col gap-4">
        <!-- Widget -->
        <WidgetsChartsPersonalScore :data="personalScoreData" />
        <!-- Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[0]" />
        <!-- Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[1]" />
        <!-- Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[2]" />
        <!-- Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[3]" />
      </div>
      <!-- Column -->
      <div class="relative flex flex-col gap-4">
        <!-- Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[4]" />
        <!-- Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[5]" />
        <!-- Widget -->
        <WidgetsUICalendar :data="calendarData" />
        <!-- Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[6]" />
      </div>
      <!-- Column -->
      <div class="relative flex flex-col gap-4">
        <!-- Widget -->
        <WidgetsUIIconText :data="iconTextWidgets[0]" />
        <!-- Widget -->
        <WidgetsUIPicture :data="pictureData" />
        <!-- Widget -->
        <WidgetsUIInboxMessage :data="inboxMessageData" />
      </div>
      <!-- Column -->
      <div class="relative flex flex-col gap-4">
        <!-- Widget -->
        <WidgetsPersonalContentList :data="popularTopicsData" />
        <!-- Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[7]" />
        <!-- Widget -->
        <ActionText
          title="Upgrade to Pro"
          text="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quid censes in Latino fore? Nam ante Aristippus, et ille melius."
          label="Upgrade Now"
          to="#"
          rounded="lg"
        />
        <!-- Widget -->
        <WidgetsUIIconText :data="iconTextWidgets[1]" />
      </div>
    </div>
  </div>
</template>
