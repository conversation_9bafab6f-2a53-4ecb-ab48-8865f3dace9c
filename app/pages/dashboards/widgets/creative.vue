<script setup lang="ts">
import type {
  CreativeGenericData,
  CreativeInfoBadgesData,
  CreativeInfoImageData,
  CreativeTeamListData,
} from '~/types/widgets'

definePageMeta({
  title: 'Creative Widgets',
  preview: {
    title: 'Creative Widgets',
    description: 'For page compositions',
    categories: ['dashboards', 'widgets'],
    src: '/img/screens/dashboards-widgets-creative.png',
    srcDark: '/img/screens/dashboards-widgets-creative-dark.png',
    order: 28,
  },
})

// Widget data configurations
const infoBadgesData: CreativeInfoBadgesData = {
  image: '/img/illustrations/widgets/1.svg',
  badgeSmall: '/img/illustrations/widgets/3.svg',
  badgeMedium: '/img/illustrations/widgets/2.svg',
  title: 'You\'ve unlocked 2 new Achievements',
  text: 'Congrats, your efforts have been rewarded. Keep up like this!',
}

const teamListData: CreativeTeamListData = {
  title: 'My Team',
  actionLabel: 'View all',
  contentComponent: 'TeamListCompact',
}

// Info Image widgets data
const infoImageWidgets: CreativeInfoImageData[] = [
  {
    image: '/img/illustrations/widgets/4.svg',
    title: 'You\'ve unlocked 2 new Achievements',
    text: 'Congrats, your efforts have been rewarded. Keep up like this!',
  },
  {
    image: '/img/illustrations/widgets/7.svg',
    title: 'You\'ve unlocked 2 new Achievements',
    text: 'Congrats, your efforts have been rewarded. Keep up like this!',
  },
  {
    image: '/img/illustrations/widgets/5.svg',
    title: 'You\'ve unlocked 2 new Achievements',
    text: 'Congrats, your efforts have been rewarded. Keep up like this!',
  },
  {
    image: '/img/illustrations/widgets/8.svg',
    title: 'You\'ve unlocked 2 new Achievements',
    text: 'Congrats, your efforts have been rewarded. Keep up like this!',
  },
]

// Generic content widgets data
const genericWidgets: CreativeGenericData[] = [
  {
    contentComponent: 'VcardRight',
    cardClass: 'p-6',
  },
  {
    contentComponent: 'SocialLinks',
    contentRounded: 'lg',
    cardClass: 'p-4',
  },
  {
    contentComponent: 'TeamSearchCompact',
    contentRounded: 'lg',
    cardClass: 'p-4',
  },
  {
    contentComponent: 'ShoppingCartCompact',
    contentRounded: 'lg',
    cardClass: 'p-6',
  },
  {
    contentComponent: 'VcardRight',
    contentProps: { centered: true },
    cardClass: 'p-6',
  },
  {
    contentComponent: 'ImageLinks',
    contentRounded: 'lg',
    cardClass: 'p-4',
  },
  {
    contentComponent: 'IconsSquare',
    contentRounded: 'lg',
    cardClass: 'p-6',
  },
]

// Special widgets that need custom handling
const iconTextData = {
  title: '@cssninjaStudio',
  icon: 'lucide:twitter',
  text: 'Tairo will be released very soon, probably around the end of the year of the Christmas holidays.',
  indicator: true,
}

const progressCircleData = {
  image: '/img/avatars/6.svg',
  title: '0% completed!',
  text: 'Congrats, your efforts have been rewarded. Keep up like this!',
  value: 78,
}

const productCompactData = {
  image: '/img/illustrations/widgets/watch-3.svg',
  title: 'Connected Watch',
  text: 'Apple — 5th Gen Connected Watch series, 64GB, 2023 issue',
  icon: 'fa6-brands:apple',
  rounded: 'lg',
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="grid gap-4 sm:grid-cols-4">
      <!-- Column 1 -->
      <div class="relative flex flex-col gap-4">
        <!-- Info Badges Widget -->
        <WidgetsCreativeInfoBadges :data="infoBadgesData" />

        <!-- VCard Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[0]" />

        <!-- Info Image Widget -->
        <WidgetsCreativeInfoImage :data="infoImageWidgets[1]" />

        <!-- Team List Widget -->
        <WidgetsCreativeTeamList :data="teamListData" />
      </div>

      <!-- Column 2 -->
      <div class="relative flex flex-col gap-4">
        <!-- Info Image Widget -->
        <WidgetsCreativeInfoImage :data="infoImageWidgets[0]" />

        <!-- Social Links Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[1]" />

        <!-- Team Search Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[2]" />

        <!-- Shopping Cart Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[3]" />
      </div>
      <!-- Column 3 -->
      <div class="relative flex flex-col gap-4">
        <!-- IconText Widget (special handling needed) -->
        <BaseCard rounded="lg" class="p-6">
          <IconText
            title="@cssninjaStudio"
            icon="lucide:twitter"
            text="Tairo will be released very soon, probably around the end of the year of the Christmas holidays."
            indicator
          >
            <div
              class="text-primary-500 mt-3 flex w-full gap-2 font-sans text-xs font-medium"
            >
              <NuxtLink to="#">
                #uikit
              </NuxtLink>
              <NuxtLink to="#">
                #dashboards
              </NuxtLink>
              <NuxtLink to="#">
                #tailwind
              </NuxtLink>
            </div>
          </IconText>
        </BaseCard>

        <!-- Info Image Widget -->
        <WidgetsCreativeInfoImage :data="infoImageWidgets[2]" />

        <!-- VCard Centered Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[4]" />

        <!-- Image Links Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[5]" />

        <!-- Progress Circle Widget (special handling needed) -->
        <BaseCard rounded="lg" class="p-6">
          <ProgressCircle
            image="/img/avatars/6.svg"
            :title="`${0}% completed!`"
            text="Congrats, your efforts have been rewarded. Keep up like this!"
            :value="78"
          />
        </BaseCard>
      </div>

      <!-- Column 4 -->
      <div class="relative flex flex-col gap-4">
        <!-- Product Compact Widget (special handling needed) -->
        <BaseCard rounded="lg" class="p-6">
          <ProductCompact
            image="/img/illustrations/widgets/watch-3.svg"
            title="Connected Watch"
            text="Apple — 5th Gen Connected Watch series, 64GB, 2023 issue"
            icon="fa6-brands:apple"
            rounded="lg"
          />
        </BaseCard>

        <!-- Icons Square Widget -->
        <WidgetsCreativeGeneric :data="genericWidgets[6]" />

        <!-- Info Image Widget -->
        <WidgetsCreativeInfoImage :data="infoImageWidgets[3]" />
      </div>
    </div>
  </div>
</template>
