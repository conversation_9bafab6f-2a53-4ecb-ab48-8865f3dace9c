<script setup lang="ts">
import type {
  CompanyCollaborationsData,
  InfluencerProfileHeaderData,
  SocialMediaStatsData,
} from '~/types/widgets'

definePageMeta({
  title: 'Content creator',
  preview: {
    title: 'Content creator dashboard',
    description: 'For social media influencers',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-influencer.png',
    srcDark: '/img/screens/dashboards-influencer-dark.png',
    order: 15,
  },
})

const instagramStats = [
  {
    label: 'Content Posts (30 days)',
    action: 'posst published',
    value: 119,
    growth: -4.5,
    growthText: 'less than usual',
    icon: 'ri:instagram-fill',
  },
  {
    label: 'Followers',
    action: 'following you',
    value: 14300,
    growth: 9.2,
    growthText: 'follower growth',
    icon: 'solar:users-group-rounded-bold-duotone',
  },
  {
    label: 'Following',
    action: 'followed by you',
    value: 224,
    growth: 1.1,
    growthText: 'more than usual',
    icon: 'solar:user-speak-bold-duotone',
  },
  {
    label: 'Likes (30 days)',
    action: 'likes of your content',
    value: 129700,
    growth: 25.7,
    growthText: 'engagement growth',
    icon: 'solar:heart-pulse-2-bold-duotone',
  },
  {
    label: 'Comments (30 days)',
    action: 'comments published',
    value: 5200,
    growth: 11.3,
    growthText: 'engagement growth',
    icon: 'solar:chat-line-bold-duotone',
  },
  {
    label: 'Profile views (30 days)',
    action: 'views of profile',
    value: 12700,
    growth: 8.5,
    growthText: 'engagement growth',
    icon: 'solar:mention-square-bold-duotone',
  },
]

const tiktokStats = [
  {
    label: 'Shorts (30 days)',
    action: 'shorts published',
    value: 75,
    growth: 11.5,
    growthText: 'more than usual',
    icon: 'fa6-brands:tiktok',
  },
  {
    label: 'Followers',
    action: 'following you',
    value: 5700,
    growth: 17.2,
    growthText: 'follower growth',
    icon: 'ph:users-four-duotone',
  },
  {
    label: 'Following',
    action: 'followed by you',
    value: 29,
    growth: 0.3,
    growthText: 'more than usual',
    icon: 'ph:users-three-duotone',
  },
  {
    label: 'Likes (30 days)',
    action: 'likes of your content',
    value: 80400,
    growth: 65.7,
    growthText: 'engagement growth',
    icon: 'ph:heart-duotone',
  },
  {
    label: 'Comments (30 days)',
    action: 'comments published',
    value: 22800,
    growth: 49.3,
    growthText: 'engagement growth',
    icon: 'ph:chat-circle-duotone',
  },
  {
    label: 'Profile views (30 days)',
    action: 'views of profile',
    value: 48300,
    growth: 8.5,
    growthText: 'engagement growth',
    icon: 'ph:user-duotone',
  },
]

const twitchStats = [
  {
    label: 'Stream (30 days)',
    action: 'hours streamed',
    value: 398,
    growth: 26.9,
    growthText: 'more than usual',
    icon: 'fa6-brands:twitch',
  },
  {
    label: 'Subscribers',
    action: 'subscribed',
    value: 5700,
    growth: 17.2,
    growthText: 'follower growth',
    icon: 'solar:users-group-rounded-bold-duotone',
  },
  {
    label: 'Following',
    action: 'followed by you',
    value: 29,
    growth: 0.3,
    growthText: 'more than usual',
    icon: 'solar:user-speak-bold-duotone',
  },
  {
    label: 'Likes (30 days)',
    action: 'likes of your content',
    value: 80400,
    growth: 65.7,
    growthText: 'engagement growth',
    icon: 'solar:heart-pulse-2-bold-duotone',
  },
  {
    label: 'Comments (30 days)',
    action: 'comments published',
    value: 22800,
    growth: 49.3,
    growthText: 'engagement growth',
    icon: 'solar:chat-line-bold-duotone',
  },
  {
    label: 'Profile views (30 days)',
    action: 'views of profile',
    value: 48300,
    growth: 8.5,
    growthText: 'engagement growth',
    icon: 'solar:mention-square-bold-duotone',
  },
]

const companies = [
  {
    name: 'Airbnb',
    logo: 'logos:airbnb-icon',
    title: 'UI / UX Designer',
    description:
      'Airbnb is a company that enables people to list, discover, and book accommodations around the world.',
    tags: ['Full Time', 'UX design', 'Senior level'],
    stats: [
      {
        label: 'Contracts',
        value: 22,
      },
      {
        label: 'Publications',
        value: 49,
      },
      {
        label: 'Income',
        value: 43.2,
      },
    ],
    followers: [
      {
        tooltip: 'Clark Smith',
        src: '/img/avatars/3.svg',
      },
      {
        tooltip: 'Maya Rosselini',
        src: '/img/avatars/2.svg',
      },
      {
        tooltip: 'Clarissa Miller',
        src: '/img/avatars/5.svg',
      },
      {
        tooltip: 'Jane Doe',
        src: '/img/avatars/4.svg',
      },
    ],
  },
  {
    name: 'Slack',
    logo: 'logos:slack-icon',
    title: 'Product Designer',
    description:
      'Slack is a cloud-based set of team collaboration tools and services.',
    tags: ['Full Time', 'Product design', 'Marketing'],
    stats: [
      {
        label: 'Contracts',
        value: 22,
      },
      {
        label: 'Publications',
        value: 49,
      },
      {
        label: 'Income',
        value: 43.2,
      },
    ],
    followers: [
      {
        tooltip: 'Hermann Mayer',
        src: '/img/avatars/16.svg',
      },
      {
        tooltip: 'Jen Rossi',
        src: '/img/avatars/10.svg',
      },
    ],
  },
  {
    name: 'Gitlab',
    logo: 'logos:gitlab',
    title: 'Project Manager',
    description:
      'GitLab is a web-based DevOps lifecycle tool that provides a Git-repository manager.',
    tags: ['Full Time', 'Product management'],
    stats: [
      {
        label: 'Contracts',
        value: 22,
      },
      {
        label: 'Publications',
        value: 49,
      },
      {
        label: 'Income',
        value: 43.2,
      },
    ],
    followers: [
      {
        tooltip: 'Alex Wielder',
        src: '/img/avatars/11.svg',
      },
      {
        tooltip: 'Rob Howards',
        src: '/img/avatars/18.svg',
      },
    ],
  },
  {
    name: 'Google',
    logo: 'logos:google-icon',
    title: 'Product Owner',
    description:
      'Google is an American multinational technology company that specializes in Internet-related services and products.',
    tags: ['Full Time', 'Scrum master', 'Management'],
    stats: [
      {
        label: 'Contracts',
        value: 22,
      },
      {
        label: 'Publications',
        value: 49,
      },
      {
        label: 'Income',
        value: 43.2,
      },
    ],
    followers: [
      {
        tooltip: 'Clarence Bodicker',
        src: '/img/avatars/13.svg',
      },
      {
        tooltip: 'Andrew Holmes',
        src: '/img/avatars/14.svg',
      },
    ],
  },
  {
    name: 'Atlassian',
    logo: 'logos:atlassian',
    title: 'Fullstack Developer',
    description:
      'Atlassian is a company that provides enterprise software for teams. It develops products for software developers, project managers, and content management.',
    tags: ['Full Time', 'Fullstack', 'Engineering'],
    stats: [
      {
        label: 'Contracts',
        value: 22,
      },
      {
        label: 'Publications',
        value: 49,
      },
      {
        label: 'Income',
        value: 43.2,
      },
    ],
    followers: [
      {
        tooltip: 'Sam Brettman',
        src: '/img/avatars/15.svg',
      },
      {
        tooltip: 'Elina Wheeler',
        src: '/img/avatars/9.svg',
      },
    ],
  },
  {
    name: 'Dribbble',
    logo: 'logos:dribbble-icon',
    title: 'Community Manager',
    description:
      'Dribbble is a community of designers sharing screenshots of their work, process, and projects.',
    tags: ['Full Time', 'Manager', 'Community'],
    stats: [
      {
        label: 'Contracts',
        value: 22,
      },
      {
        label: 'Publications',
        value: 49,
      },
      {
        label: 'Income',
        value: 43.2,
      },
    ],
    followers: [
      {
        tooltip: 'John Baxter',
        src: '/img/avatars/6.svg',
      },
      {
        tooltip: 'Maya Rosselini',
        src: '/img/avatars/2.svg',
      },
    ],
  },
  {
    name: 'Figma',
    logo: 'logos:figma',
    title: 'Business Developer',
    description:
      'Figma is a vector graphics editor and prototyping tool which is primarily web-based, with additional offline features enabled by desktop applications for macOS and Windows.',
    tags: ['Full Time', 'Business', 'Sales'],
    stats: [
      {
        label: 'Contracts',
        value: 22,
      },
      {
        label: 'Publications',
        value: 49,
      },
      {
        label: 'Income',
        value: 43.2,
      },
    ],
    followers: [
      {
        tooltip: 'Edward Rowell',
        src: '/img/avatars/8.svg',
      },
      {
        tooltip: 'Nick Kowalski',
        src: '/img/avatars/17.svg',
      },
    ],
  },
  {
    name: 'Airtable',
    logo: 'logos:airtable',
    title: 'Frontend Developer',
    description:
      'Airtable is a cloud-based spreadsheet-database hybrid that allows users to create and manage tables and data through a web browser.',
    tags: ['Full Time', 'Frontend', 'Sales'],
    stats: [
      {
        label: 'Contracts',
        value: 22,
      },
      {
        label: 'Publications',
        value: 49,
      },
      {
        label: 'Income',
        value: 43.2,
      },
    ],
    followers: [
      {
        tooltip: 'Edward Rowell',
        src: '/img/avatars/8.svg',
      },
      {
        tooltip: 'Nick Kowalski',
        src: '/img/avatars/17.svg',
      },
      {
        tooltip: 'John Baxter',
        src: '/img/avatars/6.svg',
      },
      {
        tooltip: 'Maya Rosselini',
        src: '/img/avatars/2.svg',
      },
    ],
  },
]

// Profile header data
const profileHeaderData: InfluencerProfileHeaderData = {
  username: 'Sweet_Mango12',
  realName: 'Clarissa Miller',
  avatar: '/img/avatars/5.svg',
  badgeSrc: '/img/icons/flags/united-states-of-america.svg',
  verified: true,
  stats: [
    { value: '1512', label: 'Posts' },
    { value: '38.3K', label: 'Followers' },
    { value: '329', label: 'Following' },
  ],
  bio: 'Artist, musician, songwriter, influencer, these are the many names people give me. But for you I am simply Clarissa. *Forever with all my friends* // Latest video can be found here',
  bioLink: {
    text: 'youtu.be/8Tcee5Cyz',
    href: '#',
  },
  badges: [
    {
      icon: 'ri:fire-fill',
      bgColor: 'bg-destructive-500/20',
      iconColor: 'text-destructive-500',
    },
    {
      icon: 'ri:medal-fill',
      bgColor: 'bg-success-500/20',
      iconColor: 'text-success-500',
    },
    {
      icon: 'ri:trophy-fill',
      bgColor: 'bg-warning-500/20',
      iconColor: 'text-warning-500',
    },
  ],
  ctaText: 'Add Account',
}

// Social media stats data
const instagramStatsData: SocialMediaStatsData = {
  platform: 'instagram',
  title: 'Instagram stats',
  description: 'The following stats are based on the last 30 days of your Instagram account',
  stats: instagramStats,
  ctaText: 'Manage',
}

const tiktokStatsData: SocialMediaStatsData = {
  platform: 'tiktok',
  title: 'Tik Tok stats',
  description: 'The following stats are based on the last 30 days of your Tik Tok account',
  stats: tiktokStats,
  ctaText: 'Manage',
}

const twitchStatsData: SocialMediaStatsData = {
  platform: 'twitch',
  title: 'Twitch stats',
  description: 'The following stats are based on the last 30 days of your Twitch account',
  stats: twitchStats,
  ctaText: 'Manage',
}

// Company collaborations data
const companyCollaborationsData: CompanyCollaborationsData = {
  title: 'Collaborations',
  description: 'These are the companies you are currently collaborating with or have collaborated with in the past.',
  companies,
  ctaText: 'Manage',
}

const activeTab = ref('tab-1')
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Influencer Profile Header Widget -->
    <WidgetsInfluencerProfileHeader :data="profileHeaderData" />
    <!-- Dashboard content -->
    <div class="w-full">
      <!-- Tabs -->
      <div
        class="flex items-end gap-x-8 my-6"
      >
        <button
          type="button"
          class="cursor-pointer inline-flex items-center justify-center border-b-2 py-3 font-sans text-sm"
          :class="
            activeTab === 'tab-1'
              ? 'border-primary-500 text-muted-800 dark:text-muted-100'
              : 'border-transparent text-muted-400 hover:text-muted-600 dark:hover:text-muted-100'
          "
          @click="activeTab = 'tab-1'"
        >
          <span>Accounts</span>
        </button>
        <button
          type="button"
          class="cursor-pointer inline-flex items-center justify-center border-b-2 py-3 font-sans text-sm"
          :class="
            activeTab === 'tab-2'
              ? 'border-primary-500 text-muted-800 dark:text-muted-100'
              : 'border-transparent text-muted-400 hover:text-muted-600 dark:hover:text-muted-100'
          "
          @click="activeTab = 'tab-2'"
        >
          <span>Companies</span>
        </button>
      </div>
      <!-- Tab content -->
      <div v-if="activeTab === 'tab-1'" class="relative">
        <!-- Social Media Stats Widgets -->
        <div class="space-y-16">
          <!-- Instagram Stats Widget -->
          <WidgetsInfluencerSocialMediaStats :data="instagramStatsData" />

          <!-- TikTok Stats Widget -->
          <WidgetsInfluencerSocialMediaStats :data="tiktokStatsData" />

          <!-- Twitch Stats Widget -->
          <WidgetsInfluencerSocialMediaStats :data="twitchStatsData" />
        </div>
      </div>
      <!-- Tab content -->
      <div v-else-if="activeTab === 'tab-2'" class="relative">
        <!-- Company Collaborations Widget -->
        <WidgetsInfluencerCompanyCollaborations :data="companyCollaborationsData" />
      </div>
    </div>
  </div>
</template>
