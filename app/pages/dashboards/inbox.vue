<script setup lang="ts">
import type { EmailMessage } from '~/types/email'
import EmailDeleteModal from '~~/components/EmailDeleteModal.vue'
import { EmailProvider } from '~/types/ui'

definePageMeta({
  title: 'Inbox',
  // layout: 'empty',
  preview: {
    title: 'Inbox app',
    description: 'For email and messaging apps',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-inbox.png',
    srcDark: '/img/screens/dashboards-inbox-dark.png',
    order: 25,
  },
})

const route = useRoute()
const split = ref()
const search = ref('')
const { md } = useTailwindBreakpoints()

// Email provider and folder filtering from query parameters
const provider = computed(() => route.query.provider as string | undefined)
const folder = computed(() => (route.query.folder as string) || 'inbox')

// Email functionality
const {
  messages,
  loading,
  error,
  hasMore,
  loadEmails,
  loadMoreEmails,
  refreshEmails,
  clearMessages,
  syncProvider,
  isProviderSyncing,
  getProviderIcon,
  getAttachmentIcon,
  markAsRead,
  deleteEmails,
} = useEmail()

// Integrations for provider metadata
const { integrations, emailIntegrations } = useIntegrations()

// Get current provider integration
const currentProviderIntegration = computed(() => {
  if (!provider.value)
    return null
  return emailIntegrations.value.find(integration =>
    integration.id === provider.value
    || integration.integrationId === provider.value,
  )
})

// The backend handles folder filtering, so we just return all messages
// The folder parameter is passed to the backend which filters at the API level
const folderFilteredMessages = computed(() => {
  // Backend already filtered messages by folder, so return all messages
  return messages.value
})

// Filter messages by search
const filteredMessages = computed(() => {
  const baseMessages = folderFilteredMessages.value

  if (!search.value.trim())
    return baseMessages

  const searchTerm = search.value.toLowerCase().trim()
  return baseMessages.filter(message =>
    message.subject?.toLowerCase().includes(searchTerm)
    || message.sender?.email?.toLowerCase().includes(searchTerm)
    || message.sender?.name?.toLowerCase().includes(searchTerm)
    || message.textBody?.toLowerCase().includes(searchTerm),
  )
})

const paneSize = ref(50)
watch(
  md,
  async (isMd) => {
    await nextTick()
    if (isMd) {
      paneSize.value = 50
    }
    else {
      paneSize.value = 100
    }
  },
  { immediate: true },
)

// Active message state
const activeMessage = ref<string | null>(null)

const selectedMessage = computed(() => {
  if (!activeMessage.value)
    return null
  return filteredMessages.value.find(message => message.id === activeMessage.value)
})

// Set first message as active by default
watch(filteredMessages, (newMessages) => {
  if (newMessages.length > 0 && !activeMessage.value) {
    activeMessage.value = newMessages[0].id
  }
}, { immediate: true })

// Email loading and provider/folder effects
watch(
  [provider, folder],
  async ([newProvider, newFolder], [oldProvider, oldFolder]) => {
    // Clear and reload when provider changes
    if (newProvider !== oldProvider) {
      clearMessages()
      activeMessage.value = null

      if (newProvider) {
        // Load emails for specific provider and current folder
        await loadEmails(newProvider, { limit: 25, folder: newFolder })
      }
      else {
        // For "all providers" view, we would need to load from all connected integrations
        // For now, just clear the messages
        console.log('All providers view not yet implemented')
      }
    }
    // Also reload when folder changes (backend filtering)
    else if (newFolder !== oldFolder) {
      clearMessages()
      activeMessage.value = null

      if (newProvider) {
        // Load emails for current provider and new folder
        console.log(`Loading emails for folder: ${newFolder}, provider: ${newProvider}`)
        await loadEmails(newProvider, { limit: 25, folder: newFolder })
      }
    }
  },
  { immediate: true },
)

// Format date for display
function formatEmailDate(date: Date | string): string {
  const emailDate = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffMs = now.getTime() - emailDate.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffMs / (1000 * 60))
      return `${diffMinutes} ${diffMinutes === 1 ? 'minute' : 'minutes'} ago`
    }
    return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`
  }
  else if (diffDays === 1) {
    return 'Yesterday'
  }
  else if (diffDays < 7) {
    return `${diffDays} days ago`
  }
  else {
    return emailDate.toLocaleDateString()
  }
}

// Get email preview text
function getEmailPreview(message: EmailMessage): string {
  if (message.textBody && message.textBody.length > 0) {
    return message.textBody.slice(0, 120) + (message.textBody.length > 120 ? '...' : '')
  }
  if (message.htmlBody) {
    // Strip HTML tags for preview
    const text = message.htmlBody.replace(/<[^>]*>/g, '').trim()
    return text.slice(0, 120) + (text.length > 120 ? '...' : '')
  }
  return 'No content preview available'
}

// Handle email sync
async function handleSync() {
  try {
    if (provider.value) {
      await syncProvider(provider.value)
    }
    else {
      // For "all providers" sync, we'd need to iterate through all connected integrations
      console.log('Sync all providers not yet implemented')
    }
  }
  catch (error) {
    console.error('Sync failed:', error)
  }
}

const panelActive = ref(false)
const showReplyBox = ref(false)

// Delete modal state
const showDeleteModal = ref(false)
const messagesToDelete = ref<EmailMessage[]>([])

// Get email provider icon
function getEmailProviderIcon(provider: EmailProvider | string): string {
  switch (provider) {
    case EmailProvider.GOOGLE_EMAIL:
    case 'google-email':
      return 'simple-icons:gmail'
    case EmailProvider.MICROSOFT_EMAIL:
    case 'microsoft-email':
      return 'simple-icons:microsoftoutlook'
    case EmailProvider.SMTP:
    case 'smtp':
      return 'lucide:mail'
    default:
      return 'lucide:mail'
  }
}

// Get email provider name
function getEmailProviderName(provider: EmailProvider | string): string {
  switch (provider) {
    case EmailProvider.GOOGLE_EMAIL:
    case 'google-email':
      return 'Gmail'
    case EmailProvider.MICROSOFT_EMAIL:
    case 'microsoft-email':
      return 'Outlook'
    case EmailProvider.SMTP:
    case 'smtp':
      return 'SMTP'
    default:
      return 'Email'
  }
}

// Format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0)
    return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${Math.round(bytes / k ** i * 100) / 100} ${sizes[i]}`
}

// Extract name from email address
function extractNameFromEmail(email: string): string {
  if (!email)
    return ''
  const beforeAt = email.split('@')[0]
  return beforeAt.split('.').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
}

// Sanitize HTML for safe display
function sanitizeEmailHtml(htmlContent: string): string {
  if (!htmlContent)
    return ''

  // Basic HTML sanitization - remove potentially dangerous elements and attributes
  const sanitized = htmlContent
    // Remove script tags and their content
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    // Remove onclick and other event handlers
    .replace(/\son\w+="[^"]*"/gi, '')
    .replace(/\son\w+='[^']*'/gi, '')
    // Remove javascript: links
    .replace(/href="javascript:[^"]*"/gi, 'href="#"')
    .replace(/href='javascript:[^']*'/gi, 'href=\'#\'')
    // Allow data: URLs for images but be more selective
    .replace(/src="data:(?!image\/)([^"]*)"/gi, 'src=""')
    .replace(/src='data:(?!image\/)([^']*)/gi, 'src=\'\'')
    // Convert external links to open in new window and add security
    .replace(/href="(https?:\/\/[^"]*)"/gi, 'href="$1" target="_blank" rel="noopener noreferrer"')
    // Handle relative image URLs - convert to absolute if needed
    .replace(/src="(?!\/\/|https?:\/\/|data:)([^"]*)"/gi, (match, url) => {
      // For now, remove relative URLs as they won't work without proper base URL
      return 'src=""'
    })

  return sanitized
}

// Handle email actions
function handleReply() {
  showReplyBox.value = !showReplyBox.value
}

function handleBookmark() {
  if (!selectedMessage.value)
    return

  // TODO: Implement bookmark functionality
  console.log('Bookmark email:', selectedMessage.value.id)
  // You could call an API to bookmark the email
  // await performEmailAction({
  //   messageIds: [selectedMessage.value.id],
  //   integrationId: provider.value,
  //   action: 'star'
  // })
}

async function handleDelete() {
  if (!selectedMessage.value)
    return

  // Temporarily use browser confirm to test delete functionality
  const confirmed = confirm(`Are you sure you want to delete the email "${selectedMessage.value.subject || 'No subject'}"?`)

  if (confirmed) {
    console.log('🗑️ User confirmed deletion')
    await handleDeleteConfirm([selectedMessage.value.id])
  }
}

// Handle delete confirmation from modal
async function handleDeleteConfirm(messageIds: string[]) {
  if (!provider.value)
    return

  try {
    console.log('Deleting emails:', messageIds)
    const success = await deleteEmails(messageIds, provider.value)

    if (success) {
      // Clear active message if it was deleted
      if (activeMessage.value && messageIds.includes(activeMessage.value)) {
        activeMessage.value = null
      }
      console.log('Successfully deleted emails')
    }
  }
  catch (error) {
    console.error('Failed to delete emails:', error)
  }
}

function handleSendReply() {
  // TODO: Implement send reply functionality
  console.log('Send reply functionality not yet implemented')
  showReplyBox.value = false
}

// Handle email click - mark as read and set as active
async function handleEmailClick(message: EmailMessage) {
  // Set as active message
  activeMessage.value = message.id
  panelActive.value = true

  // Mark as read if it's unread and we have a provider
  if (message.status === 'unread' && provider.value) {
    console.log(`Marking email ${message.id} as read`)
    try {
      // Extract the actual Gmail message ID (remove 'gmail-' prefix if present)
      const gmailMessageId = message.provider?.providerId || message.id.replace(/^gmail-/, '')
      console.log(`Using Gmail message ID: ${gmailMessageId}`)

      await markAsRead(gmailMessageId, provider.value)
      console.log(`Successfully marked email ${message.id} as read`)
    }
    catch (error) {
      console.error('Failed to mark email as read:', error)
      // Don't show error to user as this is a background action
    }
  }
}

// Load integrations on mount
onMounted(async () => {
  // Load integrations to get provider metadata
  // Note: This will be handled by the integrations composable
})
</script>

<template>
  <div
    class="-mt-6 w-full overflow-hidden dark:[--color-input-default-bg:var(--color-muted-950)]"
  >
    <SplitterGroup direction="horizontal" class="h-[calc(100dvh_-_56px)]!">
      <SplitterPanel
        :default-size="20"
        :min-size="md ? 30 : 100"
        :max-size="md ? 60 : 100"
      >
        <!-- Messages list -->
        <div
          class="bg-muted-50 dark:bg-muted-900 flex size-full flex-col pt-3 lg:w-full"
        >
          <!-- Head (search + provider filter) -->
          <div class="px-4 sm:px-8 py-3 space-y-3">
            <!-- Current folder and provider filter -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <!-- Folder icon and name -->
                <div class="flex items-center gap-2">
                  <Icon
                    :name="{
                      inbox: 'solar:inbox-linear',
                      sent: 'solar:inbox-out-linear',
                      important: 'solar:bookmark-linear',
                      spam: 'solar:trash-bin-trash-linear',
                      drafts: 'solar:document-linear',
                    }[folder] || 'solar:inbox-linear'"
                    class="size-5 text-muted-600 dark:text-muted-400"
                  />
                  <BaseHeading size="lg" weight="semibold" class="text-muted-900 dark:text-white">
                    {{ {
                      inbox: 'Inbox',
                      sent: 'Sent',
                      important: 'Important',
                      spam: 'Spam',
                      drafts: 'Drafts',
                    }[folder] || 'Inbox' }}
                  </BaseHeading>
                </div>

                <!-- Provider filter tag -->
                <div v-if="currentProviderIntegration" class="flex items-center gap-2">
                  <span class="text-muted-400">•</span>
                  <BaseTag size="sm" rounded="full" color="primary" class="flex items-center gap-1">
                    <Icon :name="getEmailProviderIcon(currentProviderIntegration.integrationId)" class="size-3" />
                    <span>{{ getEmailProviderName(currentProviderIntegration.integrationId) }}</span>
                  </BaseTag>
                </div>
              </div>

              <!-- Clear filters button -->
              <div v-if="currentProviderIntegration" class="flex items-center">
                <BaseButton
                  size="sm"
                  rounded="lg"
                  variant="outline"
                  @click="$router.push(`/dashboards/inbox?folder=${folder}`)"
                >
                  <Icon name="lucide:x" class="size-3 mr-1" />
                  <span>Clear provider filter</span>
                </BaseButton>
              </div>
            </div>

            <!-- Search input -->
            <TairoInput
              v-model.trim="search"
              rounded="lg"
              placeholder="Search emails..."
              icon="lucide:search"
            />

            <!-- Actions row -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <!-- Sync button -->
                <BaseButton
                  size="sm"
                  variant="outline"
                  rounded="lg"
                  :disabled="provider && isProviderSyncing(provider)"
                  @click="handleSync"
                >
                  <Icon
                    name="lucide:refresh-cw"
                    class="size-4"
                    :class="{ 'animate-spin': provider && isProviderSyncing(provider) }"
                  />
                  <span>{{ (provider && isProviderSyncing(provider)) ? 'Syncing...' : 'Sync' }}</span>
                </BaseButton>
              </div>

              <!-- Email count -->
              <div class="text-xs text-muted-500">
                {{ filteredMessages.length }} {{ filteredMessages.length === 1 ? 'email' : 'emails' }}
              </div>
            </div>
          </div>

          <!-- Loading state -->
          <div v-if="loading && filteredMessages.length === 0" class="flex items-center justify-center py-8">
            <div class="flex items-center gap-2 text-muted-500">
              <Icon name="lucide:loader-2" class="size-4 animate-spin" />
              <span>Loading emails...</span>
            </div>
          </div>

          <!-- Error state -->
          <div v-else-if="error" class="px-4 sm:px-8 py-4">
            <div class="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div class="flex items-center gap-2 text-red-700 dark:text-red-300">
                <Icon name="lucide:alert-circle" class="size-4" />
                <span class="text-sm font-medium">Failed to load emails</span>
              </div>
              <p class="text-sm text-red-600 dark:text-red-400 mt-1">
                {{ error }}
              </p>
              <BaseButton
                size="sm"
                variant="outline"
                class="mt-2"
                @click="() => provider && refreshEmails(provider)"
              >
                Try Again
              </BaseButton>
            </div>
          </div>

          <!-- Empty state -->
          <div v-else-if="filteredMessages.length === 0" class="flex items-center justify-center py-12">
            <div class="text-center">
              <Icon name="lucide:mail" class="size-12 text-muted-400 mx-auto mb-4" />
              <BaseHeading size="lg" class="text-muted-600 dark:text-muted-400 mb-2">
                {{ search ? 'No emails found' : 'No emails yet' }}
              </BaseHeading>
              <BaseParagraph class="text-muted-500">
                {{ search ? 'Try adjusting your search terms' : 'Connect an email provider to get started' }}
              </BaseParagraph>
            </div>
          </div>

          <!-- Messages list -->
          <ul
            v-else
            class="nui-slimscroll-opaque grow space-y-2 overflow-y-auto ps-4 pe-2 me-2 pb-8 sm:ps-8 sm:pe-4 sm:me-4 pt-2"
          >
            <li
              v-for="message in filteredMessages"
              :key="message.id"
              class="duration 300 cursor-pointer rounded-xl p-4 transition-colors sm:p-6"
              :class="[
                activeMessage === message.id
                  ? 'bg-muted-200/60 dark:bg-muted-950/60'
                  : 'hover:bg-muted-100 dark:hover:bg-muted-950/30',
                message.status === 'unread' ? 'opacity-100' : 'opacity-60 hover:opacity-100',
              ]"
              role="button"
              tabindex="0"
              @click="handleEmailClick(message)"
            >
              <div class="flex items-start gap-3">
                <!-- Provider icon -->
                <div class="flex-shrink-0 mt-1">
                  <Icon
                    :name="getProviderIcon(message.provider || EmailProvider.SMTP)"
                    class="size-4 text-muted-400"
                  />
                </div>

                <div class="flex-1 min-w-0">
                  <!-- Subject line -->
                  <BaseHeading
                    size="md"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white mb-1 truncate"
                    :class="{ 'font-bold': !message.read }"
                  >
                    {{ message.subject || 'No subject' }}
                  </BaseHeading>

                  <!-- Preview text -->
                  <BaseParagraph
                    size="sm"
                    lead="tight"
                    class="text-muted-500 dark:text-muted-400 pointer-events-none line-clamp-2"
                  >
                    {{ getEmailPreview(message) }}
                  </BaseParagraph>

                  <!-- Sender and timestamp -->
                  <div class="mt-2 flex items-center justify-between">
                    <div class="flex items-center gap-2 min-w-0">
                      <!-- Sender avatar placeholder -->
                      <div class="flex-shrink-0">
                        <div class="size-6 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                          <span class="text-xs font-medium text-white">
                            {{ (message.from?.name || extractNameFromEmail(message.from?.email) || 'U').charAt(0).toUpperCase() }}
                          </span>
                        </div>
                      </div>

                      <!-- Sender info -->
                      <div class="min-w-0 flex-1">
                        <BaseHeading
                          size="sm"
                          weight="medium"
                          lead="tight"
                          class="truncate"
                        >
                          <span>{{ message.from?.name || extractNameFromEmail(message.from?.email) || 'Unknown sender' }}</span>
                        </BaseHeading>
                        <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 truncate">
                          {{ message.from?.email || '<EMAIL>' }}
                        </BaseParagraph>
                      </div>
                    </div>

                    <!-- Timestamp and indicators -->
                    <div class="flex items-center gap-2 flex-shrink-0">
                      <!-- Attachment indicator -->
                      <Icon
                        v-if="message.attachments && message.attachments.length > 0"
                        name="lucide:paperclip"
                        class="size-3 text-muted-400"
                      />

                      <!-- Read status indicator -->
                      <div
                        v-if="message.status === 'unread'"
                        class="size-2 rounded-full bg-primary-500"
                      />

                      <!-- Timestamp -->
                      <span class="text-muted-400 font-sans text-xs whitespace-nowrap">
                        {{ formatEmailDate(message.date || new Date()) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </li>

            <!-- Load more button -->
            <li v-if="hasMore" class="px-4 py-2">
              <BaseButton
                variant="outline"
                size="sm"
                class="w-full"
                :disabled="loading"
                @click="() => provider && loadMoreEmails(provider)"
              >
                <Icon
                  name="lucide:loader-2"
                  class="size-4 mr-2"
                  :class="{ 'animate-spin': loading }"
                />
                <span>{{ loading ? 'Loading...' : 'Load more emails' }}</span>
              </BaseButton>
            </li>
          </ul>
        </div>
      </SplitterPanel>
      <SplitterResizeHandle class="w-1 bg-muted-100 hover:bg-muted-200 dark:bg-muted-800/50 dark:hover:bg-muted-800 transition-colors duration-100" />
      <SplitterPanel :default-size="80">
        <!-- Message details -->
        <div
          class="bg-muted-50 dark:bg-muted-900 border-muted-200 dark:border-muted-800/60 fixed end-0 top-0 z-10 flex h-full flex-col border-s transition-transform duration-300 lg:static lg:grow"
          :class="
            panelActive
              ? 'translate-x-0'
              : 'translate-x-full lg:translate-x-0'
          "
        >
          <!-- Toolbar -->
          <div class="mx-auto w-full lg:px-10">
            <div
              class="relative z-10 flex lg:hidden h-16 w-full items-center px-8"
            >
              <div
                class="ms-auto text-muted-700 dark:text-muted-300 flex items-center gap-2"
              >
                <button
                  type="button"
                  class="flex size-9 items-center justify-center"
                  @click="panelActive = false"
                >
                  <Icon name="lucide:x" class="size-5" />
                </button>
              </div>
            </div>

            <!-- Message header -->
            <div v-if="selectedMessage" class="border-muted-200 dark:border-muted-800 border-b">
              <!-- Main header info -->
              <div class="flex items-center justify-between px-8 py-4">
                <div class="flex items-center gap-x-4">
                  <!-- Sender avatar -->
                  <div class="flex-shrink-0">
                    <div class="size-10 rounded-full bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center">
                      <span class="text-sm font-medium text-white">
                        {{ (selectedMessage.from?.name || extractNameFromEmail(selectedMessage.from?.email) || 'U').charAt(0).toUpperCase() }}
                      </span>
                    </div>
                  </div>

                  <!-- Sender and provider info -->
                  <div class="flex flex-col">
                    <div class="flex items-center gap-2">
                      <BaseHeading size="md" weight="medium" lead="tight" class="text-muted-900 dark:text-white">
                        {{ selectedMessage.from?.name || extractNameFromEmail(selectedMessage.from?.email) || 'Unknown sender' }}
                      </BaseHeading>
                      <Icon
                        :name="getProviderIcon(selectedMessage.provider || EmailProvider.SMTP)"
                        class="size-4 text-muted-400"
                      />
                    </div>
                    <div class="flex items-center gap-2 text-sm text-muted-600 dark:text-muted-400">
                      <span>{{ selectedMessage.from?.email || '<EMAIL>' }}</span>
                      <span>•</span>
                      <span>{{ formatEmailDate(selectedMessage.date || new Date()) }}</span>
                    </div>
                  </div>
                </div>

                <!-- Action buttons -->
                <div class="flex gap-x-2">
                  <BaseTooltip content="Reply" variant="dark">
                    <BaseButton size="icon-sm" rounded="md" variant="outline" @click="handleReply">
                      <Icon name="lucide:reply" class="size-4" />
                    </BaseButton>
                  </BaseTooltip>
                  <BaseTooltip content="Bookmark" variant="dark">
                    <BaseButton size="icon-sm" rounded="md" variant="outline" @click="handleBookmark">
                      <Icon name="lucide:bookmark" class="size-4" />
                    </BaseButton>
                  </BaseTooltip>
                  <BaseTooltip content="Delete" variant="dark">
                    <BaseButton size="icon-sm" rounded="md" variant="outline" @click="handleDelete">
                      <Icon name="lucide:trash-2" class="size-4" />
                    </BaseButton>
                  </BaseTooltip>
                  <BaseTooltip content="More options" variant="dark">
                    <BaseButton size="icon-sm" rounded="md" variant="outline">
                      <Icon name="lucide:more-horizontal" class="size-4" />
                    </BaseButton>
                  </BaseTooltip>
                </div>
              </div>

              <!-- Email metadata -->
              <div class="px-8 pb-4">
                <div class="flex items-center justify-between text-sm">
                  <div class="flex items-center gap-4">
                    <!-- Read status -->
                    <div class="flex items-center gap-1">
                      <div :class="selectedMessage.status === 'read' ? 'bg-green-500' : 'bg-yellow-500'" class="size-2 rounded-full" />
                      <span class="text-muted-600 dark:text-muted-400">
                        {{ selectedMessage.status === 'read' ? 'Read' : 'Unread' }}
                      </span>
                    </div>

                    <!-- Provider name -->
                    <BaseTag size="sm" rounded="full" variant="outline">
                      {{ getEmailProviderName(selectedMessage.provider || 'smtp') }}
                    </BaseTag>

                    <!-- Attachment count -->
                    <div v-if="selectedMessage.attachments && selectedMessage.attachments.length > 0" class="flex items-center gap-1">
                      <Icon name="lucide:paperclip" class="size-3 text-muted-500" />
                      <span class="text-muted-600 dark:text-muted-400">
                        {{ selectedMessage.attachments.length }} {{ selectedMessage.attachments.length === 1 ? 'attachment' : 'attachments' }}
                      </span>
                    </div>
                  </div>

                  <!-- Message ID (for debugging) -->
                  <div class="text-xs text-muted-400">
                    ID: {{ selectedMessage.id.slice(-8) }}
                  </div>
                </div>
              </div>
            </div>
            <!-- Message body -->
            <div
              class="nui-slimscroll overflow-y-auto transition-all duration-300 ease-in-out"
              :class="showReplyBox ? 'h-[calc(100vh_-_500px)]' : 'h-[calc(100vh_-_200px)]'"
            >
              <!-- No message selected -->
              <div v-if="!selectedMessage" class="flex items-center justify-center h-full">
                <div class="text-center">
                  <Icon name="lucide:mail-open" class="size-16 text-muted-400 mx-auto mb-4" />
                  <BaseHeading size="lg" class="text-muted-600 dark:text-muted-400 mb-2">
                    Select an email
                  </BaseHeading>
                  <BaseParagraph class="text-muted-500">
                    Choose an email from the list to view its content
                  </BaseParagraph>
                </div>
              </div>

              <!-- Message content -->
              <div v-else class="p-8">
                <!-- Subject -->
                <BaseHeading size="2xl" weight="medium" class="text-muted-900 dark:text-white mb-6">
                  {{ selectedMessage.subject || 'No subject' }}
                </BaseHeading>

                <!-- Email content -->
                <div class="email-content-wrapper max-w-none">
                  <!-- HTML content (if available) -->
                  <div
                    v-if="selectedMessage.htmlBody"
                    class="email-html-content prose prose-sm prose-muted dark:prose-invert max-w-none"
                    v-html="sanitizeEmailHtml(selectedMessage.htmlBody)"
                  />

                  <!-- Text content fallback -->
                  <div
                    v-else-if="selectedMessage.textBody"
                    class="email-text-content whitespace-pre-wrap font-sans text-sm leading-relaxed text-muted-700 dark:text-muted-300"
                  >
                    {{ selectedMessage.textBody }}
                  </div>

                  <!-- Snippet fallback -->
                  <div
                    v-else-if="selectedMessage.preview"
                    class="email-preview-content whitespace-pre-wrap font-sans text-sm leading-relaxed text-muted-600 dark:text-muted-400 italic"
                  >
                    {{ selectedMessage.preview }}
                    <div class="mt-4 text-xs text-muted-500">
                      (Preview only - full content not available)
                    </div>
                  </div>

                  <!-- No content -->
                  <div v-else class="text-center py-8">
                    <Icon name="lucide:file-text" class="size-8 text-muted-400 mx-auto mb-2" />
                    <BaseParagraph class="text-muted-500">
                      This email has no content to display
                    </BaseParagraph>
                  </div>
                </div>

                <!-- Attachments -->
                <div v-if="selectedMessage.attachments && selectedMessage.attachments.length > 0" class="mt-8">
                  <BaseHeading size="lg" weight="medium" class="text-muted-900 dark:text-white mb-4">
                    Attachments ({{ selectedMessage.attachments.length }})
                  </BaseHeading>

                  <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                    <div
                      v-for="(attachment, index) in selectedMessage.attachments"
                      :key="index"
                      class="border border-muted-200 dark:border-muted-800 rounded-lg p-3 hover:bg-muted-50 dark:hover:bg-muted-950 transition-colors cursor-pointer group"
                    >
                      <div class="flex items-center gap-3">
                        <!-- File type icon -->
                        <div class="flex-shrink-0">
                          <div class="size-10 rounded-lg bg-muted-100 dark:bg-muted-800 flex items-center justify-center">
                            <Icon
                              :name="getAttachmentIcon(attachment.contentType || attachment.mimeType)"
                              class="size-5 text-muted-600 dark:text-muted-400"
                            />
                          </div>
                        </div>

                        <!-- File info -->
                        <div class="flex-1 min-w-0">
                          <div class="text-sm font-medium text-muted-900 dark:text-white truncate">
                            {{ attachment.filename || attachment.name || 'Unknown file' }}
                          </div>
                          <div class="text-xs text-muted-500 mt-0.5">
                            {{ formatFileSize(attachment.size || 0) }}
                            <span v-if="attachment.contentType"> • {{ attachment.contentType }}</span>
                          </div>
                        </div>

                        <!-- Download button -->
                        <div class="flex-shrink-0">
                          <BaseButton
                            size="icon-sm"
                            rounded="md"
                            variant="outline"
                            class="opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <Icon name="lucide:download" class="size-4" />
                          </BaseButton>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- Message reply -->
            <Transition
              enter-active-class="transition-all duration-300 ease-out"
              enter-from-class="transform translate-y-full opacity-0"
              enter-to-class="transform translate-y-0 opacity-100"
              leave-active-class="transition-all duration-300 ease-in"
              leave-from-class="transform translate-y-0 opacity-100"
              leave-to-class="transform translate-y-full opacity-0"
            >
              <div
                v-if="showReplyBox"
                class="relative w-full px-8 py-4 border-t border-muted-200 dark:border-muted-800 bg-muted-50 dark:bg-muted-900"
              >
                <div
                  class="border-muted-300 dark:border-muted-800/60 bg-white dark:bg-muted-950 focus-within:outline-muted-200 dark:focus-within:outline-muted-700 w-full rounded-xl border outline-none outline-offset-4 transition-all duration-300 focus-within:outline-dashed focus-within:outline-2"
                >
                  <div class="p-3 border-b border-muted-200 dark:border-muted-800">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-2">
                        <Icon name="lucide:reply" class="size-4 text-muted-500" />
                        <span class="text-sm font-medium text-muted-700 dark:text-muted-300">Reply to: {{ selectedMessage?.from?.name || selectedMessage?.from?.email }}</span>
                      </div>
                      <BaseButton
                        size="icon-sm"
                        variant="ghost"
                        rounded="md"
                        @click="showReplyBox = false"
                      >
                        <Icon name="lucide:x" class="size-4" />
                      </BaseButton>
                    </div>
                  </div>
                  <textarea
                    class="bg-transparent placeholder:text-muted-300 dark:placeholder:text-muted-600 w-full resize-none p-4 font-sans outline-none border-none"
                    placeholder="Type your reply here..."
                    rows="4"
                    @keydown.meta.enter="handleSendReply"
                    @keydown.ctrl.enter="handleSendReply"
                  />
                  <div class="flex items-center justify-between p-3 pt-0">
                    <div class="flex items-center gap-2">
                      <BaseTooltip content="Attach file" variant="dark">
                        <BaseButton
                          size="icon-sm"
                          variant="ghost"
                          rounded="lg"
                          class="text-muted-500 dark:text-muted-400"
                        >
                          <Icon name="lucide:paperclip" class="size-4" />
                        </BaseButton>
                      </BaseTooltip>
                      <BaseTooltip content="Insert image" variant="dark">
                        <BaseButton
                          size="icon-sm"
                          variant="ghost"
                          rounded="lg"
                          class="text-muted-500 dark:text-muted-400"
                        >
                          <Icon name="lucide:image" class="size-4" />
                        </BaseButton>
                      </BaseTooltip>
                    </div>
                    <div class="flex items-center gap-2">
                      <BaseButton
                        size="sm"
                        variant="outline"
                        rounded="lg"
                        @click="showReplyBox = false"
                      >
                        Cancel
                      </BaseButton>
                      <BaseButton
                        size="sm"
                        variant="solid"
                        rounded="lg"
                        @click="handleSendReply"
                      >
                        <Icon name="lucide:send" class="size-4 mr-2" />
                        Send Reply
                      </BaseButton>
                    </div>
                  </div>
                </div>
              </div>
            </Transition>
          </div>
        </div>
      </SplitterPanel>
    </SplitterGroup>

    <!-- Email Delete Modal -->
    <EmailDeleteModal
      v-model="showDeleteModal"
      :emails="messagesToDelete"
      @confirm="handleDeleteConfirm"
    />
  </div>
</template>

<style scoped>
/* Email content styling for better HTML rendering */
.email-html-content {
  /* Reset some prose styles for email content */
  color: inherit;
  line-height: 1.6;
}

.email-html-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
}

.email-html-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.email-html-content :deep(table td),
.email-html-content :deep(table th) {
  padding: 0.5rem;
  text-align: left;
  border-bottom: 1px solid var(--color-muted-200);
}

.email-html-content :deep(blockquote) {
  border-left: 4px solid var(--color-primary-500);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: var(--color-muted-600);
}

.email-html-content :deep(pre) {
  background-color: var(--color-muted-100);
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  font-size: 0.875rem;
}

.email-html-content :deep(code) {
  background-color: var(--color-muted-100);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Dark mode adjustments */
.dark .email-html-content :deep(table td),
.dark .email-html-content :deep(table th) {
  border-bottom-color: var(--color-muted-800);
}

.dark .email-html-content :deep(blockquote) {
  color: var(--color-muted-400);
}

.dark .email-html-content :deep(pre),
.dark .email-html-content :deep(code) {
  background-color: var(--color-muted-800);
}

/* Ensure email content is contained and scrollable */
.email-content-wrapper {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}
</style>
