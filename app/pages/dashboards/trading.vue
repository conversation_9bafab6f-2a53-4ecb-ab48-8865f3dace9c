<script setup lang="ts">
import type {
  InfoCardData,
  TradingMarketInsightsData,
  TradingMarketOrderData,
  TradingStockCardData,
  TradingStockChartData,
  TradingTrendingStocksData,
} from '~/types/widgets'

definePageMeta({
  title: 'Trading',
  preview: {
    title: 'Trading',
    description: 'For finance and investment',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-trading.png',
    srcDark: '/img/screens/dashboards-trading-dark.png',
    order: 5,
    new: true,
  },
})

const banks = ref([
  {
    id: 1,
    name: 'Chase',
    logo: '/img/logos/companies/chase-full.svg',
    series: [
      {
        name: 'Stock value',
        data: [
          1231.37,
          1453.78,
          1739.87,
          2156.32,
          1932.29,
          1832.38,
          1732.29,
          1832.29,
          1989.39,
          1783.29,
          2130.39,
          2256.39,
        ],
      },
    ],
  },
  {
    id: 2,
    name: 'Eurasian',
    logo: '/img/logos/companies/eurasian-full.svg',
    series: [
      {
        name: 'Stock value',
        data: [
          1200.23,
          1405.67,
          1653.42,
          1950.18,
          2301.76,
          2714.92,
          3196.57,
          3753.61,
          4393.04,
          5121.88,
          5957.25,
          6907.34,
        ],
      },
    ],
  },
  {
    id: 3,
    name: 'Bank of America',
    logo: '/img/logos/companies/bank-of-america-full.svg',
    series: [
      {
        name: 'Stock value',
        data: [
          1300.45,
          1425.67,
          1578.93,
          1750.21,
          1940.56,
          2151.99,
          2386.56,
          2646.32,
          2933.34,
          3249.67,
          3597.38,
          3979.54,
        ],
      },
    ],
  },
])

const activePeriod = ref('year')
const orderShares = ref(0)

// Dropdown state for chart selector
const open = ref(false)
const selectedBank = ref(banks.value[0])

// Functions
function openDropdown() {
  open.value = !open.value
}

function setAccount(bank: any) {
  selectedBank.value = bank
  open.value = false
  handleBankChanged(bank)
}

// Stock cards data
const stockCards: TradingStockCardData[] = [
  {
    name: 'Chase',
    logo: '/img/logos/companies/chase-full.svg',
    label: 'Chase stocks',
    change: 53.14,
    percentage: 1.2,
    changeSymbol: '+',
    isPositive: true,
  },
  {
    name: 'Eurasian',
    logo: '/img/logos/companies/eurasian-full.svg',
    label: 'Eurasian stocks',
    change: 12.37,
    percentage: 0.7,
    changeSymbol: '-',
    isPositive: false,
  },
  {
    name: 'Bank of America',
    logo: '/img/logos/companies/bank-of-america-full.svg',
    label: 'BOA stocks',
    change: 23.19,
    percentage: 1.9,
    changeSymbol: '+',
    isPositive: true,
  },
]

// Stock chart data
const stockChartData: TradingStockChartData = {
  banks: banks.value,
}

// Trending stocks data
const trendingStocksData: TradingTrendingStocksData = {
  title: 'Trending Stocks',
  viewAllLink: '#',
  viewAllLabel: 'View all',
  stocks: [
    {
      name: 'Chase Bank',
      logo: '/img/logos/companies/chase-full.svg',
      change: 53.14,
      percentage: 1.2,
      changeSymbol: '+',
      isPositive: true,
    },
    {
      name: 'Eurasian Bank',
      logo: '/img/logos/companies/eurasian-full.svg',
      change: 12.37,
      percentage: 0.7,
      changeSymbol: '-',
      isPositive: false,
    },
    {
      name: 'Bank of America',
      logo: '/img/logos/companies/bank-of-america-full.svg',
      change: 23.19,
      percentage: 1.9,
      changeSymbol: '+',
      isPositive: true,
    },
    {
      name: 'TD Bank',
      logo: '/img/logos/companies/td-full.svg',
      change: 39.71,
      percentage: 2.6,
      changeSymbol: '+',
      isPositive: true,
    },
    {
      name: 'Kaspi Bank',
      logo: '/img/logos/companies/kaspi-full.svg',
      change: 76.87,
      percentage: 5.8,
      changeSymbol: '+',
      isPositive: true,
    },
  ],
}

// Market insights data
const marketInsightsData: TradingMarketInsightsData = {
  title: 'Market Insights',
  subtitle: 'Tairo market shares',
  icon: 'TairoLogo',
  insights: [
    { label: 'Open', value: formatPrice(2394.64) },
    { label: 'High', value: formatPrice(2789.12) },
    { label: 'Low', value: formatPrice(2118.27) },
    { label: 'Close', value: formatPrice(2091.14) },
    { label: '% Change', value: '6.92%' },
  ],
}

// Market order form data (reactive)
const marketOrderData = computed<TradingMarketOrderData>(() => ({
  title: 'Market Order',
  subtitle: 'Place a share order',
  sharesLabel: 'Shares',
  shares: orderShares.value,
  priceDetails: [
    { label: 'Market price', value: formatPrice(689.17) },
    { label: 'Commissions', value: formatPrice(0.0) },
  ],
  totalLabel: 'Estimated cost',
  totalValue: formatPrice(orderShares.value * 689.17),
  buttonText: 'Buy Shares',
}))

// Info card data
const infoCardData: InfoCardData = {
  title: 'Try Quick Cash',
  description: 'Quick Cash is a new feature that allows you to send money to your friends and family in a matter of seconds.',
  icon: 'solar:bag-smile-bold-duotone',
  link: '#',
  linkLabel: 'Learn more about it',
}

// Event handlers
function handleBuyShares() {
  console.log('Buy shares clicked')
}

function handleBankChanged(bank: any) {
  console.log('Bank changed:', bank)
}

function updateShares(value: number) {
  orderShares.value = value
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header -->
    <div
      class="mb-6 flex flex-col justify-between gap-y-4 sm:flex-row sm:items-center"
    >
      <div>
        <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
          Current Trading Balance
        </BaseParagraph>
        <BaseHeading
          size="4xl"
          weight="medium"
          class="text-muted-900 dark:text-white"
        >
          <span
            class="after:text-success-500 after:relative after:-end-2 after:-top-3 after:text-sm after:content-['+3.4%']"
          >
            {{ formatPrice(22529.43) }}
          </span>
        </BaseHeading>
      </div>
      <div class="flex gap-2 sm:justify-end">
        <BaseButton
          rounded="md"
          size="sm"
          :variant="activePeriod === 'hour' ? 'primary' : 'default'"
          @click="activePeriod = 'hour'"
        >
          Hour
        </BaseButton>
        <BaseButton
          rounded="md"
          size="sm"
          :variant="activePeriod === 'day' ? 'primary' : 'default'"
          @click="activePeriod = 'day'"
        >
          Day
        </BaseButton>
        <BaseButton
          rounded="md"
          size="sm"
          :variant="activePeriod === 'week' ? 'primary' : 'default'"
          @click="activePeriod = 'week'"
        >
          Week
        </BaseButton>
        <BaseButton
          rounded="md"
          size="sm"
          :variant="activePeriod === 'month' ? 'primary' : 'default'"
          @click="activePeriod = 'month'"
        >
          Month
        </BaseButton>
        <BaseButton
          rounded="md"
          size="sm"
          :variant="activePeriod === 'year' ? 'primary' : 'default'"
          @click="activePeriod = 'year'"
        >
          Year
        </BaseButton>
      </div>
    </div>
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Column -->
      <div class="col-span-12 gap-4 lg:col-span-8">
        <div class="flex flex-col gap-4">
          <!-- Tile grid -->
          <div class="grid gap-4 sm:grid-cols-3">
            <!-- Stock Cards -->
            <WidgetsTradingStockCard
              v-for="stock in stockCards"
              :key="stock.name"
              :data="stock"
            />
          </div>
          <!-- Chart -->
          <div class="relative">
            <div class="absolute end-2 top-2 w-52">
              <div class="relative z-10 w-full">
                <button
                  type="button"
                  class="click-blur dark:bg-muted-950 border-muted-200 dark:border-muted-800 w-full rounded-full border bg-white p-3"
                  @click="openDropdown()"
                >
                  <span class="flex w-full items-center gap-3 text-start">
                    <img
                      :src="selectedBank?.logo"
                      :alt="selectedBank?.name"
                      class="size-6 shrink-0"
                    >
                    <div>
                      <BaseText
                        size="sm"
                        class="text-muted-900 dark:text-muted-200 block capitalize"
                      >
                        {{ selectedBank?.name }}
                      </BaseText>
                    </div>
                    <Icon
                      name="lucide:chevron-down"
                      class="text-muted-400 ms-auto size-4 transition-transform duration-300"
                      :class="open && 'rotate-180'"
                    />
                  </span>
                </button>
                <Transition
                  enter-active-class="transition duration-100 ease-out"
                  enter-from-class="transform scale-95 opacity-0"
                  enter-to-class="transform scale-100 opacity-100"
                  leave-active-class="transition duration-75 ease-in"
                  leave-from-class="transform scale-100 opacity-100"
                  leave-to-class="transform scale-95 opacity-0"
                >
                  <div
                    v-if="open"
                    class="border-muted-200 dark:border-muted-800 dark:bg-muted-950 shadow-muted-400/10 dark:shadow-muted-800/10 absolute start-0 top-14 w-full rounded-xl border bg-white p-2 shadow-xl"
                  >
                    <!-- banks -->
                    <ul>
                      <li v-for="bank in banks" :key="bank.id">
                        <button
                          type="button"
                          class="hover:bg-muted-100 dark:hover:bg-muted-900 flex w-full items-center gap-3 rounded-lg px-4 py-2 text-start transition-colors duration-300"
                          @click="setAccount(bank)"
                        >
                          <img
                            :src="bank.logo"
                            :alt="bank.name"
                            class="size-6 shrink-0"
                          >
                          <span class="block">
                            <span
                              class="font-heading text-muted-900 dark:text-muted-200 block text-sm capitalize"
                            >
                              {{ bank.name }}
                            </span>
                          </span>
                        </button>
                      </li>
                    </ul>
                  </div>
                </Transition>
              </div>
            </div>
            <ChartAreaStockPrice
              :series="selectedBank?.series"
              class="[--color-chart-gradient:var(--color-muted-50)] dark:[--color-chart-gradient:var(--color-muted-900)]"
            />
          </div>
          <!-- Table -->
          <WidgetsTradingTrendingStocks :data="trendingStocksData" />
        </div>
      </div>
      <!-- Column -->
      <div class="col-span-12 lg:col-span-4">
        <div class="flex flex-col gap-4">
          <!-- Insights -->
          <WidgetsTradingMarketInsights :data="marketInsightsData" />
          <!-- Buy shares -->
          <WidgetsTradingMarketOrderForm
            :data="marketOrderData"
            @update:shares="updateShares"
            @buy-shares="handleBuyShares"
          />
          <!-- Learn more -->
          <WidgetsSharedInfoCard :data="infoCardData" />
        </div>
      </div>
    </div>
  </div>
</template>
