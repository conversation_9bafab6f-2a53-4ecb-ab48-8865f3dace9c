<script setup lang="ts">
import type {
  DatePickerWidgetData,
  NotificationsWidgetData,
  PersonalScoreData,
  PromotionalCardData,
} from '~/types/widgets'
import { computed } from 'vue'
import { useAuth } from '~/composables/auth'

definePageMeta({
  title: 'Dashboard',
  preview: {
    title: 'Balance dashboard',
    description: 'For bank account overview',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-balance.png',
    srcDark: '/img/screens/dashboards-balance-dark.png',
    order: 5,
    new: true,
  },
})

const { currentProfile } = useAuth()

const displayName = computed(() => {
  const p = currentProfile.value
  if (!p)
    return 'there'
  if (p.firstName && p.lastName)
    return `${p.firstName} ${p.lastName.charAt(0)}.`
  return p.display_name || 'there'
})

const showFeatures = ref(true)

// Datepicker
const date = ref(new Date())

// Personal Score widget data
const personalScoreData: PersonalScoreData = {
  title: 'Personal Score',
  chartComponent: 'ChartRadialGaugeAlt',
  description: 'Your score has been calculated based on the latest metrics',
}

// Date picker widget data
const datePickerData: DatePickerWidgetData = {
  locale: 'en',
  label: 'Start date',
}

// Notifications widget data
const notificationsData: NotificationsWidgetData = {
  notificationComponent: 'NotificationsCompact',
}

// Promotional card data
const promotionalCardData: PromotionalCardData = {
  title: 'You\'re doing great!',
  description: 'Start using our team and project management tools',
  link: {
    href: '#',
    text: 'Learn More',
  },
  icon: {
    name: 'ph:crown-duotone',
    class: 'text-primary-600/50 size-14',
  },
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 lg:col-span-9">
        <Transition
          leave-active-class="transition origin-top duration-75 ease-in"
          leave-from-class="transform scale-y-100 opacity-100"
          leave-to-class="transform scale-y-0 opacity-0"
        >
          <div v-if="showFeatures" class="w-full pb-4">
            <!-- Features widget -->
            <WidgetsInfoFeatures
              :data="{
                title: 'New Features',
                subtitle: 'Some nice features we\'ve just released',
                features: [
                  {
                    title: 'Recurring payments',
                    description: 'Set up automatic payments for your regular expenses and never miss a payment again.',
                    image: '/img/illustrations/ui/recurring.svg',
                    linkUrl: '/features/recurring-payments',
                    linkText: 'Learn more about it',
                  },
                  {
                    title: 'Credit balance',
                    description: 'Keep track of your credit balance and optimize your cash flow with smart insights.',
                    image: '/img/illustrations/ui/cashback.svg',
                    linkUrl: '/features/credit-balance',
                    linkText: 'Learn more about it',
                  },
                ],
              }"
            >
              <template #actions>
                <BaseButton
                  size="icon-sm"
                  variant="muted"
                  data-nui-tooltip="Hide this"
                  @click="showFeatures = false"
                >
                  <Icon name="lucide:x" class="size-4" />
                </BaseButton>
              </template>
            </WidgetsInfoFeatures>
          </div>
        </Transition>
        <div class="grid grid-cols-12 gap-4">
          <!-- Grid item -->
          <div class="col-span-12 md:col-span-5">
            <!-- Welcome widget -->
            <WidgetsInfoWelcome
              :config="{
                accountTitle: 'Account',
                greeting: 'Welcome back',
                description: 'Everything seems ok and up-to-date with your account since your last visit. Would you like to fund it?',
                buttonText: 'Fund my Account',
                buttonAction: 'fund-account',
                userName: displayName.split(' ')[0] || 'there',
              }"
              @action="(type) => console.log('Action:', type)"
            />
          </div>
          <div class="col-span-12 md:col-span-7">
            <!-- Account balance widget -->
            <WidgetsFinanceAccountBalance
              :data="{
                title: 'Account Balance',
                balance: 9543.12,
                change: 149.32,
                changeLabel: 'Today, Sep 25',
                changeIcon: 'lucide:arrow-up',
                isIncrease: true,
              }"
            />
          </div>
          <div class="col-span-12 md:col-span-6">
            <!-- Money out widget -->
            <WidgetsFinanceMoneyOut
              :config="{
                title: 'Money out last 30 days',
                period: '30',
                maxItems: 4,
                linkUrl: '/dashboards/analytics',
                showProgress: true,
                signSymbol: '-',
              }"
            />
          </div>
          <div class="col-span-12 md:col-span-6">
            <!-- Money in widget -->
            <WidgetsFinanceMoneyIn
              :config="{
                title: 'Money in last 30 days',
                period: '30',
                maxItems: 4,
                linkUrl: '/dashboards/analytics',
                showProgress: true,
              }"
            />
          </div>
          <div class="col-span-12 md:col-span-12">
            <!-- Transactions widget -->
            <WidgetsTransactionsSummary
              :config="{
                title: 'Recent Transactions',
                linkText: 'View all transactions',
                linkUrl: '/transactions',
                maxItems: 8,
                showSearch: false,
              }"
            />
          </div>
        </div>
      </div>
      <div class="col-span-12 lg:col-span-3">
        <!-- Column -->
        <div class="relative flex flex-col gap-4">
          <!-- Widget -->
          <ActionText
            title="Upgrade to Pro"
            text="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quid censes in Latino fore? Nam ante Aristippus, et ille melius."
            label="Upgrade Now"
            to="#"
            rounded="md"
          />
          <!-- Personal Score Widget -->
          <WidgetsChartsPersonalScore :data="personalScoreData" />

          <!-- Date Picker Widget -->
          <WidgetsUtilsDatePicker
            v-model="date"
            :data="datePickerData"
          />

          <!-- Notifications Widget -->
          <WidgetsUtilsNotifications :data="notificationsData" />

          <!-- Promotional Card Widget -->
          <WidgetsUtilsPromotionalCard :data="promotionalCardData" />
        </div>
      </div>
    </div>
  </div>
</template>
