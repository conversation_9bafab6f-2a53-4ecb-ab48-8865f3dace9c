<script setup lang="ts">
import type {
  HobbyActivityListData,
  HobbyCategoryData,
  HobbyLocationsListData,
} from '~/types/widgets'
import { getRandomColor } from '~/utils/colors'

definePageMeta({
  title: 'Hobbies',
  preview: {
    title: 'Hobbies dashboard',
    description: 'For hobbies and interests',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-hobbies.png',
    srcDark: '/img/screens/dashboards-hobbies-dark.png',
    order: 16,
  },
})

// Hobby categories data
const hobbeyCategories: HobbyCategoryData[] = [
  {
    title: 'Trekking',
    icon: 'eos-icons:bootstrapping',
    image: '/img/illustrations/dashboards/hobbies/hobby-1.svg',
    link: '#',
    ctaText: 'View activities',
  },
  {
    title: 'Cycling',
    icon: 'fluent-emoji-high-contrast:wheel',
    image: '/img/illustrations/dashboards/hobbies/hobby-2.svg',
    link: '#',
    ctaText: 'View activities',
  },
  {
    title: 'Exploration',
    icon: 'fa6-solid:compass',
    image: '/img/illustrations/dashboards/hobbies/hobby-3.svg',
    link: '#',
    ctaText: 'View activities',
  },
  {
    title: 'Hiking',
    icon: 'mdi:pickaxe',
    image: '/img/illustrations/dashboards/hobbies/hobby-4.svg',
    link: '#',
    ctaText: 'View activities',
  },
]

// Trending activities data
const trendingActivities: HobbyActivityListData = {
  title: 'Trending now',
  subtitle: 'Chek out the latest activities',
  activities: [
    {
      name: 'Extreme foot trekk',
      date: 'Oct 31, 2022',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      icon: 'tabler:trekking',
    },
    {
      name: 'Corporate rafting trip',
      date: 'Nov 14, 2022',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      icon: 'map:rafting',
    },
    {
      name: 'Hiking in the Alps',
      date: 'Dec 3, 2022',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      icon: 'map:climbing',
    },
    {
      name: 'Bicycle team mastery',
      date: 'Dec 17, 2022',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      icon: 'map:bicycle-store',
    },
  ],
  getRandomColor,
}

// Popular activities data
const popularActivities: HobbyActivityListData = {
  title: 'Popular activities',
  subtitle: 'Chek out the latest activities',
  activities: [
    {
      name: 'Extreme triathlon',
      date: 'Feb 4, 2023',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      icon: 'map:kayaking',
    },
    {
      name: 'Jungle hiking',
      date: 'Jan 11, 2023',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      icon: 'map:playground',
    },
    {
      name: 'Archery challenge',
      date: 'Dec 3, 2022',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      icon: 'map:archery',
    },
    {
      name: 'Extreme waterskiing',
      date: 'Dec 21, 2022',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      icon: 'map:waterskiing',
    },
  ],
  getRandomColor,
}

// Locations data
const hobbeyLocations: HobbyLocationsListData = {
  title: 'Locations',
  locations: [
    {
      name: 'Mt Wilbur',
      location: 'Oregon',
      level: 'Lvl 3',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-1.svg',
      link: '#',
    },
    {
      name: 'Devil\'s Lair',
      location: 'Alabama',
      level: 'Lvl 7',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-7.svg',
      link: '#',
    },
    {
      name: 'Dragon\'s Den',
      location: 'Missouri',
      level: 'Lvl 4',
      image: '/img/illustrations/dashboards/hobbies/landscape-thumb-4.svg',
      link: '#',
    },
  ],
}

// Datepicker
const date = ref(new Date())
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header -->
    <div class="mb-8 flex flex-col justify-between md:flex-row md:items-center">
      <div
        class="flex max-w-[425px] flex-col items-center gap-4 text-center md:flex-row md:text-start lg:max-w-full"
      >
        <div>
          <BaseHeading
            as="h2"
            size="xl"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>Explore Hobbies</span>
          </BaseHeading>
          <BaseParagraph size="sm">
            <span class="text-muted-600 dark:text-muted-400">
              Explore some of the best activities nearby in your region
            </span>
          </BaseParagraph>
        </div>
      </div>
      <div
        class="mt-4 flex items-center justify-center gap-2 md:mt-0 md:justify-start"
      >
        <BaseButton rounded="lg">
          <span>Explore</span>
        </BaseButton>
      </div>
    </div>
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Column -->
      <div class="col-span-12 lg:col-span-8">
        <div class="grid grid-cols-12 gap-4">
          <!-- Hobby Category Cards Widget -->
          <WidgetsHobbiesCategoryCards :categories="hobbeyCategories" />

          <!-- Trending Activities Widget -->
          <WidgetsHobbiesActivityList :data="trendingActivities" />

          <!-- Popular Activities Widget -->
          <WidgetsHobbiesActivityList :data="popularActivities" />
        </div>
      </div>
      <!-- Column -->
      <div class="col-span-12 lg:col-span-4">
        <div class="flex flex-col gap-4">
          <!-- Widget -->
          <BaseCard class="p-6" rounded="lg">
            <div class="flex w-full items-center justify-between">
              <SearchCompact rounded="lg" />
            </div>
          </BaseCard>
          <!-- Widget -->
          <BaseCard rounded="lg" class="p-4">
            <LazyAddonDatepicker v-model="date" locale="en" label="Start date" />
          </BaseCard>
          <!-- Locations Widget -->
          <WidgetsHobbiesLocationsList :data="hobbeyLocations" />
        </div>
      </div>
    </div>
  </div>
</template>
