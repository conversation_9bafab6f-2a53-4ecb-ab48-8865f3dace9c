<script setup lang="ts">
import type {
  HRNotificationsTableData,
  HRRookiesGridData,
  HRWelcomeHeaderData,
} from '~/types/widgets'

definePageMeta({
  title: 'Human Resources',
  preview: {
    title: 'HR dashboard',
    description: 'For HR management',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-human-resources.png',
    srcDark: '/img/screens/dashboards-human-resources-dark.png',
    order: 12,
  },
})

const activeSetting = ref('candidates')

const items = [
  {
    title: 'Messages',
    subtitle: 'Inbox messages',
    icon: 'ph:envelope-duotone',
    count: 5,
    status: 'new',
  },
  {
    title: 'Tasks',
    subtitle: 'Pending tasks',
    icon: 'ph:check-circle-duotone',
    count: 2,
    status: 'pending',
  },
]

const rookies = [
  {
    name: '<PERSON><PERSON><PERSON>',
    role: 'UI/UX designer',
    avatar: '/img/avatars/5.svg',
    stack: '/img/stacks/js.svg',
  },
  {
    name: '<PERSON>',
    role: 'Fullstack developer',
    avatar: '/img/avatars/16.svg',
    stack: '/img/stacks/vuejs.svg',
  },
  {
    name: 'Margot Reinier',
    role: 'Web developer',
    avatar: '/img/avatars/12.svg',
    stack: '/img/stacks/reactjs.svg',
  },
]

// HR widget data structures
const hrWelcomeHeaderData: HRWelcomeHeaderData = {
  avatar: '/img/avatars/10.svg',
  greeting: 'Welcome back, Kendra.',
  newRookies: {
    title: 'New Rookies',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praeclarae mortes.',
    avatars: ['/img/avatars/3.svg', '/img/avatars/9.svg', '/img/avatars/5.svg'],
    ctaText: 'Add Rookie',
  },
  jobFeed: {
    title: 'Job Feed',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praeclarae mortes.',
    ctaText: 'Manage Jobs',
  },
}

const hrNotificationsData: HRNotificationsTableData = {
  title: 'Notifications',
  description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Perge porro; Oratio me istius philosophi non offendit.',
  learnMoreLink: '#',
  items: items.map(item => ({
    title: item.title,
    subtitle: item.subtitle,
    icon: item.icon,
    count: item.count,
    status: item.status,
    statusVariant: item.status === 'new' ? 'primary' : 'muted',
  })),
}

const hrRookiesData: HRRookiesGridData = {
  title: 'New rookies',
  viewAllLink: '#',
  rookies,
  ctaText: 'View Profile',
}

// Datepicker
const date = ref(new Date())
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Column -->
      <div class="lg:landscape:col-span-8 col-span-12 2xl:landscape:col-span-9">
        <!-- Column -->
        <div class="flex flex-col gap-4">
          <!-- HR Welcome Header Widget -->
          <WidgetsHrWelcomeHeader :data="hrWelcomeHeaderData" />
          <!-- Feed settings -->
          <div
            class="flex flex-col items-center justify-between gap-6 sm:flex-row mb-6"
          >
            <div>
              <BaseHeading
                as="h3"
                size="lg"
                weight="medium"
                lead="tight"
                class="text-muted-900 dark:text-muted-100 mb-1"
              >
                <span>Feed settings</span>
              </BaseHeading>
            </div>
            <div class="flex gap-2 sm:justify-end">
              <BaseButton
                rounded="md"
                size="sm"
                :variant="activeSetting === 'all' ? 'primary' : 'default'"
                @click="activeSetting = 'all'"
              >
                All
              </BaseButton>
              <BaseButton
                rounded="md"
                size="sm"
                :variant="activeSetting === 'candidates' ? 'primary' : 'default'"
                @click="activeSetting = 'candidates'"
              >
                Candidates
              </BaseButton>
              <BaseButton
                rounded="md"
                size="sm"
                :variant="activeSetting === 'companies' ? 'primary' : 'default'"
                @click="activeSetting = 'companies'"
              >
                Companies
              </BaseButton>
            </div>
          </div>
          <!-- HR Notifications Widget -->
          <WidgetsHrNotificationsTable :data="hrNotificationsData" />

          <!-- HR Rookies Grid Widget -->
          <WidgetsHrRookiesGrid :data="hrRookiesData" />
        </div>
      </div>
      <!-- Column -->
      <div class="lg:landscape:col-span-4 col-span-12 2xl:landscape:col-span-3">
        <div class="flex flex-col gap-4">
          <!-- Widget -->
          <BaseCard rounded="lg" class="p-4 md:p-6">
            <div class="flex w-full items-center justify-between">
              <SearchCompact rounded="lg" />
            </div>
          </BaseCard>
          <!-- Widget -->
          <BaseCard rounded="lg" class="p-4 md:p-6">
            <DaysSquare rounded="lg" />
          </BaseCard>
          <!-- Widget -->
          <BaseCard rounded="lg" class="p-4">
            <LazyAddonDatepicker v-model="date" locale="en" label="Start date" />
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>
