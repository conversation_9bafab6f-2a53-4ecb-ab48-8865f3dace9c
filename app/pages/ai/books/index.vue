<script setup lang="ts">
import type { BookProject } from '~/server/utils/tools/book-subgraph'

// Reactive state
const bookProjects = ref<BookProject[]>([])
const isLoading = ref(true)
const isCreating = ref(false)
const showCreateDialog = ref(false)

// Form data for new project
const newProject = ref({
  title: '',
  genre: '',
  targetAudience: '',
  summary: '',
  estimatedLength: 200,
  tone: 'balanced',
  settings: {
    writingModel: 'gemini-2.5-flash',
    planningModel: 'gemini-2.5-flash',
    style: 'balanced',
    chapterLength: 2000,
  },
})

const keyThemesText = ref('')

// Options for form fields
const genreOptions = [
  { value: 'fiction', label: 'Fiction' },
  { value: 'non-fiction', label: 'Non-Fiction' },
  { value: 'technical', label: 'Technical' },
  { value: 'business', label: 'Business' },
  { value: 'self-help', label: 'Self-Help' },
  { value: 'biography', label: 'Biography' },
  { value: 'history', label: 'History' },
  { value: 'science', label: 'Science' },
]

const toneOptions = [
  { value: 'formal', label: 'Formal' },
  { value: 'casual', label: 'Casual' },
  { value: 'academic', label: 'Academic' },
  { value: 'conversational', label: 'Conversational' },
  { value: 'balanced', label: 'Balanced' },
  { value: 'creative', label: 'Creative' },
]

const styleOptions = [
  { value: 'concise', label: 'Concise' },
  { value: 'detailed', label: 'Detailed' },
  { value: 'balanced', label: 'Balanced' },
  { value: 'narrative', label: 'Narrative' },
]

const aiModelOptions = [
  { value: 'gemini-2.5-flash', label: 'Gemini 2.5 Flash (Fast)' },
  { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro (Quality)' },
  { value: 'gpt-4', label: 'GPT-4 (Premium)' },
]

// Computed properties
function progressPercentage(project: BookProject) {
  if (!project.outline)
    return 0
  if (project.progress.chaptersPlanned === 0)
    return 0
  return Math.round((project.progress.chaptersWritten / project.progress.chaptersPlanned) * 100)
}

// Utility functions
function getStatusColor(status: string) {
  switch (status) {
    case 'planning': return 'info'
    case 'outlining': return 'warning'
    case 'writing': return 'primary'
    case 'reviewing': return 'success'
    case 'complete': return 'success'
    default: return 'default'
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// API functions
async function loadProjects() {
  isLoading.value = true
  try {
    // For now, load from localStorage or make API call
    const savedProjects = localStorage.getItem('book-projects')
    if (savedProjects) {
      bookProjects.value = JSON.parse(savedProjects)
    }
  }
  catch (error) {
    console.error('Error loading book projects:', error)
  }
  finally {
    isLoading.value = false
  }
}

async function createProject() {
  isCreating.value = true
  try {
    // Parse key themes
    const keyThemes = keyThemesText.value
      .split('\n')
      .map(theme => theme.trim())
      .filter(theme => theme.length > 0)

    // Call the book creation tool
    const response = await $fetch('/api/tools/execute', {
      method: 'POST',
      body: {
        toolId: 'book_create_project',
        parameters: {
          ...newProject.value,
          keyThemes,
        },
      },
    })

    if (response.success) {
      // Add the new project to our list
      bookProjects.value.push(response.result)

      // Save to localStorage for persistence
      localStorage.setItem('book-projects', JSON.stringify(bookProjects.value))

      // Reset form and close dialog
      resetForm()
      showCreateDialog.value = false

      // Show success message
      $toast.success('Book project created successfully!')
    }
    else {
      throw new Error(response.error?.message || 'Failed to create book project')
    }
  }
  catch (error) {
    console.error('Error creating book project:', error)
    $toast.error(`Failed to create book project: ${error.message}`)
  }
  finally {
    isCreating.value = false
  }
}

async function openProject(project: BookProject) {
  // Navigate to the book editor page
  await navigateTo(`/ai/books/${project.id}`)
}

function editProject(project: BookProject) {
  // Open edit dialog
  console.log('Edit project:', project.id)
}

async function deleteProject(project: BookProject) {
  if (confirm(`Are you sure you want to delete "${project.brief.title}"?`)) {
    try {
      // Remove from list
      bookProjects.value = bookProjects.value.filter(p => p.id !== project.id)

      // Update localStorage
      localStorage.setItem('book-projects', JSON.stringify(bookProjects.value))

      $toast.success('Book project deleted')
    }
    catch (error) {
      console.error('Error deleting project:', error)
      $toast.error('Failed to delete project')
    }
  }
}

function resetForm() {
  newProject.value = {
    title: '',
    genre: '',
    targetAudience: '',
    summary: '',
    estimatedLength: 200,
    tone: 'balanced',
    settings: {
      writingModel: 'gemini-2.5-flash',
      planningModel: 'gemini-2.5-flash',
      style: 'balanced',
      chapterLength: 2000,
    },
  }
  keyThemesText.value = ''
}

// Lifecycle
onMounted(() => {
  loadProjects()
})

// SEO
useHead({
  title: 'Book Writing Studio',
  meta: [
    {
      name: 'description',
      content: 'Create and manage AI-assisted book writing projects with advanced tools and automation.',
    },
  ],
})
</script>

<template>
  <div class="book-writing-dashboard">
    <!-- Header Section -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Book Writing Studio
          </h1>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Create and manage your AI-assisted book projects
          </p>
        </div>
        <BaseButton
          color="primary"
          @click="showCreateDialog = true"
        >
          <Icon name="lucide:plus" class="h-4 w-4 mr-2" />
          New Book Project
        </BaseButton>
      </div>
    </div>

    <!-- Project Cards Grid -->
    <div v-if="bookProjects.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <BaseCard
        v-for="project in bookProjects"
        :key="project.id"
        class="cursor-pointer hover:shadow-lg transition-shadow"
        @click="openProject(project)"
      >
        <div class="p-6">
          <!-- Project Status Badge -->
          <div class="flex items-center justify-between mb-4">
            <BaseTag
              :color="getStatusColor(project.status)"
              variant="pastel"
              size="sm"
            >
              {{ project.status.charAt(0).toUpperCase() + project.status.slice(1) }}
            </BaseTag>
            <div class="flex space-x-1">
              <BaseButton size="sm" variant="ghost" @click.stop="editProject(project)">
                <Icon name="lucide:edit" class="h-4 w-4" />
              </BaseButton>
              <BaseButton
                size="sm"
                variant="ghost"
                color="danger"
                @click.stop="deleteProject(project)"
              >
                <Icon name="lucide:trash-2" class="h-4 w-4" />
              </BaseButton>
            </div>
          </div>

          <!-- Project Info -->
          <h3 class="font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100">
            {{ project.brief.title }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
            {{ project.brief.summary }}
          </p>

          <!-- Progress Bar -->
          <div class="mb-4">
            <div class="flex justify-between text-xs text-gray-500 mb-1">
              <span>Progress</span>
              <span>{{ progressPercentage(project) }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-primary-500 h-2 rounded-full transition-all"
                :style="`width: ${progressPercentage(project)}%`"
              />
            </div>
          </div>

          <!-- Project Stats -->
          <div class="grid grid-cols-3 gap-2 text-center">
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ project.progress.chaptersWritten }}
              </div>
              <div class="text-xs text-gray-500">
                Chapters
              </div>
            </div>
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ Math.round(project.progress.totalWordCount / 1000) }}k
              </div>
              <div class="text-xs text-gray-500">
                Words
              </div>
            </div>
            <div>
              <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ project.brief.estimatedLength }}
              </div>
              <div class="text-xs text-gray-500">
                Est. Pages
              </div>
            </div>
          </div>

          <!-- Last Updated -->
          <div class="mt-4 text-xs text-gray-400">
            Last updated {{ formatDate(project.updatedAt) }}
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-16">
      <Icon name="lucide:book-open" class="h-16 w-16 text-gray-300 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        No book projects yet
      </h3>
      <p class="text-gray-600 dark:text-gray-400 mb-6">
        Start your first AI-assisted book project to begin writing.
      </p>
      <BaseButton
        color="primary"
        @click="showCreateDialog = true"
      >
        Create Your First Book
      </BaseButton>
    </div>

    <!-- Create Book Dialog -->
    <BaseModal v-model="showCreateDialog" size="lg">
      <template #header>
        <h2 class="text-xl font-semibold">
          Create New Book Project
        </h2>
      </template>

      <form class="space-y-6" @submit.prevent="createProject">
        <!-- Title -->
        <BaseInput
          v-model="newProject.title"
          label="Book Title"
          placeholder="Enter your book title"
          required
        />

        <!-- Genre -->
        <BaseListbox
          v-model="newProject.genre"
          label="Genre"
          :items="genreOptions"
          placeholder="Select genre"
          required
        />

        <!-- Target Audience -->
        <BaseInput
          v-model="newProject.targetAudience"
          label="Target Audience"
          placeholder="e.g., Young adults, Business professionals"
        />

        <!-- Summary -->
        <BaseTextarea
          v-model="newProject.summary"
          label="Book Summary"
          placeholder="Provide a brief description of your book"
          rows="4"
          required
        />

        <!-- Key Themes -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Key Themes (one per line)
          </label>
          <BaseTextarea
            v-model="keyThemesText"
            placeholder="Enter themes, one per line"
            rows="3"
          />
        </div>

        <!-- Estimated Length -->
        <BaseInput
          v-model.number="newProject.estimatedLength"
          label="Estimated Length (pages)"
          type="number"
          min="50"
          max="1000"
          placeholder="200"
        />

        <!-- Tone -->
        <BaseListbox
          v-model="newProject.tone"
          label="Writing Tone"
          :items="toneOptions"
          placeholder="Select tone"
        />

        <!-- Advanced Settings -->
        <BaseAccordion>
          <BaseAccordionItem title="Advanced Settings">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <BaseListbox
                v-model="newProject.settings.writingModel"
                label="Writing AI Model"
                :items="aiModelOptions"
              />
              <BaseInput
                v-model.number="newProject.settings.chapterLength"
                label="Target Words per Chapter"
                type="number"
                min="500"
                max="5000"
              />
              <BaseListbox
                v-model="newProject.settings.style"
                label="Writing Style"
                :items="styleOptions"
              />
            </div>
          </BaseAccordionItem>
        </BaseAccordion>
      </form>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <BaseButton
            variant="outline"
            @click="showCreateDialog = false"
          >
            Cancel
          </BaseButton>
          <BaseButton
            color="primary"
            :loading="isCreating"
            @click="createProject"
          >
            Create Book Project
          </BaseButton>
        </div>
      </template>
    </BaseModal>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-16">
      <div class="text-center">
        <Icon name="lucide:loader-2" class="h-8 w-8 animate-spin text-primary-500 mx-auto mb-2" />
        <p class="text-gray-600 dark:text-gray-400">
          Loading book projects...
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.book-writing-dashboard {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
