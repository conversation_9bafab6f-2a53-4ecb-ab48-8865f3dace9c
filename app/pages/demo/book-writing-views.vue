<script setup lang="ts">
import type { ChatMessage } from '~/types/chat'
import ChatCanvas from '~~/components/Chat/canvas/ChatCanvas.vue'
import { useBookWritingViews } from '~/composables/useBookWritingViews'

// Page metadata
definePageMeta({
  title: 'Book Writing Views Demo',
  description: 'Interactive demo of book writing chat view components',
  layout: 'demo',
})

// Composables
const bookViews = useBookWritingViews()

// Reactive state
const messages = ref<ChatMessage[]>([])
const activeView = ref<string>('suggestion_bubble')
const demoMode = ref('interactive')

// Configuration
const availableViews = [
  { type: 'suggestion_bubble', label: 'Suggestions', icon: 'lucide:lightbulb' },
  { type: 'image_gallery', label: 'Images', icon: 'lucide:image' },
  { type: 'chapter_outline', label: 'Chapters', icon: 'lucide:book-open' },
  { type: 'writing_stats', label: 'Statistics', icon: 'lucide:trending-up' },
]

const demoModeOptions = [
  { value: 'interactive', label: 'Interactive' },
  { value: 'showcase', label: 'Showcase' },
  { value: 'testing', label: 'Testing' },
]

// Methods
function setActiveView(viewType: string) {
  activeView.value = viewType
}

function addRandomMessage() {
  const sampleData = bookViews.createSampleData()
  const viewTypes = availableViews.map(v => v.type)
  const randomType = viewTypes[Math.floor(Math.random() * viewTypes.length)]

  let message: ChatMessage

  switch (randomType) {
    case 'suggestion_bubble':
      message = bookViews.createSuggestionBubbleMessage({
        suggestions: sampleData.suggestions,
        title: 'AI Writing Suggestions',
        projectId: 'demo-project',
      })
      break
    case 'image_gallery':
      message = bookViews.createImageGalleryMessage({
        images: sampleData.images,
        title: 'Book Images',
        projectId: 'demo-project',
      })
      break
    case 'chapter_outline':
      message = bookViews.createChapterOutlineMessage({
        chapters: sampleData.chapters,
        title: 'Book Outline',
        projectId: 'demo-project',
      })
      break
    case 'writing_stats':
      message = bookViews.createWritingStatsMessage({
        stats: sampleData.stats,
        activities: sampleData.activities,
        title: 'Writing Progress',
        dailyGoal: 1000,
        weeklyGoal: 7000,
        monthlyGoal: 30000,
      })
      break
    default:
      return
  }

  messages.value.push(message)
}

function clearMessages() {
  messages.value = []
}

function loadSampleConversation() {
  const sampleData = bookViews.createSampleData()

  const sampleMessages: ChatMessage[] = [
    {
      id: '1',
      role: 'user',
      content: 'Show me my current book progress and any AI suggestions',
      timestamp: new Date(Date.now() - 10 * 60 * 1000),
    },
    bookViews.createWritingStatsMessage({
      stats: sampleData.stats,
      activities: sampleData.activities,
      title: 'Your Writing Progress',
      dailyGoal: 1000,
      weeklyGoal: 7000,
      monthlyGoal: 30000,
    }),
    bookViews.createSuggestionBubbleMessage({
      suggestions: sampleData.suggestions,
      title: 'AI Suggestions for Your Book',
      projectId: 'demo-project',
    }),
    {
      id: '4',
      role: 'user',
      content: 'Can you show me the chapter outline and any images I have?',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
    },
    bookViews.createChapterOutlineMessage({
      chapters: sampleData.chapters,
      title: 'Book Outline - "The Hero\'s Journey"',
      projectId: 'demo-project',
    }),
    bookViews.createImageGalleryMessage({
      images: sampleData.images,
      title: 'Book Images',
      projectId: 'demo-project',
    }),
  ]

  // Add timestamps with realistic intervals
  sampleMessages.forEach((message, index) => {
    if (!message.timestamp) {
      message.timestamp = new Date(Date.now() - (sampleMessages.length - index) * 2 * 60 * 1000)
    }
  })

  messages.value = sampleMessages
}

function duplicateMessage(message: ChatMessage) {
  const duplicated = {
    ...message,
    id: crypto.randomUUID(),
    timestamp: new Date(),
  }
  messages.value.push(duplicated)
}

function removeMessage(messageId: string) {
  const index = messages.value.findIndex(m => m.id === messageId)
  if (index !== -1) {
    messages.value.splice(index, 1)
  }
}

function getViewTypeFromMessage(message: ChatMessage): string | null {
  return bookViews.getViewType(message)
}

function formatTime(timestamp: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  }).format(new Date(timestamp))
}

// Event handlers for view interactions
function handleSuggestionApplied(suggestion: any) {
  console.log('Suggestion applied:', suggestion)
  // In a real app, you would integrate with your book editing system
}

function handleSuggestionDismissed(suggestion: any) {
  console.log('Suggestion dismissed:', suggestion)
}

function handleChapterAdded() {
  console.log('Chapter added')
  // Refresh chapter outline view
}

function handleChapterEdited(chapter: any) {
  console.log('Chapter edited:', chapter)
}

function handleImageGenerated(image: any) {
  console.log('Image generated:', image)
}

function handleStatsRefreshed() {
  console.log('Stats refreshed')
  // Reload writing statistics
}

// Initialize with sample conversation on mount
onMounted(() => {
  if (demoMode.value === 'showcase') {
    loadSampleConversation()
  }
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Book Writing Chat Views Demo
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
          Interactive demonstration of specialized chat views for book writing workflows
        </p>

        <!-- Quick Actions -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
          <BaseButton
            v-for="view in availableViews"
            :key="view.type"
            :color="activeView === view.type ? 'primary' : 'default'"
            :variant="activeView === view.type ? 'filled' : 'outline'"
            @click="setActiveView(view.type)"
          >
            <Icon :name="view.icon" class="h-4 w-4 mr-2" />
            {{ view.label }}
          </BaseButton>
        </div>
      </div>

      <!-- Demo Controls -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-4">
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center space-x-4">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Demo Mode:</label>
            <BaseListbox
              v-model="demoMode"
              :items="demoModeOptions"
              size="sm"
              class="w-40"
            />
          </div>

          <div class="flex items-center space-x-2">
            <BaseButton
              size="sm"
              variant="outline"
              @click="addRandomMessage"
            >
              <Icon name="lucide:plus" class="h-4 w-4 mr-1" />
              Add Random View
            </BaseButton>
            <BaseButton
              size="sm"
              variant="outline"
              color="danger"
              @click="clearMessages"
            >
              <Icon name="lucide:trash-2" class="h-4 w-4 mr-1" />
              Clear All
            </BaseButton>
            <BaseButton
              size="sm"
              @click="loadSampleConversation"
            >
              <Icon name="lucide:book-open" class="h-4 w-4 mr-1" />
              Load Sample
            </BaseButton>
          </div>
        </div>
      </div>

      <!-- Chat Messages -->
      <div class="space-y-6">
        <TransitionGroup name="chat-message" tag="div" class="space-y-6">
          <div
            v-for="message in messages"
            :key="message.id"
            class="chat-message-item"
          >
            <div class="flex items-start space-x-4">
              <!-- Avatar -->
              <div
                class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center"
                :class="message.role === 'assistant' ? 'bg-primary-100 dark:bg-primary-900' : 'bg-gray-100 dark:bg-gray-800'"
              >
                <Icon
                  :name="message.role === 'assistant' ? 'lucide:bot' : 'lucide:user'"
                  class="h-5 w-5"
                  :class="message.role === 'assistant' ? 'text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400'"
                />
              </div>

              <!-- Message Content -->
              <div class="flex-1 min-w-0">
                <!-- Message Header -->
                <div class="flex items-center space-x-2 mb-2">
                  <span
                    class="text-sm font-medium"
                    :class="message.role === 'assistant' ? 'text-primary-600 dark:text-primary-400' : 'text-gray-900 dark:text-white'"
                  >
                    {{ message.role === 'assistant' ? 'AI Assistant' : 'You' }}
                  </span>
                  <span class="text-xs text-gray-500">
                    {{ formatTime(message.timestamp) }}
                  </span>
                  <BaseTag
                    v-if="getViewTypeFromMessage(message)"
                    size="xs"
                    variant="pastel"
                    color="primary"
                  >
                    {{ getViewTypeFromMessage(message) }}
                  </BaseTag>
                </div>

                <!-- Chat Canvas -->
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                  <ChatCanvas
                    :message="message"
                    :show-fallback-as-plain-text="true"
                    @suggestion-applied="handleSuggestionApplied"
                    @suggestion-dismissed="handleSuggestionDismissed"
                    @chapter-added="handleChapterAdded"
                    @chapter-edited="handleChapterEdited"
                    @image-generated="handleImageGenerated"
                    @stats-refreshed="handleStatsRefreshed"
                  />
                </div>
              </div>

              <!-- Message Actions -->
              <div class="flex-shrink-0">
                <BaseDropdown>
                  <BaseButton size="xs" variant="ghost">
                    <Icon name="lucide:more-horizontal" class="h-3 w-3" />
                  </BaseButton>

                  <template #content>
                    <BaseDropdownItem @click="duplicateMessage(message)">
                      <Icon name="lucide:copy" class="h-4 w-4 mr-2" />
                      Duplicate
                    </BaseDropdownItem>
                    <BaseDropdownItem @click="removeMessage(message.id)">
                      <Icon name="lucide:trash-2" class="h-4 w-4 mr-2" />
                      Remove
                    </BaseDropdownItem>
                  </template>
                </BaseDropdown>
              </div>
            </div>
          </div>
        </TransitionGroup>

        <!-- Empty State -->
        <div v-if="messages.length === 0" class="text-center py-12">
          <Icon name="lucide:message-circle" class="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No messages yet
          </h3>
          <p class="text-gray-600 dark:text-gray-400 mb-4">
            Start by adding a sample conversation or individual view
          </p>
          <BaseButton @click="loadSampleConversation">
            <Icon name="lucide:play" class="h-4 w-4 mr-2" />
            Load Sample Conversation
          </BaseButton>
        </div>
      </div>

      <!-- Footer Info -->
      <div class="mt-12 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div class="flex items-start space-x-3">
          <Icon name="lucide:info" class="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div>
            <h4 class="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
              About This Demo
            </h4>
            <p class="text-sm text-blue-800 dark:text-blue-200 leading-relaxed">
              This demo showcases the integrated book writing chat views system. Each view type is automatically registered
              and can be rendered dynamically in chat messages. The system supports lazy loading, skeleton states, and
              comprehensive event handling for interactive book writing workflows.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-message-enter-active,
.chat-message-leave-active {
  transition: all 0.3s ease;
}

.chat-message-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.chat-message-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.chat-message-move {
  transition: transform 0.3s ease;
}
</style>
