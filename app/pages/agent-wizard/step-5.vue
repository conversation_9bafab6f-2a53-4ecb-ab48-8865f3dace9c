<script setup lang="ts">
import { PREVIEW_CARD_CLASSES } from '~/utils/wizardCardPattern'

const { data, errors } = useMultiStepForm()

// Communication styles
const workingStyles = [
  {
    value: 'collaborative',
    label: 'Collaborative',
    description: 'Works with users as a partner, asking for input and feedback',
    icon: 'lucide:users',
  },
  {
    value: 'directive',
    label: 'Directive',
    description: 'Takes charge and provides clear, actionable guidance',
    icon: 'lucide:compass',
  },
  {
    value: 'supportive',
    label: 'Supportive',
    description: 'Focuses on helping and encouraging the user',
    icon: 'lucide:heart-handshake',
  },
  {
    value: 'analytical',
    label: 'Analytical',
    description: 'Provides detailed analysis and data-driven recommendations',
    icon: 'lucide:brain',
  },
]

const communicationStyles = [
  {
    value: 'concise',
    label: 'Concise & Direct',
    description: 'Brief, to-the-point responses focused on key information',
  },
  {
    value: 'detailed',
    label: 'Detailed & Thorough',
    description: 'Comprehensive explanations with examples and context',
  },
  {
    value: 'conversational',
    label: 'Conversational & Friendly',
    description: 'Warm, engaging tone that feels like talking to a colleague',
  },
  {
    value: 'professional',
    label: 'Professional & Formal',
    description: 'Business-appropriate tone suitable for formal settings',
  },
]

// Tone preferences
const toneOptions = [
  { value: 'helpful', label: 'Helpful', emoji: '🤝' },
  { value: 'enthusiastic', label: 'Enthusiastic', emoji: '🎉' },
  { value: 'calm', label: 'Calm', emoji: '😌' },
  { value: 'confident', label: 'Confident', emoji: '💪' },
  { value: 'empathetic', label: 'Empathetic', emoji: '❤️' },
  { value: 'witty', label: 'Witty', emoji: '😄' },
  { value: 'patient', label: 'Patient', emoji: '🧘' },
  { value: 'inspiring', label: 'Inspiring', emoji: '✨' },
]

// Initialize default values if not set
if (!data.value.workingStyle) {
  data.value.workingStyle = 'collaborative'
}
if (!data.value.communicationStyle) {
  data.value.communicationStyle = 'conversational'
}

// Sample responses based on current settings
const sampleResponse = computed(() => {
  const style = data.value.communicationStyle
  const working = data.value.workingStyle
  const personality = data.value.personality?.toLowerCase() || 'professional'

  const responses = {
    concise: {
      collaborative: `Hi! I'm ${data.value.firstName || 'Alex'}. Let's work together on this. What specific aspect would you like to focus on first?`,
      directive: `I'm ${data.value.firstName || 'Alex'}. Here's what we need to do: [clear action steps]. Ready to get started?`,
      supportive: `Hello! I'm ${data.value.firstName || 'Alex'} and I'm here to help you succeed. What can I assist you with today?`,
      analytical: `I'm ${data.value.firstName || 'Alex'}. Based on your request, here are the key factors to consider: [data points]. Which would you like to explore first?`,
    },
    detailed: {
      collaborative: `Hello! I'm ${data.value.firstName || 'Alex'}, your ${personality} AI assistant. I'd love to work with you on this challenge. Let me share some initial thoughts and then get your perspective on how we should approach this together...`,
      directive: `I'm ${data.value.firstName || 'Alex'}, and I'm here to guide you through this process step by step. Based on my analysis, here's exactly what we need to accomplish, why it matters, and how we'll measure success...`,
      supportive: `Hi there! I'm ${data.value.firstName || 'Alex'}, and I want you to know that I'm completely committed to helping you achieve your goals. Every person's situation is unique, so let me understand your specific needs and concerns...`,
      analytical: `I'm ${data.value.firstName || 'Alex'}. Let me provide you with a comprehensive analysis of this situation. Here's what the data shows, the key variables at play, potential outcomes, and my recommendations based on current best practices...`,
    },
  }

  return responses[style as keyof typeof responses]?.[working as keyof typeof responses.concise] || responses.concise.collaborative
})
</script>

<template>
  <div>
    <WizardStepTitle />
    <div class="mx-auto w-full max-w-6xl px-4 text-center">
      <div class="w-full max-w-4xl mx-auto space-y-8">
        <!-- Working Style Selection -->
        <div class="space-y-6">
          <div class="space-y-1 text-center">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              Working Style
            </BaseHeading>
            <BaseText
              size="sm"
              class="text-muted-500"
            >
              How should your agent approach tasks and collaborate with users?
            </BaseText>
          </div>

          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <WizardSelectableCard
              v-for="style in workingStyles"
              :key="style.value"
              :selected="data.workingStyle === style.value"
              @select="data.workingStyle = style.value"
            >
              <template #icon>
                <Icon :name="style.icon" class="h-5 w-5" />
              </template>
              <template #title>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="mb-1"
                >
                  {{ style.label }}
                </BaseText>
              </template>
              <template #description>
                <BaseText
                  size="xs"
                  class="text-muted-500"
                >
                  {{ style.description }}
                </BaseText>
              </template>
            </WizardSelectableCard>
          </div>
        </div>

        <!-- Communication Style Selection -->
        <div class="space-y-6">
          <div class="space-y-1 text-center">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              Communication Style
            </BaseHeading>
            <BaseText
              size="sm"
              class="text-muted-500"
            >
              How should your agent communicate and present information?
            </BaseText>
          </div>

          <div class="space-y-3">
            <WizardSelectableCard
              v-for="style in communicationStyles"
              :key="style.value"
              :selected="data.communicationStyle === style.value"
              class="text-left"
              @select="data.communicationStyle = style.value"
            >
              <template #title>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="mb-1 text-left"
                >
                  {{ style.label }}
                </BaseText>
              </template>
              <template #description>
                <BaseText
                  size="xs"
                  class="text-muted-500 text-left"
                >
                  {{ style.description }}
                </BaseText>
              </template>
            </WizardSelectableCard>
          </div>
        </div>

        <!-- Sample Response Preview -->
        <BaseCard rounded="lg" :class="PREVIEW_CARD_CLASSES.container">
          <BaseText
            size="sm"
            weight="medium"
            class="mb-3 text-muted-600 dark:text-muted-400"
          >
            Sample Response Preview
          </BaseText>
          <div class="p-4 bg-white dark:bg-muted-800 rounded-lg border border-muted-200 dark:border-muted-700">
            <div class="flex items-start space-x-3">
              <BaseAvatar
                :src="data.avatarPreview"
                size="sm"
                :text="data.firstName && data.lastName ? `${data.firstName[0]}${data.lastName[0]}` : 'AI'"
              />
              <div class="flex-1 min-w-0">
                <BaseText
                  size="sm"
                  weight="medium"
                  class="mb-1"
                >
                  {{ data.firstName || 'Agent' }} {{ data.lastName || '' }}
                </BaseText>
                <BaseText
                  size="sm"
                  class="text-muted-700 dark:text-muted-300 leading-relaxed"
                >
                  {{ sampleResponse }}
                </BaseText>
              </div>
            </div>
          </div>
          <BaseText
            size="xs"
            class="text-muted-500 mt-2"
          >
            This is how your agent might introduce themselves and start a conversation
          </BaseText>
        </BaseCard>

        <!-- Configuration Summary -->
        <BaseCard rounded="lg" :class="PREVIEW_CARD_CLASSES.container">
          <BaseText
            size="sm"
            weight="medium"
            class="mb-3 text-muted-600 dark:text-muted-400"
          >
            Communication Profile
          </BaseText>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <BaseText size="xs" class="text-muted-500 mb-1">
                Working Style:
              </BaseText>
              <BaseText size="sm" weight="medium">
                {{ workingStyles.find(s => s.value === data.workingStyle)?.label }}
              </BaseText>
            </div>
            <div>
              <BaseText size="xs" class="text-muted-500 mb-1">
                Communication Style:
              </BaseText>
              <BaseText size="sm" weight="medium">
                {{ communicationStyles.find(s => s.value === data.communicationStyle)?.label }}
              </BaseText>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>
