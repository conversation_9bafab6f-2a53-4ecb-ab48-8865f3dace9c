<script setup lang="ts">
import type { ToolRegistryEntry } from '~/server/utils/tools/index'
import { GRID_LAYOUTS, PREVIEW_CARD_CLASSES } from '~/utils/wizardCardPattern'

const { data, errors } = useMultiStepForm()
const { tools, availableTools, unavailableTools, loading, isToolAvailable, getMissingIntegrationsForTool } = useTools()
const { connectIntegration } = useIntegrations()

// Initialize arrays if not present
if (!data.value.tools) {
  data.value.tools = []
}
if (!data.value.toolPermissions) {
  data.value.toolPermissions = {}
}
if (!data.value.capabilities) {
  data.value.capabilities = []
}

// Map tool categories for display
const categoryIcons: Record<string, string> = {
  search: 'lucide:search',
  computation: 'lucide:calculator',
  api: 'lucide:link',
  custom: 'lucide:puzzle',
  communication: 'lucide:mail',
  productivity: 'lucide:calendar',
  analysis: 'lucide:file-text',
  development: 'lucide:code',
  management: 'lucide:list-checks',
  finance: 'lucide:calculator',
}

const categoryNames: Record<string, string> = {
  search: 'Search & Research',
  computation: 'Calculation',
  api: 'External Services',
  custom: 'Custom Tools',
  communication: 'Communication',
  productivity: 'Productivity',
  analysis: 'Analysis',
  development: 'Development',
  management: 'Management',
  finance: 'Finance',
}

// Available capabilities
const availableCapabilities = [
  {
    id: 'multimodal_understanding',
    name: 'Image & Document Understanding',
    description: 'Analyze images, PDFs, and other visual content',
  },
  {
    id: 'real_time_data',
    name: 'Real-time Data Access',
    description: 'Access current information and live data feeds',
  },
  {
    id: 'long_term_memory',
    name: 'Long-term Memory',
    description: 'Remember conversation history and user preferences',
  },
  {
    id: 'multi_language',
    name: 'Multi-language Support',
    description: 'Communicate fluently in multiple languages',
  },
  {
    id: 'advanced_reasoning',
    name: 'Advanced Reasoning',
    description: 'Perform complex logical analysis and problem-solving',
  },
  {
    id: 'creative_generation',
    name: 'Creative Content Generation',
    description: 'Generate original creative content and ideas',
  },
]

// Group tools by category with availability info
const toolsByCategory = computed(() => {
  const groups: Record<string, ToolRegistryEntry[]> = {}

  // Group available tools
  availableTools.value.forEach((tool) => {
    const categoryName = categoryNames[tool.category] || tool.category
    if (!groups[categoryName]) {
      groups[categoryName] = []
    }
    groups[categoryName].push(tool)
  })

  // Also include unavailable tools (for Connect CTA functionality)
  unavailableTools.value.forEach((tool) => {
    const categoryName = categoryNames[tool.category] || tool.category
    if (!groups[categoryName]) {
      groups[categoryName] = []
    }
    groups[categoryName].push(tool)
  })

  return groups
})

// Get tool icon for display
function getToolIcon(tool: ToolRegistryEntry): string {
  return categoryIcons[tool.category] || 'lucide:settings'
}

// Toggle tool selection and set default permissions
function toggleTool(toolId: string) {
  if (!data.value.tools) {
    data.value.tools = []
  }
  if (!data.value.toolPermissions) {
    data.value.toolPermissions = {}
  }

  const index = data.value.tools.indexOf(toolId)
  if (index > -1) {
    // Remove tool
    data.value.tools.splice(index, 1)
    delete data.value.toolPermissions[toolId]
  }
  else {
    // Add tool with default permission
    const tool = tools.value.find(t => t.id === toolId)
    if (tool && tool.isAvailable) {
      data.value.tools.push(toolId)
      data.value.toolPermissions[toolId] = tool.defaultPermission
    }
  }
}

// Set tool permission
function setToolPermission(toolId: string, permission: 'read' | 'write') {
  if (!data.value.toolPermissions) {
    data.value.toolPermissions = {}
  }
  data.value.toolPermissions[toolId] = permission
}

// Navigate to integrations page to connect missing integrations
function navigateToIntegrations() {
  navigateTo('/preferences/integrations')
}

function toggleCapability(capabilityId: string) {
  if (!data.value.capabilities) {
    data.value.capabilities = []
  }

  const index = data.value.capabilities.indexOf(capabilityId)
  if (index > -1) {
    data.value.capabilities.splice(index, 1)
  }
  else {
    data.value.capabilities.push(capabilityId)
  }
}

function isToolSelected(toolId: string) {
  return data.value.tools?.includes(toolId) || false
}

function isCapabilitySelected(capabilityId: string) {
  return data.value.capabilities?.includes(capabilityId) || false
}
</script>

<template>
  <div>
    <WizardStepTitle />
    <div class="mx-auto w-full max-w-6xl px-4 text-center">
      <div class="w-full max-w-5xl mx-auto space-y-8">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600" />
        </div>

        <!-- Tools Selection -->
        <div v-else class="space-y-6">
          <div class="space-y-1 text-center">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              Agent Tools
            </BaseHeading>
            <BaseText
              size="sm"
              class="text-muted-500"
            >
              Select the tools your agent can use to help users accomplish tasks
            </BaseText>
          </div>

          <!-- Unavailable Tools Warning -->
          <div v-if="unavailableTools.length > 0" class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
            <div class="flex items-center space-x-2">
              <Icon name="lucide:alert-triangle" class="h-5 w-5 text-amber-500" />
              <BaseText size="sm" class="text-amber-800 dark:text-amber-200">
                Some tools require additional integrations to be connected.
              </BaseText>
              <BaseButton
                size="sm"
                variant="outline"
                @click="navigateToIntegrations"
              >
                Connect Integrations
              </BaseButton>
            </div>
          </div>

          <!-- Tools by Category -->
          <div v-for="(categoryTools, category) in toolsByCategory" :key="category" class="space-y-4">
            <BaseText
              size="sm"
              weight="medium"
              class="text-muted-600 dark:text-muted-400 uppercase tracking-wide text-left"
            >
              {{ category }}
            </BaseText>

            <div :class="GRID_LAYOUTS.selection">
              <div
                v-for="tool in categoryTools"
                :key="tool.id"
                class="relative"
              >
                <!-- Available Tool -->
                <WizardSelectableCard
                  v-if="tool.isAvailable"
                  :selected="isToolSelected(tool.id)"
                  class="cursor-pointer"
                  @select="toggleTool(tool.id)"
                >
                  <template #icon>
                    <Icon :name="getToolIcon(tool)" class="h-5 w-5" />
                  </template>
                  <template #title>
                    <div class="flex items-center justify-between mb-1">
                      <BaseText
                        size="sm"
                        weight="medium"
                        :class="[
                          isToolSelected(tool.id)
                            ? 'text-primary-900 dark:text-primary-100'
                            : 'text-muted-900 dark:text-muted-100',
                        ]"
                      >
                        {{ tool.name }}
                      </BaseText>
                      <div class="flex items-center space-x-1">
                        <span
                          class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium" :class="[
                            tool.sensitivityLevel === 'high'
                              ? 'bg-red-100 text-red-800 dark:bg-red-500/20 dark:text-red-300'
                              : tool.sensitivityLevel === 'medium'
                                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-500/20 dark:text-yellow-300'
                                : 'bg-green-100 text-green-800 dark:bg-green-500/20 dark:text-green-300',
                          ]"
                        >
                          {{ tool.sensitivityLevel }}
                        </span>
                      </div>
                    </div>
                  </template>
                  <template #description>
                    <BaseText
                      size="xs"
                      class="text-muted-500 mb-2"
                    >
                      {{ tool.description }}
                    </BaseText>

                    <!-- Permission Controls for Selected Tools -->
                    <div v-if="isToolSelected(tool.id)" class="mt-2 pt-2 border-t border-muted-200 dark:border-muted-700">
                      <BaseText size="xs" class="text-muted-600 dark:text-muted-400 mb-1">
                        Permission Level:
                      </BaseText>
                      <div class="flex space-x-2">
                        <button
                          type="button"
                          class="px-2 py-1 text-xs rounded transition-colors" :class="[
                            data.toolPermissions?.[tool.id] === 'read'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-500/20 dark:text-blue-300'
                              : 'bg-muted-100 text-muted-600 hover:bg-muted-200 dark:bg-muted-800 dark:text-muted-400',
                          ]"
                          @click.stop="setToolPermission(tool.id, 'read')"
                        >
                          Read
                        </button>
                        <button
                          type="button"
                          class="px-2 py-1 text-xs rounded transition-colors" :class="[
                            data.toolPermissions?.[tool.id] === 'write'
                              ? 'bg-orange-100 text-orange-800 dark:bg-orange-500/20 dark:text-orange-300'
                              : 'bg-muted-100 text-muted-600 hover:bg-muted-200 dark:bg-muted-800 dark:text-muted-400',
                          ]"
                          @click.stop="setToolPermission(tool.id, 'write')"
                        >
                          Write
                        </button>
                      </div>
                    </div>
                  </template>
                </WizardSelectableCard>

                <!-- Unavailable Tool -->
                <div
                  v-else
                  class="relative p-4 border-2 border-dashed border-muted-300 dark:border-muted-600 rounded-lg bg-muted-50 dark:bg-muted-800/50 opacity-60"
                >
                  <div class="flex items-start space-x-3">
                    <Icon :name="getToolIcon(tool)" class="h-5 w-5 text-muted-400" />
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-1">
                        <BaseText
                          size="sm"
                          weight="medium"
                          class="text-muted-600 dark:text-muted-400"
                        >
                          {{ tool.name }}
                        </BaseText>
                        <Icon name="lucide:lock" class="h-4 w-4 text-muted-400" />
                      </div>
                      <BaseText
                        size="xs"
                        class="text-muted-500 mb-2"
                      >
                        {{ tool.description }}
                      </BaseText>
                      <div class="flex items-center justify-between">
                        <BaseText size="xs" class="text-muted-500">
                          Requires: {{ getMissingIntegrationsForTool(tool.id).join(', ') }}
                        </BaseText>
                        <BaseButton
                          size="xs"
                          variant="outline"
                          @click="navigateToIntegrations"
                        >
                          Connect
                        </BaseButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Divider -->
        <div class="border-t border-muted-200 dark:border-muted-800" />

        <!-- Capabilities Selection -->
        <div class="space-y-6">
          <div class="space-y-1 text-center">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              Advanced Capabilities
            </BaseHeading>
            <BaseText
              size="sm"
              class="text-muted-500"
            >
              Enable advanced AI capabilities that enhance your agent's performance
            </BaseText>
          </div>

          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <WizardSelectableCard
              v-for="capability in availableCapabilities"
              :key="capability.id"
              :selected="isCapabilitySelected(capability.id)"
              @select="toggleCapability(capability.id)"
            >
              <template #title>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="mb-1"
                  :class="[
                    isCapabilitySelected(capability.id)
                      ? 'text-primary-900 dark:text-primary-100'
                      : 'text-muted-900 dark:text-muted-100',
                  ]"
                >
                  {{ capability.name }}
                </BaseText>
              </template>
              <template #description>
                <BaseText
                  size="xs"
                  class="text-muted-500"
                >
                  {{ capability.description }}
                </BaseText>
              </template>
            </WizardSelectableCard>
          </div>
        </div>

        <!-- Selection Summary -->
        <div v-if="(data.tools && data.tools.length > 0) || (data.capabilities && data.capabilities.length > 0)" :class="PREVIEW_CARD_CLASSES.container">
          <BaseText
            size="sm"
            weight="medium"
            class="mb-3 text-muted-600 dark:text-muted-400"
          >
            Selected Features
          </BaseText>

          <div class="space-y-3">
            <div v-if="data.tools && data.tools.length > 0">
              <BaseText size="xs" class="text-muted-500 mb-1">
                Tools ({{ data.tools.length }}):
              </BaseText>
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="toolId in data.tools"
                  :key="toolId"
                  class="inline-flex items-center px-2 py-1 rounded text-xs font-medium space-x-1" :class="[
                    data.toolPermissions?.[toolId] === 'write'
                      ? 'bg-orange-100 text-orange-800 dark:bg-orange-500/20 dark:text-orange-300'
                      : 'bg-blue-100 text-blue-800 dark:bg-blue-500/20 dark:text-blue-300',
                  ]"
                >
                  <span>{{ tools.find(t => t.id === toolId)?.name }}</span>
                  <span class="text-xs opacity-75">
                    ({{ data.toolPermissions?.[toolId] || 'read' }})
                  </span>
                </span>
              </div>
            </div>

            <div v-if="data.capabilities && data.capabilities.length > 0">
              <BaseText size="xs" class="text-muted-500 mb-1">
                Capabilities ({{ data.capabilities.length }}):
              </BaseText>
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="capabilityId in data.capabilities"
                  :key="capabilityId"
                  class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-500/20 dark:text-green-300"
                >
                  {{ availableCapabilities.find(c => c.id === capabilityId)?.name }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
