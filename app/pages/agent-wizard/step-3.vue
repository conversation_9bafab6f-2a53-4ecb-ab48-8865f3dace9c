<script setup lang="ts">
import { PREVIEW_CARD_CLASSES } from '~/utils/wizardCardPattern'

const { data, errors } = useMultiStepForm()
const { aiIntegrations, connectedIntegrations, loading } = useIntegrations()

// Available AI models for each provider
const modelOptions = computed(() => {
  switch (data.value.provider) {
    case 'openai':
      return [
        { value: 'gpt-4o', label: 'GPT-4o', description: '🔥 Latest multimodal model - text, images, audio • 128K context • Most capable' },
        { value: 'gpt-4o-mini', label: 'GPT-4o Mini', description: '⚡ Fast & efficient GPT-4 level model • 128K context • Cost-effective' },
        { value: 'gpt-4-turbo', label: 'GPT-4 Turbo', description: '📝 Text + images • 128K context • Vision capabilities' },
        { value: 'gpt-4', label: 'GPT-4', description: '🧠 Most reliable for complex reasoning • 8K context • Text only' },
        { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', description: '💨 Fast & affordable • 16K context • Text only' },
      ]
    case 'anthropic':
      return [
        { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet (Latest)', description: '🔥 Best overall model • Text + images • 200K context • Computer use' },
        { value: 'claude-3-5-haiku-20241022', label: 'Claude 3.5 Haiku (Latest)', description: '⚡ Fastest model • Text + images • 200K context • Speed optimized' },
        { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus', description: '🧠 Most intelligent • Text + images • 200K context • Complex tasks' },
        { value: 'claude-3-sonnet-20240229', label: 'Claude 3 Sonnet', description: '⚖️ Balanced performance • Text + images • 200K context' },
        { value: 'claude-3-haiku-20240307', label: 'Claude 3 Haiku', description: '💨 Fastest & most affordable • Text + images • 200K context' },
      ]
    case 'gemini':
      return [
        { value: 'gemini-2.5-pro-preview', label: 'Gemini 2.5 Pro Preview', description: '🔥 Most powerful thinking model • Text, images, audio, video • 1M context • Experimental' },
        { value: 'gemini-2.0-flash-exp', label: 'Gemini 2.0 Flash (Experimental)', description: '⚡ Latest multimodal model • Text, images, audio, video • 1M context • Live API' },
        { value: 'gemini-2.0-flash', label: 'Gemini 2.0 Flash', description: '🚀 Production ready • Text, images, audio, video • 1M context • Code execution' },
        { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro', description: '🧠 Advanced reasoning • Text, images, audio, video • 2M context • Function calling' },
        { value: 'gemini-1.5-flash', label: 'Gemini 1.5 Flash', description: '💨 Fast & efficient • Text, images, audio, video • 1M context • Cost-effective' },
        { value: 'gemini-1.5-flash-8b', label: 'Gemini 1.5 Flash-8B', description: '🔥 Smallest & fastest • Text, images, audio, video • 1M context • Ultra-affordable' },
      ]
    default:
      return []
  }
})

// Set default model when provider changes
watch(() => data.value.provider, (newProvider) => {
  const models = modelOptions.value
  if (models.length > 0 && !models.find(m => m.value === data.value.modelName)) {
    data.value.modelName = models[0].value
  }

  // Set integrationIds when provider changes
  const selectedProvider = providerOptions.value.find(p => p.value === newProvider)
  if (selectedProvider) {
    data.value.integrationIds = [selectedProvider.integrationId]
    console.log('Set integrationIds for provider', newProvider, 'to', data.value.integrationIds)
  }
})

// Available providers from connected AI integrations
const providerOptions = computed(() => {
  // Get AI integrations that are actually connected
  const connectedAIIntegrations = aiIntegrations.value.filter(integration =>
    integration.connected || integration.status === 'connected',
  )

  console.log('AI Integrations:', aiIntegrations.value)
  console.log('Connected AI Integrations:', connectedAIIntegrations)

  return connectedAIIntegrations.map(integration => ({
    value: integration.aiConfig?.provider?.toLowerCase() || integration.id,
    integrationId: integration.id,
    label: integration.name,
    icon: integration.icon,
    description: integration.description,
    connected: true,
  }))
})

// Temperature descriptions
const temperatureLabels = {
  0: 'Very focused',
  0.3: 'Focused',
  0.5: 'Balanced',
  0.7: 'Creative',
  1.0: 'Very creative',
}

function getTemperatureLabel(temp: number): string {
  const closest = Object.keys(temperatureLabels).map(Number).reduce((prev, curr) =>
    Math.abs(curr - temp) < Math.abs(prev - temp) ? curr : prev,
  )
  return temperatureLabels[closest as keyof typeof temperatureLabels]
}
</script>

<template>
  <div>
    <WizardStepTitle />
    <div class="mx-auto w-full max-w-6xl px-4 text-center">
      <div class="w-full max-w-4xl mx-auto space-y-8">
        <!-- AI Provider Selection -->
        <div class="space-y-4">
          <div class="space-y-1 text-center">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              AI Provider
            </BaseHeading>
            <BaseText
              size="sm"
              class="text-muted-500"
            >
              Choose which AI provider powers your agent
            </BaseText>
          </div>

          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <WizardSelectableCard
              v-for="provider in providerOptions"
              :key="provider.value"
              :selected="data.provider === provider.value"
              :disabled="!provider.connected"
              @select="data.provider = provider.value"
            >
              <template #image>
                <div class="flex items-center justify-center w-12 h-12 rounded-lg">
                  <Icon :name="provider.icon" class="h-8 w-8" />
                </div>
              </template>
              <template #title>
                <BaseText
                  size="sm"
                  weight="medium"
                  class="text-center"
                >
                  {{ provider.label }}
                </BaseText>
              </template>
              <template #description>
                <BaseText
                  size="xs"
                  class="text-muted-500 text-center"
                >
                  {{ provider.description }}
                </BaseText>
              </template>
              <template v-if="!provider.connected" #footer>
                <div class="mt-2 text-center">
                  <BaseText
                    size="xs"
                    class="text-warning-500"
                  >
                    Not connected
                  </BaseText>
                </div>
              </template>
            </WizardSelectableCard>
          </div>

          <BaseText
            v-if="errors.provider"
            size="sm"
            class="text-danger-500"
          >
            {{ errors.provider }}
          </BaseText>
        </div>

        <!-- Model Selection -->
        <BaseCard v-if="data.provider" rounded="lg" :class="PREVIEW_CARD_CLASSES.container" class="space-y-4">
          <div class="space-y-1 text-center">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              AI Model
            </BaseHeading>
            <BaseText
              size="sm"
              class="text-muted-500"
            >
              Select the specific model version for your agent
            </BaseText>
          </div>

          <TairoSelect
            v-model="data.modelName"
            :error="errors.modelName"
            placeholder="Select a model"
          >
            <BaseSelectItem
              v-for="model in modelOptions"
              :key="model.value"
              :value="model.value"
              :text="model.description"
            >
              {{ model.label }}
            </BaseSelectItem>
          </TairoSelect>
        </BaseCard>

        <!-- Advanced Configuration -->
        <BaseCard rounded="lg" :class="PREVIEW_CARD_CLASSES.container" class="space-y-6">
          <div class="text-center">
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-900 dark:text-white"
            >
              Response Configuration
            </BaseHeading>
          </div>

          <!-- Temperature Slider -->
          <div class="space-y-4">
            <div>
              <BaseText
                as="label"
                class="nui-label pb-1 text-[0.825rem]"
              >
                Creativity Level (Temperature): {{ data.temperature }}
              </BaseText>
              <BaseText
                size="sm"
                class="text-muted-500"
              >
                {{ getTemperatureLabel(data.temperature) }} - Higher values make responses more creative but less consistent
              </BaseText>
            </div>

            <div class="px-2">
              <input
                v-model.number="data.temperature"
                type="range"
                min="0"
                max="1"
                step="0.1"
                class="w-full h-3 bg-muted-200 rounded-lg appearance-none cursor-pointer dark:bg-muted-700 slider"
                :style="`--progress: ${data.temperature * 100}%`"
              >
              <div class="flex justify-between text-xs text-muted-500 mt-1">
                <span>Focused (0.0)</span>
                <span>Balanced (0.5)</span>
                <span>Creative (1.0)</span>
              </div>
            </div>
          </div>

          <!-- Max Tokens -->
          <BaseInput
            v-model.number="data.maxTokens"
            type="number"
            label="Max Response Length (tokens)"
            placeholder="2000"
            min="100"
            max="8000"
            help="Maximum number of tokens in agent responses (roughly 4 characters per token)"
            :classes="{
              input: 'h-12',
            }"
          />
        </BaseCard>

        <!-- Configuration Preview -->
        <BaseCard rounded="lg" :class="PREVIEW_CARD_CLASSES.container">
          <BaseText
            size="sm"
            weight="medium"
            class="mb-3 text-muted-600 dark:text-muted-400"
          >
            Configuration Summary
          </BaseText>
          <div class="space-y-2">
            <div class="flex justify-between">
              <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
                Provider:
              </BaseText>
              <BaseText size="sm" weight="medium">
                {{ providerOptions.find(p => p.value === data.provider)?.label || 'Not selected' }}
              </BaseText>
            </div>
            <div class="flex justify-between">
              <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
                Model:
              </BaseText>
              <BaseText size="sm" weight="medium">
                {{ modelOptions.find(m => m.value === data.modelName)?.label || 'Not selected' }}
              </BaseText>
            </div>
            <div class="flex justify-between">
              <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
                Creativity:
              </BaseText>
              <BaseText size="sm" weight="medium">
                {{ getTemperatureLabel(data.temperature) }} ({{ data.temperature }})
              </BaseText>
            </div>
            <div class="flex justify-between">
              <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
                Max tokens:
              </BaseText>
              <BaseText size="sm" weight="medium">
                {{ data.maxTokens }}
              </BaseText>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Base slider track styling */
.slider {
  background: rgb(var(--color-muted-200));
  outline: none;
}

.dark .slider {
  background: rgb(var(--color-muted-700));
}

/* Webkit slider track */
.slider::-webkit-slider-track {
  height: 12px;
  border-radius: 6px;
  background: rgb(var(--color-muted-200));
  border: none;
}

.dark .slider::-webkit-slider-track {
  background: rgb(var(--color-muted-700));
}

/* Webkit slider thumb */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: rgb(var(--color-primary-500));
  cursor: pointer;
  border: 3px solid white;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  margin-top: -6px; /* Center the thumb on the track */
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .slider::-webkit-slider-thumb {
  border-color: rgb(var(--color-muted-800));
}

/* Firefox slider thumb */
.slider::-moz-range-thumb {
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: rgb(var(--color-primary-500));
  cursor: pointer;
  border: 3px solid white;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .slider::-moz-range-thumb {
  border-color: rgb(var(--color-muted-800));
}

/* Firefox track styling */
.slider::-moz-range-track {
  height: 12px;
  border-radius: 6px;
  background: rgb(var(--color-muted-200));
  border: none;
}

.dark .slider::-moz-range-track {
  background: rgb(var(--color-muted-700));
}

/* Remove focus outline */
.slider:focus {
  outline: none;
}

/* Progress track effect */
.slider {
  background: linear-gradient(
    to right,
    rgb(var(--color-primary-500)) 0%,
    rgb(var(--color-primary-500)) var(--progress, 50%),
    rgb(var(--color-muted-200)) var(--progress, 50%),
    rgb(var(--color-muted-200)) 100%
  );
}

.dark .slider {
  background: linear-gradient(
    to right,
    rgb(var(--color-primary-500)) 0%,
    rgb(var(--color-primary-500)) var(--progress, 50%),
    rgb(var(--color-muted-700)) var(--progress, 50%),
    rgb(var(--color-muted-700)) 100%
  );
}
</style>
