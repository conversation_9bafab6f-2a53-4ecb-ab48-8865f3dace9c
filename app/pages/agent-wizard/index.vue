<script setup lang="ts">
import { GRID_LAYOUTS, PREVIEW_CARD_CLASSES } from '~/utils/wizardCardPattern'

const { data, errors, handleSubmit, getNextStep } = useMultiStepForm()

// Function to proceed to next step
function continueToNextStep() {
  handleSubmit()
}

// Available personality types that will influence the agent's behavior
const personalities = [
  { value: 'Professional', label: 'Professional', description: 'Formal, structured, business-focused' },
  { value: 'Friendly', label: 'Friendly', description: 'Warm, approachable, conversational' },
  { value: 'Creative', label: 'Creative', description: 'Imaginative, innovative, artistic' },
  { value: 'Analytical', label: 'Analytical', description: 'Data-driven, logical, detail-oriented' },
  { value: 'Supportive', label: 'Supportive', description: 'Empathetic, encouraging, helpful' },
  { value: 'Energetic', label: 'Energetic', description: 'Dynamic, enthusiastic, motivating' },
  { value: 'Wise', label: 'Wise', description: 'Thoughtful, experienced, philosophical' },
  { value: 'Innovative', label: 'Innovative', description: 'Forward-thinking, disruptive, visionary' },
]

// Handle avatar upload
function handleAvatarChange(fileList: FileList | File[] | Event | null) {
  // Handle different FileList formats
  let file: File | null = null

  if (fileList) {
    // Check if it's an event from file input
    if ((fileList as any).target?.files) {
      const inputFiles = (fileList as any).target.files as FileList
      file = inputFiles[0] || null
    }
    // If it's an array of files
    else if (Array.isArray(fileList)) {
      file = fileList[0] || null
    }
    // If it's a proper FileList object
    else if (fileList instanceof FileList) {
      file = fileList[0] || null
    }
    // If it's passed directly as a single file
    else if (fileList instanceof File) {
      file = fileList
    }
    // Fallback for other formats
    else {
      file = (fileList as any).item ? (fileList as any).item(0) : (fileList as File)
    }
  }

  // Validate that we have a proper File object
  if (file && !(file instanceof File) && !(file instanceof Blob)) {
    console.warn('Invalid file type received:', file)
    file = null
  }

  data.value.avatar = file

  if (file && (file instanceof File || file instanceof Blob)) {
    const reader = new FileReader()
    reader.onload = (e) => {
      data.value.avatarPreview = e.target?.result as string
    }
    reader.onerror = (e) => {
      console.error('FileReader error:', e)
      data.value.avatarPreview = ''
    }
    reader.readAsDataURL(file)
  }
  else {
    data.value.avatarPreview = ''
  }
}
</script>

<template>
  <div>
    <WizardStepTitle />
    <div class="mx-auto w-full max-w-6xl px-4 text-center">
      <!-- Avatar & Name Section -->
      <BaseCard rounded="lg" class="max-w-2xl mx-auto mb-8 p-6">
        <div class="flex flex-col items-center mb-6">
          <div class="relative">
            <BaseAvatar
              :src="data.avatarPreview"
              size="4xl"
              class="mb-4"
              :class="{
                'bg-muted-200 dark:bg-muted-700 text-muted-500': !data.avatarPreview && (!data.firstName || !data.lastName),
              }"
              :text="data.firstName && data.lastName ? `${data.firstName[0]}${data.lastName[0]}` : 'AI'"
            />
            <TairoInputFileHeadless
              v-slot="{ open, remove, files }"
              accept="image/*"
              @change="handleAvatarChange"
            >
              <div class="absolute -bottom-2 -right-2">
                <BaseButtonIcon
                  v-if="!data.avatarPreview"
                  size="sm"
                  rounded="full"
                  @click="open"
                >
                  <Icon name="lucide:camera" class="h-4 w-4" />
                </BaseButtonIcon>
                <BaseButtonIcon
                  v-else
                  size="sm"
                  rounded="full"
                  color="danger"
                  @click="() => { files?.[0] && remove(files[0]); handleAvatarChange(null) }"
                >
                  <Icon name="lucide:x" class="h-4 w-4" />
                </BaseButtonIcon>
              </div>
            </TairoInputFileHeadless>
          </div>
          <p class="text-muted-500 text-sm text-center">
            Upload an avatar to make your agent more personable
          </p>
        </div>

        <!-- Name Fields -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <BaseInput
            v-model="data.firstName"
            :error="errors.firstName"
            label="First Name"
            placeholder="e.g., Sarah"
            :classes="{
              input: 'h-12',
            }"
          />

          <BaseInput
            v-model="data.lastName"
            :error="errors.lastName"
            label="Last Name"
            placeholder="e.g., Johnson"
            :classes="{
              input: 'h-12',
            }"
          />
        </div>
      </BaseCard>

      <!-- Personality Selection -->
      <div class="space-y-6">
        <div class="space-y-1 text-center">
          <BaseHeading
            as="h3"
            size="lg"
            weight="medium"
            class="text-muted-900 dark:text-white"
          >
            Agent Personality
          </BaseHeading>
          <BaseText
            size="sm"
            class="text-muted-500"
          >
            Choose the personality type that best fits how you want this agent to interact
          </BaseText>
        </div>

        <div :class="GRID_LAYOUTS.selection">
          <WizardSelectableCard
            v-for="personality in personalities"
            :key="personality.value"
            :selected="data.personality === personality.value"
            @select="data.personality = personality.value"
          >
            <template #title>
              <BaseHeading
                tag="h3"
                weight="medium"
                size="xl"
                class="text-muted-900 dark:text-white mb-2"
              >
                {{ personality.label }}
              </BaseHeading>
            </template>
            <template #description>
              <BaseParagraph class="text-muted-600 dark:text-muted-400">
                {{ personality.description }}
              </BaseParagraph>
            </template>
            <template #footer>
              <div class="mb-5 flex flex-col items-center">
                <BaseButton
                  variant="primary"
                  rounded="lg"
                  class="w-36"
                  @click="data.personality = personality.value"
                >
                  {{ data.personality === personality.value ? 'Selected' : 'Continue' }}
                </BaseButton>
              </div>
            </template>
          </WizardSelectableCard>
        </div>

        <BaseText
          v-if="errors.personality"
          size="sm"
          class="text-danger-500 text-center"
        >
          {{ errors.personality }}
        </BaseText>
      </div>

      <!-- Preview Card -->
      <div v-if="data.firstName || data.lastName || data.personality" class="mt-8" :class="PREVIEW_CARD_CLASSES.container">
        <BaseText
          size="sm"
          weight="medium"
          class="mb-4 text-muted-600 dark:text-muted-400"
        >
          Agent Preview
        </BaseText>
        <div class="flex items-center space-x-4">
          <BaseAvatar
            :src="data.avatarPreview"
            size="lg"
            :class="{
              'bg-muted-200 dark:bg-muted-700 text-muted-500': !data.avatarPreview && (!data.firstName || !data.lastName),
            }"
            :text="data.firstName && data.lastName ? `${data.firstName[0]}${data.lastName[0]}` : 'AI'"
          />
          <div>
            <BaseText
              weight="medium"
              class="text-base sm:text-lg text-muted-900 dark:text-white mb-1"
            >
              {{ data.firstName || 'First' }} {{ data.lastName || 'Last' }}
            </BaseText>
            <BaseText
              size="sm"
              class="text-muted-500"
            >
              {{ data.personality ? `${data.personality} AI Assistant` : 'AI Assistant' }}
            </BaseText>
          </div>
        </div>
      </div>

      <!-- Continue Button -->
      <div class="mt-8 flex justify-center">
        <BaseButton
          variant="primary"
          rounded="lg"
          size="lg"
          class="px-8"
          :disabled="!data.firstName || !data.lastName || !data.personality"
          @click="continueToNextStep"
        >
          Continue
        </BaseButton>
      </div>
    </div>
  </div>
</template>
