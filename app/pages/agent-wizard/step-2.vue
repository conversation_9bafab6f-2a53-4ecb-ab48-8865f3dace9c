<script setup lang="ts">
import { GRID_LAYOUTS, PREVIEW_CARD_CLASSES } from '~/utils/wizardCardPattern'

const { data, errors } = useMultiStepForm()

// Ensure expertise array is initialized
if (!data.value.expertise) {
  data.value.expertise = []
}

const expertiseAreas = [
  { value: 'Business Analysis', label: 'Business Analysis', icon: 'lucide:briefcase', description: 'Market research, strategy, operations' },
  { value: 'Content Writing', label: 'Content Writing', icon: 'lucide:pen-tool', description: 'Copywriting, blogs, marketing content' },
  { value: 'Software Development', label: 'Software Development', icon: 'lucide:code', description: 'Programming, architecture, debugging' },
  { value: 'Data Analysis', label: 'Data Analysis', icon: 'lucide:bar-chart-3', description: 'Statistics, visualization, insights' },
  { value: 'Project Management', label: 'Project Management', icon: 'lucide:calendar', description: 'Planning, coordination, execution' },
  { value: 'Customer Support', label: 'Customer Support', icon: 'lucide:headphones', description: 'Help desk, troubleshooting, communication' },
  { value: 'Marketing', label: 'Marketing', icon: 'lucide:megaphone', description: 'Campaigns, social media, SEO' },
  { value: 'Design', label: 'Design', icon: 'lucide:palette', description: 'UI/UX, graphics, user experience' },
  { value: 'Sales', label: 'Sales', icon: 'lucide:trending-up', description: 'Lead generation, negotiation, CRM' },
  { value: 'Finance', label: 'Finance', icon: 'lucide:calculator', description: 'Accounting, budgets, financial analysis' },
  { value: 'HR & Recruitment', label: 'HR & Recruitment', icon: 'lucide:users', description: 'Talent acquisition, employee relations' },
  { value: 'Research', label: 'Research', icon: 'lucide:search', description: 'Information gathering, analysis, reports' },
]

function toggleExpertise(value: string) {
  if (!data.value.expertise) {
    data.value.expertise = []
  }

  const index = data.value.expertise.indexOf(value)
  if (index > -1) {
    data.value.expertise.splice(index, 1)
  }
  else {
    data.value.expertise.push(value)
  }
}

function isExpertiseSelected(value: string) {
  return data.value.expertise?.includes(value) || false
}
</script>

<template>
  <div>
    <WizardStepTitle />
    <div class="mx-auto w-full max-w-6xl px-4 text-center">
      <!-- Expertise Selection -->
      <div class="space-y-6 mb-8">
        <div class="space-y-1 text-center">
          <BaseHeading
            as="h3"
            size="lg"
            weight="medium"
            class="text-muted-900 dark:text-white"
          >
            Areas of Expertise
          </BaseHeading>
          <BaseText
            size="sm"
            class="text-muted-500"
          >
            Select the domains where this agent should excel (choose 1-4 areas for best performance)
          </BaseText>
        </div>

        <div :class="GRID_LAYOUTS.selection">
          <WizardSelectableCard
            v-for="expertise in expertiseAreas"
            :key="expertise.value"
            :selected="isExpertiseSelected(expertise.value)"
            @select="toggleExpertise(expertise.value)"
          >
            <template #icon>
              <Icon :name="expertise.icon" class="h-4 w-4" />
            </template>
            <template #title>
              <BaseText
                size="sm"
                weight="medium"
                :class="[
                  isExpertiseSelected(expertise.value)
                    ? 'text-primary-900 dark:text-primary-100'
                    : 'text-muted-900 dark:text-muted-100',
                ]"
              >
                {{ expertise.label }}
              </BaseText>
            </template>
            <template #description>
              <BaseText
                size="xs"
                class="text-muted-500 text-left"
              >
                {{ expertise.description }}
              </BaseText>
            </template>
          </WizardSelectableCard>
        </div>

        <BaseText
          v-if="errors.expertise"
          size="sm"
          class="text-danger-500"
        >
          {{ errors.expertise }}
        </BaseText>
      </div>

      <!-- Description Field -->
      <div class="space-y-4">
        <BaseTextarea
          v-model="data.description"
          :error="errors.description"
          label="Agent Description"
          placeholder="Describe what this agent specializes in and how they can help users..."
          rows="4"
          class="resize-none"
        />
        <BaseText
          size="xs"
          class="text-muted-500"
        >
          This description will help users understand what your agent can help with
        </BaseText>
      </div>

      <!-- Selected Expertise Preview -->
      <div v-if="data.expertise && data.expertise.length > 0" class="mt-8" :class="PREVIEW_CARD_CLASSES.container">
        <BaseText
          size="sm"
          weight="medium"
          class="mb-3 text-muted-600 dark:text-muted-400"
        >
          Selected Areas ({{ data.expertise.length }})
        </BaseText>
        <div class="flex flex-wrap gap-2">
          <span
            v-for="area in data.expertise"
            :key="area"
            class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-500/20 dark:text-primary-300"
          >
            {{ area }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
