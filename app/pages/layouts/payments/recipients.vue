<script setup lang="ts">
definePageMeta({
  title: 'Recipients',
  preview: {
    title: 'Recipients',
    description: 'For contacts management',
    categories: ['layouts', 'lists', 'payments'],
    src: '/img/screens/layouts-payments-recipients.png',
    srcDark: '/img/screens/layouts-payments-recipients-dark.png',
    order: 80,
    new: true,
  },
})

const route = useRoute()
const router = useRouter()
const page = computed(() => Number.parseInt((route.query.page as string) ?? '1', 10))

const filter = ref('')
const perPage = ref(100)

watch([filter, perPage], () => {
  router.push({
    query: {
      page: undefined,
    },
  })
})

const query = computed(() => {
  return {
    filter: filter.value,
    perPage: perPage.value,
    page: page.value,
  }
})

const { data, pending, error, refresh } = await useFetch(
  '/api/payments/recipients',
  {
    query,
  },
)

const people = computed(() => {
  return data?.value?.data.filter((item: any) => item.type === 'person')
})

const companies = computed(() => {
  return data?.value?.data.filter((item: any) => item.type === 'company')
})
</script>

<template>
  <div class="w-full dark:[--color-input-default-bg:var(--color-muted-950)]">
    <div class="space-y-10">
      <!-- Recipients -->
      <div class="w-full">
        <div class="mb-5 flex items-center gap-4">
          <BaseTooltip content="Add recipient">
            <BaseButton
              to="/layouts/contacts/create"
              rounded="full"
              size="icon-sm"
            >
              <Icon name="lucide:plus" class="size-4" />
            </BaseButton>
          </BaseTooltip>
          <h3 class="font-heading text-muted-900 dark:text-white text-xl">
            Contacts
          </h3>
          <div class="ms-auto">
            <TairoInput
              v-model="filter"
              icon="lucide:search"
              rounded="lg"
              placeholder="Search contacts..."
              class="ms-auto"
            />
          </div>
        </div>

        <div class="py-6">
          <BaseParagraph
            size="xs"
            weight="medium"
            class="text-muted-600 dark:text-muted-400 mb-4 uppercase"
          >
            People
          </BaseParagraph>

          <!-- Placeholder -->
          <PlaceholderMinimal
            v-if="people?.length === 0"
            title="No contacts found"
            description="We couldn'find any matching results. Try different search terms."
            transparent
          />

          <!-- People -->
          <div v-else class="grid gap-4 md:grid-cols-3 2xl:grid-cols-4">
            <!-- Grid item -->
            <NuxtLink
              v-for="item in people"
              :key="item.id"
              :to="`/layouts/recipient/${item.slug}`"
              class="group/contact"
            >
              <BaseCard
                rounded="md"
                shadow="hover"
                class="group-hover/contact:border-primary-500 p-4"
              >
                <div class="flex items-center gap-2">
                  <BaseAvatar
                    rounded="full"
                    size="sm"
                    :src="item.picture"
                    class="bg-muted-100 dark:bg-muted-700/60"
                  />
                  <div>
                    <h5
                      class="text-muted-500 dark:text-muted-300 font-sans text-sm font-medium leading-none"
                    >
                      {{ item.name }}
                    </h5>
                    <p class="text-muted-400 font-sans text-xs">
                      {{ item.category }}
                    </p>
                  </div>
                  <div class="ms-auto">
                    <div
                      class="bg-muted-100 dark:bg-muted-900 text-muted-300 dark:text-muted-200 flex size-8 items-center justify-center rounded-full opacity-0 transition-opacity duration-200 group-hover/contact:opacity-100"
                    >
                      <Icon name="lucide:more-horizontal" class="size-5" />
                    </div>
                  </div>
                </div>
              </BaseCard>
            </NuxtLink>
          </div>
        </div>

        <div class="py-6">
          <BaseParagraph
            size="xs"
            weight="medium"
            class="text-muted-600 dark:text-muted-400 mb-4 uppercase"
          >
            Companies
          </BaseParagraph>

          <!-- Placeholder -->
          <PlaceholderMinimal
            v-if="companies?.length === 0"
            title="No contacts to show"
            description="We couldn'find any matching results. Try different search terms."
            transparent
          />

          <!-- Companies -->
          <div v-else class="grid gap-4 md:grid-cols-3 2xl:grid-cols-4">
            <!-- Grid item -->
            <NuxtLink
              v-for="item in companies"
              :key="item.id"
              :to="`/layouts/recipient/${item.slug}`"
              class="group/contact"
            >
              <BaseCard
                rounded="md"
                shadow="hover"
                class="group-hover/contact:border-primary-500 p-4"
              >
                <div class="flex items-center gap-2">
                  <BaseAvatar
                    rounded="full"
                    size="sm"
                    :src="item.picture"
                    class="bg-muted-100 dark:bg-muted-700/60"
                  />
                  <div>
                    <h5
                      class="text-muted-500 dark:text-muted-300 font-sans text-sm font-medium leading-none"
                    >
                      {{ item.name }}
                    </h5>
                    <p class="text-muted-400 font-sans text-xs">
                      {{ item.category }}
                    </p>
                  </div>
                  <div class="ms-auto">
                    <div
                      class="bg-muted-100 dark:bg-muted-900 text-muted-300 dark:text-muted-200 flex size-8 items-center justify-center rounded-full opacity-0 transition-opacity duration-200 group-hover/contact:opacity-100"
                    >
                      <Icon name="lucide:more-horizontal" class="size-5" />
                    </div>
                  </div>
                </div>
              </BaseCard>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
