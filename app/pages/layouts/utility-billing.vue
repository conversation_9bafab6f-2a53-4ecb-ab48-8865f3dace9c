<script setup lang="ts">
import type { BillingInterval, BillingNotification, BillingPlan, CardInfo, SeatInfo } from '~/types/billing'

definePageMeta({
  title: 'SaaS Billing',
  preview: {
    title: 'SaaS billing',
    description: 'For saas billing plans',
    categories: ['layouts'],
    src: '/img/screens/layouts-subpages-billing.png',
    srcDark: '/img/screens/layouts-subpages-billing-dark.png',
    order: 84,
  },
})

const customRadio = ref('enterprise')

const plans: BillingPlan[] = [
  {
    name: 'starter',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Utilitatis causa amicitia est quaesita.',
    price: {
      monthly: 9,
      yearly: 99,
    },
    features: ['3 seats', '20 projects', '20GB storage'],
    benefits: [
      'Free 1 year support',
      'Free 1 year updates',
      'Free 1 year hosting',
    ],
  },
  {
    name: 'freelancer',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Utilitatis causa amicitia est quaesita.',
    price: {
      monthly: 19,
      yearly: 199,
    },
    features: ['8 seats', '150 projects', '100GB storage'],
    benefits: ['1 year support', '1 year updates', '1 year hosting'],
  },
  {
    name: 'business',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Utilitatis causa amicitia est quaesita.',
    price: {
      monthly: 29,
      yearly: 299,
    },
    features: ['20 seats', 'Unlimited projects', '500GB storage'],
    benefits: ['1 year support', '1 year updates', '1 year hosting'],
  },
  {
    name: 'enterprise',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Utilitatis causa amicitia est quaesita.',
    price: {
      monthly: 49,
      yearly: 399,
    },
    features: ['40 seats', 'Unlimited projects', 'Unlimited storage'],
    benefits: ['1 year support', '1 year updates', '1 year hosting'],
  },
]

const selectedPlan = computed(() => {
  return plans.find(plan => plan.name === customRadio.value)
})

const planColor = computed(() => {
  switch (customRadio.value) {
    case 'starter':
      return 'text-success-500'
    case 'freelancer':
      return 'text-yellow-400'
    case 'business':
      return 'text-indigo-500'
    case 'enterprise':
      return 'text-primary-500'
  }

  return ''
})

const cardInfo = ref<CardInfo>({
  name: undefined,
  number: undefined,
  expiryYear: undefined,
  expiryMonth: undefined,
  cvc: undefined,
})

const billingCycles = ref<BillingInterval>('monthly')

// Seat management data
const seats: SeatInfo[] = [
  {
    name: 'Maya R.',
    avatar: '/img/avatars/2.svg',
    tooltipContent: 'Maya R.',
  },
  {
    name: 'Kendra W.',
    avatar: '/img/avatars/10.svg',
    tooltipContent: 'Kendra W.',
  },
  {
    name: 'Oliver D.',
    text: 'OD',
    tooltipContent: 'Oliver D.',
  },
  {
    name: 'Hermann M.',
    avatar: '/img/avatars/16.svg',
    tooltipContent: 'Hermann M.',
  },
  {
    name: 'Matteus C.',
    text: 'MC',
    tooltipContent: 'Matteus C.',
  },
  {
    name: 'Gorav M.',
    text: 'GM',
    tooltipContent: 'Gorav M.',
  },
]

// Billing notifications data
const billingNotifications = ref<BillingNotification[]>([
  {
    id: '1',
    label: 'Invoicing',
    sublabel: 'Send new invoices to my inbox',
    enabled: false,
  },
  {
    id: '2',
    label: 'Warnings',
    sublabel: 'Warn me before the end of the billing period',
    enabled: true,
  },
  {
    id: '3',
    label: 'Reports',
    sublabel: 'Send monthly reports to my inbox',
    enabled: true,
  },
])

// Event handlers
function handleManageSeats() {
  // TODO: Implement seat management logic
}

function handleNotificationsUpdate(notifications: BillingNotification[]) {
  billingNotifications.value = notifications
}

function handleInvoicesClick() {
  // TODO: Navigate to invoices page
}

function handleCardInfoUpdate(newCardInfo: CardInfo) {
  cardInfo.value = newCardInfo
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <form
      action=""
      method="POST"
      class="w-full"
      @submit.prevent
    >
      <!-- Header -->
      <div class="mb-8 flex flex-col justify-between md:flex-row md:items-center">
        <div
          class="flex max-w-[425px] flex-col items-center gap-4 text-center md:flex-row md:text-start lg:max-w-full"
        >
          <BaseAvatar src="/img/avatars/10.svg" size="lg" />
          <div>
            <BaseHeading
              as="h2"
              size="xl"
              weight="medium"
              lead="tight"
              class="text-muted-900 dark:text-white"
            >
              <span>Manage plan</span>
            </BaseHeading>
            <BaseParagraph size="sm">
              <span class="text-muted-600 dark:text-muted-400">
                Manage your plan and billing information
              </span>
            </BaseParagraph>
          </div>
        </div>
        <div
          class="mt-4 flex items-center justify-center gap-2 md:mt-0 md:justify-start"
        >
          <BaseButton type="submit" variant="primary">
            <span>Save for</span>
            <span class="font-semibold">${{ selectedPlan?.price.monthly }}</span>
            <span>/month</span>
          </BaseButton>
        </div>
      </div>
      <!-- plans -->
      <div
        class="mb-4 grid gap-4 md:grid-cols-2"
      >
        <BaseRadioGroup v-model="customRadio" class="grid grid-cols-2 gap-4">
          <TairoRadioCard
            value="starter"
            class="data-[state=checked]:ring-success-500! data-[state=checked]:border-success-500!"
          >
            <template #indicator>
              <div class="flex size-7 items-center justify-center rounded-full text-success-500 group-data-[state=unchecked]:opacity-0">
                <Icon name="solar:check-circle-bold-duotone" class="size-6" />
              </div>
            </template>
            <TairoLogo class="mx-auto mb-2 size-9 group-data-[state=checked]:text-success-500" />
            <BaseHeading
              as="h4"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Starter
            </BaseHeading>
            <BaseText
              size="xs"
              lead="tight"
              class="text-muted-400"
            >
              A basic plan made for quickstarts
            </BaseText>
          </TairoRadioCard>
          <TairoRadioCard
            value="freelancer"
            class="data-[state=checked]:ring-yellow-400! data-[state=checked]:border-yellow-400!"
          >
            <template #indicator>
              <div class="flex size-7 items-center justify-center rounded-full text-yellow-500 group-data-[state=unchecked]:opacity-0">
                <Icon name="solar:check-circle-bold-duotone" class="size-6" />
              </div>
            </template>
            <TairoLogo class="mx-auto mb-2 size-9 group-data-[state=checked]:text-yellow-400" />
            <BaseHeading
              as="h4"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Freelancer
            </BaseHeading>
            <BaseText
              size="xs"
              lead="tight"
              class="text-muted-400"
            >
              A plan for heavy working freelancers
            </BaseText>
          </TairoRadioCard>
          <TairoRadioCard
            value="business"
            class="data-[state=checked]:ring-indigo-500! data-[state=checked]:border-indigo-500!"
          >
            <template #indicator>
              <div class="flex size-7 items-center justify-center rounded-full text-indigo-500 group-data-[state=unchecked]:opacity-0">
                <Icon name="solar:check-circle-bold-duotone" class="size-6" />
              </div>
            </template>
            <TairoLogo class="mx-auto mb-2 size-9 group-data-[state=checked]:text-indigo-500" />
            <BaseHeading
              as="h4"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Business
            </BaseHeading>
            <BaseText
              size="xs"
              lead="tight"
              class="text-muted-400"
            >
              An affordable medium business plan
            </BaseText>
          </TairoRadioCard>
          <TairoRadioCard
            value="enterprise"
            class="data-[state=checked]:ring-primary-500! data-[state=checked]:border-primary-500!"
          >
            <template #indicator>
              <div class="flex size-7 items-center justify-center rounded-full text-primary-500 group-data-[state=unchecked]:opacity-0">
                <Icon name="solar:check-circle-bold-duotone" class="size-6" />
              </div>
            </template>
            <TairoLogo class="mx-auto mb-2 size-9 group-data-[state=checked]:text-primary-500" />
            <BaseHeading
              as="h4"
              size="sm"
              weight="medium"
              class="text-muted-800 dark:text-white"
            >
              Enterprise
            </BaseHeading>
            <BaseText
              size="xs"
              lead="tight"
              class="text-muted-400"
            >
              A corporate and full fledged company plan
            </BaseText>
          </TairoRadioCard>
        </BaseRadioGroup>
        <div>
          <WidgetsBillingPlanSummary
            v-if="selectedPlan"
            :plan="selectedPlan"
            :selected-color="planColor"
          />
        </div>
      </div>
      <!-- Controls -->
      <div class="grid grid-cols-12 gap-4">
        <div class="col-span-12 sm:col-span-6 lg:col-span-7">
          <div class="flex flex-col gap-4">
            <WidgetsBillingUsedSeats
              :used-seats="6"
              :total-seats="8"
              :seats="seats"
              @manage-seats="handleManageSeats"
            />
            <WidgetsBillingBillingOptions
              :notifications="billingNotifications"
              @update:notifications="handleNotificationsUpdate"
            />
            <WidgetsBillingBillingCycle
              v-model="billingCycles"
              @invoices-click="handleInvoicesClick"
            />
          </div>
        </div>
        <div class="col-span-12 sm:col-span-6 lg:col-span-5">
          <WidgetsBillingPaymentInformation
            :card-info="cardInfo"
            @update:card-info="handleCardInfoUpdate"
          />
        </div>
      </div>
      <TairoFormSave>
        <BaseButton
          type="submit"
          variant="primary"
          class="w-full"
        >
          <span>Save for</span>
          <span class="font-semibold">${{ selectedPlan?.price.monthly }}</span>
          <span>/month</span>
        </BaseButton>
      </TairoFormSave>
    </form>
  </div>
</template>
