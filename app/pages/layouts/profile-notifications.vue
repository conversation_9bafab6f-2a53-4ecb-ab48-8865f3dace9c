<script setup lang="ts">
definePageMeta({
  title: 'Notifications',
  preview: {
    title: 'Notifications',
    description: 'For displaying account notifications',
    categories: ['layouts', 'profile'],
    src: '/img/screens/layouts-subpages-notifications.png',
    srcDark: '/img/screens/layouts-subpages-notifications-dark.png',
    order: 80,
  },
})

// Use the real-time notification system
const {
  formattedNotifications: notifications,
  unreadCount,
  isLoading: loading,
  isConnected,
  error,
  hasNewNotifications,
  markAsRead,
  markAllAsRead,
  archiveNotification,
  refresh,
  requestNotificationPermission,
  lastSyncTime,
} = useRealTimeNotifications()

const { isAuthenticated } = useAuth()

// Connection status for UI feedback
const connectionStatus = computed(() => {
  if (loading.value)
    return 'loading'
  if (error.value)
    return 'error'
  if (!isConnected.value)
    return 'disconnected'
  return 'connected'
})

// Handle notification click (mark as read if unread)
async function handleNotificationClick(notification: any) {
  if (notification.status === 0) { // Unread (using legacy format)
    await markAsRead(notification.id)
  }
}

// Handle archive notification
async function handleArchiveNotification(notification: any) {
  await archiveNotification(notification.id)
}

// Handle mark all as read
async function handleMarkAllAsRead() {
  if (unreadCount.value > 0) {
    await markAllAsRead()
  }
}

// Request notification permissions on mount
onMounted(async () => {
  if (!isAuthenticated.value) {
    await navigateTo('/auth/login')
    return
  }

  // Request browser notification permission for toast notifications
  await requestNotificationPermission()
})
</script>

<template>
  <div class="min-h-screen overflow-hidden px-4 md:px-6 lg:px-8 pb-20">
    <div class="w-full max-w-5xl pt-10">
      <!-- Header with actions -->
      <div class="flex items-center justify-between mb-8">
        <div>
          <BaseHeading size="2xl" class="mb-2">
            Notifications
            <span v-if="unreadCount > 0" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-medium leading-none text-white bg-primary-500 rounded-full">
              {{ unreadCount }}
            </span>
          </BaseHeading>

          <!-- Connection status -->
          <div class="flex items-center gap-2 text-sm">
            <div
              class="w-2 h-2 rounded-full"
              :class="{
                'bg-green-500': connectionStatus === 'connected',
                'bg-yellow-500': connectionStatus === 'loading',
                'bg-red-500': connectionStatus === 'error' || connectionStatus === 'disconnected',
              }"
            />
            <span class="text-muted-600 dark:text-muted-400">
              {{ connectionStatus === 'connected' ? 'Live updates enabled'
                : connectionStatus === 'loading' ? 'Connecting...'
                  : connectionStatus === 'error' ? 'Connection error'
                    : 'Offline' }}
            </span>
            <span v-if="lastSyncTime" class="text-xs text-muted-500">
              · Last updated {{ lastSyncTime.toLocaleTimeString() }}
            </span>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="flex items-center gap-2">
          <BaseButton
            v-if="unreadCount > 0"
            variant="ghost"
            size="sm"
            class="text-primary-500 hover:text-primary-600"
            @click="handleMarkAllAsRead"
          >
            Mark all read
          </BaseButton>

          <BaseButton
            variant="ghost"
            size="sm"
            :loading="loading"
            @click="refresh"
          >
            <Icon name="ph:arrows-clockwise" class="w-4 h-4" />
            Refresh
          </BaseButton>
        </div>
      </div>

      <!-- Error state -->
      <div v-if="error && !loading" class="mb-6">
        <BaseMessage type="error" class="mb-4">
          <div class="flex items-center justify-between">
            <span>Failed to load notifications: {{ error }}</span>
            <BaseButton size="sm" variant="ghost" @click="refresh">
              Retry
            </BaseButton>
          </div>
        </BaseMessage>
      </div>

      <!-- Loading skeleton -->
      <div v-if="loading && notifications.length === 0" class="space-y-4">
        <div v-for="n in 3" :key="n" class="flex items-center gap-4 p-4">
          <div class="w-8 h-8 bg-muted-200 dark:bg-muted-700 rounded-full animate-pulse" />
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-muted-200 dark:bg-muted-700 rounded animate-pulse" />
            <div class="h-3 bg-muted-200 dark:bg-muted-700 rounded w-3/4 animate-pulse" />
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div v-else-if="notifications.length === 0" class="text-center py-12">
        <Icon name="ph:bell-slash" class="w-16 h-16 mx-auto mb-4 text-muted-400" />
        <BaseHeading size="lg" class="mb-2 text-muted-800 dark:text-muted-200">
          No notifications yet
        </BaseHeading>
        <BaseText class="text-muted-600 dark:text-muted-400 mb-6">
          You'll see notifications here when there's activity in your workspace
        </BaseText>
        <BaseButton variant="outline" @click="refresh">
          Check for updates
        </BaseButton>
      </div>

      <!-- Notifications list -->
      <div v-else class="space-y-3">
        <div
          v-for="item in notifications"
          :key="item.id"
          class="group relative"
        >
          <!-- Timeline connector -->
          <div
            v-if="notifications.indexOf(item) < notifications.length - 1"
            class="absolute left-[31px] top-12 h-full w-px bg-muted-300 dark:bg-muted-700"
          />

          <!-- Notification card -->
          <div
            class="relative flex gap-4 p-4 rounded-lg border border-muted-200 dark:border-muted-700 bg-white dark:bg-muted-800 hover:bg-muted-50 dark:hover:bg-muted-750 transition-colors cursor-pointer"
            :class="{
              'ring-2 ring-primary-500/20 bg-primary-50 dark:bg-primary-900/10': item.status === 0,
            }"
            @click="handleNotificationClick(item)"
          >
            <!-- Status indicator -->
            <div class="flex-shrink-0 pt-1">
              <div
                class="w-3 h-3 rounded-full border-2"
                :class="{
                  'bg-primary-500 border-primary-500': item.status === 0,
                  'bg-muted-300 dark:bg-muted-600 border-muted-300 dark:border-muted-600': item.status === 1,
                }"
              />
            </div>

            <!-- Avatar -->
            <div class="flex-shrink-0">
              <BaseAvatar :src="item.user.src" size="sm" />
            </div>

            <!-- Content -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <BaseText size="sm" class="mb-1">
                    <span class="font-semibold text-muted-800 dark:text-muted-100">{{ item.user.name }}</span>
                    <span class="text-muted-600 dark:text-muted-400 mx-1">{{ item.target.text }}</span>
                    <NuxtLink
                      :to="item.target.url"
                      class="text-primary-600 hover:text-primary-700 font-medium underline-offset-4 hover:underline"
                      @click.stop
                    >
                      {{ item.target.name }}
                    </NuxtLink>
                  </BaseText>

                  <div class="flex items-center gap-3 mt-2">
                    <BaseText size="xs" class="text-muted-500">
                      {{ item.date }} · {{ item.time }}
                    </BaseText>

                    <!-- People involved -->
                    <div v-if="item.people && item.people.length > 0" class="flex -space-x-1">
                      <BaseAvatar
                        v-for="user in item.people.slice(0, 3)"
                        :key="user.name"
                        :src="user.src"
                        size="xs"
                        class="ring-2 ring-white dark:ring-muted-800"
                      />
                      <span v-if="item.people.length > 3" class="ml-2 text-xs text-muted-500">
                        +{{ item.people.length - 3 }} more
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Actions (show on hover) -->
                <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <BaseButton
                    v-if="item.status === 0"
                    variant="ghost"
                    size="sm"
                    class="text-xs"
                    @click.stop="handleNotificationClick(item)"
                  >
                    Mark read
                  </BaseButton>

                  <BaseButton
                    variant="ghost"
                    size="sm"
                    class="text-xs text-muted-500 hover:text-red-600"
                    @click.stop="handleArchiveNotification(item)"
                  >
                    <Icon name="ph:archive" class="w-3 h-3" />
                  </BaseButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
