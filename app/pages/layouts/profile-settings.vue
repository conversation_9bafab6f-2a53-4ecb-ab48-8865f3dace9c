<script setup lang="ts">
definePageMeta({
  title: 'Settings',
  preview: {
    title: 'Settings',
    description: 'For displaying account settings',
    categories: ['layouts', 'profile'],
    src: '/img/screens/layouts-subpages-settings.png',
    srcDark: '/img/screens/layouts-subpages-settings-dark.png',
    order: 81,
  },
})

// Auth and profile data
const { currentProfile, fetchCurrentProfile, isAuthenticated } = useAuth()
const loading = ref(!currentProfile.value)

// Authentication guard
if (!isAuthenticated.value) {
  await navigateTo('/auth/login')
}

// Fetch profile if not available
if (!currentProfile.value) {
  await fetchCurrentProfile().finally(() => {
    loading.value = false
  })
}
else {
  loading.value = false
}

// Computed properties for UI-friendly data structure
const fullName = computed(() => {
  if (!currentProfile.value)
    return ''
  const firstName = currentProfile.value.firstName || ''
  const lastName = currentProfile.value.lastName || ''
  return `${firstName} ${lastName}`.trim() || currentProfile.value.display_name || 'User'
})

const displayBio = computed(() => {
  return currentProfile.value?.bio || 'No bio available'
})

const avatar = computed(() => {
  return currentProfile.value?.avatar_url || null
})

// Convert social links object to array format expected by template
const socials = computed(() => {
  if (!currentProfile.value?.socialLinks)
    return []

  const socialMap = {
    facebook: { name: 'Facebook', icon: 'fa6-brands:facebook' },
    twitter: { name: 'Twitter', icon: 'fa6-brands:twitter' },
    dribbble: { name: 'Dribbble', icon: 'fa6-brands:dribbble' },
    instagram: { name: 'Instagram', icon: 'fa6-brands:instagram' },
    github: { name: 'GitHub', icon: 'fa6-brands:github' },
    gitlab: { name: 'GitLab', icon: 'fa6-brands:gitlab' },
    linkedin: { name: 'LinkedIn', icon: 'fa6-brands:linkedin' },
    website: { name: 'Website', icon: 'lucide:globe' },
  }

  return Object.entries(currentProfile.value.socialLinks)
    .filter(([key, url]) => url)
    .map(([key, url]) => ({
      name: socialMap[key]?.name || key,
      url: url as string,
      icon: socialMap[key]?.icon || 'lucide:link',
    }))
})

// Placeholder data for counts (you can replace with real data later)
const profileStats = computed(() => ({
  relations: (currentProfile.value?.experiences?.length || 0) + (currentProfile.value?.skills?.length || 0),
  projects: currentProfile.value?.experiences?.length || 1,
}))

// Settings grid data
const settingsGridData = computed(() => ({
  items: [
    {
      title: 'Company',
      description: 'Manage company',
      icon: 'solar:buildings-2-linear',
      link: '#',
    },
    {
      title: 'Team',
      description: 'Manage team',
      icon: 'solar:users-group-rounded-linear',
      link: '#',
    },
    {
      title: 'Projects',
      description: 'Project settings',
      icon: 'solar:suitcase-lines-linear',
      link: '#',
    },
    {
      title: 'Permissions',
      description: 'Manage permissions',
      icon: 'solar:lock-keyhole-linear',
      link: '#',
      titleSize: 'xs',
    },
    {
      title: 'Documents',
      description: 'Data privacy',
      icon: 'solar:file-linear',
      link: '#',
      titleSize: 'xs',
    },
    {
      title: 'Upload',
      description: 'Upload settings',
      icon: 'solar:upload-linear',
      link: '#',
      titleSize: 'xs',
    },
    {
      title: 'Billing',
      description: 'Billing and plans',
      icon: 'solar:card-linear',
      link: '#',
      titleSize: 'xs',
    },
    {
      title: 'Messaging',
      description: 'Chat settings',
      icon: 'solar:chat-dots-linear',
      link: '#',
      titleSize: 'xs',
    },
    {
      title: 'Security',
      description: 'Security settings',
      icon: 'solar:shield-check-linear',
      link: '#',
      titleSize: 'xs',
    },
    {
      title: 'Preferences',
      description: 'General settings',
      icon: 'solar:settings-linear',
      link: '#',
      titleSize: 'xs',
    },
  ],
}))
</script>

<template>
  <div class="mx-auto w-full max-w-5xl px-4 md:px-6 lg:px-8 pb-20">
    <div v-if="loading" class="text-center py-20">
      <Icon name="lucide:loader-2" class="size-8 animate-spin mx-auto text-primary-500" />
      <p class="mt-4 text-muted-400">
        Loading profile...
      </p>
    </div>
    <div v-else-if="!currentProfile" class="text-center py-20">
      <Icon name="lucide:user-x" class="size-8 mx-auto text-muted-400" />
      <p class="mt-4 text-muted-400">
        Profile not found
      </p>
    </div>
    <div v-else class="relative w-full">
      <div class="absolute end-0 top-2 z-20">
        <BaseDropdown
          placement="bottom-end"
          size="md"
          class="z-20"
          rounded="lg"
        >
          <template #button>
            <BaseButton
              size="icon-sm"
              rounded="full"
              class="bg-white text-muted-400 dark:bg-muted-800 dark:text-muted-400"
            >
              <Icon name="lucide:more-horizontal" class="size-4" />
            </BaseButton>
          </template>
          <BaseDropdownItem
            to="/layouts/profile-edit"
            title="Edit"
            text="Edit profile"
          >
            <template #start>
              <Icon name="solar:pen-2-linear" class="me-2 block size-5" />
            </template>
          </BaseDropdownItem>
          <BaseDropdownSeparator />
          <BaseDropdownItem
            to="#"
            title="Security"
            text="Security settings"
          >
            <template #start>
              <Icon name="solar:lock-keyhole-linear" class="me-2 block size-5" />
            </template>
          </BaseDropdownItem>
          <BaseDropdownItem
            to="#"
            title="Billing"
            text="Manage billing"
          >
            <template #start>
              <Icon name="solar:card-linear" class="me-2 block size-5" />
            </template>
          </BaseDropdownItem>
          <BaseDropdownSeparator />
          <BaseDropdownItem
            to="#"
            title="Share"
            text="Share profile"
          >
            <template #start>
              <Icon name="solar:link-circle-linear" class="me-2 block size-5" />
            </template>
          </BaseDropdownItem>
        </BaseDropdown>
      </div>
      <div class="flex w-full flex-col">
        <BaseAvatar
          :src="avatar"
          size="2xl"
          class="mx-auto"
        />
        <div class="mx-auto w-full max-w-md text-center">
          <BaseHeading
            tag="h2"
            size="xl"
            weight="medium"
            class="mt-4"
          >
            {{ fullName }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 mb-3 mt-1">
            {{ displayBio }}
          </BaseParagraph>
          <div
            class="divide-muted-200 dark:divide-muted-800 flex items-center justify-center divide-x"
          >
            <div class="text-muted-400 flex h-8 items-center gap-1 px-4">
              <Icon name="solar:widget-6-linear" class="size-5" />
              <BaseText size="sm">
                {{ profileStats.relations }}+ relations
              </BaseText>
            </div>
            <div
              class="text-muted-400 hidden h-8 items-center gap-1 px-4 sm:flex"
            >
              <Icon name="solar:suitcase-lines-linear" class="size-5" />
              <BaseText size="sm">
                {{ profileStats.projects }} projects
              </BaseText>
            </div>
            <div v-if="socials.length" class="flex h-8 items-center gap-2 px-4">
              <NuxtLink
                v-for="link in socials"
                :key="link.name"
                :to="link.url"
                target="_blank"
                rel="noopener noreferrer"
                class="border-muted-200 hover:border-primary-500 dark:border-muted-700 dark:hover:border-primary-500 dark:bg-muted-800 text-muted-400 hover:text-primary-500 flex size-8 items-center justify-center rounded-full border bg-white transition-colors duration-300"
              >
                <Icon :name="link.icon" class="size-3" />
                <span class="sr-only">{{ link.name }}</span>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>

      <WidgetsSettingsSettingsGrid :data="settingsGridData" />
    </div>
  </div>
</template>
