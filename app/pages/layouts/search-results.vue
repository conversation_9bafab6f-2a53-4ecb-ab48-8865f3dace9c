<script setup lang="ts">
import type {
  SearchResult,
  WidgetsSearchFilterGroupData,
  WidgetsSearchResultsData,
  WidgetsSearchTabsData,
} from '~/types/search'

definePageMeta({
  title: 'Search Results',
  preview: {
    title: 'Search results',
    description: 'For displaying search results',
    categories: ['layouts'],
    src: '/img/screens/layouts-subpages-search-results.png',
    srcDark: '/img/screens/layouts-subpages-search-results-dark.png',
    order: 82,
  },
})

const { data, pending, error, refresh } = await useFetch('/api/search')

const searchTerms = ref('internal projects')
const results = ref<SearchResult[]>([])
const activeFilter = ref('all')

// Initialize results from API data
if (data.value) {
  results.value = data.value as SearchResult[]
}

// Filter group data for widgets
const publicationFiltersData = ref<WidgetsSearchFilterGroupData>({
  title: 'Publication',
  filters: [
    { id: 'new', label: 'Newly added', value: false, count: 24 },
    { id: 'updated', label: 'Recently updated', value: false, count: 39 },
    { id: 'upvoted', label: 'Upvoted', value: false, count: 17 },
  ],
})

const topicsFiltersData = ref<WidgetsSearchFilterGroupData>({
  title: 'Topics',
  filters: [
    { id: 'engineering', label: 'Engineering', value: false, count: 62 },
    { id: 'software', label: 'Software', value: false, count: 148 },
    { id: 'business', label: 'Business', value: false, count: 23 },
    { id: 'management', label: 'Management', value: false, count: 8 },
    { id: 'hr', label: 'Human Resources', value: false, count: 43 },
  ],
})

const typeFiltersData = ref<WidgetsSearchFilterGroupData>({
  title: 'Result type',
  filters: [
    { id: 'members', label: 'Members', value: false, count: 12 },
    { id: 'projects', label: 'Projects', value: false, count: 54 },
    { id: 'files', label: 'Files', value: false, count: 31 },
  ],
})

// Tab data for search tabs
const tabsData = computed<WidgetsSearchTabsData>(() => ({
  activeFilter: activeFilter.value,
  onFilterChange: (filter: string) => {
    activeFilter.value = filter
  },
  tabs: [
    { id: 'all', label: 'All' },
    { id: 'people', label: 'People' },
    { id: 'project', label: 'Projects' },
    { id: 'file', label: 'Files' },
  ],
}))

// Filtered results based on active tab
const filteredResults = computed<SearchResult[]>(() => {
  if (!results.value || activeFilter.value === 'all') {
    return results.value || []
  }
  return results.value.filter(
    (result: SearchResult) => result.type === activeFilter.value,
  )
})

// Search results widget data
const searchResultsData = computed<WidgetsSearchResultsData>(() => ({
  results: filteredResults.value,
  totalCount: results.value?.length || 0,
  loading: pending.value,
  emptyStateConfig: {
    title: 'No matching results',
    subtitle: 'Looks like we couldn\'t find any matching results for your search terms. Try other search terms.',
    imageSrc: '/img/illustrations/placeholders/flat/placeholder-search-4.svg',
    imageSrcDark: '/img/illustrations/placeholders/flat/placeholder-search-4-dark.svg',
  },
}))

// Event handlers
function handleTabChange(tabId: string) {
  activeFilter.value = tabId
}

function handlePublicationFilterUpdate(filters: Array<{ id: string, value: boolean }>) {
  filters.forEach((filter) => {
    const existingFilter = publicationFiltersData.value.filters.find(f => f.id === filter.id)
    if (existingFilter) {
      existingFilter.value = filter.value
    }
  })
  // You could trigger a search refresh here based on active filters
}

function handleTopicsFilterUpdate(filters: Array<{ id: string, value: boolean }>) {
  filters.forEach((filter) => {
    const existingFilter = topicsFiltersData.value.filters.find(f => f.id === filter.id)
    if (existingFilter) {
      existingFilter.value = filter.value
    }
  })
  // You could trigger a search refresh here based on active filters
}

function handleTypeFilterUpdate(filters: Array<{ id: string, value: boolean }>) {
  filters.forEach((filter) => {
    const existingFilter = typeFiltersData.value.filters.find(f => f.id === filter.id)
    if (existingFilter) {
      existingFilter.value = filter.value
    }
  })
  // You could trigger a search refresh here based on active filters
}

function handleResultClick(result: SearchResult) {
  // Handle result click - navigate to result or show details
  navigateTo(result.url)
}

function handleResultAction(result: SearchResult, action: 'view' | 'download') {
  // Handle specific result actions
  if (action === 'download') {
    // Handle file download
    window.open(result.url, '_blank')
  }
  else {
    // Handle view action
    navigateTo(result.url)
  }
}
</script>

<template>
  <div class="w-full px-4 md:px-6 lg:px-8 pb-20 dark:[--color-input-default-bg:var(--color-muted-950)]">
    <!-- Search Header -->
    <div class="my-6 flex w-full items-center gap-3">
      <TairoInput
        v-model="searchTerms"
        icon="lucide:search"
        placeholder="Search..."
        rounded="full"
      />
      <div>
        <BaseText
          size="sm"
          class="text-muted-400"
        >
          {{ searchResultsData.totalCount }} results found
        </BaseText>
      </div>
    </div>

    <!-- Search Content -->
    <div v-if="results && results.length > 0">
      <!-- Search Tabs -->
      <WidgetsSearchTabs
        :data="tabsData"
        @tab-change="handleTabChange"
      />

      <!-- Search Layout -->
      <div class="grid grid-cols-12 gap-4">
        <!-- Filters Sidebar -->
        <div class="col-span-12 lg:col-span-4 xl:col-span-3">
          <div class="flex flex-col gap-4 mt-4">
            <!-- Publication Filters -->
            <WidgetsSearchFilterGroup
              :data="publicationFiltersData"
              @update:filters="handlePublicationFilterUpdate"
            />

            <!-- Topics Filters -->
            <WidgetsSearchFilterGroup
              :data="topicsFiltersData"
              @update:filters="handleTopicsFilterUpdate"
            />

            <!-- Type Filters -->
            <WidgetsSearchFilterGroup
              :data="typeFiltersData"
              @update:filters="handleTypeFilterUpdate"
            />
          </div>
        </div>

        <!-- Search Results -->
        <div class="col-span-12 lg:col-span-8 xl:col-span-9">
          <WidgetsSearchResults
            :data="searchResultsData"
            @result-click="handleResultClick"
            @result-action="handleResultAction"
          />
        </div>
      </div>
    </div>

    <!-- Empty state when no results from API -->
    <div v-else-if="results === null || (results && results.length === 0)">
      <BasePlaceholderPage
        title="No matching results"
        subtitle="Looks like we couldn't find any matching results for your search terms. Try other search terms."
      >
        <template #image>
          <img
            class="block dark:hidden"
            src="/img/illustrations/placeholders/flat/placeholder-search-4.svg"
            alt="Placeholder image"
          >
          <img
            class="hidden dark:block"
            src="/img/illustrations/placeholders/flat/placeholder-search-4-dark.svg"
            alt="Placeholder image"
          >
        </template>
      </BasePlaceholderPage>
    </div>
  </div>
</template>
