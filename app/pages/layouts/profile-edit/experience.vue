<script setup lang="ts">
import type { Experience } from '../../../types/auth'
import ExperienceFormModal from '../../../../components/ExperienceFormModal.vue'

definePageMeta({
  title: 'Experience',
  preview: {
    title: 'Edit profile 2',
    description: 'For editing a user profile',
    categories: ['layouts', 'profile', 'forms'],
    src: '/img/screens/layouts-subpages-profile-edit-2.png',
    srcDark: '/img/screens/layouts-subpages-profile-edit-2-dark.png',
    order: 77,
  },
  pageTransition: {
    enterActiveClass: 'transition-all duration-500 ease-out',
    enterFromClass: 'translate-y-20 opacity-0',
    enterToClass: 'translate-y-0 opacity-100',
    leaveActiveClass: 'transition-all duration-200 ease-in',
    leaveFromClass: 'translate-y-0 opacity-100',
    leaveToClass: 'translate-y-20 opacity-0',
  },
})

// Use auth for current profile state and profile data for operations
const { currentProfile, fetchCurrentProfile } = useAuth()
const { updateExperiences } = useProfileData()

// Fetch the latest profile data
if (process.client) {
  await fetchCurrentProfile()
}

// Reactive computed properties for experience data
const experiences = computed(() => currentProfile.value?.experiences || [])

// Modal states
const showExperienceModal = ref(false)
const editingExperience = ref<Experience | null>(null)

// Helper functions for modals
function openExperienceModal(experience?: Experience) {
  editingExperience.value = experience || null
  showExperienceModal.value = true
}

// Save function for modal
async function onSaveExperience(experience: Experience) {
  try {
    const newExperiences = [...experiences.value]

    // Check if editing existing experience (by id) or adding new
    const existingIndex = newExperiences.findIndex(e => e.id === experience.id)

    if (existingIndex >= 0) {
      // Update existing experience
      newExperiences[existingIndex] = experience
    }
    else {
      // Add new experience
      newExperiences.push(experience)
    }

    // Update the profile
    await updateExperiences(newExperiences)

    // Close modal
    showExperienceModal.value = false
    editingExperience.value = null
  }
  catch (error) {
    console.error('Failed to save experience:', error)
  }
}

// Delete function
async function deleteExperience(experience: Experience) {
  const newExperiences = experiences.value.filter(e => e.id !== experience.id)
  await updateExperiences(newExperiences)
}
</script>

<template>
  <form class="w-full pb-16 max-w-3xl dark:[--color-input-default-bg:var(--color-muted-950)]">
    <div class="flex items-center justify-end border-b border-muted-300 dark:border-muted-800 pb-4 mb-6">
      <div class="flex items-center gap-2">
        <BaseButton class="w-24" to="/layouts/profile">
          Cancel
        </BaseButton>
        <BaseButton variant="primary" class="w-24">
          Save
        </BaseButton>
      </div>
    </div>
    <div>
      <div v-if="experiences.length === 0">
        <BasePlaceholderPage
          title="No experiences"
          subtitle="Looks like you didn't add any experience yet. Share your experience to improve your profile."
          class="scale-90"
        >
          <template #image>
            <img
              class="block dark:hidden"
              src="/img/illustrations/placeholders/flat/placeholder-search-2.svg"
              alt="Placeholder image"
            >
            <img
              class="hidden dark:block"
              src="/img/illustrations/placeholders/flat/placeholder-search-2-dark.svg"
              alt="Placeholder image"
            >
          </template>
          <BaseButton class="mt-4 w-40" @click="openExperienceModal()">
            Add Experience
          </BaseButton>
        </BasePlaceholderPage>
      </div>
      <div v-else class="space-y-12">
        <TairoFormGroup
          label="Previous Experiences"
          sublabel="This will help others assess your experience"
        >
          <div class="space-y-8">
            <div
              v-for="item in experiences"
              :key="`${item.company}-${item.position}`"
              class="flex w-full gap-3"
            >
              <img
                :src="item.logo"
                :alt="item.company"
                class="border-muted-200 dark:border-muted-700 dark:bg-muted-800 size-10 rounded-full border bg-white"
              >
              <div>
                <BaseHeading
                  tag="h3"
                  size="sm"
                  weight="medium"
                  class="text-muted-900 dark:text-white"
                >
                  {{ item.company }}
                </BaseHeading>
                <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
                  <span>{{ item.period }}</span>
                </BaseParagraph>
                <BaseParagraph size="xs" class="text-primary-500">
                  <span>{{ item.position }}</span>
                </BaseParagraph>
              </div>
              <div class="ms-auto">
                <BaseDropdown
                  label="Dropdown"
                  placement="bottom-end"
                  size="md"
                  class="z-20"
                  rounded="lg"
                >
                  <template #button>
                    <BaseButton
                      size="icon-sm"
                      rounded="full"
                      class="bg-white text-muted-400 dark:bg-muted-800 dark:text-muted-400"
                    >
                      <Icon name="lucide:more-horizontal" class="size-4" />
                    </BaseButton>
                  </template>
                  <BaseDropdownItem
                    title="Edit"
                    text="Edit this experience"
                    @click.prevent="openExperienceModal(item)"
                  >
                    <template #start>
                      <Icon
                        name="solar:pen-2-linear"
                        class="me-2 block size-5"
                      />
                    </template>
                  </BaseDropdownItem>
                  <BaseDropdownItem
                    title="Delete"
                    text="Delete this experience"
                    @click.prevent="deleteExperience(item)"
                  >
                    <template #start>
                      <Icon
                        name="solar:trash-bin-minimalistic-linear"
                        class="me-2 block size-5"
                      />
                    </template>
                  </BaseDropdownItem>
                </BaseDropdown>
              </div>
            </div>
          </div>
          <div
            class="border-muted-200 dark:border-muted-800 mt-8 flex w-full items-center gap-2 border-t pt-8"
          >
            <div
              class="bg-muted-100 dark:bg-muted-800/60 text-muted-400 flex size-[50px] items-center justify-center rounded-full"
            >
              <Icon name="solar:suitcase-lines-linear" class="size-5" />
            </div>
            <div>
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
              >
                New Experience
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-400">
                <span>Add a new work experience item</span>
              </BaseParagraph>
            </div>
            <div class="ms-auto">
              <BaseButton rounded="full" size="icon-sm" @click="openExperienceModal()">
                <Icon name="lucide:plus" class="size-4" />
              </BaseButton>
            </div>
          </div>
        </TairoFormGroup>
      </div>
    </div>
    <TairoFormSave rounded="md" />

    <!-- Experience Form Modal -->
    <ExperienceFormModal
      :open="showExperienceModal"
      :experience="editingExperience"
      @close="showExperienceModal = false; editingExperience = null"
      @submit="onSaveExperience"
    />
  </form>
</template>
