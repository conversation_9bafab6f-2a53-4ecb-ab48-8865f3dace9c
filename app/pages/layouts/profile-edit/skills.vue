<script setup lang="ts">
import type { Language, Skill, Tool } from '../../../types/auth'
import LanguageFormModal from '../../../../components/LanguageFormModal.vue'
import SkillFormModal from '../../../../components/SkillFormModal.vue'
import ToolFormModal from '../../../../components/ToolFormModal.vue'

definePageMeta({
  title: 'Skills',
  preview: {
    title: 'Edit profile 3',
    description: 'For editing a user profile',
    categories: ['layouts', 'profile', 'forms'],
    src: '/img/screens/layouts-subpages-profile-edit-3.png',
    srcDark: '/img/screens/layouts-subpages-profile-edit-3-dark.png',
    order: 78,
  },
  pageTransition: {
    enterActiveClass: 'transition-all duration-500 ease-out',
    enterFromClass: 'translate-y-20 opacity-0',
    enterToClass: 'translate-y-0 opacity-100',
    leaveActiveClass: 'transition-all duration-200 ease-in',
    leaveFromClass: 'translate-y-0 opacity-100',
    leaveToClass: 'translate-y-20 opacity-0',
  },
})

// Use auth for current profile state and profile data for operations
const { currentProfile, fetchCurrentProfile } = useAuth()
const { updateLanguages, updateSkills, updateTools } = useProfileData()

// Fetch the latest profile data
if (process.client) {
  await fetchCurrentProfile()
}

// Reactive computed properties for skills data
const languages = computed(() => currentProfile.value?.languages || [])
const skills = computed(() => currentProfile.value?.skills || [])
const tools = computed(() => currentProfile.value?.tools || [])

// Modal states
const showLanguageModal = ref(false)
const showSkillModal = ref(false)
const showToolModal = ref(false)
const editingLanguage = ref<Language | null>(null)
const editingSkill = ref<Skill | null>(null)
const editingTool = ref<Tool | null>(null)

// Helper functions for modals
function openLanguageModal(language?: Language) {
  editingLanguage.value = language || null
  showLanguageModal.value = true
}

function openSkillModal(skill?: Skill) {
  editingSkill.value = skill || null
  showSkillModal.value = true
}

function openToolModal(tool?: Tool) {
  editingTool.value = tool || null
  showToolModal.value = true
}

// Save functions for modals
async function onSaveLanguage(language: Language) {
  try {
    const newLanguages = [...languages.value]

    // Check if editing existing language (by id) or adding new
    const existingIndex = newLanguages.findIndex(l => l.id === language.id)

    if (existingIndex >= 0) {
      // Update existing language
      newLanguages[existingIndex] = language
    }
    else {
      // Add new language
      newLanguages.push(language)
    }

    // Update the profile
    await updateLanguages(newLanguages)

    // Close modal
    showLanguageModal.value = false
    editingLanguage.value = null
  }
  catch (error) {
    console.error('Failed to save language:', error)
  }
}

async function onSaveSkill(skill: Skill) {
  try {
    const newSkills = [...skills.value]

    // Check if editing existing skill (by id) or adding new
    const existingIndex = newSkills.findIndex(s => s.id === skill.id)

    if (existingIndex >= 0) {
      // Update existing skill
      newSkills[existingIndex] = skill
    }
    else {
      // Add new skill
      newSkills.push(skill)
    }

    // Update the profile
    await updateSkills(newSkills)

    // Close modal
    showSkillModal.value = false
    editingSkill.value = null
  }
  catch (error) {
    console.error('Failed to save skill:', error)
  }
}

async function onSaveTool(tool: Tool) {
  try {
    const newTools = [...tools.value]

    // Check if editing existing tool (by id) or adding new
    const existingIndex = newTools.findIndex(t => t.id === tool.id)

    if (existingIndex >= 0) {
      // Update existing tool
      newTools[existingIndex] = tool
    }
    else {
      // Add new tool
      newTools.push(tool)
    }

    // Update the profile
    await updateTools(newTools)

    // Close modal
    showToolModal.value = false
    editingTool.value = null
  }
  catch (error) {
    console.error('Failed to save tool:', error)
  }
}

// Delete functions
async function deleteLanguage(language: Language) {
  const newLanguages = languages.value.filter(l => l.id !== language.id)
  await updateLanguages(newLanguages)
}

async function deleteSkill(skill: Skill) {
  const newSkills = skills.value.filter(s => s.id !== skill.id)
  await updateSkills(newSkills)
}

async function deleteTool(tool: Tool) {
  const newTools = tools.value.filter(t => t.id !== tool.id)
  await updateTools(newTools)
}
</script>

<template>
  <form class="w-full pb-16 max-w-3xl dark:[--color-input-default-bg:var(--color-muted-950)]">
    <div class="flex items-center justify-end border-b border-muted-300 dark:border-muted-800 pb-4 mb-6">
      <div class="flex items-center gap-2">
        <BaseButton class="w-24" to="/layouts/profile">
          Cancel
        </BaseButton>
        <BaseButton variant="primary" class="w-24">
          Save
        </BaseButton>
      </div>
    </div>
    <div class="space-y-8">
      <div v-if="!currentProfile">
        <BasePlaceholderPage
          title="No data to show"
          subtitle="There is currently no data to show. Take the time to go through your profile to fill required information."
          class="scale-90"
        >
          <template #image>
            <img
              class="block dark:hidden"
              src="/img/illustrations/placeholders/flat/placeholder-search-6.svg"
              alt="Placeholder image"
            >
            <img
              class="hidden dark:block"
              src="/img/illustrations/placeholders/flat/placeholder-search-6-dark.svg"
              alt="Placeholder image"
            >
          </template>
        </BasePlaceholderPage>
      </div>
      <div v-else class="space-y-20">
        <TairoFormGroup
          label="Languages"
          sublabel="How many languages do you speak?"
        >
          <div v-if="languages.length === 0">
            <BasePlaceholderPage
              title="No languages"
              subtitle="Looks like you didn't add any language yet. Share your skills to improve your profile."
              class="scale-90"
            >
              <template #image>
                <img
                  class="block dark:hidden"
                  src="/img/illustrations/placeholders/flat/placeholder-search-3.svg"
                  alt="Placeholder image"
                >
                <img
                  class="hidden dark:block"
                  src="/img/illustrations/placeholders/flat/placeholder-search-3-dark.svg"
                  alt="Placeholder image"
                >
              </template>
              <BaseButton class="mt-4 w-40" @click="openLanguageModal()">
                Add Language
              </BaseButton>
            </BasePlaceholderPage>
          </div>
          <div v-else class="space-y-8">
            <div
              v-for="item in languages"
              :key="item.name"
              class="flex w-full items-center gap-2"
            >
              <div
                class="border-muted-200 dark:border-muted-700 dark:bg-muted-800 relative flex size-[50px] shrink-0 items-center justify-center rounded-full border bg-white"
              >
                <img
                  :src="item.icon"
                  :alt="item.name"
                  class="size-8 rounded-full"
                >
                <BaseProgressCircle
                  :size="68"
                  :thickness="1.5"
                  :model-value="item.level"
                  variant="primary"
                  class="absolute -start-2.5 -top-2.5"
                />
              </div>
              <div>
                <BaseHeading
                  tag="h3"
                  size="sm"
                  weight="medium"
                >
                  {{ item.name }}
                </BaseHeading>
                <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
                  <span>{{ item.mastery }}</span>
                </BaseParagraph>
              </div>
              <div class="ms-auto">
                <BaseDropdown
                  label="Dropdown"
                  placement="bottom-end"
                  size="md"
                  class="z-20"
                  rounded="lg"
                >
                  <template #button>
                    <BaseButton
                      size="icon-sm"
                      rounded="full"
                      class="bg-white text-muted-400 dark:bg-muted-800 dark:text-muted-400"
                    >
                      <Icon name="lucide:more-horizontal" class="size-4" />
                    </BaseButton>
                  </template>
                  <BaseDropdownItem
                    title="Edit"
                    text="Edit this language"
                    @click.prevent="openLanguageModal(item)"
                  >
                    <template #start>
                      <Icon
                        name="solar:pen-2-linear"
                        class="me-2 block size-5"
                      />
                    </template>
                  </BaseDropdownItem>
                  <BaseDropdownItem
                    title="Delete"
                    text="Delete this language"
                    @click.prevent="deleteLanguage(item)"
                  >
                    <template #start>
                      <Icon
                        name="solar:trash-bin-minimalistic-linear"
                        class="me-2 block size-5"
                      />
                    </template>
                  </BaseDropdownItem>
                </BaseDropdown>
              </div>
            </div>
          </div>
          <div
            class="border-muted-200 dark:border-muted-800 mt-8 flex w-full items-center gap-2 border-t pt-8"
          >
            <div
              class="bg-muted-100 dark:bg-muted-800/60 text-muted-400 flex size-[50px] items-center justify-center rounded-full"
            >
              <Icon name="solar:book-2-linear" class="size-6" />
            </div>
            <div>
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
              >
                New Language
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-400">
                <span>Add a new language you speak</span>
              </BaseParagraph>
            </div>
            <div class="ms-auto">
              <BaseButton rounded="full" size="icon-sm" @click="openLanguageModal()">
                <Icon name="lucide:plus" class="size-4" />
              </BaseButton>
            </div>
          </div>
        </TairoFormGroup>
        <TairoFormGroup label="Skills" sublabel="Add your best skills">
          <div v-if="skills.length === 0">
            <BasePlaceholderPage
              title="No skills"
              subtitle="Looks like you didn't add any skill yet. Share your skills to improve your profile."
              class="scale-90"
            >
              <template #image>
                <img
                  class="block dark:hidden"
                  src="/img/illustrations/placeholders/flat/placeholder-search-4.svg"
                  alt="Placeholder image"
                >
                <img
                  class="hidden dark:block"
                  src="/img/illustrations/placeholders/flat/placeholder-search-4-dark.svg"
                  alt="Placeholder image"
                >
              </template>
              <BaseButton class="mt-4 w-40" @click="openSkillModal()">
                Add Skill
              </BaseButton>
            </BasePlaceholderPage>
          </div>
          <div v-else class="space-y-8">
            <div
              v-for="item in skills"
              :key="item.name"
              class="flex w-full items-center gap-2"
            >
              <div
                class="border-muted-200 dark:border-muted-700 dark:bg-muted-800 relative flex size-[50px] shrink-0 items-center justify-center rounded-full border bg-white"
              >
                <img
                  v-if="'logo' in item"
                  :src="item.logo"
                  :alt="item.name"
                  class="size-8 rounded-full"
                >
                <Icon
                  v-else
                  :name="item.icon"
                  class="text-muted-400 size-6"
                />
                <BaseProgressCircle
                  :size="68"
                  :thickness="1.5"
                  :model-value="item.level"
                  variant="primary"
                  class="absolute -start-2.5 -top-2.5"
                />
              </div>
              <div>
                <BaseHeading
                  tag="h3"
                  size="sm"
                  weight="medium"
                >
                  {{ item.name }}
                </BaseHeading>
                <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
                  <span>{{ item.experience }} years of experience</span>
                </BaseParagraph>
              </div>
              <div class="ms-auto">
                <BaseDropdown
                  label="Dropdown"
                  placement="bottom-end"
                  size="md"
                  class="z-20"
                  rounded="lg"
                >
                  <template #button>
                    <BaseButton
                      size="icon-sm"
                      rounded="full"
                      class="bg-white text-muted-400 dark:bg-muted-800 dark:text-muted-400"
                    >
                      <Icon name="lucide:more-horizontal" class="size-4" />
                    </BaseButton>
                  </template>
                  <BaseDropdownItem
                    title="Edit"
                    text="Edit this skill"
                    @click.prevent="openSkillModal(item)"
                  >
                    <template #start>
                      <Icon
                        name="solar:pen-2-linear"
                        class="me-2 block size-5"
                      />
                    </template>
                  </BaseDropdownItem>
                  <BaseDropdownItem
                    title="Delete"
                    text="Delete this skill"
                    @click.prevent="deleteSkill(item)"
                  >
                    <template #start>
                      <Icon
                        name="solar:trash-bin-minimalistic-linear"
                        class="me-2 block size-5"
                      />
                    </template>
                  </BaseDropdownItem>
                </BaseDropdown>
              </div>
            </div>
          </div>
          <div
            class="border-muted-200 dark:border-muted-800 mt-8 flex w-full items-center gap-2 border-t pt-8"
          >
            <div
              class="bg-muted-100 dark:bg-muted-800/60 text-muted-400 flex size-[50px] items-center justify-center rounded-full"
            >
              <Icon name="solar:add-folder-linear" class="size-6" />
            </div>
            <div>
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
              >
                New Skill
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-400">
                <span>Add a new skill you master</span>
              </BaseParagraph>
            </div>
            <div class="ms-auto">
              <BaseButton rounded="full" size="icon-sm" @click="openSkillModal()">
                <Icon name="lucide:plus" class="size-4" />
              </BaseButton>
            </div>
          </div>
        </TairoFormGroup>
        <TairoFormGroup label="Tools" sublabel="Add the tools you work with">
          <div v-if="tools.length === 0">
            <BasePlaceholderPage
              title="No tools"
              subtitle="Looks like you didn't add any tools yet. Share your skills to improve your profile."
              class="scale-90"
            >
              <template #image>
                <img
                  class="block dark:hidden"
                  src="/img/illustrations/placeholders/flat/placeholder-search-5.svg"
                  alt="Placeholder image"
                >
                <img
                  class="hidden dark:block"
                  src="/img/illustrations/placeholders/flat/placeholder-search-5-dark.svg"
                  alt="Placeholder image"
                >
              </template>
              <BaseButton class="mt-4 w-40" @click="openToolModal()">
                Add Tool
              </BaseButton>
            </BasePlaceholderPage>
          </div>
          <div v-else class="space-y-8">
            <div
              v-for="item in tools"
              :key="item.name"
              class="flex w-full items-center gap-2"
            >
              <div
                class="border-muted-200 dark:border-muted-700 dark:bg-muted-800 relative flex size-[50px] shrink-0 items-center justify-center rounded-full border bg-white"
              >
                <img
                  :src="item.logo"
                  :alt="item.name"
                  class="size-8 rounded-full"
                >
                <BaseProgressCircle
                  :size="68"
                  :thickness="1.5"
                  :model-value="item.level"
                  variant="primary"
                  class="absolute -start-2.5 -top-2.5"
                />
              </div>
              <div>
                <BaseHeading
                  tag="h3"
                  size="sm"
                  weight="medium"
                >
                  {{ item.name }}
                </BaseHeading>
                <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
                  <span>{{ item.mastery }}</span>
                </BaseParagraph>
              </div>
              <div class="ms-auto">
                <BaseDropdown
                  label="Dropdown"
                  placement="bottom-end"
                  size="md"
                  class="z-20"
                  rounded="lg"
                >
                  <template #button>
                    <BaseButton
                      size="icon-sm"
                      rounded="full"
                      class="bg-white text-muted-400 dark:bg-muted-800 dark:text-muted-400"
                    >
                      <Icon name="lucide:more-horizontal" class="size-4" />
                    </BaseButton>
                  </template>
                  <BaseDropdownItem
                    title="Edit"
                    text="Edit this tool"
                    @click.prevent="openToolModal(item)"
                  >
                    <template #start>
                      <Icon
                        name="solar:pen-2-linear"
                        class="me-2 block size-5"
                      />
                    </template>
                  </BaseDropdownItem>
                  <BaseDropdownItem
                    title="Delete"
                    text="Delete this tool"
                    @click.prevent="deleteTool(item)"
                  >
                    <template #start>
                      <Icon
                        name="solar:trash-bin-minimalistic-linear"
                        class="me-2 block size-5"
                      />
                    </template>
                  </BaseDropdownItem>
                </BaseDropdown>
              </div>
            </div>
          </div>
          <div
            class="border-muted-200 dark:border-muted-800 mt-8 flex w-full items-center gap-2 border-t pt-8"
          >
            <div
              class="bg-muted-100 dark:bg-muted-800/60 text-muted-400 flex size-[50px] items-center justify-center rounded-full"
            >
              <Icon name="solar:settings-linear" class="size-6" />
            </div>
            <div>
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
              >
                New Tool
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-400">
                <span>Add a new tool you work with</span>
              </BaseParagraph>
            </div>
            <div class="ms-auto">
              <BaseButton rounded="full" size="icon-sm" @click="openToolModal()">
                <Icon name="lucide:plus" class="size-4" />
              </BaseButton>
            </div>
          </div>
        </TairoFormGroup>
      </div>
    </div>
    <TairoFormSave rounded="md" />

    <!-- Language Form Modal -->
    <LanguageFormModal
      :open="showLanguageModal"
      :language="editingLanguage"
      @close="showLanguageModal = false; editingLanguage = null"
      @submit="onSaveLanguage"
    />

    <!-- Skill Form Modal -->
    <SkillFormModal
      :open="showSkillModal"
      :skill="editingSkill"
      @close="showSkillModal = false; editingSkill = null"
      @submit="onSaveSkill"
    />

    <!-- Tool Form Modal -->
    <ToolFormModal
      :open="showToolModal"
      :tool="editingTool"
      @close="showToolModal = false; editingTool = null"
      @submit="onSaveTool"
    />
  </form>
</template>
