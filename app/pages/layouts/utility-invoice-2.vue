<script setup lang="ts">
import type {
  InvoiceLineItem,
  WidgetsInvoiceActionsData,
  WidgetsInvoiceAmountData,
  WidgetsInvoiceCustomerData,
} from '~/types/invoice'

definePageMeta({
  title: 'Invoice',
  preview: {
    title: 'Invoice',
    description: 'For services and accounting',
    categories: ['layouts'],
    src: '/img/screens/layouts-utility-invoice-2.png',
    srcDark: '/img/screens/layouts-utility-invoice-2-dark.png',
    order: 8,
    new: true,
  },
})

// Invoice settings
const pdfAttachment = ref(true)
const invoiceNumber = ref('INV-2023-276')
const invoiceDate = ref('2023-06-11')
const dueDate = ref('2023-07-11')
const taxRate = ref(6.5)

// Line items data - single editable item
const items = ref<InvoiceLineItem[]>([
  {
    id: 'item-1',
    description: 'UI/UX Design',
    hours: 20,
    rate: 50.45,
  },
])

// Computed values based on items
const subtotal = computed(() => {
  return items.value.reduce((sum, item) => sum + (item.hours * item.rate), 0)
})

const taxAmount = computed(() => {
  return (subtotal.value * taxRate.value) / 100
})

const total = computed(() => {
  return subtotal.value + taxAmount.value
})

// Customer data
const customerData = computed<WidgetsInvoiceCustomerData>(() => ({
  customer: {
    id: 'customer-1',
    name: 'Kendra Wilson',
    email: '<EMAIL>',
    avatarUrl: '/img/avatars/10.svg',
    address: {
      street: '148, Victoria Street, Suite D23, 3rd floor',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA',
    },
  },
  editUrl: '/customers/customer-1/edit',
}))

// Amount data for widget
const amountData = computed<WidgetsInvoiceAmountData>(() => ({
  subtotal: subtotal.value,
  taxRate: taxRate.value,
  taxAmount: taxAmount.value,
  total: total.value,
}))

// Actions data
const actionsData = computed<WidgetsInvoiceActionsData>(() => ({
  description: 'Preview and manage your invoice.',
  canDownload: true,
  canPrint: false,
  canEmail: true,
  canDuplicate: false,
  canDelete: false,
}))

// Event handlers
function handleItemsUpdate(updatedItems: InvoiceLineItem[]) {
  items.value = updatedItems
}

function handleCustomerEdit(customerId: string) {
  navigateTo(`/customers/${customerId}/edit`)
}

function handlePayInvoice(amount: number) {
  console.log('Processing payment for:', amount)
}

function handleDownload() {
  console.log('Downloading invoice PDF')
}

function handleEmail() {
  console.log('Sending invoice via email')
}

function handlePreview() {
  console.log('Previewing invoice')
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 lg:col-span-8">
        <div class="flex flex-col gap-4">
          <BaseCard
            rounded="md"
            class="p-4 md:p-6"
          >
            <div class="mb-8 flex items-center gap-4">
              <BaseAvatar
                src="/img/logos/companies/amazon.svg"
                alt="Amazon"
                size="md"
                rounded="none"
                mask="blob"
                class="bg-muted-100 dark:bg-muted-900"
              />
              <div>
                <BaseHeading
                  weight="medium"
                  size="lg"
                  lead="none"
                  class="line-clamp-1 text-muted-900 dark:text-muted-100"
                >
                  Amazon
                </BaseHeading>
                <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 line-clamp-1">
                  Ecommerce Company
                </BaseParagraph>
              </div>
              <div class="ms-auto">
                <div class="text-end">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 line-clamp-1">
                    48, Church Street
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 line-clamp-1">
                    Los Angeles, 92384
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 line-clamp-1">
                    United States
                  </BaseParagraph>
                </div>
              </div>
            </div>
            <div
              class="bg-primary-800 relative flex flex-col justify-between gap-y-6 overflow-hidden rounded-2xl p-8 sm:flex-row"
            >
              <div
                class="absolute -bottom-10 -start-10 size-48 rounded-full bg-white/10"
              />
              <div
                class="absolute -end-24 -top-10 hidden size-80 rounded-full bg-white/10 sm:block"
              />
              <div class="relative z-20">
                <BaseHeading
                  as="h5"
                  weight="medium"
                  size="sm"
                  lead="none"
                  class="mb-1 text-white"
                >
                  Invoice number
                </BaseHeading>
                <BaseParagraph
                  weight="semibold"
                  class="mb-2 text-white"
                >
                  INV-2023-276
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="mb-1 text-white"
                >
                  Issued date: Jun 11, 2023
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="text-white"
                >
                  Due date: Jul 11, 2023
                </BaseParagraph>
              </div>
              <div class="relative z-20 sm:text-end">
                <BaseHeading
                  as="h5"
                  weight="medium"
                  size="sm"
                  lead="none"
                  class="mb-1 text-white"
                >
                  Billed to
                </BaseHeading>
                <BaseParagraph
                  weight="semibold"
                  class="mb-2 text-white"
                >
                  Kendra Wilson
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="mb-1 text-white"
                >
                  21, Jumpwall Street
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="mb-1 text-white"
                >
                  Suite G2, Block 23
                </BaseParagraph>
                <BaseParagraph
                  size="sm"
                  class="text-white"
                >
                  Los Angeles, USA
                </BaseParagraph>
              </div>
            </div>
            <div class="py-6">
              <div class="mb-8 flex items-center">
                <div>
                  <BaseHeading
                    weight="medium"
                    size="sm"
                    class="text-mute-900 dark:text-muted-100"
                  >
                    Invoice details
                  </BaseHeading>
                  <BaseParagraph
                    size="xs"
                    class="text-muted-600 dark:text-muted-400"
                  >
                    List the invoiced items
                  </BaseParagraph>
                </div>
                <div class="ms-auto">
                  <BaseButton rounded="md">
                    Customize
                  </BaseButton>
                </div>
              </div>
              <div
                class="border-muted-200 dark:border-muted-800 w-full overflow-x-auto border-b pb-8"
              >
                <table class="w-full text-start">
                  <thead>
                    <tr>
                      <th
                        class="min-w-[200px] px-2 text-start sm:w-1/3 sm:min-w-[auto]"
                      >
                        <BaseText
                          size="xs"
                          weight="medium"
                          class="text-muted-500 dark:text-muted-400"
                        >
                          Item Name
                        </BaseText>
                      </th>
                      <th class="min-w-[90px] px-2 text-start sm:min-w-[auto]">
                        <BaseText
                          size="xs"
                          weight="medium"
                          class="text-muted-500 dark:text-muted-400"
                        >
                          Hours
                        </BaseText>
                      </th>
                      <th class="min-w-[120px] px-2 text-start sm:min-w-[auto]">
                        <BaseText
                          size="xs"
                          weight="medium"
                          class="text-muted-500 dark:text-muted-400"
                        >
                          Rate/hr
                        </BaseText>
                      </th>
                      <th class="min-w-[60px] px-2 text-start sm:min-w-[60px]">
                        <BaseText
                          size="xs"
                          weight="medium"
                          class="text-muted-500 dark:text-muted-400"
                        >
                          Tax
                        </BaseText>
                      </th>
                      <th class="min-w-[120px] px-2 text-start sm:min-w-[120px]">
                        <BaseText
                          size="xs"
                          weight="medium"
                          class="text-muted-500 dark:text-muted-400"
                        >
                          Subtotal
                        </BaseText>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td class="px-2 py-4">
                        <BaseInput
                          v-model="itemName"
                          placeholder="Name of the item"
                        />
                      </td>
                      <td class="px-2 py-4">
                        <BaseInput v-model="itemHours" placeholder="0" />
                      </td>
                      <td class="px-2 py-4">
                        <TairoInput
                          v-model="hourRate"
                          icon="lucide:dollar-sign"
                          placeholder="0.00"
                        />
                      </td>
                      <td class="px-2 py-4">
                        <TairoInput
                          v-model="taxRate"
                          icon="lucide:percent"
                          placeholder="0"
                        />
                      </td>
                      <td class="px-2 py-4">
                        <TairoInput
                          v-model="itemSubtotal"
                          icon="lucide:dollar-sign"
                          placeholder="0.00"
                          readonly
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div>
                  <button
                    type="button"
                    class="text-primary-500 flex items-center gap-1 underline-offset-4 hover:underline"
                  >
                    <Icon name="lucide:plus" class="size-4" />
                    <BaseText
                      size="xs"
                      weight="semibold"
                    >
                      Add a new item
                    </BaseText>
                  </button>
                </div>
              </div>
              <div class="w-full pt-8">
                <div class="grid grid-cols-1 gap-12 sm:grid-cols-2">
                  <!-- Payment method -->
                  <div>
                    <div class="mb-6 flex items-center justify-between">
                      <BaseHeading
                        weight="medium"
                        size="sm"
                        class="text-muted-900 dark:text-muted-100"
                      >
                        Payment method
                      </BaseHeading>
                      <BaseButton
                        to="/layouts/card/new"
                        rounded="lg"
                        size="icon-sm"
                      >
                        <Icon name="solar:pen-2-linear" class="size-4" />
                      </BaseButton>
                    </div>
                    <div class="bg-muted-100 dark:bg-muted-900 rounded-xl p-6">
                      <div class="mb-6 flex items-center justify-between">
                        <div>
                          <BaseHeading as="h4" size="sm">
                            Paypal
                          </BaseHeading>
                        </div>
                        <div>
                          <Icon name="logos:paypal" class="size-6" />
                        </div>
                      </div>
                      <div class="space-y-1">
                        <div class="flex items-center justify-between">
                          <BaseParagraph
                            size="sm"
                            class="text-muted-600 dark:text-muted-400"
                          >
                            Name
                          </BaseParagraph>
                          <BaseParagraph
                            size="sm"
                            weight="semibold"
                            class="text-muted-900 dark:text-muted-100"
                          >
                            Kendra Wilson
                          </BaseParagraph>
                        </div>
                        <div class="flex items-center justify-between">
                          <BaseParagraph
                            size="sm"
                            class="text-muted-600 dark:text-muted-400"
                          >
                            Account
                          </BaseParagraph>
                          <BaseParagraph
                            size="sm"
                            weight="medium"
                            class="text-muted-900 dark:text-muted-100"
                          >
                            3524 65456 3245 4869
                          </BaseParagraph>
                        </div>
                        <div class="flex items-center justify-between">
                          <BaseParagraph
                            size="sm"
                            class="text-muted-600 dark:text-muted-400"
                          >
                            Routing
                          </BaseParagraph>
                          <BaseParagraph
                            size="sm"
                            weight="medium"
                            class="text-muted-900 dark:text-muted-100"
                          >
                            4238293
                          </BaseParagraph>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- Total -->
                  <div class="flex h-full flex-col">
                    <div class="mb-6 space-y-2 sm:ps-10">
                      <div class="mb-6 flex items-center justify-between">
                        <BaseHeading
                          weight="medium"
                          size="sm"
                          class="text-muted-900 dark:text-muted-100"
                        >
                          Amount billed
                        </BaseHeading>
                        <BaseButton
                          to="/layouts/card/new"
                          rounded="lg"
                          size="icon-sm"
                        >
                          <Icon name="lucide:printer" class="size-4" />
                        </BaseButton>
                      </div>
                      <div class="flex items-center justify-between">
                        <BaseParagraph
                          size="sm"
                          class="text-muted-600 dark:text-muted-400"
                        >
                          Subtotal
                        </BaseParagraph>
                        <BaseParagraph
                          size="sm"
                          weight="semibold"
                          class="text-muted-900 dark:text-muted-100"
                        >
                          {{ formatPrice(1525.18) }}
                        </BaseParagraph>
                      </div>
                      <div class="flex items-center justify-between">
                        <BaseParagraph
                          size="sm"
                          class="text-muted-600 dark:text-muted-400"
                        >
                          Discount
                        </BaseParagraph>
                        <BaseParagraph
                          size="sm"
                          weight="semibold"
                          class="text-muted-900 dark:text-muted-100"
                        >
                          {{ formatPrice(0.0) }}
                        </BaseParagraph>
                      </div>
                      <div class="flex items-center justify-between">
                        <BaseParagraph
                          size="sm"
                          class="text-muted-600 dark:text-muted-400"
                        >
                          Taxes
                        </BaseParagraph>
                        <BaseParagraph
                          size="sm"
                          weight="semibold"
                          class="text-muted-900 dark:text-muted-100"
                        >
                          {{ formatPrice(0.0) }}
                        </BaseParagraph>
                      </div>
                    </div>
                    <div class="mt-auto sm:ps-10">
                      <div
                        class="border-muted-200 dark:border-muted-800/80 flex items-center justify-between border-t pt-6"
                      >
                        <BaseParagraph
                          size="sm"
                          class="text-muted-600 dark:text-muted-400"
                        >
                          Total
                        </BaseParagraph>
                        <BaseParagraph
                          size="sm"
                          weight="semibold"
                          class="text-muted-800 dark:text-muted-100"
                        >
                          {{ formatPrice(parseInt(itemSubtotal, 10)) }}
                        </BaseParagraph>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>
      </div>
      <div class="col-span-12 lg:col-span-4">
        <div class="flex flex-col gap-4">
          <!-- Customer -->
          <WidgetsInvoiceCustomer
            :data="customerData"
            @edit="handleCustomerEdit"
          />
          <!-- Amount -->
          <WidgetsInvoiceAmountSimple
            v-model:pdf-attachment="pdfAttachment"
            :total="total"
            :due-date="dueDate"
          />
          <!-- Actions -->
          <WidgetsInvoiceActionsSimple
            @preview="handlePreview"
            @download="handleDownload"
            @send="handleEmail"
          />
        </div>
      </div>
    </div>
  </div>
</template>
