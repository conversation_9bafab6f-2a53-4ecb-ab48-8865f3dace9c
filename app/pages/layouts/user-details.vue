<script setup lang="ts">
import type { Address } from '../../types/auth'
import { useAddresses } from '../../composables/useAddresses'

definePageMeta({
  title: 'User',
  preview: {
    title: 'User',
    description: 'For viewing user info',
    categories: ['layouts', 'lists'],
    src: '/img/screens/layouts-user.png',
    srcDark: '/img/screens/layouts-user-dark.png',
    order: 30,
    new: true,
  },
})

// Auth and profile data
const { user, currentProfile, fetchCurrentProfile, updateProfile, isLoading: isAuthLoading } = useAuth()
const { showSuccess, showError } = useToaster()

// Address management
const {
  getAddressesByType,
  createAddress,
  updateAddress,
  deleteAddress,
  isLoading: isAddressLoading,
  error: addressError,
} = useAddresses()

// Reactive state
const mailingAddresses = ref<Address[]>([])
const legalAddresses = ref<Address[]>([])
const showAddressModal = ref(false)
const editingAddress = ref<Address | null>(null)
const addressModalType = ref<'mailing' | 'legal'>('mailing')

// Inline editing state
type EditableField = 'firstName' | 'lastName' | 'display_name' | 'email' | 'date_of_birth' | 'phone_number' | 'gender' | 'family_status'
const editingField = ref<EditableField | null>(null)
const editingValue = ref('')
const isSaving = ref<Record<string, boolean>>({})
const fieldErrors = ref<Record<string, string>>({})
const originalValues = ref<Record<string, string>>({})

// Loading state
const isLoading = computed(() => isAuthLoading.value || isAddressLoading.value)

// Computed properties for display
const userDisplayName = computed(() => {
  if (!user.value)
    return 'Loading...'
  if (currentProfile.value?.firstName && currentProfile.value?.lastName) {
    return `${currentProfile.value.firstName} ${currentProfile.value.lastName}`
  }
  return currentProfile.value?.display_name || user.value.username || 'Unknown User'
})

const userEmail = computed(() => user.value?.email || 'No email')

const userAvatar = computed(() => {
  return currentProfile.value?.avatar_url || '/img/avatars/placeholder.svg'
})

// Profile fields with fallbacks
const profileFields = computed(() => ({
  legalName: userDisplayName.value,
  preferredName: currentProfile.value?.display_name || userDisplayName.value,
  email: userEmail.value,
  dateOfBirth: currentProfile.value?.date_of_birth || 'Not set',
  phoneNumber: currentProfile.value?.phone_number || 'Not set',
  gender: currentProfile.value?.gender || 'Not set',
  familyStatus: currentProfile.value?.family_status || 'Not set',
}))

// Load data on mount
onMounted(async () => {
  try {
    // Ensure we have the latest profile data
    await fetchCurrentProfile()

    // Load addresses
    await loadAddresses()
  }
  catch (error) {
    console.error('Error loading user details:', error)
  }
})

// Load addresses function
async function loadAddresses() {
  try {
    const [mailing, legal] = await Promise.all([
      getAddressesByType('mailing', { showErrorToast: false }),
      getAddressesByType('legal', { showErrorToast: false }),
    ])

    mailingAddresses.value = mailing
    legalAddresses.value = legal
  }
  catch (error) {
    console.error('Error loading addresses:', error)
  }
}

// Address modal functions
function openAddressModal(type: 'mailing' | 'legal', address?: Address) {
  addressModalType.value = type
  editingAddress.value = address || null
  showAddressModal.value = true
}

function closeAddressModal() {
  showAddressModal.value = false
  editingAddress.value = null
}

async function handleAddressSubmit(addressData: any) {
  try {
    if (editingAddress.value) {
      // Update existing address
      await updateAddress(editingAddress.value.id, addressData)
    }
    else {
      // Create new address
      await createAddress({
        ...addressData,
        type: addressModalType.value,
      })
    }

    // Reload addresses
    await loadAddresses()
    closeAddressModal()
  }
  catch (error) {
    console.error('Error saving address:', error)
  }
}

async function handleDeleteAddress(address: Address) {
  if (confirm('Are you sure you want to delete this address?')) {
    try {
      await deleteAddress(address.id)
      await loadAddresses()
    }
    catch (error) {
      console.error('Error deleting address:', error)
    }
  }
}

// Inline editing functions
function startEditField(fieldName: EditableField) {
  // Prevent multiple simultaneous edits
  if (editingField.value) {
    cancelEdit()
  }

  editingField.value = fieldName
  fieldErrors.value[fieldName] = ''

  // Set current value based on field type
  let currentValue = ''
  switch (fieldName) {
    case 'firstName':
      currentValue = currentProfile.value?.firstName || ''
      break
    case 'lastName':
      currentValue = currentProfile.value?.lastName || ''
      break
    case 'display_name':
      currentValue = currentProfile.value?.display_name || ''
      break
    case 'email':
      currentValue = user.value?.email || ''
      break
    case 'date_of_birth':
      currentValue = currentProfile.value?.date_of_birth || ''
      break
    case 'phone_number':
      currentValue = currentProfile.value?.phone_number || ''
      break
    case 'gender':
      currentValue = currentProfile.value?.gender || ''
      break
    case 'family_status':
      currentValue = currentProfile.value?.family_status || ''
      break
  }

  editingValue.value = currentValue
  originalValues.value[fieldName] = currentValue

  // Auto-focus the input after Vue updates the DOM
  nextTick(() => {
    const input = document.querySelector(`[data-field="${fieldName}"]`) as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  })
}

function cancelEdit() {
  if (!editingField.value)
    return

  const fieldName = editingField.value
  editingValue.value = originalValues.value[fieldName] || ''
  fieldErrors.value[fieldName] = ''
  editingField.value = null
}

function validateField(fieldName: EditableField, value: string): string | null {
  switch (fieldName) {
    case 'firstName':
    case 'lastName':
      if (!value.trim())
        return 'This field is required'
      if (value.length < 2)
        return 'Must be at least 2 characters'
      if (value.length > 50)
        return 'Must be less than 50 characters'
      break
    case 'display_name':
      if (!value.trim())
        return 'This field is required'
      if (value.length < 2)
        return 'Must be at least 2 characters'
      if (value.length > 100)
        return 'Must be less than 100 characters'
      break
    case 'email':
      if (!value.trim())
        return 'Email is required'
      const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/
      if (!emailRegex.test(value))
        return 'Invalid email format'
      break
    case 'phone_number':
      if (value && value.length < 10)
        return 'Phone number must be at least 10 digits'
      break
    case 'date_of_birth':
      if (value) {
        const date = new Date(value)
        if (isNaN(date.getTime()))
          return 'Invalid date format'
        if (date > new Date())
          return 'Date cannot be in the future'
      }
      break
  }
  return null
}

async function saveField(fieldName: EditableField, value: string) {
  if (!currentProfile.value)
    return

  // Validate the field
  const error = validateField(fieldName, value)
  if (error) {
    fieldErrors.value[fieldName] = error
    return
  }

  // Clear any existing error
  fieldErrors.value[fieldName] = ''

  // Set saving state
  isSaving.value[fieldName] = true

  try {
    // Prepare update data based on field type
    const updateData: any = {}

    switch (fieldName) {
      case 'firstName':
        updateData.firstName = value.trim()
        break
      case 'lastName':
        updateData.lastName = value.trim()
        break
      case 'display_name':
        updateData.display_name = value.trim()
        break
      case 'email':
        // Note: Email updates might require special handling in your auth system
        // For now, we'll just show an error that it can't be changed here
        fieldErrors.value[fieldName] = 'Email changes require special verification. Please use account settings.'
        return
      case 'date_of_birth':
        updateData.date_of_birth = value || null
        break
      case 'phone_number':
        updateData.phone_number = value || null
        break
      case 'gender':
        updateData.gender = value || null
        break
      case 'family_status':
        updateData.family_status = value || null
        break
    }

    // Update the profile
    await updateProfile(updateData)

    // Show success message
    showSuccess(`${fieldName.replace('_', ' ')} updated successfully`)

    // Exit edit mode
    editingField.value = null
  }
  catch (error: any) {
    console.error('Error updating field:', error)
    fieldErrors.value[fieldName] = error.message || 'Failed to update field'
    showError(`Failed to update ${fieldName.replace('_', ' ')}`)
  }
  finally {
    isSaving.value[fieldName] = false
  }
}

// Keyboard event handlers
function handleKeydown(event: KeyboardEvent, fieldName: EditableField) {
  if (event.key === 'Enter') {
    event.preventDefault()
    saveField(fieldName, editingValue.value)
  }
  else if (event.key === 'Escape') {
    event.preventDefault()
    cancelEdit()
  }
}

// Helper function to get field display value
function getFieldValue(fieldName: EditableField): string {
  switch (fieldName) {
    case 'firstName':
      return currentProfile.value?.firstName || 'Not set'
    case 'lastName':
      return currentProfile.value?.lastName || 'Not set'
    case 'display_name':
      return currentProfile.value?.display_name || 'Not set'
    case 'email':
      return user.value?.email || 'Not set'
    case 'date_of_birth':
      return currentProfile.value?.date_of_birth || 'Not set'
    case 'phone_number':
      return currentProfile.value?.phone_number || 'Not set'
    case 'gender':
      return currentProfile.value?.gender || 'Not set'
    case 'family_status':
      return currentProfile.value?.family_status || 'Not set'
    default:
      return 'Not set'
  }
}
</script>

<template>
  <div class="w-full px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header -->
    <div class="border-muted-200 dark:border-muted-800 border-b pb-6">
      <div class="flex items-center gap-3">
        <BaseAvatar
          size="lg"
          :src="userAvatar"
          rounded="none"
          mask="blob"
        />
        <div>
          <BaseHeading
            as="h2"
            size="2xl"
            weight="medium"
            lead="tight"
            class="text-muted-800 dark:text-white"
          >
            {{ userDisplayName }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
            {{ userEmail }}
          </BaseParagraph>
        </div>
      </div>
    </div>
    <!-- Content -->
    <div class="space-y-10">
      <!-- Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              General info
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              Some information we need to know about you, and to process legal
              matters.
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <BaseHeading
            as="h3"
            size="xs"
            weight="medium"
            class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
          >
            About you
          </BaseHeading>
          <div
            class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
          >
            <!-- Legal name (First + Last) -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Legal name
                </BaseHeading>
                <button
                  v-if="editingField !== 'firstName' && editingField !== 'lastName'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('firstName')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode for first name -->
              <div v-if="editingField === 'firstName'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="First name"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <div v-else class="flex items-center gap-2">
                <BaseText size="sm" class="text-muted-600 dark:text-muted-300">
                  {{ getFieldValue('firstName') }}
                </BaseText>
                <BaseText size="sm" class="text-muted-600 dark:text-muted-300">
                  {{ getFieldValue('lastName') }}
                </BaseText>
                <button
                  class="text-xs text-primary-500 hover:text-primary-600 transition-colors ml-auto opacity-0 group-hover:opacity-100"
                  @click="startEditField('lastName')"
                >
                  Edit last name
                </button>
              </div>

              <!-- Editing mode for last name -->
              <div v-if="editingField === 'lastName'" class="space-y-2 mt-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="Last name"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>
            </div>

            <!-- Preferred name -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Preferred name
                </BaseHeading>
                <button
                  v-if="editingField !== 'display_name'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('display_name')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'display_name'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="Preferred name"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('display_name') }}
              </BaseText>
            </div>

            <!-- Email address -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Email address
                </BaseHeading>
                <button
                  v-if="editingField !== 'email'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('email')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'email'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="Email address"
                    type="email"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('email') }}
              </BaseText>
            </div>
          </div>
        </div>
      </div>
      <!-- Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              Personal info
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              Some information we need to know about you, and to process legal
              matters.
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <BaseHeading
            as="h3"
            size="xs"
            weight="medium"
            class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
          >
            Additional info
          </BaseHeading>
          <div
            class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
          >
            <!-- Date of birth -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Date of birth
                </BaseHeading>
                <button
                  v-if="editingField !== 'date_of_birth'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('date_of_birth')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'date_of_birth'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="YYYY-MM-DD"
                    type="date"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('date_of_birth') }}
              </BaseText>
            </div>

            <!-- Phone number -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Phone number
                </BaseHeading>
                <button
                  v-if="editingField !== 'phone_number'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('phone_number')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'phone_number'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="Phone number"
                    type="tel"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('phone_number') }}
              </BaseText>
            </div>

            <!-- Gender -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Gender
                </BaseHeading>
                <button
                  v-if="editingField !== 'gender'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('gender')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'gender'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseSelect
                    v-model="editingValue"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                  >
                    <option value="">
                      Select gender
                    </option>
                    <option value="male">
                      Male
                    </option>
                    <option value="female">
                      Female
                    </option>
                    <option value="non-binary">
                      Non-binary
                    </option>
                    <option value="prefer-not-to-say">
                      Prefer not to say
                    </option>
                  </BaseSelect>
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('gender') }}
              </BaseText>
            </div>

            <!-- Family status -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Family status
                </BaseHeading>
                <button
                  v-if="editingField !== 'family_status'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('family_status')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'family_status'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseSelect
                    v-model="editingValue"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                  >
                    <option value="">
                      Select family status
                    </option>
                    <option value="single">
                      Single
                    </option>
                    <option value="married">
                      Married
                    </option>
                    <option value="divorced">
                      Divorced
                    </option>
                    <option value="widowed">
                      Widowed
                    </option>
                    <option value="partnered">
                      Partnered
                    </option>
                  </BaseSelect>
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('family_status') }}
              </BaseText>
            </div>
          </div>
        </div>
      </div>
      <!-- Mailing Addresses Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              Mailing Addresses
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              We'll send physical cards as well as gifts and special offers to
              these addresses
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <div class="space-y-4">
            <!-- Loading State -->
            <div v-if="isLoading" class="flex items-center justify-center p-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
            </div>

            <!-- No Addresses State -->
            <div
              v-else-if="mailingAddresses.length === 0"
              class="border-muted-200 dark:border-muted-800 border rounded-lg p-6 text-center"
            >
              <Icon name="solar:home-linear" class="size-12 mx-auto text-muted-400 mb-3" />
              <BaseHeading as="h4" size="sm" class="text-muted-600 dark:text-muted-300 mb-2">
                No mailing addresses
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-4">
                Add a mailing address to receive physical items
              </BaseParagraph>
              <BaseButton
                size="sm"
                variant="primary"
                @click="openAddressModal('mailing')"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                Add Address
              </BaseButton>
            </div>

            <!-- Address Cards -->
            <template v-else>
              <div
                v-for="address in mailingAddresses"
                :key="address.id"
                class="border-muted-200 dark:border-muted-800 border rounded-lg px-4 pb-4 pt-3 group hover:border-muted-300 dark:hover:border-muted-700 transition-colors"
              >
                <div class="flex items-start justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <BaseHeading
                      as="h4"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ address.name || 'Mailing Address' }}
                    </BaseHeading>
                    <BaseBadge v-if="address.is_primary" size="xs" variant="primary">
                      Primary
                    </BaseBadge>
                  </div>
                  <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="openAddressModal('mailing', address)"
                    >
                      <Icon name="solar:pen-2-linear" class="size-4" />
                    </BaseButton>
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="handleDeleteAddress(address)"
                    >
                      <Icon name="solar:trash-bin-minimalistic-linear" class="size-4" />
                    </BaseButton>
                  </div>
                </div>
                <div class="space-y-1">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.address_line_1 }}
                  </BaseParagraph>
                  <BaseParagraph
                    v-if="address.address_line_2"
                    size="sm"
                    class="text-muted-600 dark:text-muted-300"
                  >
                    {{ address.address_line_2 }}
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.city }}<template v-if="address.state">
                      , {{ address.state }}
                    </template><template v-if="address.postal_code">
                      {{ address.postal_code }}
                    </template>
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.country }}
                  </BaseParagraph>
                </div>
              </div>

              <!-- Add New Address Button -->
              <BaseButton
                variant="outline"
                class="w-full"
                @click="openAddressModal('mailing')"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                Add Mailing Address
              </BaseButton>
            </template>
          </div>
        </div>
      </div>
      <!-- Legal Addresses Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              Legal Addresses
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              These are billing addresses for your cards and will appear on your
              documents
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <div class="space-y-4">
            <!-- Loading State -->
            <div v-if="isLoading" class="flex items-center justify-center p-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
            </div>

            <!-- No Addresses State -->
            <div
              v-else-if="legalAddresses.length === 0"
              class="border-muted-200 dark:border-muted-800 border rounded-lg p-6 text-center"
            >
              <Icon name="solar:document-linear" class="size-12 mx-auto text-muted-400 mb-3" />
              <BaseHeading as="h4" size="sm" class="text-muted-600 dark:text-muted-300 mb-2">
                No legal addresses
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-4">
                Add a legal address for billing and documents
              </BaseParagraph>
              <BaseButton
                size="sm"
                variant="primary"
                @click="openAddressModal('legal')"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                Add Address
              </BaseButton>
            </div>

            <!-- Address Cards -->
            <template v-else>
              <div
                v-for="address in legalAddresses"
                :key="address.id"
                class="border-muted-200 dark:border-muted-800 border rounded-lg px-4 pb-4 pt-3 group hover:border-muted-300 dark:hover:border-muted-700 transition-colors"
              >
                <div class="flex items-start justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <BaseHeading
                      as="h4"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ address.name || 'Legal Address' }}
                    </BaseHeading>
                    <BaseBadge v-if="address.is_primary" size="xs" variant="primary">
                      Primary
                    </BaseBadge>
                  </div>
                  <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="openAddressModal('legal', address)"
                    >
                      <Icon name="solar:pen-2-linear" class="size-4" />
                    </BaseButton>
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="handleDeleteAddress(address)"
                    >
                      <Icon name="solar:trash-bin-minimalistic-linear" class="size-4" />
                    </BaseButton>
                  </div>
                </div>
                <div class="space-y-1">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.address_line_1 }}
                  </BaseParagraph>
                  <BaseParagraph
                    v-if="address.address_line_2"
                    size="sm"
                    class="text-muted-600 dark:text-muted-300"
                  >
                    {{ address.address_line_2 }}
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.city }}<template v-if="address.state">
                      , {{ address.state }}
                    </template><template v-if="address.postal_code">
                      {{ address.postal_code }}
                    </template>
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.country }}
                  </BaseParagraph>
                </div>
              </div>

              <!-- Add New Address Button -->
              <BaseButton
                variant="outline"
                class="w-full"
                @click="openAddressModal('legal')"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                Add Legal Address
              </BaseButton>
            </template>
          </div>
        </div>
      </div>
      <!-- Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              Main Card
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              This is your company main credit card. You can use it to pay for
              any type of expenses
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <div
            class="border-muted-200 dark:border-muted-800 border-b px-4 pb-4"
          >
            <CreditCardReal
              status="active"
              name="Kendra Wilson"
              number="•••• •••• •••• 4728"
              brand="mastercard"
              class="mx-0!"
            />
            <div class="mt-4">
              <LinkArrow to="/layouts/cards" label="Manage your cards" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Address Form Modal -->
    <AddressFormModal
      :open="showAddressModal"
      :type="addressModalType"
      :address="editingAddress"
      @close="closeAddressModal"
      @submit="handleAddressSubmit"
    />
  </div>
</template>
