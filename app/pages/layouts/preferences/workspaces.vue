<script setup lang="ts">
import { useAuth } from '~/composables/auth'
import { useData<PERSON><PERSON> } from '~/composables/data'

definePageMeta({
  title: 'Workspaces',
  preview: {
    title: 'Preferences - Workspaces',
    description: 'For account management',
    categories: ['layouts', 'settings'],
    src: '/img/screens/layouts-preferences-workspaces.png',
    srcDark: '/img/screens/layouts-preferences-workspaces-dark.png',
    order: 85,
    new: true,
  },
})

const {
  workspaces,
  currentWorkspace,
  switchWorkspace,
  user,
  isLoading: authLoading,
} = useAuth()

const {
  getByField,
  read,
  isLoading: dataLoading,
} = useDataApi()

// Track selected workspace - initially set to current workspace index
const selectedWorkspaceIndex = ref(0)
const workspaceMembers = ref<any[]>([])
const profiles = ref<any[]>([])
const isLoadingMembers = ref(false)

async function loadWorkspaceMembers(workspaceId: string) {
  if (!workspaceId)
    return

  isLoadingMembers.value = true
  try {
    // Get workspace members
    const membershipResponse = await getByField<any>(
      'workspace_members',
      'workspace_id',
      workspaceId,
      {
        showErrorToast: false,
      },
    )

    if (membershipResponse && membershipResponse.data) {
      const members = Array.isArray(membershipResponse.data) ? membershipResponse.data : [membershipResponse.data]
      workspaceMembers.value = members

      // Load profiles for each member
      const profilePromises = members.map(async (member: any) => {
        const profileId = `${member.user_id}_${workspaceId}`
        try {
          const profileResponse = await read<any>(
            'profiles',
            profileId,
            {
              showErrorToast: false,
            },
          )

          if (profileResponse && profileResponse.data) {
            return {
              ...profileResponse.data,
              role: member.role,
              member_id: member.user_id,
            }
          }
        }
        catch (error) {
          console.warn(`Could not load profile for user ${member.user_id}:`, error)
        }
        return null
      })

      const loadedProfiles = await Promise.all(profilePromises)
      profiles.value = loadedProfiles.filter(p => p !== null)
    }
  }
  catch (error) {
    console.error('Failed to load workspace members:', error)
    workspaceMembers.value = []
    profiles.value = []
  }
  finally {
    isLoadingMembers.value = false
  }
}

// Watch for current workspace changes and update selected index
watch(
  currentWorkspace,
  (newWorkspace) => {
    if (newWorkspace) {
      const index = workspaces.value.findIndex(w => w.id === newWorkspace.id)
      if (index !== -1) {
        selectedWorkspaceIndex.value = index
        loadWorkspaceMembers(newWorkspace.id)
      }
    }
  },
  { immediate: true },
)

// Handle workspace selection
async function selectWorkspace(index: number) {
  selectedWorkspaceIndex.value = index
  const workspace = workspaces.value[index]
  if (workspace && workspace.id !== currentWorkspace.value?.id) {
    try {
      await switchWorkspace(workspace.id)
    }
    catch (error) {
      console.error('Failed to switch workspace:', error)
    }
  }
  else {
    // If it's the same workspace, just reload members
    await loadWorkspaceMembers(workspace.id)
  }
}

const selectedWorkspace = computed(() => {
  return workspaces.value[selectedWorkspaceIndex.value]
})

function getAvatarUrl(profile: any) {
  return profile.avatar_url || '/img/avatars/10.svg'
}

function getDisplayName(profile: any) {
  if (profile.display_name)
    return profile.display_name
  if (profile.first_name && profile.last_name) {
    return `${profile.first_name} ${profile.last_name}`
  }
  return profile.first_name || profile.last_name || 'Unknown User'
}
</script>

<template>
  <div class="mt-8 space-y-8">
    <BaseCard rounded="lg" class="overflow-hidden">
      <div class="px-4 py-5 sm:p-6">
        <div class="sm:flex sm:items-center sm:justify-between">
          <div class="space-y-1">
            <BaseHeading
              as="h4"
              size="md"
              weight="medium"
              class="text-muted-900 text-base font-bold dark:text-white"
            >
              Workspaces you are on
            </BaseHeading>
            <BaseParagraph
              size="sm"
              class="text-muted-500 dark:text-muted-400"
            >
              Lorem ipsum dolor sit amet, consectetur adipis.
            </BaseParagraph>
          </div>

          <div class="mt-4 sm:mt-0">
            <BaseButton rounded="md" class="font-medium">
              <Icon name="lucide:plus" class="size-4" />
              <span>New Workspace</span>
            </BaseButton>
          </div>
        </div>

        <div class="mt-8 flow-root">
          <div class="divide-muted-100 dark:divide-muted-800/80 -my-5 divide-y">
            <div
              v-for="(workspace, index) in workspaces"
              :key="workspace.id"
              class="cursor-pointer p-4 transition-colors duration-300"
              :class="selectedWorkspaceIndex === index ? 'bg-muted-50 dark:bg-muted-900/50' : 'hover:bg-muted-50 dark:hover:bg-muted-900/30'"
              role="button"
              tabindex="0"
              @click="selectWorkspace(index)"
            >
              <div class="flex items-center gap-4">
                <div class="relative">
                  <BaseAvatar
                    :src="workspace.logo_url || '/img/icons/logos/default.svg'"
                    size="sm"
                  />
                </div>
                <div class="flex-1">
                  <BaseParagraph
                    size="sm"
                    weight="medium"
                    class="text-muted-900 dark:text-muted-100"
                  >
                    {{ workspace.name }}
                  </BaseParagraph>
                  <BaseParagraph
                    size="sm"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    <span v-if="isLoadingMembers && selectedWorkspaceIndex === index">
                      <Icon name="lucide:loader-2" class="size-3 animate-spin inline mr-1" />
                      Loading members...
                    </span>
                    <span v-else>
                      {{ index === selectedWorkspaceIndex ? profiles.length : '...' }} members
                    </span>
                  </BaseParagraph>
                </div>
                <div v-if="selectedWorkspaceIndex === index">
                  <BaseTag
                    variant="primary"
                    size="sm"
                    rounded="full"
                  >
                    <Icon name="lucide:check" class="size-3 mr-1" />
                    Current
                  </BaseTag>
                </div>

                <div class="ms-auto flex items-center gap-2">
                  <BaseButton
                    v-if="selectedWorkspaceIndex === index"
                    class="text-primary-500! font-medium"
                    rounded="md"
                    size="sm"
                  >
                    <Icon name="lucide:settings" class="size-4" />
                    Settings
                  </BaseButton>
                  <BaseButton
                    v-if="workspace.role !== 'owner'"
                    rounded="md"
                    size="sm"
                    variant="muted"
                  >
                    <Icon name="lucide:log-out" class="size-4" />
                    Leave
                  </BaseButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseCard>

    <BaseCard rounded="lg" class="overflow-hidden">
      <div class="px-4 py-5 sm:p-6">
        <div class="sm:flex sm:items-center sm:justify-between">
          <div class="space-y-1">
            <BaseHeading
              as="h4"
              size="md"
              weight="medium"
              class="text-muted-900 text-base dark:text-white"
            >
              Workspace members
            </BaseHeading>
            <BaseParagraph
              size="sm"
              class="text-muted-500 dark:text-muted-400"
            >
              Lorem ipsum dolor sit amet, consectetur adipis.
            </BaseParagraph>
          </div>

          <div class="mt-4 sm:mt-0">
            <BaseButton rounded="md" class="font-medium">
              <Icon name="lucide:plus" class="size-4" />
              <span>New Member</span>
            </BaseButton>
          </div>
        </div>

        <div class="mt-8 flow-root">
          <div v-if="isLoadingMembers" class="flex items-center justify-center py-8">
            <Icon name="lucide:loader-2" class="size-6 animate-spin mr-2" />
            <BaseParagraph size="sm" class="text-muted-500">
              Loading workspace members...
            </BaseParagraph>
          </div>
          <div v-else-if="profiles.length === 0">
            <BasePlaceholderPage
              title="No members found"
              subtitle="There are no members to show in this workspace. Start inviting people to join."
            >
              <template #image>
                <img
                  src="/img/illustrations/placeholders/flat/placeholder-team.svg"
                  alt="No members"
                  class="block dark:hidden"
                >
                <img
                  src="/img/illustrations/placeholders/flat/placeholder-team-dark.svg"
                  alt="No members"
                  class="hidden dark:block"
                >
              </template>
              <div class="mt-4 flex justify-center gap-2">
                <BaseButton
                  rounded="md"
                  variant="primary"
                  class="font-medium"
                >
                  <Icon name="lucide:user-plus" class="size-4" />
                  <span>Invite New Member</span>
                </BaseButton>
              </div>
            </BasePlaceholderPage>
          </div>
          <div v-else class="divide-muted-100 dark:divide-muted-800/80 -my-5 divide-y">
            <div
              v-for="(profile, index) in profiles"
              :key="profile.member_id || index"
              class="p-4 transition-colors duration-300 hover:bg-muted-50 dark:hover:bg-muted-900/30"
            >
              <div class="flex items-center gap-4">
                <div class="relative">
                  <BaseAvatar
                    :src="getAvatarUrl(profile)"
                    size="sm"
                  />
                </div>
                <div class="flex-1">
                  <BaseParagraph
                    size="sm"
                    weight="medium"
                    class="text-muted-900 dark:text-muted-100"
                  >
                    {{ getDisplayName(profile) }}
                    <BaseTag
                      v-if="profile.member_id === user?.id"
                      variant="success"
                      size="xs"
                      rounded="full"
                      class="ml-2"
                    >
                      You
                    </BaseTag>
                  </BaseParagraph>
                  <BaseParagraph
                    size="sm"
                    class="text-muted-500 dark:text-muted-400"
                  >
                    {{ profile.role || 'member' }}
                    <span v-if="profile.job_title" class="ml-1">· {{ profile.job_title }}</span>
                  </BaseParagraph>
                </div>

                <div class="ms-auto flex items-center gap-2">
                  <BaseButton
                    v-if="user?.id === profile.member_id || currentWorkspace?.role === 'owner'"
                    class="text-primary-500! font-medium"
                    rounded="md"
                    size="sm"
                  >
                    <Icon name="lucide:edit" class="size-4" />
                    Edit
                  </BaseButton>
                  <BaseButton
                    v-if="profile.member_id !== user?.id && currentWorkspace?.role === 'owner'"
                    rounded="md"
                    size="sm"
                    variant="muted"
                  >
                    <Icon name="lucide:user-minus" class="size-4" />
                    Remove
                  </BaseButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseCard>
  </div>
</template>
