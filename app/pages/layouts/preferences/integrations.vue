<script setup lang="ts">
import type { Integration } from '~/types/ui'

definePageMeta({
  title: 'Integrations',
  preview: {
    title: 'Preferences - Integrations',
    description: 'For account management',
    categories: ['layouts', 'settings'],
    src: '/img/screens/layouts-preferences-integrations.png',
    srcDark: '/img/screens/layouts-preferences-integrations-dark.png',
    order: 87,
    new: true,
  },
})

// Use real integration data and state management
const {
  connectedIntegrations,
  availableIntegrations,
  loading,
  connecting,
  connectIntegration,
  disconnectIntegration,
  loadIntegrations,
} = useIntegrations()

// Modal state
const showConnectionModal = ref(false)
const selectedIntegration = ref<Integration | null>(null)

// Search functionality
const searchQuery = ref('')
const filteredConnected = computed(() => {
  if (!searchQuery.value)
    return connectedIntegrations.value
  const query = searchQuery.value.toLowerCase()
  return connectedIntegrations.value.filter(integration =>
    integration.name.toLowerCase().includes(query)
    || integration.description.toLowerCase().includes(query),
  )
})

const filteredAvailable = computed(() => {
  if (!searchQuery.value)
    return availableIntegrations.value
  const query = searchQuery.value.toLowerCase()
  return availableIntegrations.value.filter(integration =>
    integration.name.toLowerCase().includes(query)
    || integration.description.toLowerCase().includes(query),
  )
})

// Helper functions
function openDocsUrl(url: string | undefined) {
  if (url) {
    window.open(url, '_blank')
  }
}

function handleConnect(integration: Integration) {
  selectedIntegration.value = integration
  showConnectionModal.value = true
}

// Add loading state for disconnect actions
const disconnecting = ref<string | null>(null)

async function handleDisconnect(integration: Integration) {
  const confirmed = confirm(
    `Are you sure you want to disconnect ${integration.name}?\n\n`
    + 'This will remove the integration and all associated credentials. '
    + 'This action cannot be undone.',
  )

  if (confirmed) {
    disconnecting.value = integration.id
    try {
      await disconnectIntegration(integration.id)
    }
    finally {
      disconnecting.value = null
    }
  }
}

function handleConnectionModalClose() {
  showConnectionModal.value = false
  selectedIntegration.value = null
}

async function handleConnectionSubmit(formData: any) {
  if (!selectedIntegration.value)
    return

  const success = await connectIntegration({
    integration: selectedIntegration.value,
    credentials: formData.credentials,
    testConnection: formData.testConnection,
  })

  if (success) {
    handleConnectionModalClose()
  }
}

// Connection status helpers
function getStatusColor(status?: string) {
  switch (status) {
    case 'connected': return 'text-success-500'
    case 'error': return 'text-destructive-500'
    case 'testing': return 'text-warning-500'
    default: return 'text-muted-500'
  }
}

function getStatusText(integration: Integration) {
  if (integration.connecting)
    return 'Connecting...'
  if (integration.status === 'connected')
    return 'Connected'
  if (integration.status === 'error')
    return 'Error'
  if (integration.status === 'testing')
    return 'Testing...'
  return 'Not connected'
}

// Utility function to format relative time
function formatRelativeTime(date: Date | string): string {
  const now = new Date()
  const target = new Date(date)
  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'just now'
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`
  }

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`
  }

  const diffInMonths = Math.floor(diffInDays / 30)
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`
  }

  const diffInYears = Math.floor(diffInMonths / 12)
  return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`
}

// Refresh integrations on page load
onMounted(() => {
  loadIntegrations()
})
</script>

<template>
  <div class="mt-8 dark:[--color-input-default-bg:var(--color-muted-950)]">
    <div class="border-primary-500 bg-primary-500/10 rounded-2xl border-2">
      <div class="p-4">
        <div class="gap-6 md:flex md:items-center md:justify-between">
          <BaseAvatar
            rounded="none"
            mask="blob"
            src="/img/avatars/15.svg"
            size="lg"
          />
          <div class="max-w-xs flex-1">
            <BaseParagraph weight="semibold" class="text-primary-700 dark:text-primary-400">
              Learn how to connect to our API
            </BaseParagraph>
            <BaseParagraph
              size="sm"
              class="text-primary-600 dark:text-primary-300"
            >
              We've put together a nice and simple tutorial.
            </BaseParagraph>
          </div>

          <div class="mt-6 flex items-center justify-start gap-3 md:ms-auto md:mt-0 md:justify-end md:space-x-reverse">
            <BaseButton rounded="md">
              Dismiss
            </BaseButton>
            <BaseButton
              variant="primary"
              rounded="md"
            >
              View Tutorial
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-8 sm:flex sm:items-center sm:justify-between">
      <div class="space-y-1">
        <BaseHeading
          as="h4"
          size="md"
          weight="medium"
          class="text-muted-900 text-base font-bold dark:text-white"
        >
          Connected integrations
        </BaseHeading>
        <BaseParagraph
          size="sm"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          View and manager your connected integrations.
        </BaseParagraph>
      </div>

      <TairoInput
        v-model="searchQuery"
        icon="lucide:search"
        rounded="md"
        placeholder="Search integrations..."
        class="mt-4 sm:mt-0"
      />
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="mt-8 flex justify-center py-12">
      <div class="flex items-center space-x-3">
        <Icon name="lucide:loader-2" class="size-6 animate-spin text-primary-500" />
        <BaseParagraph class="text-muted-600">
          Loading integrations...
        </BaseParagraph>
      </div>
    </div>

    <!-- Connected Integrations List -->
    <div v-else-if="filteredConnected.length > 0" class="mt-8 flow-root">
      <div class="divide-muted-200 dark:divide-muted-700 -my-5 divide-y">
        <div
          v-for="integration in filteredConnected"
          :key="integration.id"
          class="py-5"
        >
          <div class="sm:flex sm:items-center sm:justify-between sm:space-x-5">
            <div class="flex min-w-0 flex-1 items-center">
              <div class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 mb-3 flex size-12 items-center justify-center rounded-xl border bg-white">
                <div class="bg-muted-100 dark:bg-muted-800 flex size-10 items-center justify-center rounded-lg">
                  <Icon :name="integration.icon" class="text-muted-500 dark:text-muted-400 group-hover/chain:text-primary-500 dark:group-hover/chain:text-primary-500 size-6 transition-colors duration-500" />
                </div>
              </div>
              <div class="ms-4 min-w-0 flex-1 space-y-1">
                <BaseParagraph
                  size="sm"
                  weight="semibold"
                  class="text-muted-900 dark:text-muted-100 truncate"
                >
                  {{ integration.name }}
                </BaseParagraph>
                <BaseParagraph
                  size="xs"
                  weight="medium"
                  class="text-muted-500 dark:text-muted-400"
                >
                  {{ integration.description }}
                </BaseParagraph>
              </div>
            </div>

            <div class="mt-4 flex items-center justify-between gap-3 ps-14 sm:mt-0 sm:justify-end sm:space-x-6 sm:ps-0">
              <!-- Connection Status -->
              <div class="flex flex-col items-end gap-1">
                <div class="flex items-center gap-2">
                  <div class="size-2 rounded-full" :class="getStatusColor(integration.status).replace('text-', 'bg-')" />
                  <span class="text-xs font-medium" :class="getStatusColor(integration.status)">
                    {{ getStatusText(integration) }}
                  </span>
                </div>
                <div v-if="integration.lastConnected" class="text-xs text-muted-500">
                  Connected {{ formatRelativeTime(integration.lastConnected) }}
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center gap-2">
                <BaseButton
                  size="sm"
                  :aria-label="`Learn more about ${integration.name} integration`"
                  tabindex="0"
                  @click="() => openDocsUrl(integration.docsUrl)"
                >
                  Learn More
                </BaseButton>

                <BaseButton
                  size="sm"
                  variant="destructive"
                  rounded="md"
                  :disabled="disconnecting === integration.id"
                  :aria-label="`Disconnect ${integration.name} integration`"
                  tabindex="0"
                  @click="() => handleDisconnect(integration)"
                >
                  <Icon
                    v-if="disconnecting === integration.id"
                    name="lucide:loader-2"
                    class="size-4 animate-spin mr-2"
                  />
                  <Icon
                    v-else
                    name="lucide:trash-2"
                    class="size-4 mr-2"
                  />
                  {{ disconnecting === integration.id ? 'Removing...' : 'Delete' }}
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Integrations Section -->
    <div class="mt-12 sm:flex sm:items-center sm:justify-between">
      <div class="space-y-1">
        <BaseHeading
          as="h4"
          size="md"
          weight="medium"
          class="text-muted-900 text-base font-bold dark:text-white"
        >
          Available integrations
        </BaseHeading>
        <BaseParagraph
          size="sm"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          Connect new services to extend your workflow capabilities.
        </BaseParagraph>
      </div>

      <TairoInput
        v-model="searchQuery"
        icon="lucide:search"
        rounded="md"
        placeholder="Search available integrations..."
        class="mt-4 sm:mt-0"
      />
    </div>

    <!-- Available Integrations List -->
    <div v-if="filteredAvailable.length > 0" class="mt-8 flow-root">
      <div class="divide-muted-200 dark:divide-muted-700 -my-5 divide-y">
        <div
          v-for="integration in filteredAvailable"
          :key="integration.id"
          class="py-5"
        >
          <div class="sm:flex sm:items-center sm:justify-between sm:space-x-5">
            <div class="flex min-w-0 flex-1 items-center">
              <div class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 mb-3 flex size-12 items-center justify-center rounded-xl border bg-white">
                <div class="bg-muted-100 dark:bg-muted-800 flex size-10 items-center justify-center rounded-lg">
                  <Icon
                    :name="integration.icon"
                    class="size-6 transition-colors duration-300" :class="[
                      integration.connecting
                        ? 'text-primary-500 animate-pulse'
                        : 'text-muted-400 dark:text-muted-500 opacity-60',
                    ]"
                  />
                </div>
              </div>
              <div class="ms-4 min-w-0 flex-1 space-y-1">
                <div class="flex items-center gap-2">
                  <BaseParagraph
                    size="sm"
                    weight="semibold"
                    class="text-muted-900 dark:text-muted-100 truncate"
                  >
                    {{ integration.name }}
                  </BaseParagraph>

                  <!-- AI Provider Badge -->
                  <BaseBadge
                    v-if="integration.category === 'ai_llm'"
                    size="xs"
                    variant="primary"
                    rounded="md"
                  >
                    AI
                  </BaseBadge>

                  <!-- Email Provider Badge -->
                  <BaseBadge
                    v-if="integration.category === 'email'"
                    size="xs"
                    variant="success"
                    rounded="md"
                  >
                    Email
                  </BaseBadge>

                  <!-- Calendar Provider Badge -->
                  <BaseBadge
                    v-if="integration.category === 'calendar'"
                    size="xs"
                    variant="warning"
                    rounded="md"
                  >
                    Calendar
                  </BaseBadge>

                  <!-- Auth Type Badge -->
                  <BaseBadge
                    size="xs"
                    :variant="integration.authType === 'api_key' ? 'muted' : 'info'"
                    rounded="md"
                  >
                    {{ integration.authType === 'api_key' ? 'API Key'
                      : integration.authType === 'oauth' ? 'OAuth'
                        : integration.authType === 'url' ? 'URL' : 'Built-in' }}
                  </BaseBadge>
                </div>

                <BaseParagraph
                  size="xs"
                  weight="medium"
                  class="text-muted-500 dark:text-muted-400"
                >
                  {{ integration.description }}
                </BaseParagraph>

                <!-- AI Models Info -->
                <div v-if="integration.aiConfig?.models" class="flex flex-wrap gap-1 mt-2">
                  <span
                    v-for="model in integration.aiConfig.models.slice(0, 3)"
                    :key="model"
                    class="text-xs text-muted-400 bg-muted-100 dark:bg-muted-800 px-1.5 py-0.5 rounded"
                  >
                    {{ model }}
                  </span>
                  <span
                    v-if="integration.aiConfig.models.length > 3"
                    class="text-xs text-muted-400"
                  >
                    +{{ integration.aiConfig.models.length - 3 }} more
                  </span>
                </div>
              </div>
            </div>

            <div class="mt-4 flex items-center justify-between gap-3 ps-14 sm:mt-0 sm:justify-end sm:space-x-6 sm:ps-0">
              <BaseButton
                size="sm"
                :aria-label="`Learn more about ${integration.name} integration`"
                tabindex="0"
                @click="() => openDocsUrl(integration.docsUrl)"
              >
                Learn More
              </BaseButton>

              <BaseButton
                variant="primary"
                size="sm"
                rounded="md"
                :disabled="integration.connecting"
                :aria-label="`Connect ${integration.name} integration`"
                tabindex="0"
                @click="() => handleConnect(integration)"
              >
                <Icon
                  v-if="integration.connecting"
                  name="lucide:loader-2"
                  class="size-4 animate-spin mr-2"
                />
                {{ integration.connecting ? 'Connecting...' : 'Connect' }}
              </BaseButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading && searchQuery && filteredAvailable.length === 0" class="mt-8 text-center py-12">
      <Icon name="lucide:search-x" class="size-12 text-muted-400 mx-auto mb-4" />
      <BaseParagraph class="text-muted-600">
        No integrations found matching "{{ searchQuery }}"
      </BaseParagraph>
    </div>

    <!-- Connection Modal -->
    <IntegrationConnectionModal
      :open="showConnectionModal"
      :integration="selectedIntegration"
      :loading="connecting === selectedIntegration?.id"
      @close="handleConnectionModalClose"
      @submit="handleConnectionSubmit"
    />
  </div>
</template>
