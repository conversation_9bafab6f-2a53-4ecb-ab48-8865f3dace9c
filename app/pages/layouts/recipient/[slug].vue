<script setup lang="ts">
import type {
  RecipientDetailsCardData,
  RecipientMetricCardData,
  RecipientProfileCardData,
  RecipientTransactionsCardData,
} from '~/types/widgets'

definePageMeta({
  title: 'Recipient',
  preview: [
    {
      title: 'Company recipient',
      description: 'For company details',
      categories: ['layouts', 'lists', 'details'],
      src: '/img/screens/layouts-recipient-airbnb.png',
      srcDark: '/img/screens/layouts-recipient-airbnb-dark.png',
      order: 37,
      params: {
        slug: 'airbnb',
      },
      new: true,
    },
    {
      title: 'Contact recipient',
      description: 'For user details',
      categories: ['layouts', 'lists', 'details'],
      src: '/img/screens/layouts-recipient-jackie-strauss.png',
      srcDark: '/img/screens/layouts-recipient-jackie-strauss-dark.png',
      order: 37,
      params: {
        slug: 'jackie-strauss',
      },
      new: true,
    },
  ],
})

const notifications = ref(true)
const route = useRoute()
const slug = computed(() => route.params.slug)

const query = computed(() => {
  return {
    slug: slug.value,
  }
})

const { data, pending, error, refresh } = await useFetch(
  '/api/payments/recipients',
  {
    query,
  },
)

if (!data.value?.recipient) {
  await navigateTo('/layouts/payments/recipients')
}

const totalSent = computed(() => {
  return data.value?.recipient?.transactions.reduce(
    (acc: number, item: any) => {
      if (item.type === 'out') {
        return acc + item.amount
      }
      return acc
    },
    0,
  )
})

const totalReceived = computed(() => {
  return data.value?.recipient?.transactions.reduce(
    (acc: number, item: any) => {
      if (item.type === 'in') {
        return acc + item.amount
      }
      return acc
    },
    0,
  )
})

// Computed properties for widget data
const profileCardData = computed((): RecipientProfileCardData => ({
  name: data.value?.recipient?.name || '',
  picture: data.value?.recipient?.picture || '',
  category: data.value?.recipient?.category || '',
  address: data.value?.recipient?.address || [],
  country: data.value?.recipient?.country || '',
}))

const detailsCardData = computed((): RecipientDetailsCardData => ({
  title: 'Additional info',
  subtitle: 'More details about this contact',
  showNotifications: true,
  fields: [
    {
      label: 'Status',
      icon: 'solar:sticker-smile-square-linear',
      value: data.value?.recipient?.status || '',
      type: 'status',
    },
    {
      label: 'Email',
      icon: 'solar:letter-linear',
      value: data.value?.recipient?.email || '',
      type: 'text',
    },
    {
      label: 'Phone number',
      icon: 'solar:phone-rounded-linear',
      value: data.value?.recipient?.phoneNumber || '',
      type: 'text',
    },
    {
      label: 'Tax ID',
      icon: 'solar:bill-list-linear',
      value: data.value?.recipient?.taxId || '',
      type: 'text',
    },
    {
      label: 'Language',
      icon: 'solar:chat-dots-linear',
      value: data.value?.recipient?.language || '',
      type: 'language',
    },
  ],
}))

const paymentMethodCardData = computed((): RecipientMetricCardData => ({
  type: 'payment-method',
  title: data.value?.recipient?.paymentMethod?.name || '',
  subtitle: 'Payment method',
  paymentMethod: data.value?.recipient?.paymentMethod?.name || '',
}))

const sentAmountCardData = computed((): RecipientMetricCardData => ({
  type: 'amount',
  title: '',
  subtitle: 'Lifetime sent',
  formattedAmount: `$${totalSent.value?.toFixed(2) || '0.00'}`,
}))

const receivedAmountCardData = computed((): RecipientMetricCardData => ({
  type: 'amount',
  title: '',
  subtitle: 'Lifetime received',
  formattedAmount: `$${totalReceived.value?.toFixed(2) || '0.00'}`,
}))

const transactionsCardData = computed((): RecipientTransactionsCardData => ({
  title: 'History',
  actionButtonText: 'Send Money',
  emptyTitle: 'Nothing to show',
  emptyMessage: 'Looks like there are no transactions to show for this recipient. Come back once you have sent or received money from this recipient.',
  transactions: data.value?.recipient?.transactions?.map((transaction: any) => ({
    id: transaction.id,
    date: transaction.date,
    type: transaction.type,
    amount: transaction.amount,
    formattedAmount: `$${transaction.amount.toFixed(2)}`,
    status: transaction.status,
    method: transaction.method,
  })) || [],
}))

function handleEditContact() {
  // Handle edit contact action
  console.log('Edit contact clicked')
}

function handleSendMoney() {
  navigateTo('/layouts/send')
}
</script>

<template>
  <div v-if="data?.recipient" class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header -->
    <div class="flex items-center justify-end pb-6">
      <!-- Buttons -->
      <div class="hidden items-center gap-2 md:flex">
        <BaseButton
          to="/layouts/payments/recipients"
          rounded="md"
          size="sm"
        >
          <Icon name="lucide:arrow-left" class="size-4" />
          <span>Go Back</span>
        </BaseButton>
        <BaseButton
          to="/layouts/contacts/create"
          rounded="md"
          size="sm"
        >
          <Icon name="lucide:plus" class="size-4" />
          <span>New Contact</span>
        </BaseButton>
      </div>
    </div>
    <div class="grid grid-cols-12 gap-4">
      <!-- Info -->
      <div class="col-span-12 lg:col-span-4">
        <div class="flex flex-col gap-4">
          <!-- Recipient -->
          <WidgetsRecipientsProfileCard
            :data="profileCardData"
            @edit="handleEditContact"
          />
          <!-- Details -->
          <WidgetsRecipientsDetailsCard
            v-model:notifications="notifications"
            :data="detailsCardData"
          />
        </div>
      </div>
      <!-- Content -->
      <div class="col-span-12 lg:col-span-8">
        <!-- Grid -->
        <div class="grid grid-cols-12 gap-4">
          <!-- Payment method tile -->
          <div class="col-span-12 sm:col-span-4">
            <WidgetsRecipientsMetricCard :data="paymentMethodCardData" />
          </div>
          <!-- Lifetime sent tile -->
          <div class="col-span-12 sm:col-span-4">
            <WidgetsRecipientsMetricCard :data="sentAmountCardData" />
          </div>
          <!-- Lifetime received tile -->
          <div class="col-span-12 sm:col-span-4">
            <WidgetsRecipientsMetricCard :data="receivedAmountCardData" />
          </div>
          <!-- Transactions -->
          <div class="col-span-12">
            <WidgetsRecipientsTransactionsCard
              :data="transactionsCardData"
              @send-money="handleSendMoney"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
