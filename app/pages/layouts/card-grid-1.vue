<script setup lang="ts">
definePageMeta({
  title: 'Card Grid',
  preview: {
    title: 'Card grid 1',
    description: 'For item grids and collections',
    categories: ['layouts'],
    src: '/img/screens/layouts-card-grid-1.png',
    srcDark: '/img/screens/layouts-card-grid-1-dark.png',
    order: 57,
  },
})

// Feature flag integration for DataView migration
// Use URL parameter for testing: ?useDataView=true or ?useDataView=false
const route = useRoute()
const useDataView = computed(() => {
  const flagParam = route.query.useDataView as string
  // Default to false for now (stable legacy implementation)
  return flagParam === 'true'
})

// Development mode check
const isDevelopment = computed(() => process.env.NODE_ENV === 'development')

// Enhanced debugging for feature flag status
if (process.env.NODE_ENV === 'development') {
  watch(useDataView, (newValue) => {
    console.log(`[card-grid-1] Feature flag useDataView: ${newValue}`)
  }, { immediate: true })
}
</script>

<template>
  <!-- Feature Flag Toggle for Development (only show in dev mode) -->
  <div v-if="isDevelopment" class="fixed top-4 right-4 z-50 bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-800 rounded-lg p-3 shadow-lg">
    <div class="flex items-center gap-2">
      <Icon name="lucide:settings" class="size-4 text-muted-500" />
      <span class="text-sm font-medium text-muted-700 dark:text-muted-300">DataView</span>
      <BaseButton
        :variant="useDataView ? 'primary' : 'muted'"
        size="xs"
        rounded="md"
        @click="() => $router.push({ query: { ...route.query, useDataView: useDataView ? 'false' : 'true' } })"
      >
        {{ useDataView ? 'ON' : 'OFF' }}
      </BaseButton>
    </div>
  </div>

  <!-- Use CardGridDataViewAdapter with feature flag -->
  <CardGridDataViewAdapter :use-data-view="useDataView" />
</template>
