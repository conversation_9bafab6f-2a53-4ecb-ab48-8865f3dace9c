<script setup lang="ts">
definePageMeta({
  title: 'Profile',
  preview: {
    title: 'Profile',
    description: 'For displaying a user profile',
    categories: ['layouts', 'profile'],
    src: '/img/screens/layouts-subpages-profile.png',
    srcDark: '/img/screens/layouts-subpages-profile-dark.png',
    order: 75,
  },
})

// Auth and profile data
const { currentProfile, fetchCurrentProfile, isAuthenticated } = useAuth()
const loading = ref(!currentProfile.value)

// Authentication guard
if (!isAuthenticated.value) {
  await navigateTo('/auth/login')
}

// Fetch profile if not available
if (!currentProfile.value) {
  await fetchCurrentProfile().finally(() => {
    loading.value = false
  })
}
else {
  loading.value = false
}

// Computed properties for UI-friendly data structure
const fullName = computed(() => {
  if (!currentProfile.value)
    return ''
  const firstName = currentProfile.value.firstName || ''
  const lastName = currentProfile.value.lastName || ''
  return `${firstName} ${lastName}`.trim() || currentProfile.value.display_name || 'User'
})

const displayBio = computed(() => {
  return currentProfile.value?.bio || 'No bio available'
})

const avatar = computed(() => {
  return currentProfile.value?.avatar_url || null
})

const experiences = computed(() => {
  return currentProfile.value?.experiences || []
})

const languages = computed(() => {
  return currentProfile.value?.languages || []
})

const skills = computed(() => {
  return currentProfile.value?.skills || []
})

const tools = computed(() => {
  return currentProfile.value?.tools || []
})

// Convert social links object to array format expected by template
const socials = computed(() => {
  if (!currentProfile.value?.socialLinks)
    return []

  const socialMap = {
    facebook: { name: 'Facebook', icon: 'fa6-brands:facebook' },
    twitter: { name: 'Twitter', icon: 'fa6-brands:twitter' },
    dribbble: { name: 'Dribbble', icon: 'fa6-brands:dribbble' },
    instagram: { name: 'Instagram', icon: 'fa6-brands:instagram' },
    github: { name: 'GitHub', icon: 'fa6-brands:github' },
    gitlab: { name: 'GitLab', icon: 'fa6-brands:gitlab' },
    linkedin: { name: 'LinkedIn', icon: 'fa6-brands:linkedin' },
    website: { name: 'Website', icon: 'lucide:globe' },
  }

  // Additional safety check to ensure socialLinks is an object
  const socialLinks = currentProfile.value.socialLinks
  if (!socialLinks || typeof socialLinks !== 'object')
    return []

  return Object.entries(socialLinks)
    .filter(([key, url]) => url && typeof url === 'string')
    .map(([key, url]) => ({
      name: socialMap[key as keyof typeof socialMap]?.name || key,
      url: url as string,
      icon: socialMap[key as keyof typeof socialMap]?.icon || 'lucide:link',
    }))
})

// Placeholder data for counts (you can replace with real data later)
const profileStats = computed(() => ({
  relations: experiences.value.length + skills.value.length,
  projects: experiences.value.length || 1,
}))

// Placeholder for notifications setting
const notifications = ref(currentProfile.value?.profileSettings?.notifications?.enabled ?? true)

// Mock data for recommendations and recent views (you can replace with real data later)
const recommendations = ref([
  {
    name: 'John Doe',
    role: 'Senior Developer',
    src: '/img/avatars/25.svg',
    text: 'Great to work with!',
    date: '2 weeks ago',
    badge: '/img/icons/flags/united-states-of-america.svg',
  },
])

const recentViews = ref([
  {
    name: 'Sarah Johnson',
    role: 'Product Manager',
    src: '/img/avatars/19.svg',
    badge: '/img/icons/flags/france.svg',
  },
])
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="mx-auto w-full">
      <div v-if="loading" class="text-center py-20">
        <Icon name="lucide:loader-2" class="size-8 animate-spin mx-auto text-primary-500" />
        <p class="mt-4 text-muted-400">
          Loading profile...
        </p>
      </div>
      <div v-else-if="!currentProfile" class="text-center py-20">
        <Icon name="lucide:user-x" class="size-8 mx-auto text-muted-400" />
        <p class="mt-4 text-muted-400">
          Profile not found
        </p>
      </div>
      <div v-else class="relative w-full">
        <div class="absolute end-0 top-2 z-20">
          <BaseDropdown
            placement="bottom-end"
            size="md"
            class="z-20"
            rounded="lg"
          >
            <template #button>
              <BaseButton
                size="icon-sm"
                rounded="full"
                class="bg-white text-muted-400 dark:bg-muted-800 dark:text-muted-400"
              >
                <Icon name="lucide:more-horizontal" class="size-4" />
              </BaseButton>
            </template>
            <BaseDropdownItem
              to="/layouts/profile-edit"
              title="Edit"
              text="Edit profile"
            >
              <template #start>
                <Icon name="solar:pen-2-linear" class="me-2 block size-5" />
              </template>
            </BaseDropdownItem>
            <BaseDropdownSeparator />
            <BaseDropdownItem
              to="#"
              title="Security"
              text="Security settings"
            >
              <template #start>
                <Icon name="solar:lock-keyhole-linear" class="me-2 block size-5" />
              </template>
            </BaseDropdownItem>
            <BaseDropdownItem
              to="#"
              title="Billing"
              text="Manage billing"
            >
              <template #start>
                <Icon name="solar:card-linear" class="me-2 block size-5" />
              </template>
            </BaseDropdownItem>
            <BaseDropdownSeparator />
            <BaseDropdownItem
              to="#"
              title="Share"
              text="Share profile"
            >
              <template #start>
                <Icon name="solar:link-circle-linear" class="me-2 block size-5" />
              </template>
            </BaseDropdownItem>
          </BaseDropdown>
        </div>
        <div class="flex w-full flex-col">
          <BaseAvatar
            :src="avatar"
            size="2xl"
            class="mx-auto"
            :classes="{
              root: 'mx-auto',
            }"
          />
          <div class="mx-auto w-full max-w-md text-center">
            <BaseHeading
              tag="h2"
              size="xl"
              weight="medium"
              class="mt-4"
            >
              {{ fullName }}
            </BaseHeading>
            <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 mb-3 mt-1">
              {{ displayBio }}
            </BaseParagraph>
            <div
              class="divide-muted-200 dark:divide-muted-800 flex items-center justify-center divide-x"
            >
              <div class="text-muted-400 flex h-8 items-center gap-1 px-4">
                <Icon name="solar:widget-6-linear" class="size-5" />
                <BaseText size="sm">
                  {{ profileStats.relations }}+ relations
                </BaseText>
              </div>
              <div
                class="text-muted-400 hidden h-8 items-center gap-1 px-4 sm:flex"
              >
                <Icon name="solar:suitcase-lines-linear" class="size-5" />
                <BaseText size="sm">
                  {{ profileStats.projects }} projects
                </BaseText>
              </div>
              <div v-if="socials.length" class="flex h-8 items-center gap-2 px-4">
                <NuxtLink
                  v-for="link in socials"
                  :key="link.name"
                  :to="link.url"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="border-muted-200 hover:border-primary-500 dark:border-muted-800/80 dark:hover:border-primary-500 dark:bg-muted-800 text-muted-400 hover:text-primary-500 flex size-8 items-center justify-center rounded-full border bg-white transition-colors duration-300"
                >
                  <Icon :name="link.icon" class="size-3" />
                  <span class="sr-only">{{ link.name }}</span>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-6 grid grid-cols-12 gap-4">
          <div class="col-span-12 lg:landscape:col-span-8">
            <div class="flex flex-col gap-4">
              <!-- Main profile information widget -->
              <WidgetsProfileMainInfo
                :bio="displayBio"
                :experiences="experiences"
                :languages="languages"
                :skills="skills"
              />
              <!-- Recommendations widget -->
              <WidgetsProfileRecommendations
                :recommendations="recommendations"
              />
            </div>
          </div>
          <div class="col-span-12 lg:landscape:col-span-4">
            <div class="flex flex-col gap-4">
              <!-- Notifications settings widget -->
              <WidgetsProfileNotificationSettings
                v-model="notifications"
              />
              <!-- Tools widget -->
              <WidgetsProfileTools
                :tools="tools"
              />
              <!-- Recent Views widget -->
              <WidgetsProfileRecentViews
                :recent-views="recentViews"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
