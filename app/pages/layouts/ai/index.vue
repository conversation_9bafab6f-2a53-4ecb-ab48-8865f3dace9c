<script setup lang="ts">
import type { AiPromptSuggestion } from '~/types/ai'
import type { AgentConfig, ChatMode, ChatSessionConfig, ToolConfig } from '~/types/chat'
import type { AIProvider } from '~/types/ui'
import { logger } from '~/utils/logger'

definePageMeta({
  layout: 'empty',
  title: 'Tairo AI Chat',
  preview: {
    title: 'AI Interface',
    description: 'Multi-agent AI chat with LangGraph orchestration',
    categories: ['layouts', 'AI interface'],
    src: '/img/screens/layouts-ai.png',
    srcDark: '/img/screens/layouts-ai-dark.png',
    order: 0,
    new: true,
  },
})

// UI State
const isOpen = ref(true)
const isMobileOpen = ref(false)
const isEphemeral = ref(false)
const isConfigDrawerOpen = ref(false)

// Chat functionality
const {
  conversations,
  conversationsByPeriod,
  loading: conversationsLoading,
  createConversation,
  loadConversations,
} = useConversations()

// Use chat for WebSocket connections
const {
  messages,
  currentConversation,
  isConnected,
  isStreaming,
  streamingContent,
  isTyping,
  typingAgentId,
  canSendMessage,
  connectionStatus,
  error: chatError,
  startChat,
  sendMessage,
  clearConversation,
  clearError,
  heartbeatPulse,
} = useChat()

// Debug: watch messages array changes
watch(messages, (newMessages) => {
  console.log('[AI Chat Page] Messages updated:', {
    count: newMessages.length,
    lastMessage: newMessages[newMessages.length - 1],
    allMessages: newMessages.map(m => ({
      id: m.id,
      role: m.role,
      content: m.content?.substring(0, 50) + (m.content?.length > 50 ? '...' : ''),
      isStreaming: m.isStreaming,
    })),
  })
}, { deep: true, immediate: true })

// Session rehydration for historical conversations
const {
  session: rehydratedSession,
  messages: rehydratedMessages,
  loading: sessionLoading,
  error: sessionError,
  hasMore: hasMoreMessages,
  loadingMore: loadingMoreMessages,
  loadSession,
  loadMoreMessages,
  refreshSession,
  clearSession,
} = useChatSession()

// Use the real integrations system from the correct path
const {
  integrations,
  aiIntegrations,
  connectedIntegrations,
  loadIntegrations,
  reloadIntegrations,
} = useIntegrations()
const { allAgents, builtInAgents, customAgents } = useAgentLibrary()

// Chat configuration
const chatMode = ref<ChatMode>('single')
const selectedAgents = ref<AgentConfig[]>([])
const agentModels = ref<Record<string, string>>({})
const messageInput = ref('')
const showingConversation = ref<string | null>(null)
const enhancedChatEnabled = ref<boolean>(false)

// Available tools (mock for now)
const availableTools: ToolConfig[] = [
  { id: 'search', name: 'Web Search', description: 'Search the web for information', category: 'search' },
  { id: 'computation', name: 'Calculator', description: 'Perform mathematical calculations', category: 'computation' },
  { id: 'file', name: 'File Operations', description: 'Read and write files', category: 'file' },
  { id: 'api', name: 'API Calls', description: 'Make HTTP requests to APIs', category: 'api' },
]

// Suggested prompts configuration
const suggestedPrompts: AiPromptSuggestion[] = [
  {
    id: 'prompt-1',
    title: 'Create an image for your business presentation',
    prompt: 'Create an image for my business presentation',
    description: 'Create an image for your business presentation',
    icon: 'solar:archive-down-minimlistic-broken',
    color: 'primary',
  },
  {
    id: 'prompt-2',
    title: 'Make a summary of the last meeting',
    prompt: 'Make a summary of the last meeting',
    description: 'Make a summary of the last meeting',
    icon: 'solar:document-text-linear',
    color: 'success',
  },
  {
    id: 'prompt-3',
    title: 'Analyze uploaded image',
    prompt: 'Help me analyze this uploaded image',
    description: 'Retrieve the date and place of an uploaded image',
    icon: 'solar:camera-linear',
    color: 'warning',
  },
  {
    id: 'prompt-4',
    title: 'Marketing scenario creation',
    prompt: 'Create a scenario for marketing leads and phone calls',
    description: 'Create a scenario for marketing leads and phone calls',
    icon: 'solar:call-chat-rounded-linear',
    color: 'yellow',
  },
]

// Computed
const hasActiveChat = computed(() => !!currentConversation.value)
const currentMessages = computed(() => {
  if (showingConversation.value) {
    // Show rehydrated messages for historical conversations
    return rehydratedMessages.value
  }
  // Show live messages for current conversation
  return messages.value
})
const isConfigured = computed(() => {
  const hasAgents = selectedAgents.value.length > 0
  logger.debug('isConfigured check:', {
    hasAgents,
    selectedAgentsCount: selectedAgents.value.length,
    selectedAgentNames: selectedAgents.value.map(a => a.name),
    chatMode: chatMode.value,
  })
  return hasAgents
})

const availableProviders = computed(() => {
  // Get real connected providers only - no demo fallback
  const realProviders = aiIntegrations.value
    .filter(i => i.connected && i.aiConfig?.provider)
    .map(i => i.aiConfig!.provider as AIProvider)

  logger.debug('Available providers (real integrations only):', {
    realProviders,
    aiIntegrations: aiIntegrations.value.map(i => ({ name: i.name, connected: i.connected })),
  })

  return { providers: realProviders, isDemoMode: false }
})

// Auto-select first agent when agents become available
let hasAutoSelected = false
watch(builtInAgents, (newAgents) => {
  if (!hasAutoSelected && selectedAgents.value.length === 0 && newAgents.length > 0) {
    logger.info('Auto-selecting first built-in agent:', newAgents[0].name)
    selectedAgents.value = [newAgents[0]]
    hasAutoSelected = true
  }
}, { immediate: true })

// Debug watch to see if selectedAgents is being changed unexpectedly
watch(selectedAgents, (newAgents, oldAgents) => {
  logger.debug('=== SELECTEDAGENTS CHANGED ===')
  logger.debug('Old agents:', oldAgents?.map(a => a.name) || [])
  logger.debug('New agents:', newAgents?.map(a => a.name) || [])
  logger.debug('=== END SELECTEDAGENTS CHANGE ===')
}, { deep: true })

// Methods
function handleAgentSelection(agent: any, checked: boolean) {
  logger.debug('=== AGENT SELECTION START ===')
  logger.debug('Agent selection called:', {
    agentName: agent.name,
    agentId: agent.id,
    checked,
    currentMode: chatMode.value,
    currentSelectedCount: selectedAgents.value.length,
    currentSelectedNames: selectedAgents.value.map(a => a.name),
  })

  if (checked) {
    if (chatMode.value === 'single') {
      logger.debug('SINGLE MODE: Replacing all agents with:', agent.name)
      // Replace all agents in single mode
      selectedAgents.value = [agent]
      logger.debug('SINGLE MODE: After replacement:', selectedAgents.value.map(a => a.name))
    }
    else {
      // Add agent in multi mode (max 5)
      if (selectedAgents.value.length < 5) {
        logger.debug('MULTI MODE: Adding agent:', agent.name)
        selectedAgents.value = [...selectedAgents.value, agent]
        logger.debug('MULTI MODE: After addition:', selectedAgents.value.map(a => a.name))
      }
      else {
        logger.debug('MULTI MODE: Cannot add more agents, limit reached')
      }
    }
  }
  else {
    logger.debug('UNCHECKING: Removing agent:', agent.name)
    // Remove agent
    const beforeCount = selectedAgents.value.length
    const beforeNames = selectedAgents.value.map(a => a.name)
    selectedAgents.value = selectedAgents.value.filter(a => a.id !== agent.id)
    logger.debug('UNCHECKING: Before:', beforeNames, 'After:', selectedAgents.value.map(a => a.name))
  }

  // Force reactivity update
  nextTick(() => {
    logger.debug('AFTER NEXTTICK - Selected agents:', selectedAgents.value.map(a => a.name))
    logger.debug('AFTER NEXTTICK - Is configured:', isConfigured.value)
    logger.debug('=== AGENT SELECTION END ===')
  })
}

async function handleNewChat() {
  logger.debug('handleNewChat called:', {
    isConfigured: isConfigured.value,
    selectedAgents: selectedAgents.value.map(a => a.name),
    chatMode: chatMode.value,
    availableProviders: availableProviders.value,
  })

  if (!isConfigured.value) {
    logger.debug('Opening config drawer because not configured')
    isConfigDrawerOpen.value = true
    return
  }

  // Validate that selected agents have compatible providers
  const incompatibleAgents = selectedAgents.value.filter(
    agent => !availableProviders.value.providers.includes(agent.provider),
  )

  if (incompatibleAgents.length > 0) {
    logger.warn('Incompatible agents found:', incompatibleAgents.map(a => ({ name: a.name, provider: a.provider })))
    // Could show error here, but for now continue anyway
  }

  try {
    logger.debug('Creating chat session config...')
    const config: ChatSessionConfig = {
      mode: chatMode.value,
      agents: selectedAgents.value,
      tools: availableTools,
      maxTurns: 10, // Reduced from 20 to prevent overly long conversations
      timeout: 300000, // 5 minutes
    }

    logger.info('Starting chat with config:', config)
    await startChat(config)
    showingConversation.value = null // Show current chat

    // Close mobile sidebar
    isMobileOpen.value = false

    logger.info('Chat started successfully')
  }
  catch (error) {
    logger.error('Failed to start chat:', error)
    // Show error to user (TODO: Replace with toast notification)
    // eslint-disable-next-line no-alert
    alert(`Failed to start chat: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function handleSendMessage() {
  const content = messageInput.value.trim()
  if (!content)
    return

  try {
    // If there's no active chat yet, start one automatically
    if (!hasActiveChat.value) {
      if (!isConfigured.value) {
        // Open config drawer if not configured
        isConfigDrawerOpen.value = true
        return
      }
      await handleNewChat()
    }

    await sendMessage(content)
    messageInput.value = ''
  }
  catch (error) {
    logger.error('Failed to send message:', error)
  }
}

async function handleSelectConversation(conversationId: string) {
  try {
    // Clear current live chat to show historical session
    clearConversation()
    showingConversation.value = conversationId

    // Load session with message history
    await loadSession(conversationId)

    logger.info('Historical session loaded:', {
      sessionId: conversationId,
      messageCount: rehydratedMessages.value.length,
    })

    // Close mobile sidebar
    isMobileOpen.value = false
  }
  catch (error) {
    logger.error('Failed to load conversation:', { conversationId, error })
    // Show error to user (TODO: Replace with toast notification)
    // eslint-disable-next-line no-alert
    alert(`Failed to load conversation: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSendMessage()
  }
}

// Enhanced chat input handlers
async function handleEnhancedSubmit(payload: any) {
  logger.debug('Enhanced chat input submitted:', payload)

  try {
    // If there's no active chat yet, start one automatically
    if (!hasActiveChat.value) {
      if (!isConfigured.value) {
        // Open config drawer if not configured
        isConfigDrawerOpen.value = true
        return
      }
      await handleNewChat()
    }

    // Use the text from the enhanced payload
    const content = payload.text?.trim()
    if (!content) {
      logger.warn('No text content in enhanced payload')
      return
    }

    await sendMessage(content)
    logger.info('Enhanced message sent successfully')
  }
  catch (error) {
    logger.error('Failed to send enhanced message:', error)
    handleInputError(error instanceof Error ? error.message : 'Failed to send message')
  }
}

function handleInputError(error: string) {
  logger.error('Input error:', error)
  // You could show a toast notification here or set a reactive error state
  // For now, using the existing chatError mechanism
  // TODO: Implement better error handling with toast notifications
}

// Enhanced textarea functionality
const enhancedTextarea = ref<HTMLTextAreaElement>()

function handleEnhancedKeydown(event: KeyboardEvent) {
  // Ctrl/Cmd + Enter to submit
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    handleSendMessage()
    return
  }

  // Regular Enter behavior (allow for multi-line by default)
  if (event.key === 'Enter' && !event.shiftKey && !event.ctrlKey && !event.metaKey) {
    // Let it create a new line
    return
  }

  // TODO: Add slash command detection
  if (event.key === '/' && messageInput.value === '') {
    // Could trigger slash command menu
  }

  // TODO: Add mention detection
  if (event.key === '@') {
    // Could trigger mention picker
  }
}

function adjustTextareaHeight() {
  nextTick(() => {
    if (enhancedTextarea.value) {
      enhancedTextarea.value.style.height = 'auto'
      enhancedTextarea.value.style.height = `${Math.min(enhancedTextarea.value.scrollHeight, 200)}px`
    }
  })
}

// Auto-resize on input change
watch(messageInput, () => {
  adjustTextareaHeight()
})

// Initialize textarea height on mount
onMounted(() => {
  adjustTextareaHeight()
})

async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text)
    // TODO: Show success toast
  }
  catch (error) {
    logger.error('Failed to copy:', error)
  }
}

// Model selection methods
function updateAgentModel(agentId: string, modelName: string) {
  logger.debug('Updating agent model:', { agentId, modelName })

  // Update the agentModels reactive object
  agentModels.value[agentId] = modelName

  // Update the agent's modelName in selectedAgents
  const agentIndex = selectedAgents.value.findIndex(a => a.id === agentId)
  if (agentIndex !== -1) {
    selectedAgents.value[agentIndex] = {
      ...selectedAgents.value[agentIndex],
      modelName,
    }
    logger.debug('Updated agent in selectedAgents:', selectedAgents.value[agentIndex])
  }
  else {
    logger.warn('Agent not found in selectedAgents:', agentId)
  }
}

function getAvailableModelsForProvider(provider: AIProvider) {
  const modelMap: Record<AIProvider, { value: string, label: string }[]> = {
    openai: [
      { value: 'gpt-4o', label: 'GPT-4o (Latest)' },
      { value: 'gpt-4o-mini', label: 'GPT-4o Mini' },
      { value: 'gpt-4-turbo', label: 'GPT-4 Turbo' },
      { value: 'gpt-4', label: 'GPT-4' },
      { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
    ],
    anthropic: [
      { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet (Latest)' },
      { value: 'claude-3-5-haiku-20241022', label: 'Claude 3.5 Haiku' },
      { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus' },
      { value: 'claude-3-sonnet-20240229', label: 'Claude 3 Sonnet' },
      { value: 'claude-3-haiku-20240307', label: 'Claude 3 Haiku' },
    ],
    gemini: [
      { value: 'gemini-1.5-pro-002', label: 'Gemini 1.5 Pro (Latest)' },
      { value: 'gemini-1.5-flash-002', label: 'Gemini 1.5 Flash' },
      { value: 'gemini-1.0-pro', label: 'Gemini 1.0 Pro' },
    ],
  }

  return modelMap[provider] || []
}

// Handle prompt selection
function onPromptSelect(item: AiPromptSuggestion) {
  messageInput.value = item.prompt
  handleNewChat()
}

// Auto-scroll to bottom when new messages arrive or content changes
const chatContainer = ref<HTMLElement>()

// Enhanced scroll management for streaming messages (AC-1.2-2)
watch([currentMessages, isStreaming, streamingContent], () => {
  nextTick(() => {
    if (chatContainer.value) {
      // Scroll to bottom smoothly for new messages or during streaming
      chatContainer.value.scrollTo({
        top: chatContainer.value.scrollHeight,
        behavior: 'smooth',
      })
    }
  })
}, { deep: true })

// Helper methods for input state
function getInputPlaceholder() {
  if (!isConnected.value) {
    return 'Connecting to server...'
  }
  if (!isConfigured.value) {
    return 'Configure agents to start chatting...'
  }
  if (isStreaming.value) {
    return 'Please wait for the assistant to finish...'
  }
  return 'Type your message...'
}

function getSendButtonTooltip() {
  if (!messageInput.value.trim()) {
    return 'Enter a message to send'
  }
  if (!isConnected.value) {
    return 'Connecting to server...'
  }
  if (!isConfigured.value) {
    return 'Configure at least one agent first'
  }
  if (isStreaming.value) {
    return 'Please wait for the assistant to finish responding'
  }
  return 'Send message'
}

// Initialization is handled by the composables automatically
</script>

<template>
  <div class="bg-muted-white dark:bg-muted-900 h-screen w-full overflow-hidden">
    <!-- Sidebar -->
    <div
      class="bg-muted-100 dark:bg-muted-950 fixed start-0 top-0 z-40 flex h-full w-[280px] flex-col transition-transform duration-300"
      :class="[
        isOpen ? 'lg:translate-x-0' : 'lg:-translate-x-full!',
        isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0',
      ]"
    >
      <!-- Sidebar header -->
      <div class="flex h-16 shrink-0 items-center gap-2 px-4">
        <div class="lg:hidden">
          <button
            type="button"
            class="nui-mask nui-mask-blob hover:bg-muted-200 dark:hover:bg-muted-800 flex size-10 items-center justify-center transition-colors duration-300"
            @click="isMobileOpen = false"
          >
            <Icon name="lucide:x" class="size-5" />
          </button>
        </div>
        <BaseTooltip content="Close Sidebar">
          <button
            type="button"
            class="nui-mask nui-mask-blob hover:bg-muted-200 dark:hover:bg-muted-800 hidden lg:flex size-10 items-center justify-center transition-colors duration-300"
            @click="isOpen = false"
          >
            <Icon name="solar:siderbar-linear" class="size-5" />
          </button>
        </BaseTooltip>
        <BaseTooltip content="New Chat">
          <button
            type="button"
            class="nui-mask nui-mask-blob hover:bg-muted-200 dark:hover:bg-muted-800 flex size-10 items-center justify-center transition-colors duration-300"
            @click="handleNewChat"
          >
            <Icon name="lucide:plus" class="size-5" />
          </button>
        </BaseTooltip>
        <BaseTooltip content="Create Agent">
          <button
            type="button"
            class="nui-mask nui-mask-blob hover:bg-muted-200 dark:hover:bg-muted-800 flex size-10 items-center justify-center transition-colors duration-300"
            @click="() => navigateTo('/agent-wizard')"
          >
            <Icon name="lucide:user-plus" class="size-5" />
          </button>
        </BaseTooltip>
      </div>

      <!-- Chat modes -->
      <div class="px-4 pb-4">
        <div class="grid grid-cols-2 gap-2">
          <button
            type="button"
            class="flex items-center gap-2 rounded-lg border p-2 transition-colors"
            :class="[
              chatMode === 'single'
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                : 'border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800',
            ]"
            @click="chatMode = 'single'"
          >
            <Icon name="lucide:user" class="size-4" />
            <span class="text-xs font-medium">Single</span>
          </button>
          <button
            type="button"
            class="flex items-center gap-2 rounded-lg border p-2 transition-colors"
            :class="[
              chatMode === 'multi'
                ? 'border-success-500 bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-300'
                : 'border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800',
            ]"
            @click="chatMode = 'multi'"
          >
            <Icon name="lucide:users" class="size-4" />
            <span class="text-xs font-medium">Multi</span>
          </button>
        </div>
      </div>

      <!-- Conversation list -->
      <div class="nui-slimscroll grow overflow-y-auto px-4">
        <!-- Current chat indicator -->
        <div v-if="hasActiveChat && !showingConversation" class="mb-4">
          <div class="flex items-center gap-2 rounded-lg bg-primary-50 dark:bg-primary-900/20 p-3 border border-primary-200 dark:border-primary-800">
            <Icon name="lucide:message-circle" class="size-4 text-primary-600" />
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-primary-800 dark:text-primary-200">
                Current Chat
              </div>
              <div class="text-xs text-primary-600 dark:text-primary-400">
                {{ currentConversation?.title }}
              </div>
            </div>
            <button
              type="button"
              class="flex size-6 items-center justify-center rounded-md hover:bg-primary-100 dark:hover:bg-primary-800 transition-colors"
              @click="showingConversation = null"
            >
              <Icon name="lucide:arrow-right" class="size-3 text-primary-600" />
            </button>
          </div>
        </div>

        <!-- Loading state -->
        <div v-if="conversationsLoading" class="flex justify-center py-8">
          <div class="flex items-center gap-2 text-muted-500">
            <Icon name="lucide:loader-2" class="size-4 animate-spin" />
            <span class="text-sm">Loading conversations...</span>
          </div>
        </div>

        <!-- Conversation history -->
        <div v-else-if="!conversationsLoading">
          <!-- Today -->
          <div v-if="conversationsByPeriod.today.length > 0" class="mb-6">
            <BaseParagraph
              weight="semibold"
              size="xs"
              class="text-muted-900 dark:text-muted-100 px-3 uppercase tracking-wide mb-3"
            >
              Today
            </BaseParagraph>
            <ul class="space-y-1">
              <li
                v-for="conversation in conversationsByPeriod.today"
                :key="conversation.id"
                class="group/item relative"
              >
                <button
                  type="button"
                  class="text-muted-600 group-hover/item:text-muted-900 dark:text-muted-500 dark:group-hover/item:text-muted-200 group-hover/item:bg-muted-200 dark:group-hover/item:bg-muted-800 flex w-full items-center gap-2 rounded-xl px-3 py-2 text-start transition-colors duration-300"
                  :class="[
                    showingConversation === conversation.id
                      ? 'bg-muted-200 dark:bg-muted-800 text-muted-900 dark:text-muted-100'
                      : '',
                  ]"
                  @click="handleSelectConversation(conversation.id)"
                >
                  <div
                    class="flex size-6 shrink-0 items-center justify-center rounded-md"
                    :class="conversation.mode === 'multi' ? 'bg-success-500' : 'bg-primary-500'"
                  >
                    <Icon
                      :name="conversation.mode === 'multi' ? 'lucide:users' : 'lucide:user'"
                      class="size-3 text-white"
                    />
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="font-medium text-sm line-clamp-1">
                      {{ conversation.title }}
                    </div>
                    <div class="text-xs opacity-75 line-clamp-1">
                      {{ conversation.lastMessage }}
                    </div>
                  </div>
                </button>
                <!-- Conversation actions dropdown -->
                <div class="absolute end-1 top-1">
                  <BaseDropdown
                    rounded="lg"
                    label="Dropdown"
                    orientation="start"
                  >
                    <template #button>
                      <button type="button" class="pointer-events-none flex size-8 items-center justify-center rounded-lg p-1 opacity-0 transition-all duration-300 group-hover/item:pointer-events-auto group-hover/item:opacity-100">
                        <Icon name="lucide:more-horizontal" class="size-4" />
                      </button>
                    </template>
                    <BaseDropdownItem title="Share" text="Share this chat" rounded="sm" @click="() => {}">
                      <template #start>
                        <Icon name="lucide:share" class="me-2 block size-4" />
                      </template>
                    </BaseDropdownItem>
                    <BaseDropdownItem title="Rename" text="Rename this chat" rounded="sm" @click="() => {}">
                      <template #start>
                        <Icon name="lucide:edit-3" class="me-2 block size-4" />
                      </template>
                    </BaseDropdownItem>
                    <BaseDropdownItem title="Archive" text="Archive this chat" rounded="sm" @click="() => {}">
                      <template #start>
                        <Icon name="lucide:archive" class="me-2 block size-4" />
                      </template>
                    </BaseDropdownItem>
                  </BaseDropdown>
                </div>
              </li>
            </ul>
          </div>

          <!-- Last 7 days -->
          <div v-if="conversationsByPeriod.lastWeek.length > 0" class="mb-6">
            <BaseParagraph
              weight="semibold"
              size="xs"
              class="text-muted-900 dark:text-muted-100 px-3 uppercase tracking-wide mb-3"
            >
              Last 7 days
            </BaseParagraph>
            <ul class="space-y-1">
              <li
                v-for="conversation in conversationsByPeriod.lastWeek"
                :key="conversation.id"
                class="group/item relative"
              >
                <button
                  type="button"
                  class="text-muted-600 group-hover/item:text-muted-900 dark:text-muted-500 dark:group-hover/item:text-muted-200 group-hover/item:bg-muted-200 dark:group-hover/item:bg-muted-800 flex w-full items-center gap-2 rounded-xl px-3 py-2 text-start transition-colors duration-300"
                  :class="[
                    showingConversation === conversation.id
                      ? 'bg-muted-200 dark:bg-muted-800 text-muted-900 dark:text-muted-100'
                      : '',
                  ]"
                  @click="handleSelectConversation(conversation.id)"
                >
                  <div
                    class="flex size-6 shrink-0 items-center justify-center rounded-md"
                    :class="conversation.mode === 'multi' ? 'bg-success-500' : 'bg-primary-500'"
                  >
                    <Icon
                      :name="conversation.mode === 'multi' ? 'lucide:users' : 'lucide:user'"
                      class="size-3 text-white"
                    />
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="font-medium text-sm line-clamp-1">
                      {{ conversation.title }}
                    </div>
                    <div class="text-xs opacity-75 line-clamp-1">
                      {{ conversation.lastMessage }}
                    </div>
                  </div>
                </button>
              </li>
            </ul>
          </div>

          <!-- Last 30 days -->
          <div v-if="conversationsByPeriod.lastMonth.length > 0" class="mb-6">
            <BaseParagraph
              weight="semibold"
              size="xs"
              class="text-muted-900 dark:text-muted-100 px-3 uppercase tracking-wide mb-3"
            >
              Last 30 days
            </BaseParagraph>
            <ul class="space-y-1">
              <li
                v-for="conversation in conversationsByPeriod.lastMonth"
                :key="conversation.id"
                class="group/item relative"
              >
                <button
                  type="button"
                  class="text-muted-600 group-hover/item:text-muted-900 dark:text-muted-500 dark:group-hover/item:text-muted-200 group-hover/item:bg-muted-200 dark:group-hover/item:bg-muted-800 flex w-full items-center gap-2 rounded-xl px-3 py-2 text-start transition-colors duration-300"
                  :class="[
                    showingConversation === conversation.id
                      ? 'bg-muted-200 dark:bg-muted-800 text-muted-900 dark:text-muted-100'
                      : '',
                  ]"
                  @click="handleSelectConversation(conversation.id)"
                >
                  <div
                    class="flex size-6 shrink-0 items-center justify-center rounded-md"
                    :class="conversation.mode === 'multi' ? 'bg-success-500' : 'bg-primary-500'"
                  >
                    <Icon
                      :name="conversation.mode === 'multi' ? 'lucide:users' : 'lucide:user'"
                      class="size-3 text-white"
                    />
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="font-medium text-sm line-clamp-1">
                      {{ conversation.title }}
                    </div>
                    <div class="text-xs opacity-75 line-clamp-1">
                      {{ conversation.lastMessage }}
                    </div>
                  </div>
                </button>
              </li>
            </ul>
          </div>

          <!-- Older -->
          <div v-if="conversationsByPeriod.older.length > 0">
            <BaseParagraph
              weight="semibold"
              size="xs"
              class="text-muted-900 dark:text-muted-100 px-3 uppercase tracking-wide mb-3"
            >
              Older
            </BaseParagraph>
            <ul class="space-y-1">
              <li
                v-for="conversation in conversationsByPeriod.older"
                :key="conversation.id"
                class="group/item relative"
              >
                <button
                  type="button"
                  class="text-muted-600 group-hover/item:text-muted-900 dark:text-muted-500 dark:group-hover/item:text-muted-200 group-hover/item:bg-muted-200 dark:group-hover/item:bg-muted-800 flex w-full items-center gap-2 rounded-xl px-3 py-2 text-start transition-colors duration-300"
                  :class="[
                    showingConversation === conversation.id
                      ? 'bg-muted-200 dark:bg-muted-800 text-muted-900 dark:text-muted-100'
                      : '',
                  ]"
                  @click="handleSelectConversation(conversation.id)"
                >
                  <div
                    class="flex size-6 shrink-0 items-center justify-center rounded-md"
                    :class="conversation.mode === 'multi' ? 'bg-success-500' : 'bg-primary-500'"
                  >
                    <Icon
                      :name="conversation.mode === 'multi' ? 'lucide:users' : 'lucide:user'"
                      class="size-3 text-white"
                    />
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="font-medium text-sm line-clamp-1">
                      {{ conversation.title }}
                    </div>
                    <div class="text-xs opacity-75 line-clamp-1">
                      {{ conversation.lastMessage }}
                    </div>
                  </div>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Sidebar footer -->
      <div class="border-t border-muted-200 dark:border-muted-800 p-4">
        <div class="flex items-center gap-2 mb-3">
          <div
            class="flex size-2 rounded-full"
            :class="[
              connectionStatus === 'connected' ? 'bg-success-500'
              : connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse'
                : connectionStatus === 'reconnecting' ? 'bg-orange-500 animate-pulse'
                  : connectionStatus === 'error' ? 'bg-destructive-500'
                    : 'bg-muted-400',
            ]"
          />
          <Icon
            name="lucide:heart"
            class="size-3 text-rose-500"
            :class="heartbeatPulse ? 'animate-pulse' : 'opacity-30'"
            aria-label="heartbeat"
          />
          <div class="flex-1 min-w-0">
            <BaseText size="xs" class="text-muted-500 capitalize">
              {{ connectionStatus === 'connected' ? 'Connected'
                : connectionStatus === 'connecting' ? 'Connecting...'
                  : connectionStatus === 'reconnecting' ? 'Reconnecting...'
                    : connectionStatus === 'error' ? 'Connection Error'
                      : 'Disconnected' }}
            </BaseText>
          </div>
        </div>
        <BaseButton
          variant="outline"
          size="sm"
          class="w-full"
          @click="isConfigDrawerOpen = true"
        >
          <Icon name="lucide:settings" class="size-4 me-2" />
          Configure Chat
        </BaseButton>
      </div>
    </div>
    <!-- Main content -->
    <div class="h-screen transition-[width,margin] duration-300 overflow-hidden" :class="isOpen ? 'w-full lg:w-[calc(100%_-_280px)] lg:ms-[280px]' : 'w-full ms-0'">
      <div class="flex h-screen flex-col">
        <!-- Navigation -->
        <div class="flex h-16 shrink-0 items-center justify-between border-b border-muted-200 dark:border-muted-800 bg-white dark:bg-muted-950 px-6">
          <div class="flex items-center gap-2">
            <button
              type="button"
              class="nui-mask nui-mask-blob hover:bg-muted-100 dark:hover:bg-muted-800 flex size-10 items-center justify-center transition-colors duration-300 lg:hidden"
              @click="isMobileOpen = true"
            >
              <Icon name="lucide:menu" class="size-5" />
            </button>

            <!-- Chat title and mode indicator -->
            <div v-if="hasActiveChat && !showingConversation" class="flex-1">
              <div class="flex items-center gap-3">
                <div class="flex items-center gap-2">
                  <div
                    class="flex size-8 items-center justify-center rounded-lg"
                    :class="currentConversation?.mode === 'multi' ? 'bg-success-500' : 'bg-primary-500'"
                  >
                    <Icon
                      :name="currentConversation?.mode === 'multi' ? 'lucide:users' : 'lucide:user'"
                      class="size-4 text-white"
                    />
                  </div>
                  <div>
                    <BaseText weight="medium">
                      {{ currentConversation?.title }}
                    </BaseText>
                    <div class="flex items-center gap-1">
                      <BaseBadge
                        :color="currentConversation?.mode === 'multi' ? 'success' : 'primary'"
                        size="xs"
                        variant="pastel"
                      >
                        {{ currentConversation?.mode === 'multi' ? 'Multi-agent' : 'Single agent' }}
                      </BaseBadge>
                      <BaseBadge
                        v-if="isStreaming"
                        color="warning"
                        size="xs"
                        variant="pastel"
                      >
                        Generating...
                      </BaseBadge>
                    </div>
                  </div>
                </div>

                <!-- Multi-agent controls -->
                <div class="ms-auto me-4">
                  <MultiAgentControls
                    :is-multi-agent="currentConversation?.mode === 'multi'"
                    :agent-count="currentConversation?.agents?.length || 0"
                  />
                </div>
              </div>
            </div>

            <BaseTooltip content="Open Sidebar">
              <button
                v-if="!isOpen"
                type="button"
                class="nui-mask nui-mask-blob hover:bg-muted-100 dark:hover:bg-muted-800 flex size-10 items-center justify-center transition-colors duration-300"
                @click="isOpen = true"
              >
                <Icon name="solar:siderbar-linear" class="size-5" />
              </button>
            </BaseTooltip>
          </div>

          <div class="flex items-center gap-2">
            <!-- Enhanced Chat Toggle -->
            <BaseTooltip :content="enhancedChatEnabled ? 'Disable enhanced chat rendering' : 'Enable enhanced chat rendering with animations'">
              <button
                type="button"
                class="flex size-9 items-center justify-center rounded-lg border transition-all duration-200"
                :class="[
                  enhancedChatEnabled
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-600'
                    : 'border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800 text-muted-600 dark:text-muted-400',
                ]"
                @click="enhancedChatEnabled = !enhancedChatEnabled"
              >
                <Icon
                  :name="enhancedChatEnabled ? 'lucide:sparkles' : 'lucide:message-square'"
                  class="size-4"
                />
              </button>
            </BaseTooltip>

            <div class="scale-90">
              <BaseThemeSwitch />
            </div>
            <div>
              <BaseButton size="sm" rounded="full">
                <Icon name="lucide:share-2" class="size-4" />
                <span>Share</span>
              </BaseButton>
            </div>
            <AccountMenu horizontal />
          </div>
        </div>

        <!-- Chat content area -->
        <div class="flex-1 overflow-hidden">
          <!-- Empty state -->
          <div
            v-if="!hasActiveChat && !showingConversation"
            class="flex h-full flex-col items-center justify-center px-6"
          >
            <!-- Logo section -->
            <div class="dark:bg-muted-950 border-muted-300 dark:border-muted-800 mb-8 flex size-16 items-center justify-center rounded-xl border bg-white">
              <div class="bg-muted-100 dark:bg-muted-800 flex size-14 items-center justify-center rounded-lg">
                <TairoLogo class="text-primary-500 size-8" />
              </div>
            </div>

            <!-- Welcome message -->
            <div class="text-center mb-8">
              <BaseHeading size="xl" weight="semibold" class="mb-2">
                Welcome to Tairo AI
              </BaseHeading>
              <BaseText class="text-muted-500 max-w-md">
                Configure your agents and start a conversation. Choose between single-agent or multi-agent collaborative chats.
              </BaseText>
            </div>

            <!-- Quick start actions -->
            <div class="flex flex-col gap-4 mb-8 max-w-md">
              <div class="flex gap-4">
                <BaseButton
                  size="lg"
                  variant="solid"
                  color="primary"
                  class="flex-1"
                  @click="() => { chatMode = 'single'; handleNewChat() }"
                >
                  <Icon name="lucide:user" class="size-5 me-2" />
                  Start Single Agent Chat
                </BaseButton>
                <BaseButton
                  size="lg"
                  variant="outline"
                  color="success"
                  class="flex-1"
                  @click="() => { chatMode = 'multi'; handleNewChat() }"
                >
                  <Icon name="lucide:users" class="size-5 me-2" />
                  Start Multi-Agent Chat
                </BaseButton>
              </div>

              <!-- Create custom agent option -->
              <div class="text-center">
                <BaseText size="sm" class="text-muted-500 mb-2">
                  Or create your own custom agent
                </BaseText>
                <BaseButton
                  size="md"
                  variant="pastel"
                  color="primary"
                  @click="() => navigateTo('/agent-wizard')"
                >
                  <Icon name="lucide:wand-2" class="size-4 me-2" />
                  Create Custom Agent
                </BaseButton>
              </div>
            </div>

            <!-- Suggested prompts -->
            <WidgetsAiPromptSuggestion
              :data="{
                items: suggestedPrompts,
                columns: { base: 2, md: 4 },
                clickable: true,
              }"
              @select="onPromptSelect"
            />
          </div>

          <!-- Active chat -->
          <div
            v-else
            class="flex h-full flex-col"
          >
            <!-- Historical session header -->
            <div v-if="showingConversation && rehydratedSession" class="border-b border-muted-200 dark:border-muted-800 px-6 py-4 bg-muted-50 dark:bg-muted-900/50">
              <div class="flex items-center gap-3 mb-3">
                <div
                  class="flex size-8 items-center justify-center rounded-lg"
                  :class="rehydratedSession.mode === 'multi' ? 'bg-success-500' : 'bg-primary-500'"
                >
                  <Icon
                    :name="rehydratedSession.mode === 'multi' ? 'lucide:users' : 'lucide:user'"
                    class="size-4 text-white"
                  />
                </div>
                <div class="flex-1">
                  <BaseText weight="medium" class="mb-1">
                    {{ rehydratedSession.title }}
                  </BaseText>
                  <div class="flex items-center gap-2 flex-wrap">
                    <BaseBadge
                      :color="rehydratedSession.mode === 'multi' ? 'success' : 'primary'"
                      size="xs"
                      variant="pastel"
                    >
                      {{ rehydratedSession.mode === 'multi' ? 'Multi-agent' : 'Single agent' }}
                    </BaseBadge>
                    <BaseBadge color="muted" size="xs" variant="outline">
                      {{ rehydratedMessages.length }} messages
                    </BaseBadge>
                    <BaseBadge color="muted" size="xs" variant="outline">
                      {{ new Date(rehydratedSession.createdAt).toLocaleDateString() }}
                    </BaseBadge>
                  </div>
                </div>
                <BaseButton
                  size="sm"
                  variant="outline"
                  @click="showingConversation = null; clearSession()"
                >
                  Back to Current
                </BaseButton>
              </div>

              <!-- Agent chips for multi-agent sessions -->
              <div v-if="rehydratedSession.mode === 'multi' && rehydratedSession.agents.length > 0" class="flex flex-wrap gap-2">
                <div
                  v-for="agent in rehydratedSession.agents"
                  :key="agent.id"
                  class="flex items-center gap-2 px-3 py-1.5 bg-white dark:bg-muted-800 rounded-full border border-muted-200 dark:border-muted-700"
                >
                  <span class="text-sm">{{ agent.avatar || '🤖' }}</span>
                  <BaseText size="xs" weight="medium">
                    {{ agent.name }}
                  </BaseText>
                  <BaseBadge
                    :color="agent.provider === 'openai' ? 'success' : agent.provider === 'anthropic' ? 'primary' : 'info'"
                    size="xs"
                    variant="dot"
                  >
                    {{ agent.provider }}
                  </BaseBadge>
                </div>
              </div>
            </div>

            <!-- Messages area -->
            <div
              ref="chatContainer"
              class="flex-1 overflow-y-auto px-6 py-4 nui-slimscroll"
            >
              <!-- Load more messages button for historical sessions -->
              <div v-if="showingConversation && hasMoreMessages" class="mb-4 text-center">
                <BaseButton
                  :loading="loadingMoreMessages"
                  variant="outline"
                  size="sm"
                  @click="loadMoreMessages"
                >
                  <Icon name="lucide:chevron-up" class="size-4 me-2" />
                  Load older messages
                </BaseButton>
              </div>
              <!-- Connection status -->
              <div
                v-if="!isConnected"
                class="mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-900/20"
              >
                <div class="flex items-center gap-2">
                  <Icon
                    name="lucide:wifi-off"
                    class="size-4 text-yellow-600 dark:text-yellow-400"
                  />
                  <BaseText size="sm" class="text-yellow-800 dark:text-yellow-200">
                    {{ connectionStatus === 'connecting' ? 'Connecting...'
                      : connectionStatus === 'reconnecting' ? 'Reconnecting...'
                        : 'Disconnected - trying to reconnect' }}
                  </BaseText>
                </div>
              </div>

              <!-- Error display -->
              <div
                v-if="chatError"
                class="mb-4 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20"
              >
                <div class="flex items-start gap-2">
                  <Icon name="lucide:alert-circle" class="size-4 text-red-600 dark:text-red-400 mt-0.5" />
                  <div class="flex-1">
                    <BaseText size="sm" class="text-red-800 dark:text-red-200 mb-2">
                      {{ chatError }}
                    </BaseText>
                    <BaseButton size="xs" variant="outline" @click="clearError">
                      Dismiss
                    </BaseButton>
                  </div>
                </div>
              </div>

              <!-- Messages -->
              <div class="space-y-4">
                <ChatMessage
                  v-for="(message, index) in currentMessages"
                  :key="message.id"
                  :message="message"
                  :is-last="index === currentMessages.length - 1"
                  :use-enhanced-rendering="enhancedChatEnabled"
                  :animate="true"
                  :compact="false"
                  @copy="copyToClipboard"
                  @retry="() => {}"
                  @delete="() => {}"
                  @react="(messageId, emoji) => console.log('React:', messageId, emoji)"
                />
              </div>

              <!-- Enhanced typing indicator with agent context (AC-1.2-4) -->
              <div
                v-if="isStreaming && isTyping"
                class="flex items-center gap-3 px-4 py-2 animate-in fade-in-0 slide-in-from-left-2"
              >
                <div class="flex size-8 items-center justify-center rounded-full bg-muted-200 dark:bg-muted-700">
                  <Icon name="lucide:cpu" class="size-4" />
                </div>
                <div class="flex items-center gap-2">
                  <BaseText size="sm" class="text-muted-600">
                    <span v-if="typingAgentId">
                      {{ messages.find(m => m.agentId === typingAgentId)?.agentName || 'AI' }} is responding...
                    </span>
                    <span v-else>
                      AI is thinking...
                    </span>
                  </BaseText>
                  <div class="flex gap-1">
                    <div class="size-1 rounded-full bg-current opacity-60 animate-pulse" />
                    <div class="size-1 rounded-full bg-current opacity-40 animate-pulse" style="animation-delay: 0.2s" />
                    <div class="size-1 rounded-full bg-current opacity-20 animate-pulse" style="animation-delay: 0.4s" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced Chat Input -->
          <div
            class="sticky bottom-0 z-50 border-t border-muted-200/50 dark:border-muted-800/50 backdrop-blur-sm bg-white/80 dark:bg-muted-950/80 supports-backdrop-blur:bg-white/60 supports-backdrop-blur:dark:bg-muted-950/60 px-6 py-4"
          >
            <div class="space-y-3">
              <!-- Multi/Single Agent Toggle -->
              <div v-if="isConfigured" class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                  <!-- Chat Mode Toggle -->
                  <div class="flex items-center gap-2 p-1 bg-muted-100 dark:bg-muted-800 rounded-lg">
                    <button
                      type="button"
                      class="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors"
                      :class="[
                        chatMode === 'single'
                          ? 'bg-white dark:bg-muted-700 text-primary-600 dark:text-primary-400 shadow-sm'
                          : 'text-muted-600 dark:text-muted-400 hover:text-muted-800 dark:hover:text-muted-200',
                      ]"
                      @click="chatMode = 'single'"
                    >
                      <Icon name="lucide:user" class="h-3 w-3" />
                      Single
                    </button>
                    <button
                      type="button"
                      class="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors"
                      :class="[
                        chatMode === 'multi'
                          ? 'bg-white dark:bg-muted-700 text-success-600 dark:text-success-400 shadow-sm'
                          : 'text-muted-600 dark:text-muted-400 hover:text-muted-800 dark:hover:text-muted-200',
                      ]"
                      @click="chatMode = 'multi'"
                    >
                      <Icon name="lucide:users" class="h-3 w-3" />
                      Multi
                    </button>
                  </div>

                  <!-- Agent Display -->
                  <div v-if="selectedAgents.length > 0" class="flex items-center gap-2">
                    <div
                      v-for="agent in selectedAgents.slice(0, 2)"
                      :key="agent.id"
                      class="flex items-center gap-1 px-2 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-md text-xs font-medium"
                    >
                      <Icon :name="agent.avatar || 'lucide:cpu'" class="h-3 w-3" />
                      {{ agent.name }}
                    </div>
                    <div v-if="selectedAgents.length > 2" class="text-xs text-muted-500">
                      +{{ selectedAgents.length - 2 }} more
                    </div>
                  </div>
                </div>

                <!-- Quick Actions -->
                <div class="flex items-center gap-2">
                  <!-- Fast Forward (Human-in-Loop Skip) -->
                  <BaseTooltip content="Skip human approval">
                    <button
                      type="button"
                      class="flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors"
                      :class="[
                        isEphemeral
                          ? 'text-warning-600 dark:text-warning-400'
                          : 'text-muted-500',
                      ]"
                      @click="isEphemeral = !isEphemeral"
                    >
                      <Icon name="lucide:fast-forward" class="h-4 w-4" />
                    </button>
                  </BaseTooltip>

                  <!-- Configuration -->
                  <BaseTooltip content="Configure agents">
                    <button
                      type="button"
                      class="flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors text-muted-500"
                      @click="isConfigDrawerOpen = true"
                    >
                      <Icon name="lucide:settings" class="h-4 w-4" />
                    </button>
                  </BaseTooltip>
                </div>
              </div>

              <!-- Enhanced Chat Input (Inline Implementation) -->
              <div class="enhanced-chat-input-inline">
                <div class="relative">
                  <!-- Main Input Area with Controls -->
                  <div class="flex items-end gap-3 p-3 bg-muted-50 dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded-xl focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-primary-500">
                    <!-- Left side controls -->
                    <div class="flex items-center gap-2">
                      <!-- Slash Commands -->
                      <BaseTooltip content="Slash commands">
                        <button
                          type="button"
                          class="flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted-200 dark:hover:bg-muted-800 transition-colors text-muted-500 hover:text-primary-500"
                          title="Use / for commands"
                        >
                          <span class="text-sm font-mono">/</span>
                        </button>
                      </BaseTooltip>

                      <!-- Fast Forward -->
                      <BaseTooltip content="Skip human approval">
                        <button
                          type="button"
                          class="flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted-200 dark:hover:bg-muted-800 transition-colors"
                          :class="[
                            isEphemeral
                              ? 'text-warning-600 dark:text-warning-400 bg-warning-50 dark:bg-warning-900/20'
                              : 'text-muted-500 hover:text-warning-500',
                          ]"
                          @click="isEphemeral = !isEphemeral"
                        >
                          <Icon name="lucide:fast-forward" class="h-4 w-4" />
                        </button>
                      </BaseTooltip>

                      <!-- Voice Input -->
                      <BaseTooltip content="Voice input (coming soon)">
                        <button
                          type="button"
                          class="flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted-200 dark:hover:bg-muted-800 transition-colors text-muted-500 hover:text-primary-500"
                          disabled
                        >
                          <Icon name="lucide:mic" class="h-4 w-4" />
                        </button>
                      </BaseTooltip>

                      <!-- Mentions -->
                      <BaseTooltip content="@ Mentions">
                        <button
                          type="button"
                          class="flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted-200 dark:hover:bg-muted-800 transition-colors text-muted-500 hover:text-primary-500"
                          title="Use @ to mention"
                        >
                          <Icon name="lucide:at-sign" class="h-4 w-4" />
                        </button>
                      </BaseTooltip>

                      <!-- Attachments -->
                      <BaseTooltip content="Attach files">
                        <button
                          type="button"
                          class="flex h-8 w-8 items-center justify-center rounded-md hover:bg-muted-200 dark:hover:bg-muted-800 transition-colors text-muted-500 hover:text-primary-500"
                          :disabled="!isConfigured"
                        >
                          <Icon name="lucide:paperclip" class="h-4 w-4" />
                        </button>
                      </BaseTooltip>
                    </div>

                    <!-- Growing Textarea -->
                    <div class="flex-1">
                      <textarea
                        ref="enhancedTextarea"
                        v-model="messageInput"
                        :placeholder="getInputPlaceholder()"
                        class="w-full resize-none border-0 bg-transparent text-sm placeholder-muted-400 focus:outline-none focus:ring-0 min-h-[20px] max-h-[200px]"
                        :disabled="!canSendMessage"
                        rows="1"
                        @keydown="handleEnhancedKeydown"
                        @input="adjustTextareaHeight"
                      />
                    </div>

                    <!-- Right side: Agent chips & Send -->
                    <div class="flex items-center gap-2">
                      <!-- Agent Model Display -->
                      <div v-if="selectedAgents.length > 0" class="flex items-center gap-1 px-2 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-md text-xs font-medium">
                        <Icon name="lucide:cpu" class="h-3 w-3" />
                        {{ selectedAgents[0]?.modelName || selectedAgents[0]?.model || 'AI' }}
                      </div>

                      <!-- Send Button -->
                      <button
                        type="button"
                        class="flex h-8 w-8 items-center justify-center rounded-md transition-colors"
                        :class="[
                          messageInput.trim() && canSendMessage
                            ? 'bg-primary-500 hover:bg-primary-600 text-white'
                            : 'bg-muted-300 dark:bg-muted-700 text-muted-500 cursor-not-allowed',
                        ]"
                        :disabled="!messageInput.trim() || !canSendMessage"
                        @click="handleSendMessage"
                      >
                        <Icon
                          v-if="isStreaming"
                          name="lucide:loader-2"
                          class="h-4 w-4 animate-spin"
                        />
                        <Icon
                          v-else
                          name="lucide:send"
                          class="h-4 w-4"
                        />
                      </button>
                    </div>
                  </div>

                  <!-- Character Count (when approaching limit) -->
                  <div
                    v-if="messageInput.length > 8000"
                    class="absolute -top-6 right-0 text-xs"
                    :class="{
                      'text-muted-500': messageInput.length <= 9000,
                      'text-warning-500': messageInput.length > 9000 && messageInput.length <= 9500,
                      'text-danger-500': messageInput.length > 9500,
                    }"
                  >
                    {{ messageInput.length.toLocaleString() }}/10,000
                  </div>
                </div>

                <!-- Help Text -->
                <div class="mt-2 flex items-center justify-between text-xs text-muted-400">
                  <div class="flex items-center gap-4">
                    <span>Press <kbd class="px-1 py-0.5 bg-muted-100 dark:bg-muted-700 rounded">Ctrl+Enter</kbd> to send</span>
                    <span class="opacity-75">Use <kbd>/</kbd> for commands, <kbd>@</kbd> to mention</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Space for better visual balance -->
            <div class="h-1" />
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Drawer -->
    <BaseModal
      :open="isConfigDrawerOpen"
      size="lg"
      @close="isConfigDrawerOpen = false"
    >
      <template #header>
        <div class="flex items-center gap-2">
          <Icon name="lucide:settings" class="size-5" />
          <BaseHeading size="lg">
            Configure AI Chat
          </BaseHeading>
        </div>
      </template>

      <div class="space-y-6">
        <!-- No providers warning -->
        <div v-if="availableProviders.providers.length === 0" class="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20">
          <div class="flex items-start gap-2">
            <Icon name="lucide:alert-triangle" class="size-5 text-yellow-600 dark:text-yellow-400" />
            <div>
              <BaseText weight="medium" class="text-yellow-800 dark:text-yellow-200">
                No AI Providers Connected
              </BaseText>
              <BaseText size="sm" class="text-yellow-700 dark:text-yellow-300 mt-1">
                Please configure at least one AI integration (OpenAI, Anthropic, Gemini, etc.) in your workspace settings before starting a chat.
              </BaseText>
              <NuxtLink to="/app/integrations" class="text-yellow-600 hover:text-yellow-500 text-sm font-medium mt-2 inline-block">
                Configure AI integrations →
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- Agent selector -->
        <div v-if="availableProviders.providers.length > 0">
          <div class="space-y-6">
            <!-- Chat mode selection -->
            <div class="space-y-3">
              <BaseText size="lg" weight="semibold" class="text-muted-800 dark:text-muted-200">
                Chat Mode
              </BaseText>
              <div class="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  class="flex items-center gap-3 rounded-lg border-2 p-4 transition-colors"
                  :class="[
                    chatMode === 'single'
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                      : 'border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800',
                  ]"
                  @click="chatMode = 'single'"
                >
                  <Icon name="lucide:user" class="size-5" />
                  <div class="text-left">
                    <div class="text-sm font-medium">
                      Single Agent
                    </div>
                    <div class="text-xs opacity-75">
                      Chat with one specialized agent
                    </div>
                  </div>
                </button>
                <button
                  type="button"
                  class="flex items-center gap-3 rounded-lg border-2 p-4 transition-colors"
                  :class="[
                    chatMode === 'multi'
                      ? 'border-success-500 bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-300'
                      : 'border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800',
                  ]"
                  @click="chatMode = 'multi'"
                >
                  <Icon name="lucide:users" class="size-5" />
                  <div class="text-left">
                    <div class="text-sm font-medium">
                      Multi-Agent
                    </div>
                    <div class="text-xs opacity-75">
                      Team of agents collaborating
                    </div>
                  </div>
                </button>
              </div>
            </div>

            <!-- Agent selection -->
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <BaseText size="lg" weight="semibold" class="text-muted-800 dark:text-muted-200">
                  Select Agents
                </BaseText>
                <BaseButton
                  variant="pastel"
                  color="primary"
                  size="sm"
                  @click="() => { isConfigDrawerOpen = false; navigateTo('/agent-wizard') }"
                >
                  <Icon name="lucide:plus" class="size-4 me-1" />
                  Create New
                </BaseButton>
              </div>

              <BaseText size="sm" class="text-muted-500 mb-4">
                {{ chatMode === 'single' ? 'Choose one agent for your chat' : 'Select 2-5 agents to work as a team' }}
              </BaseText>

              <!-- Built-in agents -->
              <div v-if="builtInAgents.length > 0" class="space-y-3">
                <BaseText size="sm" weight="medium" class="text-muted-600 dark:text-muted-400 uppercase tracking-wide">
                  Built-in Agents
                </BaseText>
                <div class="grid grid-cols-1 gap-3">
                  <div
                    v-for="agent in builtInAgents"
                    :key="agent.id"
                    class="relative"
                  >
                    <label
                      class="flex items-start gap-3 p-3 cursor-pointer rounded-lg border-2 transition-colors"
                      :class="[
                        selectedAgents.some(a => a.id === agent.id)
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-muted-200 dark:border-muted-700 hover:border-muted-300 dark:hover:border-muted-600',
                      ]"
                    >
                      <BaseCheckbox
                        :checked="selectedAgents.some(a => a.id === agent.id)"
                        @click="(checked: boolean) => handleAgentSelection(agent, checked)"
                      />
                      <div class="flex items-center gap-3 flex-1">
                        <div class="text-2xl">{{ agent.avatar }}</div>
                        <div class="flex-1">
                          <div class="font-medium text-sm">{{ agent.name }}</div>
                          <div class="text-xs text-muted-500">{{ agent.description }}</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Custom agents -->
              <div v-if="customAgents.length > 0" class="space-y-3">
                <BaseText size="sm" weight="medium" class="text-muted-600 dark:text-muted-400 uppercase tracking-wide">
                  Your Custom Agents
                </BaseText>
                <div class="grid grid-cols-1 gap-3">
                  <div
                    v-for="agent in customAgents"
                    :key="agent.id"
                    class="relative"
                  >
                    <label
                      class="flex items-start gap-3 p-3 cursor-pointer rounded-lg border-2 transition-colors"
                      :class="[
                        selectedAgents.some(a => a.id === agent.id)
                          ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                          : 'border-muted-200 dark:border-muted-700 hover:border-muted-300 dark:hover:border-muted-600',
                      ]"
                    >
                      <BaseCheckbox
                        :checked="selectedAgents.some(a => a.id === agent.id)"
                        @click="(checked: boolean) => handleAgentSelection(agent, checked)"
                      />
                      <div class="flex items-center gap-3 flex-1">
                        <BaseAvatar
                          :src="typeof agent.avatar === 'string' && agent.avatar.startsWith('data:') ? agent.avatar : undefined"
                          :text="typeof agent.avatar === 'string' && !agent.avatar.startsWith('data:') ? agent.avatar : (agent.name?.substring(0, 2) || 'AI')"
                          size="sm"
                        />
                        <div class="flex-1">
                          <div class="font-medium text-sm">{{ agent.name }}</div>
                          <div class="text-xs text-muted-500">{{ agent.description }}</div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Empty state -->
              <div v-if="allAgents.length === 0" class="text-center py-8">
                <Icon name="lucide:bot" class="size-12 text-muted-400 mx-auto mb-4" />
                <BaseText size="lg" weight="medium" class="text-muted-600 mb-2">
                  No agents available
                </BaseText>
                <BaseText size="sm" class="text-muted-500 mb-4">
                  Create your first agent to get started
                </BaseText>
                <BaseButton
                  variant="solid"
                  color="primary"
                  @click="() => { isConfigDrawerOpen = false; navigateTo('/agent-wizard') }"
                >
                  <Icon name="lucide:plus" class="size-4 me-2" />
                  Create Agent
                </BaseButton>
              </div>
            </div>

            <!-- Model selection for selected agents -->
            <div v-if="selectedAgents.length > 0" class="space-y-4">
              <BaseText size="lg" weight="semibold" class="text-muted-800 dark:text-muted-200">
                Model Configuration
              </BaseText>
              <BaseText size="sm" class="text-muted-500 mb-4">
                Select the AI model for each agent
              </BaseText>

              <div class="space-y-4">
                <div
                  v-for="agent in selectedAgents"
                  :key="agent.id"
                  class="rounded-lg border border-muted-200 dark:border-muted-700 p-4"
                >
                  <div class="flex items-center gap-3 mb-3">
                    <div class="text-lg">
                      {{ agent.avatar }}
                    </div>
                    <div class="flex-1">
                      <div class="font-medium text-sm">
                        {{ agent.name }}
                      </div>
                      <div class="text-xs text-muted-500">
                        {{ agent.provider }}
                      </div>
                    </div>
                  </div>

                  <div class="space-y-2">
                    <BaseLabel size="sm">
                      Model
                    </BaseLabel>
                    <TairoSelect
                      :model-value="agentModels[agent.id] || agent.modelName || ''"
                      placeholder="Select model"
                      size="sm"
                      @update:model-value="(value) => updateAgentModel(agent.id, value)"
                    >
                      <option
                        v-for="model in getAvailableModelsForProvider(agent.provider)"
                        :key="model.value"
                        :value="model.value"
                      >
                        {{ model.label }}
                      </option>
                    </TairoSelect>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-3">
          <BaseButton
            variant="outline"
            @click="isConfigDrawerOpen = false"
          >
            Cancel
          </BaseButton>
          <BaseButton
            :disabled="!isConfigured"
            @click="() => { isConfigDrawerOpen = false; handleNewChat() }"
          >
            Start Chat
          </BaseButton>
        </div>
      </template>
    </BaseModal>
    <!-- Overlay -->
    <div
      role="button"
      tabindex="0"
      class="bg-muted-950/80 fixed start-0 top-0 z-[39] size-full transition-opacity duration-300 lg:pointer-events-none! lg:opacity-0!"
      :class="isMobileOpen ? 'opacity-100 pointer-events-all' : 'opacity-0 pointer-events-none'"
      @click="isMobileOpen = false"
    />
  </div>
</template>
