<script setup lang="ts">
import type { Address } from '../types/auth'

definePageMeta({
  title: 'Company',
  preview: {
    title: 'Company',
    description: 'For company details',
    categories: ['layouts', 'lists'],
    src: '/img/screens/layouts-company.png',
    srcDark: '/img/screens/layouts-company-dark.png',
    order: 32,
    new: true,
  },
})

// Auth and workspace data
const { user, currentWorkspace, currentProfile, isLoading: isAuthLoading } = useAuth()
const { getByField, create, update, remove } = useDataApi()
const { showSuccess, showError } = useToaster()

// Address management using workspace addresses composable
const {
  getWorkspaceAddressesByType,
  createWorkspaceAddress,
  updateWorkspaceAddress,
  deleteWorkspaceAddress,
  isLoading: isAddressLoading,
  error: addressError,
} = useWorkspaceAddresses()

// Reactive state
const mailingAddresses = ref<Address[]>([])
const legalAddresses = ref<Address[]>([])
const showAddressModal = ref(false)
const editingAddress = ref<Address | null>(null)
const addressModalType = ref<'mailing' | 'legal'>('mailing')

// Inline editing state for workspace fields
type EditableField = 'name' | 'description' | 'email' | 'phone' | 'website'
const editingField = ref<EditableField | null>(null)
const editingValue = ref('')
const isSaving = ref<Record<string, boolean>>({})
const fieldErrors = ref<Record<string, string>>({})
const originalValues = ref<Record<string, string>>({})

// Loading state
const isLoading = computed(() => isAuthLoading.value || isAddressLoading.value)

// Computed properties for workspace display
const workspaceName = computed(() => currentWorkspace.value?.name || 'Company')
const workspaceDescription = computed(() => {
  return currentWorkspace.value?.description || `View ${workspaceName.value} details`
})

const workspaceLogo = computed(() => {
  return currentWorkspace.value?.logo_url || '/img/avatars/placeholder-workspace.svg'
})

// Company fields with workspace data
const companyFields = computed(() => ({
  legalName: currentWorkspace.value?.name || 'Not set',
  dba: currentWorkspace.value?.name || 'Not set',
  commonName: currentWorkspace.value?.name || 'Not set',
  email: '<EMAIL>', // TODO: Add to workspace model
  phone: '+****************', // TODO: Add to workspace model
  website: '@company', // TODO: Add to workspace model
}))

// Load data on mount
onMounted(async () => {
  try {
    await loadWorkspaceAddresses()
  }
  catch (error) {
    console.error('Error loading company details:', error)
  }
})

// Load workspace addresses function
async function loadWorkspaceAddresses() {
  if (!currentWorkspace.value?.id)
    return

  try {
    const [mailing, legal] = await Promise.all([
      getWorkspaceAddressesByType('mailing', { showErrorToast: false }),
      getWorkspaceAddressesByType('legal', { showErrorToast: false }),
    ])

    mailingAddresses.value = mailing
    legalAddresses.value = legal
  }
  catch (error) {
    console.error('Error loading workspace addresses:', error)
  }
}

// Address modal functions
function openAddressModal(type: 'mailing' | 'legal', address?: Address) {
  addressModalType.value = type
  editingAddress.value = address || null
  showAddressModal.value = true
}

function closeAddressModal() {
  showAddressModal.value = false
  editingAddress.value = null
}

async function handleAddressSubmit(addressData: any) {
  if (!currentWorkspace.value?.id)
    return

  try {
    if (editingAddress.value) {
      // Update existing address
      await update<Address>('addresses', editingAddress.value.id, addressData)
    }
    else {
      // Create new address
      await create<Address>('addresses', {
        ...addressData,
        type: addressModalType.value,
        workspace_ids: [currentWorkspace.value.id],
        profile_ids: [], // Workspace addresses are not tied to specific profiles
      }, {
        embed: ['name', 'address_line_1', 'address_line_2', 'city', 'state', 'country'],
      })
    }

    // Reload addresses
    await loadWorkspaceAddresses()
    closeAddressModal()
  }
  catch (error) {
    console.error('Error saving address:', error)
  }
}

async function handleDeleteAddress(address: Address) {
  if (confirm('Are you sure you want to delete this address?')) {
    try {
      await remove('addresses', address.id)
      await loadWorkspaceAddresses()
    }
    catch (error) {
      console.error('Error deleting address:', error)
    }
  }
}

// Get primary address for display
function getPrimaryAddress(addresses: Address[]): Address | null {
  return addresses.find(addr => addr.is_primary) || addresses[0] || null
}

const primaryMailingAddress = computed(() => getPrimaryAddress(mailingAddresses.value))
const primaryLegalAddress = computed(() => getPrimaryAddress(legalAddresses.value))

// Inline editing functions for workspace fields
function startEditField(fieldName: EditableField) {
  // Prevent multiple simultaneous edits
  if (editingField.value) {
    cancelEdit()
  }

  editingField.value = fieldName
  fieldErrors.value[fieldName] = ''

  // Set current value based on field type
  let currentValue = ''
  switch (fieldName) {
    case 'name':
      currentValue = currentWorkspace.value?.name || ''
      break
    case 'description':
      currentValue = currentWorkspace.value?.description || ''
      break
    case 'email':
      currentValue = currentWorkspace.value?.email || ''
      break
    case 'phone':
      currentValue = currentWorkspace.value?.phone || ''
      break
    case 'website':
      currentValue = currentWorkspace.value?.website || ''
      break
  }

  editingValue.value = currentValue
  originalValues.value[fieldName] = currentValue

  // Auto-focus the input after Vue updates the DOM
  nextTick(() => {
    const input = document.querySelector(`[data-field="${fieldName}"]`) as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  })
}

function cancelEdit() {
  if (!editingField.value)
    return

  const fieldName = editingField.value
  editingValue.value = originalValues.value[fieldName] || ''
  fieldErrors.value[fieldName] = ''
  editingField.value = null
}

function validateField(fieldName: EditableField, value: string): string | null {
  switch (fieldName) {
    case 'name':
      if (!value.trim())
        return 'Company name is required'
      if (value.length < 2)
        return 'Must be at least 2 characters'
      if (value.length > 100)
        return 'Must be less than 100 characters'
      break
    case 'email':
      if (value && value.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/
        if (!emailRegex.test(value))
          return 'Invalid email format'
      }
      break
    case 'phone':
      if (value && value.length < 10)
        return 'Phone number must be at least 10 digits'
      break
    case 'website':
      if (value && value.trim()) {
        try {
          new URL(value.startsWith('http') ? value : `https://${value}`)
        }
        catch {
          return 'Invalid website URL'
        }
      }
      break
  }
  return null
}

async function saveField(fieldName: EditableField, value: string) {
  if (!currentWorkspace.value)
    return

  // Validate the field
  const error = validateField(fieldName, value)
  if (error) {
    fieldErrors.value[fieldName] = error
    return
  }

  // Clear any existing error
  fieldErrors.value[fieldName] = ''

  // Set saving state
  isSaving.value[fieldName] = true

  try {
    // Prepare update data based on field type
    const updateData: any = {}

    switch (fieldName) {
      case 'name':
        updateData.name = value.trim()
        break
      case 'description':
        updateData.description = value.trim() || null
        break
      case 'email':
        updateData.email = value.trim() || null
        break
      case 'phone':
        updateData.phone = value.trim() || null
        break
      case 'website':
        updateData.website = value.trim() || null
        break
    }

    // Update the workspace using the data API
    await update('workspaces', currentWorkspace.value.id, updateData, {
      embed: ['name', 'description', 'email', 'phone', 'website'],
    })

    // Update local workspace state
    Object.assign(currentWorkspace.value, updateData)

    // Show success message
    showSuccess(`${fieldName.replace('_', ' ')} updated successfully`)

    // Exit edit mode
    editingField.value = null
  }
  catch (error: any) {
    console.error('Error updating field:', error)
    fieldErrors.value[fieldName] = error.message || 'Failed to update field'
    showError(`Failed to update ${fieldName.replace('_', ' ')}`)
  }
  finally {
    isSaving.value[fieldName] = false
  }
}

// Keyboard event handlers
function handleKeydown(event: KeyboardEvent, fieldName: EditableField) {
  if (event.key === 'Enter') {
    event.preventDefault()
    saveField(fieldName, editingValue.value)
  }
  else if (event.key === 'Escape') {
    event.preventDefault()
    cancelEdit()
  }
}

// Helper function to get field display value
function getFieldValue(fieldName: EditableField): string {
  switch (fieldName) {
    case 'name':
      return currentWorkspace.value?.name || 'Not set'
    case 'description':
      return currentWorkspace.value?.description || 'Not set'
    case 'email':
      return currentWorkspace.value?.email || 'Not set'
    case 'phone':
      return currentWorkspace.value?.phone || 'Not set'
    case 'website':
      return currentWorkspace.value?.website || 'Not set'
    default:
      return 'Not set'
  }
}
</script>

<template>
  <div class="w-full px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header -->
    <div class="border-muted-200 dark:border-muted-800 border-b pb-6">
      <div class="flex items-center gap-3">
        <div
          class="nui-mask nui-mask-blob bg-muted-200 dark:bg-muted-800 flex size-16 shrink-0 items-center justify-center"
        >
          <Icon name="solar:buildings-linear" class="size-7" />
        </div>
        <div>
          <BaseHeading
            as="h2"
            size="2xl"
            weight="medium"
            class="text-muted-800 dark:text-white"
          >
            {{ workspaceName }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
            {{ workspaceDescription }}
          </BaseParagraph>
        </div>
      </div>
    </div>
    <!-- Content -->
    <div class="space-y-10">
      <!-- Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              General info
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              Some information we need to know about your company, and to
              process legal matters.
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <BaseHeading
            as="h3"
            size="xs"
            weight="medium"
            class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
          >
            Company info
          </BaseHeading>
          <div
            class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
          >
            <!-- Legal name -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Legal name
                </BaseHeading>
                <button
                  v-if="editingField !== 'name'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('name')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'name'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="Company legal name"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('name') }}
              </BaseText>
            </div>

            <!-- Description -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Description
                </BaseHeading>
                <button
                  v-if="editingField !== 'description'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('description')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'description'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseTextarea
                    v-model="editingValue"
                    placeholder="Company description"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    rows="2"
                    class="flex-1"
                  />
                  <div class="flex flex-col gap-1">
                    <BaseButton
                      size="sm"
                      variant="primary"
                      :loading="isSaving[editingField]"
                      @click="saveField(editingField, editingValue)"
                    >
                      <Icon name="solar:check-linear" class="size-3" />
                    </BaseButton>
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      :disabled="isSaving[editingField]"
                      @click="cancelEdit"
                    >
                      <Icon name="solar:close-linear" class="size-3" />
                    </BaseButton>
                  </div>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('description') }}
              </BaseText>
            </div>
          </div>
        </div>
      </div>
      <!-- Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              Contact info
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              Some information we need to be able to contact you and send you
              documents or updates.
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <BaseHeading
            as="h3"
            size="xs"
            weight="medium"
            class="border-muted-200 dark:border-muted-800 text-muted-800 dark:text-muted-100 border-b px-4 pb-4"
          >
            Contact channels
          </BaseHeading>
          <div
            class="divide-muted-200 dark:divide-muted-800 flex flex-col divide-y"
          >
            <!-- Email address -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Email address
                </BaseHeading>
                <button
                  v-if="editingField !== 'email'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('email')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'email'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="<EMAIL>"
                    type="email"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('email') }}
              </BaseText>
            </div>

            <!-- Phone number -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Phone number
                </BaseHeading>
                <button
                  v-if="editingField !== 'phone'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('phone')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'phone'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="+****************"
                    type="tel"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('phone') }}
              </BaseText>
            </div>

            <!-- Website -->
            <div class="group p-4">
              <div class="flex items-center justify-between mb-2">
                <BaseHeading
                  as="h3"
                  size="xs"
                  weight="medium"
                  class="text-muted-400"
                >
                  Website
                </BaseHeading>
                <button
                  v-if="editingField !== 'website'"
                  class="text-muted-400 hover:text-primary-500 transition-colors p-1 rounded"
                  @click="startEditField('website')"
                >
                  <Icon name="solar:pen-2-linear" class="size-4" />
                </button>
              </div>

              <!-- Editing mode -->
              <div v-if="editingField === 'website'" class="space-y-2">
                <div class="flex items-center gap-2">
                  <BaseInput
                    v-model="editingValue"
                    placeholder="https://company.com"
                    type="url"
                    size="sm"
                    :data-field="editingField"
                    :disabled="isSaving[editingField]"
                    class="flex-1"
                    @keydown="handleKeydown($event, editingField)"
                  />
                  <BaseButton
                    size="sm"
                    variant="primary"
                    :loading="isSaving[editingField]"
                    @click="saveField(editingField, editingValue)"
                  >
                    <Icon name="solar:check-linear" class="size-3" />
                  </BaseButton>
                  <BaseButton
                    size="sm"
                    variant="ghost"
                    :disabled="isSaving[editingField]"
                    @click="cancelEdit"
                  >
                    <Icon name="solar:close-linear" class="size-3" />
                  </BaseButton>
                </div>
                <BaseText v-if="fieldErrors[editingField]" size="xs" class="text-red-500">
                  {{ fieldErrors[editingField] }}
                </BaseText>
              </div>

              <!-- Display mode -->
              <BaseText v-else size="sm" class="text-muted-600 dark:text-muted-300">
                {{ getFieldValue('website') }}
              </BaseText>
            </div>
          </div>
        </div>
      </div>
      <!-- Mailing Addresses Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              Mailing Addresses
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              We'll send physical cards as well as gifts and special offers to
              these addresses
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <div class="space-y-4">
            <!-- Loading State -->
            <div v-if="isLoading" class="flex items-center justify-center p-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
            </div>

            <!-- No Addresses State -->
            <div
              v-else-if="mailingAddresses.length === 0"
              class="border-muted-200 dark:border-muted-800 border rounded-lg p-6 text-center"
            >
              <Icon name="solar:home-linear" class="size-12 mx-auto text-muted-400 mb-3" />
              <BaseHeading as="h4" size="sm" class="text-muted-600 dark:text-muted-300 mb-2">
                No mailing addresses
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-4">
                Add a mailing address to receive physical items
              </BaseParagraph>
              <BaseButton
                size="sm"
                variant="primary"
                @click="openAddressModal('mailing')"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                Add Address
              </BaseButton>
            </div>

            <!-- Address Cards -->
            <template v-else>
              <div
                v-for="address in mailingAddresses"
                :key="address.id"
                class="border-muted-200 dark:border-muted-800 border rounded-lg px-4 pb-4 pt-3 group hover:border-muted-300 dark:hover:border-muted-700 transition-colors"
              >
                <div class="flex items-start justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <BaseHeading
                      as="h4"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ address.name || 'Mailing Address' }}
                    </BaseHeading>
                    <BaseBadge v-if="address.is_primary" size="xs" variant="primary">
                      Primary
                    </BaseBadge>
                  </div>
                  <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="openAddressModal('mailing', address)"
                    >
                      <Icon name="solar:pen-2-linear" class="size-4" />
                    </BaseButton>
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="handleDeleteAddress(address)"
                    >
                      <Icon name="solar:trash-bin-minimalistic-linear" class="size-4" />
                    </BaseButton>
                  </div>
                </div>
                <div class="space-y-1">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.address_line_1 }}
                  </BaseParagraph>
                  <BaseParagraph
                    v-if="address.address_line_2"
                    size="sm"
                    class="text-muted-600 dark:text-muted-300"
                  >
                    {{ address.address_line_2 }}
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.city }}<template v-if="address.state">
                      , {{ address.state }}
                    </template><template v-if="address.postal_code">
                      {{ address.postal_code }}
                    </template>
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.country }}
                  </BaseParagraph>
                </div>
              </div>

              <!-- Add New Address Button -->
              <BaseButton
                variant="outline"
                class="w-full"
                @click="openAddressModal('mailing')"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                Add Mailing Address
              </BaseButton>
            </template>
          </div>
        </div>
      </div>
      <!-- Legal Addresses Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              Legal Addresses
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              These are billing addresses for your cards and will appear on your
              documents
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <div class="space-y-4">
            <!-- Loading State -->
            <div v-if="isLoading" class="flex items-center justify-center p-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500" />
            </div>

            <!-- No Addresses State -->
            <div
              v-else-if="legalAddresses.length === 0"
              class="border-muted-200 dark:border-muted-800 border rounded-lg p-6 text-center"
            >
              <Icon name="solar:document-linear" class="size-12 mx-auto text-muted-400 mb-3" />
              <BaseHeading as="h4" size="sm" class="text-muted-600 dark:text-muted-300 mb-2">
                No legal addresses
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-4">
                Add a legal address for billing and documents
              </BaseParagraph>
              <BaseButton
                size="sm"
                variant="primary"
                @click="openAddressModal('legal')"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                Add Address
              </BaseButton>
            </div>

            <!-- Address Cards -->
            <template v-else>
              <div
                v-for="address in legalAddresses"
                :key="address.id"
                class="border-muted-200 dark:border-muted-800 border rounded-lg px-4 pb-4 pt-3 group hover:border-muted-300 dark:hover:border-muted-700 transition-colors"
              >
                <div class="flex items-start justify-between mb-2">
                  <div class="flex items-center gap-2">
                    <BaseHeading
                      as="h4"
                      size="sm"
                      weight="medium"
                      class="text-muted-800 dark:text-muted-100"
                    >
                      {{ address.name || 'Legal Address' }}
                    </BaseHeading>
                    <BaseBadge v-if="address.is_primary" size="xs" variant="primary">
                      Primary
                    </BaseBadge>
                  </div>
                  <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="openAddressModal('legal', address)"
                    >
                      <Icon name="solar:pen-2-linear" class="size-4" />
                    </BaseButton>
                    <BaseButton
                      size="sm"
                      variant="ghost"
                      @click="handleDeleteAddress(address)"
                    >
                      <Icon name="solar:trash-bin-minimalistic-linear" class="size-4" />
                    </BaseButton>
                  </div>
                </div>
                <div class="space-y-1">
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.address_line_1 }}
                  </BaseParagraph>
                  <BaseParagraph
                    v-if="address.address_line_2"
                    size="sm"
                    class="text-muted-600 dark:text-muted-300"
                  >
                    {{ address.address_line_2 }}
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.city }}<template v-if="address.state">
                      , {{ address.state }}
                    </template><template v-if="address.postal_code">
                      {{ address.postal_code }}
                    </template>
                  </BaseParagraph>
                  <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
                    {{ address.country }}
                  </BaseParagraph>
                </div>
              </div>

              <!-- Add New Address Button -->
              <BaseButton
                variant="outline"
                class="w-full"
                @click="openAddressModal('legal')"
              >
                <Icon name="lucide:plus" class="size-4 mr-2" />
                Add Legal Address
              </BaseButton>
            </template>
          </div>
        </div>
      </div>
      <!-- Section -->
      <div class="grid gap-8 pt-6 md:grid-cols-12">
        <!-- Column -->
        <div class="md:col-span-5">
          <div class="w-full max-w-xs">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              class="text-muted-800 dark:text-muted-100 mb-1"
            >
              Company Card
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              This is your company main credit card. You can use it to pay for
              any type of expenses
            </BaseParagraph>
          </div>
        </div>
        <!-- Column -->
        <div class="md:col-span-7">
          <div
            class="border-muted-200 dark:border-muted-800 border-b px-4 pb-4"
          >
            <CreditCardReal
              status="active"
              name="Acme LLC"
              number="•••• •••• •••• 7297"
              brand="visa"
              class="mx-0!"
            />
            <div class="mt-4">
              <LinkArrow to="#" label="Manage company card" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Address Form Modal -->
    <AddressFormModal
      :open="showAddressModal"
      :type="addressModalType"
      :address="editingAddress"
      @close="closeAddressModal"
      @submit="handleAddressSubmit"
    />
  </div>
</template>
