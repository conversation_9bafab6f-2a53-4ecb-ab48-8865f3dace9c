<script setup lang="ts">
import type {
  ProjectInfoCardData,
  ProjectOverviewCardData,
  ProjectTaskCardData,
  ProjectTeamCardData,
} from '~/types/widgets'
import { PanelTask } from '#components'

definePageMeta({
  title: 'Project Details',
  preview: [
    {
      title: 'Project details',
      description: 'For displaying fitness project details',
      categories: ['layouts', 'projects'],
      src: '/img/screens/layouts-projects-details.png',
      srcDark: '/img/screens/layouts-projects-details-dark.png',
      order: 72,
      params: {
        slug: 'health-and-fitness-dashboard',
      },
    },
    {
      title: 'Project details',
      description: 'For displaying banking project details',
      categories: ['layouts', 'projects'],
      src: '/img/screens/layouts-projects-details-2.png',
      srcDark: '/img/screens/layouts-projects-details-2-dark.png',
      order: 72,
      params: {
        slug: 'banking-and-finance-dashboard',
      },
    },
  ],
})

const { open } = usePanels()
const toasts = useNuiToasts()

const route = useRoute()
const slug = computed(() => route.params.slug)

const query = computed(() => {
  return {
    slug: slug.value,
  }
})

const { data, pending, error, refresh } = await useFetch(
  '/api/company/projects',
  {
    query,
  },
)

if (!data.value?.project) {
  await navigateTo('/layouts/projects')
}

const currentTask = ref()

async function openTaskPanel(id: number, tasks: any) {
  currentTask.value = tasks.find((task: any) => task.id === id)

  const [message] = await open(PanelTask, {
    task: currentTask,
  })

  if (message) {
    toasts.add({
      title: 'Comment added',
      description: message.comment,
    })
  }
}

// Computed properties for widget data
const projectOverviewData = computed((): ProjectOverviewCardData => ({
  name: data.value?.project?.name || '',
  category: data.value?.project?.category || '',
  description: data.value?.project?.description || '',
  image: data.value?.project?.image || '',
  owner: {
    name: data.value?.project?.owner?.name || '',
    avatar: data.value?.project?.owner?.avatar || '',
  },
  team: data.value?.project?.team || [],
  features: [
    {
      title: 'UI/UX Design',
      description: 'Designing a perfect user experience is in the scope of this project.',
      icon: 'solar:widget-4-bold-duotone',
    },
    {
      title: '1 Week Sprints',
      description: 'This project development iterations follow a 1 week sprint pattern.',
      icon: 'solar:alarm-bold-duotone',
    },
    {
      title: '3 Months',
      description: 'This project and all related tasks should completed within 3 months.',
      icon: 'solar:calendar-bold-duotone',
    },
    {
      title: 'Fixed',
      description: 'This project\'s budget planning is based on an imutable fixed bid.',
      icon: 'solar:wallet-2-bold-duotone',
    },
  ],
  files: data.value?.project?.files || [],
}))

const customerCardData = computed((): ProjectInfoCardData => ({
  title: 'Customer',
  type: 'customer',
  customer: {
    name: data.value?.project?.customer?.name || '',
    logo: data.value?.project?.customer?.logo || '',
    text: data.value?.project?.customer?.text || '',
    progress: data.value?.project?.completed || 0,
  },
}))

const toolsCardData = computed((): ProjectInfoCardData => ({
  title: 'Project Tools',
  type: 'list',
  items: data.value?.project?.tools?.map((tool: any) => ({
    name: tool.name,
    icon: tool.icon,
    description: tool.description,
  })) || [],
}))

const stacksCardData = computed((): ProjectInfoCardData => ({
  title: 'Project Stacks',
  type: 'list',
  items: data.value?.project?.stacks?.map((stack: any) => ({
    name: stack.name,
    icon: stack.icon,
    description: stack.description,
  })) || [],
}))

const ownerTeamCardData = computed((): ProjectTeamCardData => ({
  name: data.value?.project?.owner?.name || '',
  avatar: data.value?.project?.owner?.avatar || '',
  badge: data.value?.project?.owner?.badge,
  role: 'Project owner',
  bio: data.value?.project?.owner?.bio || '',
}))

const teamCardsData = computed((): ProjectTeamCardData[] =>
  data.value?.project?.team?.map((member: any) => ({
    name: member.tooltip,
    avatar: member.src,
    badge: member.badge,
    role: member.role,
    bio: member.bio,
  })) || [],
)

const taskCardsData = computed((): (ProjectTaskCardData & { id: number })[] =>
  data.value?.project?.tasks?.map((task: any) => ({
    id: task.id,
    name: task.name,
    description: task.description,
    status: task.status,
    completion: task.completion,
    assignee: task.assignee,
    filesCount: task.files?.length || 0,
    commentsCount: task.comments?.length || 0,
  })) || [],
)
</script>

<template>
  <div class="relative px-4 md:px-6 lg:px-8 pb-20">
    <div v-if="data?.project === undefined">
      <BasePlaceholderPage
        title="Project not found"
        subtitle="We couldn't find a project matching this namespace."
      >
        <template #image>
          <img
            class="block dark:hidden"
            src="/img/illustrations/placeholders/flat/placeholder-projects.svg"
            alt="Placeholder image"
          >
          <img
            class="hidden dark:block"
            src="/img/illustrations/placeholders/flat/placeholder-projects-dark.svg"
            alt="Placeholder image"
          >
        </template>
      </BasePlaceholderPage>
    </div>
    <div v-else class="h-full">
      <BaseTabs
        default-value="overview"
        type="box"
        :tabs="[
          {
            label: 'Overview',
            value: 'overview',
          },
          {
            label: 'Team',
            value: 'team',
          },
          {
            label: 'Tasks',
            value: 'tasks',
          },
        ]"
      >
        <!-- Overview -->
        <BaseTabsContent value="overview">
          <div class="grid grid-cols-12 gap-4">
            <div class="col-span-12 lg:col-span-8">
              <WidgetsProjectsOverviewCard :data="projectOverviewData" />
            </div>
            <div class="col-span-12 lg:col-span-4">
              <div class="space-y-4">
                <!-- Actions -->
                <div class="flex items-center justify-end gap-3">
                  <BaseButton
                    rounded="md"
                    :to="`/layouts/projects/board/${slug}`"
                  >
                    <Icon name="solar:widget-4-linear" class="size-4" />
                    <span>Open Board</span>
                  </BaseButton>
                  <BaseDropdown
                    label="Manage"
                    placement="bottom-end"
                    size="md"
                    class="z-20"
                    rounded="md"
                  >
                    <BaseDropdownItem
                      :to="`/layouts/projects/board/${slug}`"
                      title="Board view"
                      text="Swicth to board view"
                      class="sm:hidden"
                    >
                      <template #start>
                        <Icon name="solar:widget-4-linear" class="me-2 block size-5" />
                      </template>
                    </BaseDropdownItem>
                    <BaseDropdownSeparator class="sm:hidden" />
                    <BaseDropdownItem
                      to="#"
                      title="Edit"
                      text="Edit this project"
                    >
                      <template #start>
                        <Icon name="solar:pen-2-linear" class="me-2 block size-5" />
                      </template>
                    </BaseDropdownItem>
                    <BaseDropdownSeparator />
                    <BaseDropdownItem
                      to="#"
                      title="Permissions"
                      text="Manage permissions"
                    >
                      <template #start>
                        <Icon name="solar:lock-keyhole-linear" class="me-2 block size-5" />
                      </template>
                    </BaseDropdownItem>
                    <BaseDropdownItem
                      to="#"
                      title="Files"
                      text="Manage files"
                    >
                      <template #start>
                        <Icon name="solar:file-check-linear" class="me-2 block size-5" />
                      </template>
                    </BaseDropdownItem>
                    <BaseDropdownSeparator />
                    <BaseDropdownItem
                      to="#"
                      title="Delete"
                      text="Delete this project"
                    >
                      <template #start>
                        <Icon name="solar:trash-bin-minimalistic-linear" class="me-2 block size-5" />
                      </template>
                    </BaseDropdownItem>
                  </BaseDropdown>
                </div>
                <!-- Customer -->
                <WidgetsProjectsInfoCard :data="customerCardData" />
                <!-- Tools -->
                <WidgetsProjectsInfoCard :data="toolsCardData" />
                <!-- Stacks -->
                <WidgetsProjectsInfoCard :data="stacksCardData" />
              </div>
            </div>
          </div>
        </BaseTabsContent>
        <!-- Team -->
        <BaseTabsContent value="team">
          <div class="grid gap-4 sm:grid-cols-3">
            <WidgetsProjectsTeamCard
              :data="ownerTeamCardData"
              :is-owner="true"
              href="#"
            />
            <WidgetsProjectsTeamCard
              v-for="member in teamCardsData"
              :key="member.name"
              :data="member"
              href="#"
            />
            <!-- Invite -->
            <div>
              <button
                type="button"
                class="border-muted-300 dark:border-muted-800 hover:border-primary-500 dark:hover:border-primary-500 group flex size-full items-center justify-center gap-2 rounded-lg border-2 border-dashed px-6 py-8 transition-colors duration-300"
              >
                <span class="block text-center font-sans">
                  <span
                    class="text-muted-800 dark:text-muted-100 group-hover:text-primary-500 dark:group-hover:text-primary-500 block transition-colors duration-300"
                  >
                    Invite a new member
                  </span>
                  <span class="text-muted-400 block text-sm">
                    Send an invitation to join your project team
                  </span>
                </span>
              </button>
            </div>
          </div>
        </BaseTabsContent>
        <!-- Tasks -->
        <BaseTabsContent value="tasks">
          <div class="grid gap-4 sm:grid-cols-3">
            <WidgetsProjectsTaskCard
              v-for="task in taskCardsData"
              :key="task.name"
              :data="task"
              @click="() => openTaskPanel(task.id, data?.project?.tasks)"
            />
            <!-- Invite -->
            <div>
              <button
                type="button"
                class="border-muted-300 dark:border-muted-800 hover:border-primary-500 dark:hover:border-primary-500 group flex size-full items-center justify-center gap-2 rounded-lg border-2 border-dashed p-6 transition-colors duration-300"
              >
                <span class="block text-center font-sans">
                  <span
                    class="font-medium text-muted-900 dark:text-muted-100 group-hover:text-primary-500 dark:group-hover:text-primary-500 block transition-colors duration-300"
                  >
                    Create a new task
                  </span>
                  <span class="text-muted-400 block text-sm">
                    Add a new task to your project
                  </span>
                </span>
              </button>
            </div>
          </div>
        </BaseTabsContent>
      </BaseTabs>
    </div>
  </div>
</template>
