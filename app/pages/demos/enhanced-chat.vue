<script setup lang="ts">
import type { ChatMessage } from '~/types/chat'
import EnhancedChatCanvas from '~~/components/Chat/canvas/EnhancedChatCanvas.vue'

definePageMeta({
  layout: 'empty',
  title: 'Enhanced Chat Canvas Demo',
})

// Simple demo message
const demoMessage: ChatMessage = {
  id: '1',
  conversationId: 'demo',
  type: 'assistant',
  role: 'assistant',
  content: `# Welcome to Enhanced Chat Canvas!

I'm excited to show you the new **beautiful chat interface** with enhanced features:

## ✨ Key Features:
- **Smooth animations** with @vueuse/motion
- **Markdown rendering** with syntax highlighting
- **Glassmorphism effects** and gradients
- **Interactive message bubbles** with hover states

## 🎨 Visual Enhancements:
- Provider badges (OpenAI, Gemini, Anthropic)
- Status indicators and timestamps  
- Tool execution results
- Message reactions and actions

\`\`\`typescript
// Example of enhanced message structure
interface ChatMessage {
  content: string
  metadata: {
    provider: 'openai' | 'gemini' | 'anthropic'
    model: string
    view?: { type: 'enhanced_message' }
  }
}
\`\`\`

> This is just the beginning! More features coming soon.`,
  agentId: 'assistant-1',
  agentName: 'Enhanced Assistant',
  metadata: {
    provider: 'openai',
    model: 'gpt-4o',
    tokens: 156,
    view: {
      type: 'enhanced_message',
    },
  },
  userId: 'demo-user',
  workspaceId: 'demo-workspace',
  profileId: 'demo-profile',
  createdAt: new Date(),
}

// Demo state
const showStatePanel = ref(false)
const animate = ref(true)

function handleCopy(content: string) {
  navigator.clipboard?.writeText(content)
  console.log('Copied:', content)
}

function handleRetry(messageId: string) {
  console.log('Retry message:', messageId)
}

function handleDelete(messageId: string) {
  console.log('Delete message:', messageId)
}

function handleReact(messageId: string, emoji: string) {
  console.log('React to message:', messageId, 'with', emoji)
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-muted-50 to-white dark:from-muted-950 dark:to-muted-900">
    <!-- Header -->
    <div class="bg-white/80 dark:bg-muted-900/80 backdrop-blur-sm border-b border-muted-200 dark:border-muted-800 sticky top-0 z-10">
      <div class="max-w-4xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-muted-900 dark:text-muted-100">
              Enhanced Chat Canvas Demo
            </h1>
            <p class="text-muted-600 dark:text-muted-400 text-sm mt-1">
              Beautiful, dynamic chat interface with animations
            </p>
          </div>

          <NuxtLink to="/">
            <BaseButton size="sm" variant="pastel" color="muted">
              <Icon name="lucide:arrow-left" class="size-4" />
              Back
            </BaseButton>
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Chat Demo -->
    <div class="max-w-4xl mx-auto px-6 py-8">
      <div class="bg-white dark:bg-muted-900 rounded-xl border border-muted-200 dark:border-muted-700 shadow-2xl overflow-hidden">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-primary-500 to-primary-600 p-4">
          <div class="flex items-center gap-3">
            <div class="size-8 bg-white/20 rounded-full flex items-center justify-center">
              <Icon name="lucide:message-circle" class="size-4 text-white" />
            </div>
            <div class="flex-1">
              <h3 class="font-medium text-white">
                Enhanced Chat Interface
              </h3>
              <p class="text-white/80 text-sm">
                Demonstrating beautiful message rendering
              </p>
            </div>
            <div class="flex items-center gap-2">
              <div class="size-2 bg-green-400 rounded-full animate-pulse" />
              <span class="text-white/80 text-xs">Online</span>
            </div>
          </div>
        </div>

        <!-- Chat Content -->
        <div class="p-6 min-h-[500px] bg-gradient-to-b from-muted-50/50 to-white dark:from-muted-950/50 dark:to-muted-900">
          <EnhancedChatCanvas
            :message="demoMessage"
            :use-enhanced-bubble="true"
            :animate="animate"
            :show-state-panel="showStatePanel"
            @copy="handleCopy"
            @retry="handleRetry"
            @delete="handleDelete"
            @react="handleReact"
          />
        </div>
      </div>
    </div>

    <!-- Feature Highlights -->
    <div class="max-w-4xl mx-auto px-6 pb-12">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-muted-800 rounded-lg p-6 border border-muted-200 dark:border-muted-700 shadow-lg">
          <div class="size-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mb-4">
            <Icon name="lucide:sparkles" class="size-6 text-white" />
          </div>
          <h3 class="font-semibold text-muted-900 dark:text-muted-100 mb-2">
            Beautiful Animations
          </h3>
          <p class="text-muted-600 dark:text-muted-400 text-sm">
            Smooth spring animations powered by @vueuse/motion with entrance effects and hover interactions.
          </p>
        </div>

        <div class="bg-white dark:bg-muted-800 rounded-lg p-6 border border-muted-200 dark:border-muted-700 shadow-lg">
          <div class="size-12 bg-gradient-to-br from-success-500 to-success-600 rounded-lg flex items-center justify-center mb-4">
            <Icon name="lucide:type" class="size-6 text-white" />
          </div>
          <h3 class="font-semibold text-muted-900 dark:text-muted-100 mb-2">
            Rich Content
          </h3>
          <p class="text-muted-600 dark:text-muted-400 text-sm">
            Markdown rendering, syntax highlighting, code blocks, tables, and more with automatic detection.
          </p>
        </div>

        <div class="bg-white dark:bg-muted-800 rounded-lg p-6 border border-muted-200 dark:border-muted-700 shadow-lg">
          <div class="size-12 bg-gradient-to-br from-info-500 to-info-600 rounded-lg flex items-center justify-center mb-4">
            <Icon name="lucide:layers" class="size-6 text-white" />
          </div>
          <h3 class="font-semibold text-muted-900 dark:text-muted-100 mb-2">
            Dynamic Canvas
          </h3>
          <p class="text-muted-600 dark:text-muted-400 text-sm">
            Extensible registry system for custom message types with glassmorphism effects and gradients.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Demo page specific styles */
.page-enter-active,
.page-leave-active {
  transition: all 0.4s;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
