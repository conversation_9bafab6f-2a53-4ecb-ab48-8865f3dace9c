<script setup lang="ts">
// Demo data for all widgets
const demoData = {
  profile: {
    bio: 'Passionate full-stack developer with expertise in Vue.js, TypeScript, and modern web technologies. Experienced in building scalable applications and leading development teams.',
    experiences: [
      {
        company: 'TechCorp Inc',
        position: 'Senior Full Stack Developer',
        period: '2022-Present',
        logo: '/demo/logos/techcorp.png',
      },
      {
        company: 'StartupXYZ',
        position: 'Frontend Developer',
        period: '2020-2022',
        logo: '/demo/logos/startup.png',
      },
      {
        company: 'WebAgency',
        position: 'Junior Developer',
        period: '2019-2020',
      },
    ],
    languages: [
      {
        name: 'English',
        level: 95,
        mastery: 'Native',
        icon: '/demo/flags/en.png',
      },
      {
        name: 'Spanish',
        level: 80,
        mastery: 'Professional',
      },
      {
        name: 'French',
        level: 65,
        mastery: 'Intermediate',
      },
    ],
    skills: [
      {
        name: 'Vue.js',
        level: 90,
        experience: 4,
        logo: '/demo/tech/vue.png',
      },
      {
        name: 'TypeScript',
        level: 85,
        experience: 3,
        icon: 'simple-icons:typescript',
      },
      {
        name: 'Node.js',
        level: 80,
        experience: 3,
      },
      {
        name: 'Nuxt.js',
        level: 88,
        experience: 2,
        logo: '/demo/tech/nuxt.png',
      },
    ],
    recommendations: [
      {
        name: 'Sarah Johnson',
        role: 'Tech Lead at TechCorp',
        src: '/demo/avatars/sarah.jpg',
        text: 'Exceptional developer with strong problem-solving skills. Always delivers high-quality code and mentors junior developers effectively.',
        date: 'December 2024',
        badge: '/demo/badges/linkedin.png',
      },
      {
        name: 'Mike Chen',
        role: 'CTO at StartupXYZ',
        src: '/demo/avatars/mike.jpg',
        text: 'One of the most talented developers I have worked with. Great technical expertise and excellent communication skills.',
        date: 'November 2024',
      },
      {
        name: 'Emma Wilson',
        role: 'Product Manager',
        src: '/demo/avatars/emma.jpg',
        text: 'Reliable, innovative, and always goes above and beyond. A valuable team player who understands business requirements.',
        date: 'October 2024',
        badge: '/demo/badges/verified.png',
      },
    ],
    notificationSettings: {
      email: true,
      push: false,
      sms: true,
      marketing: false,
    },
    tools: [
      { name: 'VS Code', category: 'Editor', usage: 'daily' },
      { name: 'Git', category: 'VCS', usage: 'daily' },
      { name: 'Figma', category: 'Design', usage: 'weekly' },
      { name: 'Docker', category: 'DevOps', usage: 'weekly' },
    ],
    recentViews: [
      { title: 'Profile Settings', url: '/profile/settings', timestamp: '2 hours ago' },
      { title: 'Project Dashboard', url: '/projects', timestamp: '5 hours ago' },
      { title: 'Team Members', url: '/team', timestamp: '1 day ago' },
    ],
  },
  finance: {
    accountBalance: 15847.32,
    balanceChange: 1243.18,
    moneyIn: 8500.00,
    moneyOut: 3250.75,
    incomingTransactions: [
      { description: 'Salary Payment', amount: 5000.00, date: '2024-01-15' },
      { description: 'Freelance Project', amount: 2500.00, date: '2024-01-10' },
      { description: 'Investment Return', amount: 1000.00, date: '2024-01-05' },
    ],
    outgoingTransactions: [
      { description: 'Rent Payment', amount: 1500.00, date: '2024-01-01' },
      { description: 'Utilities', amount: 250.75, date: '2024-01-03' },
      { description: 'Groceries', amount: 450.00, date: '2024-01-07' },
    ],
  },
  charts: {
    revenueData: {
      current: 45000,
      previous: 38000,
      chartData: [28000, 32000, 38000, 42000, 45000],
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    },
    personalScore: 85,
  },
  stats: {
    totalUsers: 12547,
    projectProgress: 78,
    monthlyRevenue: 145000,
    quickStats: [
      { label: 'Active Users', value: 1234 },
      { label: 'Conversions', value: 89 },
      { label: 'Revenue', value: 45000, format: 'currency' },
    ],
  },
  creative: {
    genericContent: {
      title: 'Creative Content Showcase',
      description: 'This widget demonstrates flexible content display capabilities.',
      image: '/demo/creative/showcase.jpg',
      tags: ['Design', 'Creative', 'Showcase'],
    },
  },
  info: {
    welcomeMessage: 'Welcome to the Widget Showcase! Explore our comprehensive collection of reusable components.',
    currentUser: {
      name: 'Demo User',
      avatar: '/demo/avatars/user.jpg',
      role: 'Developer',
    },
  },
  utils: {
    notifications: [
      {
        title: 'New Message',
        message: 'You have received a new message from team lead',
        time: '5 minutes ago',
        type: 'info',
      },
      {
        title: 'Task Completed',
        message: 'Widget showcase page has been completed successfully',
        time: '1 hour ago',
        type: 'success',
      },
      {
        title: 'Warning',
        message: 'Some widgets may require additional props for full functionality',
        time: '2 hours ago',
        type: 'warning',
      },
    ],
    selectedDate: new Date(),
    calendarEvents: [
      { date: '2024-01-15', title: 'Team Meeting' },
      { date: '2024-01-18', title: 'Project Review' },
      { date: '2024-01-22', title: 'Sprint Planning' },
    ],
    promotion: {
      title: 'Widget Library Complete!',
      description: 'All BaseCard instances have been successfully converted to reusable widgets.',
      action: 'View Documentation',
      image: '/demo/promos/success.jpg',
    },
  },
}

// Page metadata
useHead({
  title: 'Widget Showcase - Component Library',
  meta: [
    {
      name: 'description',
      content: 'Complete showcase of reusable widget components created during the BaseCard refactor project.',
    },
  ],
})
</script>

<template>
  <div class="p-8">
    <BaseHeading as="h1" size="2xl" weight="bold" class="mb-8">
      Widget Showcase
    </BaseHeading>
    <BaseParagraph size="lg" class="text-muted-600 dark:text-muted-400 mb-12 max-w-3xl">
      Complete collection of reusable widget components created during the BaseCard refactor project.
      These widgets provide consistent, maintainable UI patterns across the application.
    </BaseParagraph>

    <!-- Profile Widgets Section -->
    <section class="mb-16">
      <BaseHeading as="h2" size="xl" weight="medium" class="mb-6 text-primary-500">
        Profile Widgets
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-6">
        User profile related components for displaying personal information, recommendations, and settings.
      </BaseParagraph>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <WidgetsProfileMainInfo
          :bio="demoData.profile.bio"
          :experiences="demoData.profile.experiences"
          :languages="demoData.profile.languages"
          :skills="demoData.profile.skills"
        />

        <WidgetsProfileRecommendations
          :recommendations="demoData.profile.recommendations"
        />
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <WidgetsProfileNotificationSettings
          :settings="demoData.profile.notificationSettings"
        />

        <WidgetsProfileTools
          :tools="demoData.profile.tools"
        />

        <WidgetsProfileRecentViews
          :items="demoData.profile.recentViews"
        />
      </div>
    </section>

    <!-- Finance Widgets Section -->
    <section class="mb-16">
      <BaseHeading as="h2" size="xl" weight="medium" class="mb-6 text-primary-500">
        Finance Widgets
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-6">
        Financial data display components for banking, investments, and transaction management.
      </BaseParagraph>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <WidgetsFinanceAccountBalance
          :balance="demoData.finance.accountBalance"
          :change="demoData.finance.balanceChange"
          :is-increase="true"
        />

        <WidgetsFinanceMoneyIn
          :amount="demoData.finance.moneyIn"
          :transactions="demoData.finance.incomingTransactions"
        />

        <WidgetsFinanceMoneyOut
          :amount="demoData.finance.moneyOut"
          :transactions="demoData.finance.outgoingTransactions"
        />
      </div>
    </section>

    <!-- Charts Widgets Section -->
    <section class="mb-16">
      <BaseHeading as="h2" size="xl" weight="medium" class="mb-6 text-primary-500">
        Chart Widgets
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-6">
        Data visualization components for analytics, reporting, and dashboard displays.
      </BaseParagraph>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <WidgetsChartsRevenueOverview
          :data="demoData.charts.revenueData"
          title="Revenue Overview"
        />

        <WidgetsChartsPersonalScore
          :score="demoData.charts.personalScore"
          :max-score="100"
          title="Performance Score"
        />
      </div>
    </section>

    <!-- Stats Widgets Section -->
    <section class="mb-16">
      <BaseHeading as="h2" size="xl" weight="medium" class="mb-6 text-primary-500">
        Stats Widgets
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-6">
        Key performance indicator and statistics display components.
      </BaseParagraph>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <WidgetsStatsKpiTile
          title="Total Users"
          :value="demoData.stats.totalUsers"
          trend="up"
          :change="12.5"
          icon="solar:users-group-two-rounded-linear"
        />

        <WidgetsStatsProgressTile
          title="Project Progress"
          :progress="demoData.stats.projectProgress"
          :total="100"
          color="primary"
        />

        <WidgetsStatsQuick
          :stats="demoData.stats.quickStats"
        />

        <WidgetsStatsTile
          title="Monthly Revenue"
          :value="demoData.stats.monthlyRevenue"
          format="currency"
          color="success"
        />
      </div>
    </section>

    <!-- Creative Widgets Section -->
    <section class="mb-16">
      <BaseHeading as="h2" size="xl" weight="medium" class="mb-6 text-primary-500">
        Creative & Info Widgets
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-6">
        Creative content display and information presentation components.
      </BaseParagraph>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <WidgetsCreativeGeneric
          :content="demoData.creative.genericContent"
          variant="showcase"
        />

        <WidgetsInfoWelcome
          :message="demoData.info.welcomeMessage"
          :user="demoData.info.currentUser"
        />
      </div>
    </section>

    <!-- Utility Widgets Section -->
    <section class="mb-16">
      <BaseHeading as="h2" size="xl" weight="medium" class="mb-6 text-primary-500">
        Utility Widgets
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-6">
        Utility and support components for notifications, date pickers, and promotional content.
      </BaseParagraph>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <WidgetsUtilsNotifications
          :notifications="demoData.utils.notifications"
        />

        <WidgetsUtilsDatePicker
          :selected-date="demoData.utils.selectedDate"
          :events="demoData.utils.calendarEvents"
        />

        <WidgetsUtilsPromotionalCard
          :promotion="demoData.utils.promotion"
        />
      </div>
    </section>

    <!-- Widget Statistics -->
    <section class="mb-16 bg-muted-50 dark:bg-muted-900 rounded-lg p-8">
      <BaseHeading as="h2" size="lg" weight="medium" class="mb-4">
        Refactor Achievement Stats
      </BaseHeading>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div class="text-center">
          <BaseHeading as="h3" size="2xl" weight="bold" class="text-primary-500">
            103
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Widgets Created
          </BaseParagraph>
        </div>
        <div class="text-center">
          <BaseHeading as="h3" size="2xl" weight="bold" class="text-success-500">
            26
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Widget Domains
          </BaseParagraph>
        </div>
        <div class="text-center">
          <BaseHeading as="h3" size="2xl" weight="bold" class="text-warning-500">
            121
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            BaseCards Eliminated
          </BaseParagraph>
        </div>
        <div class="text-center">
          <BaseHeading as="h3" size="2xl" weight="bold" class="text-info-500">
            69
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Pages Refactored
          </BaseParagraph>
        </div>
      </div>
    </section>

    <!-- Navigation Links -->
    <section class="border-t border-muted-200 dark:border-muted-800 pt-8">
      <BaseHeading as="h3" size="lg" weight="medium" class="mb-4">
        Related Resources
      </BaseHeading>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <BaseCard class="p-4 hover:bg-muted-50 dark:hover:bg-muted-800 transition-colors cursor-pointer">
          <BaseHeading as="h4" size="sm" weight="medium" class="mb-2">
            Component Organization Guide
          </BaseHeading>
          <BaseParagraph size="xs" class="text-muted-500">
            Learn about our component architecture and organization patterns
          </BaseParagraph>
        </BaseCard>

        <BaseCard class="p-4 hover:bg-muted-50 dark:hover:bg-muted-800 transition-colors cursor-pointer">
          <BaseHeading as="h4" size="sm" weight="medium" class="mb-2">
            Migration Guide
          </BaseHeading>
          <BaseParagraph size="xs" class="text-muted-500">
            Step-by-step guide for migrating from BaseCard to widgets
          </BaseParagraph>
        </BaseCard>

        <BaseCard class="p-4 hover:bg-muted-50 dark:hover:bg-muted-800 transition-colors cursor-pointer">
          <BaseHeading as="h4" size="sm" weight="medium" class="mb-2">
            Widget Test Coverage
          </BaseHeading>
          <BaseParagraph size="xs" class="text-muted-500">
            Unit test coverage and testing patterns for all widgets
          </BaseParagraph>
        </BaseCard>
      </div>
    </section>
  </div>
</template>
