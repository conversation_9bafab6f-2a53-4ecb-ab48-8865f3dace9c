import type { Component } from 'vue'
import { defineAsyncComponent, defineComponent, h, onMounted, readonly, ref } from 'vue'
import SkeletonBlock from '~~/components/Chat/blocks/SkeletonBlock.vue'

export interface LazyComponentOptions {
  /**
   * Type of skeleton to show while loading
   */
  skeletonType?: 'code' | 'chart' | 'table' | 'tool_card' | 'doc' | 'generic'

  /**
   * Height of skeleton in pixels
   */
  skeletonHeight?: number

  /**
   * Delay in milliseconds before showing skeleton
   */
  skeletonDelay?: number

  /**
   * Maximum time to wait for component to load (in milliseconds)
   */
  timeout?: number

  /**
   * Whether to show skeleton animations
   */
  animate?: boolean

  /**
   * Custom error component
   */
  errorComponent?: Component

  /**
   * Retry attempts for failed loads
   */
  retryAttempts?: number
}

/**
 * Creates a lazy-loaded component with skeleton loading state
 */
export function createLazyComponent(
  loader: () => Promise<Component>,
  options: LazyComponentOptions = {},
) {
  const {
    skeletonType = 'generic',
    skeletonHeight = 200,
    skeletonDelay = 200,
    timeout = 10000,
    animate = true,
    errorComponent,
    retryAttempts = 3,
  } = options

  return defineAsyncComponent({
    loader: createRetryLoader(loader, retryAttempts),
    loadingComponent: defineComponent({
      setup() {
        return () => h(SkeletonBlock, {
          type: skeletonType,
          height: skeletonHeight,
          animate,
        })
      },
    }),
    errorComponent: errorComponent || defineComponent({
      setup() {
        return () => h('div', {
          class: 'p-4 text-center text-red-600 dark:text-red-400 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-900/20',
        }, [
          h('p', { class: 'font-medium' }, 'Component failed to load'),
          h('p', { class: 'text-sm mt-1' }, 'Please try refreshing the page'),
        ])
      },
    }),
    delay: skeletonDelay,
    timeout,
  })
}

/**
 * Creates a loader function with retry logic
 */
function createRetryLoader(
  loader: () => Promise<Component>,
  maxAttempts: number,
): () => Promise<Component> {
  return async () => {
    let lastError: Error

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const component = await loader()
        return component
      }
      catch (error) {
        lastError = error as Error

        // If this isn't the last attempt, wait before retrying
        if (attempt < maxAttempts) {
          const delay = Math.min(1000 * 2 ** (attempt - 1), 5000) // Exponential backoff, max 5s
          await new Promise(resolve => setTimeout(resolve, delay))
          continue
        }
      }
    }

    throw lastError!
  }
}

/**
 * Pre-defined lazy component configurations for common block types
 */
export const lazyBlockConfigs = {
  code: {
    skeletonType: 'code' as const,
    skeletonHeight: 300,
    skeletonDelay: 100,
  },
  chart: {
    skeletonType: 'chart' as const,
    skeletonHeight: 350,
    skeletonDelay: 150,
  },
  table: {
    skeletonType: 'table' as const,
    skeletonHeight: 400,
    skeletonDelay: 100,
  },
  tool_card: {
    skeletonType: 'tool_card' as const,
    skeletonHeight: 200,
    skeletonDelay: 50,
  },
  doc: {
    skeletonType: 'doc' as const,
    skeletonHeight: 250,
    skeletonDelay: 100,
  },
} as const

/**
 * Bundle size analysis helper
 */
export interface ComponentBundleInfo {
  name: string
  estimatedSize: number // in KB
  dependencies: string[]
  loadPriority: 'high' | 'medium' | 'low'
}

export const componentBundleMap: Record<string, ComponentBundleInfo> = {
  CodeBlock: {
    name: 'CodeBlock',
    estimatedSize: 15, // Small - mostly UI logic
    dependencies: ['useClipboard'],
    loadPriority: 'medium',
  },
  ChartBlock: {
    name: 'ChartBlock',
    estimatedSize: 45, // Medium - SVG generation logic
    dependencies: [],
    loadPriority: 'low',
  },
  TableBlock: {
    name: 'TableBlock',
    estimatedSize: 35, // Medium - sorting, filtering, pagination
    dependencies: [],
    loadPriority: 'medium',
  },
  GraphStatePanel: {
    name: 'GraphStatePanel',
    estimatedSize: 20, // Small-medium - mostly UI
    dependencies: [],
    loadPriority: 'high', // Used frequently in chat
  },
}

/**
 * Preload components based on priority and usage patterns
 */
export function preloadComponents(priorities: Array<'high' | 'medium' | 'low'> = ['high']) {
  const componentsToPreload = Object.entries(componentBundleMap)
    .filter(([_, info]) => priorities.includes(info.loadPriority))
    .map(([name]) => name)

  // Preload in idle time
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      componentsToPreload.forEach(async (componentName) => {
        try {
          await getComponentLoader(componentName)()
        }
        catch (error) {
          console.warn(`Failed to preload ${componentName}:`, error)
        }
      })
    })
  }
}

/**
 * Get the appropriate component loader function
 */
function getComponentLoader(componentName: string): () => Promise<Component> {
  switch (componentName) {
    case 'CodeBlock':
      return () => import('~~/components/Chat/blocks/CodeBlock.vue')
    case 'ChartBlock':
      return () => import('~~/components/Chat/blocks/ChartBlock.vue')
    case 'TableBlock':
      return () => import('~~/components/Chat/blocks/TableBlock.vue')
    case 'GraphStatePanel':
      return () => import('~~/components/Chat/GraphStatePanel.vue')
    default:
      throw new Error(`Unknown component: ${componentName}`)
  }
}

/**
 * Track component load performance
 */
export function trackComponentLoad(componentName: string, startTime: number) {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const loadTime = performance.now() - startTime
    console.debug(`Component ${componentName} loaded in ${loadTime.toFixed(2)}ms`)

    // Could be integrated with analytics service
    // analytics.track('component_load', { componentName, loadTime })
  }
}

/**
 * Vue composable for lazy component loading
 */
export function useLazyComponent(
  loader: (() => Promise<any>) | string,
  options: LazyComponentOptions & {
    skeleton?: {
      component: () => Promise<any>
      props?: Record<string, any>
    }
  } = {},
) {
  const isLoading = ref(true)
  const hasError = ref(false)
  const component = ref<Component | null>(null)
  const error = ref<Error | null>(null)

  const load = async () => {
    const startTime = performance.now()

    try {
      isLoading.value = true
      hasError.value = false
      error.value = null

      let componentLoader: () => Promise<any>

      if (typeof loader === 'string') {
        // Legacy string-based component loading
        componentLoader = getComponentLoader(loader)
      }
      else {
        // Direct import function
        componentLoader = loader
      }

      const loadedModule = await createRetryLoader(componentLoader, options.retryAttempts || 3)()

      // Handle both default exports and named exports
      const loadedComponent = loadedModule.default || loadedModule
      component.value = loadedComponent

      if (typeof loader === 'string') {
        trackComponentLoad(loader, startTime)
      }
    }
    catch (err) {
      console.error(`Failed to load component:`, err)
      hasError.value = true
      error.value = err as Error
    }
    finally {
      isLoading.value = false
    }
  }

  // Auto-load on creation
  onMounted(() => {
    load()
  })

  return {
    component: readonly(component),
    isLoading: readonly(isLoading),
    hasError: readonly(hasError),
    error: readonly(error),
    load,
    reload: load,
  }
}
