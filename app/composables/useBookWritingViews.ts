import type {
  Activity,
  BookImage,
  Chapter,
  Suggestion,
  WritingStats,
} from '~~/components/Chat/views'
import type { ChatMessage } from '~/types/chat'

export interface BookWritingViewMetadata {
  view: {
    type: 'suggestion_bubble' | 'image_gallery' | 'chapter_outline' | 'writing_stats' | 'document_editor'
    props: Record<string, any>
  }
}

export interface SuggestionBubbleData {
  suggestions: Suggestion[]
  title?: string
  projectId?: string
}

export interface ImageGalleryData {
  images: BookImage[]
  title?: string
  projectId?: string
}

export interface ChapterOutlineData {
  chapters: Chapter[]
  title?: string
  projectId?: string
}

export interface WritingStatsData {
  stats?: WritingStats
  activities?: Activity[]
  title?: string
  dailyGoal?: number
  weeklyGoal?: number
  monthlyGoal?: number
  bestWritingTime?: string
  averageSessionLength?: number
  averageWPM?: number
}

export interface DocumentEditorData {
  documentId: string
  title: string
  content: string
  type: 'chapter' | 'scene' | 'notes' | 'outline'
  chapterNumber?: number
  sceneId?: string
  lastModified?: Date
  wordCount?: number
  characterCount?: number
  readingTime?: number
  collaborators?: Array<{
    id: string
    name: string
    avatar?: string
    isOnline?: boolean
  }>
  metadata?: Record<string, any>
  projectId?: string
}

/**
 * Composable for creating and managing book writing chat view messages
 */
export function useBookWritingViews() {
  /**
   * Create a suggestion bubble view message
   */
  function createSuggestionBubbleMessage(
    data: SuggestionBubbleData,
    role: 'assistant' | 'user' = 'assistant',
  ): ChatMessage {
    return {
      id: crypto.randomUUID(),
      role,
      content: `Displaying ${data.suggestions.length} AI suggestions for your book.`,
      timestamp: new Date(),
      metadata: {
        view: {
          type: 'suggestion_bubble',
          props: data,
        },
      } as BookWritingViewMetadata,
    }
  }

  /**
   * Create an image gallery view message
   */
  function createImageGalleryMessage(
    data: ImageGalleryData,
    role: 'assistant' | 'user' = 'assistant',
  ): ChatMessage {
    return {
      id: crypto.randomUUID(),
      role,
      content: `Image gallery with ${data.images.length} book images.`,
      timestamp: new Date(),
      metadata: {
        view: {
          type: 'image_gallery',
          props: data,
        },
      } as BookWritingViewMetadata,
    }
  }

  /**
   * Create a chapter outline view message
   */
  function createChapterOutlineMessage(
    data: ChapterOutlineData,
    role: 'assistant' | 'user' = 'assistant',
  ): ChatMessage {
    const totalWords = data.chapters.reduce((sum, chapter) => sum + (chapter.wordCount || 0), 0)
    const completedChapters = data.chapters.filter(chapter => chapter.status === 'completed').length

    return {
      id: crypto.randomUUID(),
      role,
      content: `Book outline with ${data.chapters.length} chapters (${completedChapters} completed, ${totalWords.toLocaleString()} words).`,
      timestamp: new Date(),
      metadata: {
        view: {
          type: 'chapter_outline',
          props: data,
        },
      } as BookWritingViewMetadata,
    }
  }

  /**
   * Create a writing stats view message
   */
  function createWritingStatsMessage(
    data: WritingStatsData,
    role: 'assistant' | 'user' = 'assistant',
  ): ChatMessage {
    const totalWords = data.stats?.totalWords || 0
    const streak = data.stats?.streak || 0

    return {
      id: crypto.randomUUID(),
      role,
      content: `Writing statistics: ${totalWords.toLocaleString()} words total, ${streak} day streak.`,
      timestamp: new Date(),
      metadata: {
        view: {
          type: 'writing_stats',
          props: data,
        },
      } as BookWritingViewMetadata,
    }
  }

  /**
   * Create a document editor view message
   */
  function createDocumentEditorMessage(
    data: DocumentEditorData,
    role: 'assistant' | 'user' = 'assistant',
  ): ChatMessage {
    const wordCount = data.wordCount || 0
    const typeLabel = data.type === 'chapter'
      ? 'Chapter'
      : data.type === 'scene'
        ? 'Scene'
        : data.type === 'notes' ? 'Notes' : 'Outline'

    let contentDescription = `${typeLabel}: ${data.title}`
    if (data.chapterNumber) {
      contentDescription = `${typeLabel} ${data.chapterNumber}: ${data.title}`
    }
    if (wordCount > 0) {
      contentDescription += ` (${wordCount.toLocaleString()} words)`
    }

    return {
      id: crypto.randomUUID(),
      role,
      content: `Editing document: ${contentDescription}`,
      timestamp: new Date(),
      metadata: {
        view: {
          type: 'document_editor',
          props: data,
        },
      } as BookWritingViewMetadata,
    }
  }

  /**
   * Check if a message contains a book writing view
   */
  function isBookWritingView(message: ChatMessage): boolean {
    const metadata = message.metadata as BookWritingViewMetadata | undefined
    return Boolean(
      metadata?.view?.type
      && ['suggestion_bubble', 'image_gallery', 'chapter_outline', 'writing_stats', 'document_editor'].includes(metadata.view.type),
    )
  }

  /**
   * Get the view type from a message
   */
  function getViewType(message: ChatMessage): string | null {
    const metadata = message.metadata as BookWritingViewMetadata | undefined
    return metadata?.view?.type || null
  }

  /**
   * Create sample data for testing/demo purposes
   */
  function createSampleData() {
    const sampleSuggestions: Suggestion[] = [
      {
        id: '1',
        type: 'plot',
        priority: 'high',
        status: 'pending',
        description: 'Consider adding a subplot about the protagonist\'s childhood trauma',
        before: 'Chapter 3: The hero faces the villain',
        after: 'Chapter 3: The hero faces the villain, haunted by memories of their troubled past',
        reason: 'This would add depth to the character and explain their motivation',
        confidence: 0.85,
        impact: 'major',
      },
      {
        id: '2',
        type: 'character',
        priority: 'medium',
        status: 'pending',
        description: 'The supporting character needs more development',
        before: 'Sarah appears briefly in two scenes',
        after: 'Sarah becomes a more integral part of the story with her own arc',
        reason: 'Readers will connect better with well-developed supporting characters',
        confidence: 0.75,
        impact: 'moderate',
      },
    ]

    const sampleImages: BookImage[] = [
      {
        id: '1',
        title: 'Fantasy Castle',
        prompt: 'A majestic castle on a hill with dragons flying overhead',
        url: 'https://example.com/castle.jpg',
        type: 'scene',
        chapterNumber: 1,
        status: 'completed',
        createdAt: new Date(),
        metadata: { width: 1024, height: 768, format: 'jpg' },
      },
      {
        id: '2',
        title: 'Main Character Portrait',
        prompt: 'A brave knight with silver armor and determined eyes',
        type: 'character',
        status: 'generating',
        createdAt: new Date(),
      },
    ]

    const sampleChapters: Chapter[] = [
      {
        id: '1',
        number: 1,
        title: 'The Beginning',
        summary: 'Our hero starts their journey in a small village',
        wordCount: 2500,
        targetWordCount: 3000,
        status: 'completed',
        scenes: [
          {
            id: '1-1',
            title: 'Village Morning',
            summary: 'The hero wakes up to a peaceful village',
            wordCount: 800,
            status: 'completed',
            createdAt: new Date(),
          },
          {
            id: '1-2',
            title: 'The Call to Adventure',
            summary: 'A mysterious messenger arrives',
            wordCount: 1200,
            status: 'completed',
            createdAt: new Date(),
          },
        ],
        notes: 'This chapter sets the tone for the entire story',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2',
        number: 2,
        title: 'The Journey Begins',
        summary: 'The hero leaves home and encounters their first challenges',
        wordCount: 1800,
        targetWordCount: 3500,
        status: 'in-progress',
        scenes: [
          {
            id: '2-1',
            title: 'Leaving Home',
            summary: 'The hero says goodbye to family',
            wordCount: 900,
            status: 'completed',
            createdAt: new Date(),
          },
          {
            id: '2-2',
            title: 'First Challenge',
            summary: 'The hero faces their first obstacle',
            wordCount: 900,
            status: 'in-progress',
            createdAt: new Date(),
          },
        ],
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]

    const sampleStats: WritingStats = {
      totalWords: 12500,
      wordsChange: 850,
      dailyAverage: 425,
      sessionsCount: 15,
      totalTime: 320, // minutes
      streak: 7,
      bestStreak: 12,
      todayWords: 650,
      weeklyWords: 3200,
      monthlyWords: 12500,
    }

    const sampleActivities: Activity[] = [
      {
        id: '1',
        type: 'writing',
        description: 'Completed Chapter 1, Scene 2',
        wordCount: 450,
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      },
      {
        id: '2',
        type: 'editing',
        description: 'Revised Chapter 1 opening',
        wordCount: 120,
        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
      },
      {
        id: '3',
        type: 'planning',
        description: 'Outlined Chapter 3',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      },
    ]

    return {
      suggestions: sampleSuggestions,
      images: sampleImages,
      chapters: sampleChapters,
      stats: sampleStats,
      activities: sampleActivities,
    }
  }

  return {
    // Message creators
    createSuggestionBubbleMessage,
    createImageGalleryMessage,
    createChapterOutlineMessage,
    createWritingStatsMessage,
    createDocumentEditorMessage,

    // Utilities
    isBookWritingView,
    getViewType,
    createSampleData,

    // Type exports for convenience
    type: {
      SuggestionBubbleData,
      ImageGalleryData,
      ChapterOutlineData,
      WritingStatsData,
      DocumentEditorData,
      BookWritingViewMetadata,
    },
  }
}
