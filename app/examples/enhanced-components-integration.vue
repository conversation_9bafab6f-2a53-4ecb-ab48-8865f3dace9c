<script setup lang="ts">
import { ref } from 'vue'
import BaseChart from '~~/components/base/BaseChart.vue'
import BaseFormModal from '~~/components/base/BaseFormModal.vue'
import BaseTable from '~~/components/base/BaseTable.vue'
import BaseAnalyticsChart from '~~/components/base/charts/BaseAnalyticsChart.vue'
import BaseKpiChart from '~~/components/base/charts/BaseKpiChart.vue'

// Example data structures
interface User {
  id: number
  name: string
  email: string
  status: 'active' | 'inactive'
  lastActive: string
  role: string
}

interface UserFormData {
  name: string
  email: string
  role: string
  status: 'active' | 'inactive'
}

// Sample data
const users = ref<User[]>([
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'active',
    lastActive: '2024-01-15',
    role: 'Admin',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'active',
    lastActive: '2024-01-14',
    role: 'User',
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'inactive',
    lastActive: '2024-01-10',
    role: 'User',
  },
])

// Table configuration
const tableColumns = [
  {
    key: 'name' as keyof User,
    label: 'Name',
    sortable: true,
    align: 'left' as const,
  },
  {
    key: 'email' as keyof User,
    label: 'Email',
    sortable: true,
    align: 'left' as const,
  },
  {
    key: 'role' as keyof User,
    label: 'Role',
    sortable: true,
    align: 'center' as const,
  },
  {
    key: 'status' as keyof User,
    label: 'Status',
    sortable: true,
    align: 'center' as const,
    render: (value: string) => value === 'active' ? '🟢 Active' : '🔴 Inactive',
  },
  {
    key: 'lastActive' as keyof User,
    label: 'Last Active',
    sortable: true,
    align: 'right' as const,
  },
]

// Form modal state
const showUserForm = ref(false)
const editingUser = ref<User | null>(null)
const userFormFields = [
  {
    key: 'name',
    label: 'Full Name',
    type: 'text' as const,
    required: true,
    placeholder: 'Enter full name',
    description: 'Enter the user\'s full name',
  },
  {
    key: 'email',
    label: 'Email Address',
    type: 'email' as const,
    required: true,
    placeholder: '<EMAIL>',
    description: 'Must be a valid email address',
  },
  {
    key: 'role',
    label: 'Role',
    type: 'select' as const,
    required: true,
    options: [
      { value: 'Admin', label: 'Administrator' },
      { value: 'User', label: 'Regular User' },
      { value: 'Manager', label: 'Manager' },
    ],
  },
  {
    key: 'status',
    label: 'Status',
    type: 'radio' as const,
    required: true,
    options: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
    ],
  },
]

// Chart data
const analyticsData = ref([
  { date: '2024-01-01', value: 150 },
  { date: '2024-01-02', value: 180 },
  { date: '2024-01-03', value: 220 },
  { date: '2024-01-04', value: 200 },
  { date: '2024-01-05', value: 250 },
  { date: '2024-01-06', value: 290 },
  { date: '2024-01-07', value: 320 },
])

const kpiData = ref([
  {
    label: 'Revenue',
    current: 85000,
    target: 100000,
    previous: 75000,
    format: 'currency' as const,
  },
  {
    label: 'Users',
    current: 1250,
    target: 1500,
    previous: 1100,
    format: 'number' as const,
  },
  {
    label: 'Conversion',
    current: 3.2,
    target: 4.0,
    previous: 2.8,
    format: 'percentage' as const,
  },
  {
    label: 'Satisfaction',
    current: 92,
    target: 95,
    previous: 89,
    format: 'custom' as const,
    customUnit: '%',
  },
])

// State management
const tableLoading = ref(false)
const currentSortBy = ref<keyof User>('name')
const currentSortDirection = ref<'asc' | 'desc'>('asc')
const selectedUsers = ref<User[]>([])
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Event handlers
function handleTableSort(column: keyof User, direction: 'asc' | 'desc') {
  currentSortBy.value = column
  currentSortDirection.value = direction
}

function handleUserSelection(selectedItems: User[]) {
  selectedUsers.value = selectedItems
}

function handleRowClick(user: User) {
  console.log('User clicked:', user)
}

function openUserForm(user?: User) {
  editingUser.value = user || null
  showUserForm.value = true
}

function handleUserSubmit(formData: UserFormData) {
  if (editingUser.value) {
    // Update existing user
    const index = users.value.findIndex(u => u.id === editingUser.value!.id)
    if (index !== -1) {
      users.value[index] = {
        ...users.value[index],
        ...formData,
      }
    }
  }
  else {
    // Add new user
    const newUser: User = {
      id: Math.max(...users.value.map(u => u.id)) + 1,
      ...formData,
      lastActive: new Date().toISOString().split('T')[0],
    }
    users.value.push(newUser)
  }

  showUserForm.value = false
  editingUser.value = null
}

function closeUserForm() {
  showUserForm.value = false
  editingUser.value = null
}

// Computed pagination
const totalUsers = computed(() => users.value.length)
const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return users.value.slice(start, end)
})

const pagination = computed(() => ({
  page: currentPage.value,
  limit: itemsPerPage.value,
  total: totalUsers.value,
}))

function handlePageChange(page: number) {
  currentPage.value = page
}
</script>

<template>
  <div class="space-y-8 p-6">
    <BaseHeading as="h1" size="2xl" weight="bold" class="text-muted-900 dark:text-white">
      Enhanced Components Integration Example
    </BaseHeading>

    <!-- User Management Table -->
    <section>
      <div class="flex items-center justify-between mb-4">
        <BaseHeading as="h2" size="xl" weight="semibold" class="text-muted-800 dark:text-muted-200">
          User Management
        </BaseHeading>
        <BaseButton variant="primary" @click="openUserForm()">
          <Icon name="lucide:plus" class="size-4 mr-2" />
          Add User
        </BaseButton>
      </div>

      <BaseTable
        :columns="tableColumns"
        :data="paginatedUsers"
        :loading="tableLoading"
        :sort-by="currentSortBy"
        :sort-direction="currentSortDirection"
        :pagination="pagination"
        :selectable="true"
        :selected-items="selectedUsers"
        empty-message="No users found"
        @sort="handleTableSort"
        @page-change="handlePageChange"
        @selection-change="handleUserSelection"
        @row-click="handleRowClick"
      />

      <!-- Selection Summary -->
      <div v-if="selectedUsers.length > 0" class="mt-4 p-3 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg">
        <BaseText size="sm" class="text-primary-700 dark:text-primary-300">
          {{ selectedUsers.length }} user(s) selected
        </BaseText>
      </div>
    </section>

    <!-- Analytics Dashboard -->
    <section>
      <BaseHeading as="h2" size="xl" weight="semibold" class="text-muted-800 dark:text-muted-200 mb-4">
        Analytics Dashboard
      </BaseHeading>

      <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <!-- Analytics Chart -->
        <BaseCard class="p-6">
          <BaseAnalyticsChart
            :data="analyticsData"
            title="Daily Active Users"
            subtitle="User engagement over the last week"
            metric="Users"
            :height="300"
            :show-comparison="false"
            date-format="day"
            :fill-gradient="true"
          />
        </BaseCard>

        <!-- KPI Chart - Compact Mode -->
        <BaseCard class="p-6">
          <BaseKpiChart
            :data="kpiData"
            title="Key Performance Indicators"
            :compact-mode="true"
            :show-progress="true"
          />
        </BaseCard>
      </div>

      <!-- Full KPI Chart -->
      <BaseCard class="p-6 mt-6">
        <BaseKpiChart
          :data="kpiData"
          title="Performance Overview"
          :height="400"
          layout="horizontal"
          :compact-mode="false"
        />
      </BaseCard>
    </section>

    <!-- Basic Chart Examples -->
    <section>
      <BaseHeading as="h2" size="xl" weight="semibold" class="text-muted-800 dark:text-muted-200 mb-4">
        Chart Variations
      </BaseHeading>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Line Chart -->
        <BaseCard class="p-6">
          <BaseChart
            type="line"
            :data="[{ name: 'Sales', data: [30, 40, 35, 50, 49, 60, 70, 91] }]"
            :categories="['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug']"
            title="Monthly Sales"
            :height="300"
            :smooth="true"
            :enable-zoom="true"
          />
        </BaseCard>

        <!-- Donut Chart -->
        <BaseCard class="p-6">
          <BaseChart
            type="donut"
            :data="[44, 55, 41, 17]"
            :categories="['Desktop', 'Mobile', 'Tablet', 'Other']"
            title="Traffic Sources"
            :height="300"
            :enable-data-labels="true"
          />
        </BaseCard>
      </div>
    </section>

    <!-- User Form Modal -->
    <BaseFormModal
      :open="showUserForm"
      :title="editingUser ? 'Edit User' : 'Add New User'"
      :data="editingUser"
      :fields="userFormFields"
      submit-text="Save User"
      :grid-columns="2"
      size="lg"
      @close="closeUserForm"
      @submit="handleUserSubmit"
      @cancel="closeUserForm"
    />
  </div>
</template>

<style scoped>
/* Any additional custom styles */
</style>
