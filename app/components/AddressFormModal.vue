<script setup lang="ts">
import type { Address, AddressCreateInput, AddressUpdateInput } from '../app/types/auth'

interface Props {
  open: boolean
  type: 'mailing' | 'legal'
  address?: Address | null
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: AddressCreateInput | AddressUpdateInput): void
}

const props = withDefaults(defineProps<Props>(), {
  address: null,
})

const emit = defineEmits<Emits>()

// Form state
const form = reactive<AddressCreateInput & { is_primary: boolean }>({
  name: '',
  type: props.type,
  address_line_1: '',
  address_line_2: '',
  city: '',
  state: '',
  postal_code: '',
  country: '',
  is_primary: false,
})

const errors = reactive<Record<string, string>>({})
const isSubmitting = ref(false)
const submitError = ref<string>('')

// Computed properties
const editing = computed(() => !!props.address)

const isFormValid = computed(() => {
  return form.address_line_1.trim()
    && form.city.trim()
    && form.country.trim()
    && Object.keys(errors).length === 0
})

// Watch for address changes to populate form
watch(() => props.address, (address) => {
  if (address) {
    form.name = address.name || ''
    form.type = address.type
    form.address_line_1 = address.address_line_1
    form.address_line_2 = address.address_line_2 || ''
    form.city = address.city
    form.state = address.state || ''
    form.postal_code = address.postal_code || ''
    form.country = address.country
    form.is_primary = address.is_primary || false
  }
  else {
    resetForm()
  }
}, { immediate: true })

// Watch for prop.type changes
watch(() => props.type, (newType) => {
  form.type = newType
})

// Reset form
function resetForm() {
  form.name = ''
  form.type = props.type
  form.address_line_1 = ''
  form.address_line_2 = ''
  form.city = ''
  form.state = ''
  form.postal_code = ''
  form.country = ''
  form.is_primary = false

  // Clear errors
  Object.keys(errors).forEach(key => delete errors[key])
  submitError.value = ''
}

// Validation
function validateForm() {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  // Required field validation
  if (!form.address_line_1.trim()) {
    errors.address_line_1 = 'Address line 1 is required'
  }

  if (!form.city.trim()) {
    errors.city = 'City is required'
  }

  if (!form.country.trim()) {
    errors.country = 'Country is required'
  }

  return Object.keys(errors).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true
  submitError.value = ''

  try {
    const addressData = {
      name: form.name.trim() || undefined,
      type: form.type,
      address_line_1: form.address_line_1.trim(),
      address_line_2: form.address_line_2.trim() || undefined,
      city: form.city.trim(),
      state: form.state.trim() || undefined,
      postal_code: form.postal_code.trim() || undefined,
      country: form.country.trim(),
      is_primary: form.is_primary,
    }

    emit('submit', addressData)
  }
  catch (error: any) {
    submitError.value = error.message || 'Failed to save address'
  }
  finally {
    isSubmitting.value = false
  }
}

// Watch for open state to reset form when modal opens
watch(() => props.open, (open) => {
  if (open && !props.address) {
    resetForm()
  }
})
</script>

<template>
  <BaseModal
    :open="open"
    size="md"
    @close="$emit('close')"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <BaseHeading
          as="h3"
          size="lg"
          weight="semibold"
          class="text-muted-900 dark:text-white"
        >
          {{ editing ? 'Edit' : 'Add' }} {{ type === 'mailing' ? 'Mailing' : 'Legal' }} Address
        </BaseHeading>
        <BaseButton
          size="sm"
          variant="ghost"
          @click="$emit('close')"
        >
          <Icon name="lucide:x" class="size-4" />
        </BaseButton>
      </div>
    </template>

    <form class="space-y-6" @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 gap-4">
        <!-- Address Name -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.name"
          :disabled="isSubmitting"
        >
          <BaseLabel>Address Name (Optional)</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.name"
            placeholder="e.g., Home, Office, etc."
            :aria-invalid="errors.name ? 'true' : undefined"
          />
        </BaseField>

        <!-- Address Line 1 -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.address_line_1"
          :disabled="isSubmitting"
          required
        >
          <BaseLabel>Address Line 1</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.address_line_1"
            placeholder="Street address, building, etc."
            :aria-invalid="errors.address_line_1 ? 'true' : undefined"
            required
          />
        </BaseField>

        <!-- Address Line 2 -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.address_line_2"
          :disabled="isSubmitting"
        >
          <BaseLabel>Address Line 2 (Optional)</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.address_line_2"
            placeholder="Apartment, suite, unit, etc."
            :aria-invalid="errors.address_line_2 ? 'true' : undefined"
          />
        </BaseField>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <!-- City -->
          <BaseField
            v-slot="{ inputAttrs, inputRef }"
            :error="errors.city"
            :disabled="isSubmitting"
            required
          >
            <BaseLabel>City</BaseLabel>
            <TairoInput
              :ref="inputRef"
              v-bind="inputAttrs"
              v-model="form.city"
              placeholder="City"
              :aria-invalid="errors.city ? 'true' : undefined"
              required
            />
          </BaseField>

          <!-- State/Province -->
          <BaseField
            v-slot="{ inputAttrs, inputRef }"
            :error="errors.state"
            :disabled="isSubmitting"
          >
            <BaseLabel>State/Province (Optional)</BaseLabel>
            <TairoInput
              :ref="inputRef"
              v-bind="inputAttrs"
              v-model="form.state"
              placeholder="State or Province"
              :aria-invalid="errors.state ? 'true' : undefined"
            />
          </BaseField>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <!-- Postal Code -->
          <BaseField
            v-slot="{ inputAttrs, inputRef }"
            :error="errors.postal_code"
            :disabled="isSubmitting"
          >
            <BaseLabel>Postal Code (Optional)</BaseLabel>
            <TairoInput
              :ref="inputRef"
              v-bind="inputAttrs"
              v-model="form.postal_code"
              placeholder="Postal/ZIP Code"
              :aria-invalid="errors.postal_code ? 'true' : undefined"
            />
          </BaseField>

          <!-- Country -->
          <BaseField
            v-slot="{ inputAttrs, inputRef }"
            :error="errors.country"
            :disabled="isSubmitting"
            required
          >
            <BaseLabel>Country</BaseLabel>
            <TairoInput
              :ref="inputRef"
              v-bind="inputAttrs"
              v-model="form.country"
              placeholder="Country"
              :aria-invalid="errors.country ? 'true' : undefined"
              required
            />
          </BaseField>
        </div>

        <!-- Primary Address Toggle -->
        <div class="flex items-center gap-3">
          <BaseCheckbox
            v-model="form.is_primary"
            :disabled="isSubmitting"
            color="primary"
          />
          <div>
            <BaseLabel class="text-sm font-medium text-muted-700 dark:text-muted-300">
              Set as primary {{ type === 'mailing' ? 'mailing' : 'legal' }} address
            </BaseLabel>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              {{ type === 'mailing' ? 'This will be your default shipping address' : 'This will be your default billing address' }}
            </BaseParagraph>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="submitError" class="p-3 bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg">
        <div class="flex items-start gap-3">
          <Icon name="lucide:alert-circle" class="size-4 text-danger-500 mt-0.5 flex-shrink-0" />
          <div>
            <BaseHeading as="h4" size="xs" class="text-danger-700 dark:text-danger-400 mb-1">
              Error saving address
            </BaseHeading>
            <BaseParagraph size="sm" class="text-danger-600 dark:text-danger-300">
              {{ submitError }}
            </BaseParagraph>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-end gap-3 pt-4 border-t border-muted-200 dark:border-muted-800">
        <BaseButton
          variant="ghost"
          :disabled="isSubmitting"
          @click="$emit('close')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          type="submit"
          variant="primary"
          :disabled="!isFormValid || isSubmitting"
          :loading="isSubmitting"
        >
          {{ editing ? 'Update' : 'Add' }} Address
        </BaseButton>
      </div>
    </form>
  </BaseModal>
</template>
