<script setup lang="ts">
import type { ConnectionFormData, Integration } from '~/types/ui'
import { z } from 'zod'

interface Props {
  open: boolean
  integration?: Integration
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: ConnectionFormData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form validation schema
const apiKeySchema = z.object({
  apiKey: z.string()
    .min(1, 'API key is required')
    .min(10, 'API key must be at least 10 characters')
    .refine(
      (key) => {
        // Basic format validation for different providers
        if (props.integration?.id === 'openai') {
          return key.startsWith('sk-') && key.length >= 40
        }
        if (props.integration?.id === 'anthropic') {
          return key.startsWith('sk-ant-') && key.length >= 40
        }
        if (props.integration?.id === 'grok') {
          return key.startsWith('xai-') || key.length >= 20
        }
        return true
      },
      {
        message: 'Invalid API key format for this provider',
      },
    ),
  testConnection: z.boolean().default(true),
})

type FormData = z.infer<typeof apiKeySchema>

// Form state
const formData = ref<FormData>({
  apiKey: '',
  testConnection: true,
})

const errors = ref<Partial<Record<keyof FormData, string>>>({})
const isSubmitting = ref(false)
const showApiKey = ref(false)

// Validation
function validateForm(): boolean {
  try {
    apiKeySchema.parse(formData.value)
    errors.value = {}
    return true
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      errors.value = error.errors.reduce((acc, err) => {
        const path = err.path[0] as keyof FormData
        acc[path] = err.message
        return acc
      }, {} as Record<keyof FormData, string>)
    }
    return false
  }
}

// Submit handler
async function handleSubmit() {
  if (!props.integration || !validateForm())
    return

  isSubmitting.value = true

  try {
    const connectionData: ConnectionFormData = {
      integration: props.integration,
      credentials: {
        apiKey: formData.value.apiKey,
      },
      testConnection: formData.value.testConnection,
    }

    emit('submit', connectionData)
  }
  finally {
    isSubmitting.value = false
  }
}

// Reset form when modal closes
watch(() => props.open, (isOpen) => {
  if (!isOpen) {
    formData.value = {
      apiKey: '',
      testConnection: true,
    }
    errors.value = {}
    showApiKey.value = false
  }
})

// Provider-specific instructions
function getInstructions() {
  switch (props.integration?.id) {
    case 'openai':
      return {
        title: 'Get your OpenAI API Key',
        steps: [
          'Go to platform.openai.com/api-keys',
          'Click "Create new secret key"',
          'Copy the key that starts with "sk-"',
          'Paste it below',
        ],
        docsUrl: 'https://platform.openai.com/docs/quickstart',
      }
    case 'anthropic':
      return {
        title: 'Get your Anthropic API Key',
        steps: [
          'Go to console.anthropic.com',
          'Navigate to "API Keys"',
          'Create a new key',
          'Copy the key that starts with "sk-ant-"',
        ],
        docsUrl: 'https://docs.anthropic.com/en/api/getting-started',
      }
    case 'grok':
      return {
        title: 'Get your xAI API Key',
        steps: [
          'Go to console.x.ai',
          'Navigate to API Keys section',
          'Generate a new API key',
          'Copy the generated key',
        ],
        docsUrl: 'https://docs.x.ai/',
      }
    default:
      return {
        title: 'Get your API Key',
        steps: ['Follow the provider documentation to obtain an API key'],
        docsUrl: props.integration?.docsUrl,
      }
  }
}

const instructions = computed(() => getInstructions())
</script>

<template>
  <BaseModal
    :open="open"
    size="md"
    @close="emit('close')"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <div class="flex size-12 items-center justify-center rounded-lg bg-muted-100 dark:bg-muted-800">
          <Icon
            v-if="integration?.icon"
            :name="integration.icon"
            class="size-6 text-muted-600 dark:text-muted-400"
          />
        </div>
        <div>
          <BaseHeading as="h3" size="lg" weight="semibold">
            Connect {{ integration?.name }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Enter your API key to connect
          </BaseParagraph>
        </div>
      </div>
    </template>

    <div class="space-y-6">
      <!-- Instructions -->
      <div class="bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
        <BaseHeading as="h4" size="sm" weight="medium" class="mb-3">
          {{ instructions.title }}
        </BaseHeading>
        <ol class="list-decimal list-inside space-y-1 text-sm text-muted-600 dark:text-muted-400">
          <li v-for="(step, index) in instructions.steps" :key="index">
            {{ step }}
          </li>
        </ol>
        <div class="mt-3">
          <BaseButton
            size="xs"
            variant="outline"
            :to="instructions.docsUrl"
            external
            target="_blank"
          >
            <Icon name="lucide:external-link" class="size-3 mr-1" />
            View Documentation
          </BaseButton>
        </div>
      </div>

      <!-- Form -->
      <form class="space-y-4" @submit.prevent="handleSubmit">
        <!-- API Key Input -->
        <div>
          <BaseLabel for="apiKey" class="mb-2">
            API Key
          </BaseLabel>
          <div class="relative">
            <TairoInput
              id="apiKey"
              v-model="formData.apiKey"
              :type="showApiKey ? 'text' : 'password'"
              placeholder="Enter your API key..."
              :error="!!errors.apiKey"
              class="pr-10"
              @input="() => validateForm()"
            />
            <button
              type="button"
              class="absolute right-3 top-1/2 -translate-y-1/2 text-muted-400 hover:text-muted-600"
              @click="showApiKey = !showApiKey"
            >
              <Icon
                :name="showApiKey ? 'lucide:eye-off' : 'lucide:eye'"
                class="size-4"
              />
            </button>
          </div>
          <BaseParagraph
            v-if="errors.apiKey"
            size="xs"
            class="text-destructive-500 mt-1"
          >
            {{ errors.apiKey }}
          </BaseParagraph>
        </div>

        <!-- Test Connection Checkbox -->
        <div class="flex items-center gap-2">
          <BaseCheckbox
            id="testConnection"
            v-model="formData.testConnection"
          />
          <BaseLabel for="testConnection" class="text-sm">
            Test connection before saving
          </BaseLabel>
        </div>

        <!-- Security Notice -->
        <div class="bg-info-50 dark:bg-info-950 border border-info-200 dark:border-info-800 rounded-lg p-3">
          <div class="flex items-start gap-2">
            <Icon name="lucide:shield-check" class="size-4 text-info-600 dark:text-info-400 mt-0.5 flex-shrink-0" />
            <div class="text-xs text-info-700 dark:text-info-300">
              <strong>Secure Storage:</strong> Your API key will be encrypted and stored securely.
              Only you can access it within your workspace.
            </div>
          </div>
        </div>
      </form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <BaseButton
          variant="outline"
          @click="emit('close')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          variant="primary"
          :disabled="isSubmitting || loading"
          @click="handleSubmit"
        >
          <Icon
            v-if="isSubmitting || loading"
            name="lucide:loader-2"
            class="size-4 animate-spin mr-2"
          />
          {{ formData.testConnection ? 'Test & Connect' : 'Connect' }}
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
