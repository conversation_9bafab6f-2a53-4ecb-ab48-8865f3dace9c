<script setup lang="ts">
import type { ConnectionFormData, Integration } from '~/types/ui'
import { z } from 'zod'

interface Props {
  open: boolean
  integration?: Integration
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: ConnectionFormData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form validation schema
const smtpSchema = z.object({
  connectionName: z.string()
    .min(1, 'Connection name is required')
    .min(2, 'Connection name must be at least 2 characters')
    .max(50, 'Connection name must be less than 50 characters'),
  host: z.string()
    .min(1, 'SMTP host is required')
    .refine((host) => {
      // Basic hostname validation
      const hostnameRegex = /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?(\.[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i
      return hostnameRegex.test(host)
    }, 'Invalid hostname format'),
  port: z.number()
    .min(1, 'Port must be greater than 0')
    .max(65535, 'Port must be less than 65536')
    .default(587),
  username: z.string()
    .min(1, 'Username/Email is required')
    .email('Must be a valid email address'),
  password: z.string()
    .min(1, 'Password is required')
    .min(6, 'Password must be at least 6 characters'),
  security: z.enum(['none', 'tls', 'ssl']).default('tls'),
  // IMAP settings for receiving emails
  enableImap: z.boolean().default(true),
  imapHost: z.string().optional(),
  imapPort: z.number().min(1).max(65535).default(993).optional(),
  imapSecurity: z.enum(['none', 'tls', 'ssl']).default('ssl').optional(),
  testConnection: z.boolean().default(true),
})

type FormData = z.infer<typeof smtpSchema>

// Form state
const formData = ref<FormData>({
  connectionName: '',
  host: '',
  port: 587,
  username: '',
  password: '',
  security: 'tls',
  enableImap: true,
  imapHost: '',
  imapPort: 993,
  imapSecurity: 'ssl',
  testConnection: true,
})

const errors = ref<Partial<Record<keyof FormData, string>>>({})
const isSubmitting = ref(false)
const showPassword = ref(false)
const showAdvanced = ref(false)

// Security options
const securityOptions = [
  { label: 'None', value: 'none' },
  { label: 'TLS/STARTTLS', value: 'tls' },
  { label: 'SSL', value: 'ssl' },
]

// Common SMTP providers presets
const commonProviders = [
  {
    name: 'Gmail',
    host: 'smtp.gmail.com',
    port: 587,
    security: 'tls',
    imapHost: 'imap.gmail.com',
    imapPort: 993,
    imapSecurity: 'ssl',
  },
  {
    name: 'Outlook/Hotmail',
    host: 'smtp-mail.outlook.com',
    port: 587,
    security: 'tls',
    imapHost: 'outlook.office365.com',
    imapPort: 993,
    imapSecurity: 'ssl',
  },
  {
    name: 'Yahoo',
    host: 'smtp.mail.yahoo.com',
    port: 587,
    security: 'tls',
    imapHost: 'imap.mail.yahoo.com',
    imapPort: 993,
    imapSecurity: 'ssl',
  },
]

// Auto-fill IMAP settings based on SMTP host
watch(() => formData.value.host, (newHost) => {
  if (!newHost || !formData.value.enableImap)
    return

  // Try to auto-fill IMAP host
  if (!formData.value.imapHost) {
    const imapHost = newHost.replace('smtp.', 'imap.')
    if (imapHost !== newHost) {
      formData.value.imapHost = imapHost
    }
  }
})

// Apply provider preset
function applyPreset(provider: typeof commonProviders[0]) {
  formData.value.host = provider.host
  formData.value.port = provider.port
  formData.value.security = provider.security as 'none' | 'tls' | 'ssl'

  if (formData.value.enableImap) {
    formData.value.imapHost = provider.imapHost
    formData.value.imapPort = provider.imapPort
    formData.value.imapSecurity = provider.imapSecurity as 'none' | 'tls' | 'ssl'
  }
}

// Validation
function validateForm(): boolean {
  try {
    // Adjust schema for IMAP fields if enabled
    let schema = smtpSchema
    if (formData.value.enableImap) {
      schema = smtpSchema.extend({
        imapHost: z.string().min(1, 'IMAP host is required when IMAP is enabled'),
        imapPort: z.number().min(1, 'IMAP port is required').max(65535, 'Invalid IMAP port'),
      })
    }

    schema.parse(formData.value)
    errors.value = {}
    return true
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      errors.value = error.errors.reduce((acc, err) => {
        const path = err.path[0] as keyof FormData
        acc[path] = err.message
        return acc
      }, {} as Record<keyof FormData, string>)
    }
    return false
  }
}

// Submit handler
async function handleSubmit() {
  if (!props.integration || !validateForm())
    return

  isSubmitting.value = true

  try {
    const credentials: any = {
      connectionName: formData.value.connectionName,
      host: formData.value.host,
      port: formData.value.port,
      username: formData.value.username,
      password: formData.value.password,
      security: formData.value.security,
    }

    // Add IMAP settings if enabled
    if (formData.value.enableImap && formData.value.imapHost) {
      credentials.imap = {
        host: formData.value.imapHost,
        port: formData.value.imapPort,
        security: formData.value.imapSecurity,
      }
    }

    const connectionData: ConnectionFormData = {
      integration: props.integration,
      credentials,
      testConnection: formData.value.testConnection,
    }

    emit('submit', connectionData)
  }
  finally {
    isSubmitting.value = false
  }
}

// Reset form when modal closes
watch(() => props.open, (isOpen) => {
  if (!isOpen) {
    formData.value = {
      connectionName: '',
      host: '',
      port: 587,
      username: '',
      password: '',
      security: 'tls',
      enableImap: true,
      imapHost: '',
      imapPort: 993,
      imapSecurity: 'ssl',
      testConnection: true,
    }
    errors.value = {}
    showPassword.value = false
    showAdvanced.value = false
  }
})
</script>

<template>
  <BaseModal
    :open="open"
    size="lg"
    @close="emit('close')"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <div class="flex size-12 items-center justify-center rounded-lg bg-muted-100 dark:bg-muted-800">
          <Icon
            v-if="integration?.icon"
            :name="integration.icon"
            class="size-6 text-muted-600 dark:text-muted-400"
          />
        </div>
        <div>
          <BaseHeading as="h3" size="lg" weight="semibold">
            Connect SMTP Email
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Configure your SMTP server settings
          </BaseParagraph>
        </div>
      </div>
    </template>

    <div class="space-y-6">
      <!-- Provider Presets -->
      <div class="bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
        <BaseHeading as="h4" size="sm" weight="medium" class="mb-3">
          Quick Setup for Common Providers
        </BaseHeading>
        <div class="flex flex-wrap gap-2">
          <BaseButton
            v-for="provider in commonProviders"
            :key="provider.name"
            size="xs"
            variant="outline"
            @click="applyPreset(provider)"
          >
            {{ provider.name }}
          </BaseButton>
        </div>
        <BaseParagraph size="xs" class="text-muted-500 mt-2">
          Click a provider to auto-fill settings, then enter your credentials
        </BaseParagraph>
      </div>

      <!-- Form -->
      <form class="space-y-6" @submit.prevent="handleSubmit">
        <!-- Connection Name -->
        <div>
          <BaseLabel for="connectionName" class="mb-2">
            Connection Name *
          </BaseLabel>
          <TairoInput
            id="connectionName"
            v-model="formData.connectionName"
            placeholder="e.g., My Work Email, Personal Gmail"
            :error="!!errors.connectionName"
            @input="() => validateForm()"
          />
          <BaseParagraph
            v-if="errors.connectionName"
            size="xs"
            class="text-destructive-500 mt-1"
          >
            {{ errors.connectionName }}
          </BaseParagraph>
          <BaseParagraph size="xs" class="text-muted-500 mt-1">
            A friendly name to identify this email connection
          </BaseParagraph>
        </div>

        <!-- SMTP Server Settings -->
        <div class="space-y-4">
          <BaseHeading as="h4" size="sm" weight="medium">
            SMTP Server Settings
          </BaseHeading>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Host -->
            <div class="md:col-span-2">
              <BaseLabel for="host" class="mb-2">
                SMTP Host *
              </BaseLabel>
              <TairoInput
                id="host"
                v-model="formData.host"
                placeholder="smtp.example.com"
                :error="!!errors.host"
                @input="() => validateForm()"
              />
              <BaseParagraph
                v-if="errors.host"
                size="xs"
                class="text-destructive-500 mt-1"
              >
                {{ errors.host }}
              </BaseParagraph>
            </div>

            <!-- Port -->
            <div>
              <BaseLabel for="port" class="mb-2">
                Port *
              </BaseLabel>
              <TairoInput
                id="port"
                v-model.number="formData.port"
                type="number"
                min="1"
                max="65535"
                :error="!!errors.port"
                @input="() => validateForm()"
              />
              <BaseParagraph
                v-if="errors.port"
                size="xs"
                class="text-destructive-500 mt-1"
              >
                {{ errors.port }}
              </BaseParagraph>
            </div>
          </div>

          <!-- Security -->
          <div>
            <BaseLabel class="mb-2">
              Security
            </BaseLabel>
            <div class="flex gap-4">
              <BaseRadio
                v-for="option in securityOptions"
                :id="`security-${option.value}`"
                :key="option.value"
                v-model="formData.security"
                :value="option.value"
                name="security"
                :label="option.label"
              />
            </div>
          </div>
        </div>

        <!-- Credentials -->
        <div class="space-y-4">
          <BaseHeading as="h4" size="sm" weight="medium">
            Email Credentials
          </BaseHeading>

          <!-- Username/Email -->
          <div>
            <BaseLabel for="username" class="mb-2">
              Email Address *
            </BaseLabel>
            <TairoInput
              id="username"
              v-model="formData.username"
              type="email"
              placeholder="<EMAIL>"
              :error="!!errors.username"
              @input="() => validateForm()"
            />
            <BaseParagraph
              v-if="errors.username"
              size="xs"
              class="text-destructive-500 mt-1"
            >
              {{ errors.username }}
            </BaseParagraph>
          </div>

          <!-- Password -->
          <div>
            <BaseLabel for="password" class="mb-2">
              Password *
            </BaseLabel>
            <div class="relative">
              <TairoInput
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="Enter your email password or app password"
                :error="!!errors.password"
                class="pr-10"
                @input="() => validateForm()"
              />
              <button
                type="button"
                class="absolute right-3 top-1/2 -translate-y-1/2 text-muted-400 hover:text-muted-600"
                @click="showPassword = !showPassword"
              >
                <Icon
                  :name="showPassword ? 'lucide:eye-off' : 'lucide:eye'"
                  class="size-4"
                />
              </button>
            </div>
            <BaseParagraph
              v-if="errors.password"
              size="xs"
              class="text-destructive-500 mt-1"
            >
              {{ errors.password }}
            </BaseParagraph>
            <BaseParagraph size="xs" class="text-muted-500 mt-1">
              For Gmail/Google Workspace, use an App Password instead of your regular password
            </BaseParagraph>
          </div>
        </div>

        <!-- IMAP Settings (Advanced) -->
        <div>
          <div class="flex items-center justify-between mb-4">
            <div>
              <BaseHeading as="h4" size="sm" weight="medium">
                Receive Email Settings (IMAP)
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-500">
                Configure IMAP to receive and read emails
              </BaseParagraph>
            </div>
            <BaseSwitch
              v-model="formData.enableImap"
              label="Enable IMAP"
            />
          </div>

          <div v-if="formData.enableImap" class="space-y-4 bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- IMAP Host -->
              <div class="md:col-span-2">
                <BaseLabel for="imapHost" class="mb-2">
                  IMAP Host *
                </BaseLabel>
                <TairoInput
                  id="imapHost"
                  v-model="formData.imapHost"
                  placeholder="imap.example.com"
                  :error="!!errors.imapHost"
                  @input="() => validateForm()"
                />
                <BaseParagraph
                  v-if="errors.imapHost"
                  size="xs"
                  class="text-destructive-500 mt-1"
                >
                  {{ errors.imapHost }}
                </BaseParagraph>
              </div>

              <!-- IMAP Port -->
              <div>
                <BaseLabel for="imapPort" class="mb-2">
                  IMAP Port *
                </BaseLabel>
                <TairoInput
                  id="imapPort"
                  v-model.number="formData.imapPort"
                  type="number"
                  min="1"
                  max="65535"
                  :error="!!errors.imapPort"
                  @input="() => validateForm()"
                />
                <BaseParagraph
                  v-if="errors.imapPort"
                  size="xs"
                  class="text-destructive-500 mt-1"
                >
                  {{ errors.imapPort }}
                </BaseParagraph>
              </div>
            </div>

            <!-- IMAP Security -->
            <div>
              <BaseLabel class="mb-2">
                IMAP Security
              </BaseLabel>
              <div class="flex gap-4">
                <BaseRadio
                  v-for="option in securityOptions"
                  :id="`imap-security-${option.value}`"
                  :key="`imap-${option.value}`"
                  v-model="formData.imapSecurity"
                  :value="option.value"
                  name="imapSecurity"
                  :label="option.label"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Test Connection -->
        <div class="flex items-center gap-2">
          <BaseCheckbox
            id="testConnection"
            v-model="formData.testConnection"
          />
          <BaseLabel for="testConnection" class="text-sm">
            Test connection before saving
          </BaseLabel>
        </div>

        <!-- Security Notice -->
        <div class="bg-info-50 dark:bg-info-950 border border-info-200 dark:border-info-800 rounded-lg p-3">
          <div class="flex items-start gap-2">
            <Icon name="lucide:shield-check" class="size-4 text-info-600 dark:text-info-400 mt-0.5 flex-shrink-0" />
            <div class="text-xs text-info-700 dark:text-info-300">
              <strong>Secure Storage:</strong> Your email credentials will be encrypted and stored securely.
              Only you can access them within your workspace.
            </div>
          </div>
        </div>
      </form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <BaseButton
          variant="outline"
          @click="emit('close')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          variant="primary"
          :disabled="isSubmitting || loading"
          @click="handleSubmit"
        >
          <Icon
            v-if="isSubmitting || loading"
            name="lucide:loader-2"
            class="size-4 animate-spin mr-2"
          />
          {{ formData.testConnection ? 'Test & Connect' : 'Connect' }}
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
