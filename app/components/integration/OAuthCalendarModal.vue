<script setup lang="ts">
import type { ConnectionFormData, Integration } from '~/types/ui'
import { z } from 'zod'

interface Props {
  open: boolean
  integration?: Integration
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: ConnectionFormData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form validation schema
const oauthCalendarSchema = z.object({
  connectionName: z.string()
    .min(1, 'Connection name is required')
    .min(2, 'Connection name must be at least 2 characters')
    .max(50, 'Connection name must be less than 50 characters'),
  testConnection: z.boolean().default(true),
})

type FormData = z.infer<typeof oauthCalendarSchema>

// Form state
const formData = ref<FormData>({
  connectionName: '',
  testConnection: true,
})

const errors = ref<Partial<Record<keyof FormData, string>>>({})
const isSubmitting = ref(false)
const isAuthenticating = ref(false)
const oauthData = ref<{
  accessToken?: string
  refreshToken?: string
  email?: string
  expiresAt?: Date
  primaryCalendarId?: string
  calendarsTotal?: number
} | null>(null)

// Provider-specific configurations
function getProviderConfig() {
  switch (props.integration?.id) {
    case 'google-calendar':
      return {
        scopes: [
          'https://www.googleapis.com/auth/calendar.readonly',
          'https://www.googleapis.com/auth/calendar.events',
          'https://www.googleapis.com/auth/calendar.calendars',
          'https://www.googleapis.com/auth/userinfo.email',
        ],
        authUrl: 'https://accounts.google.com/o/oauth2/auth',
        configKey: 'googleOAuthClientId',
        callbackPath: '/api/oauth/callback/google-calendar',
      }
    case 'microsoft-calendar':
      return {
        scopes: [
          'https://graph.microsoft.com/Calendars.Read',
          'https://graph.microsoft.com/Calendars.ReadWrite',
          'https://graph.microsoft.com/User.Read',
        ],
        authUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        configKey: 'microsoftOAuthClientId',
        callbackPath: '/api/oauth/callback/microsoft-calendar',
      }
    default:
      return null
  }
}

const providerConfig = computed(() => getProviderConfig())

// Validation
function validateForm(): boolean {
  try {
    oauthCalendarSchema.parse(formData.value)
    errors.value = {}
    return true
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      errors.value = error.errors.reduce((acc, err) => {
        const path = err.path[0] as keyof FormData
        acc[path] = err.message
        return acc
      }, {} as Record<keyof FormData, string>)
    }
    return false
  }
}

// OAuth authentication flow
async function initiateOAuthFlow() {
  if (!props.integration || !providerConfig.value)
    return

  isAuthenticating.value = true

  try {
    // Get OAuth client ID from runtime config
    const config = useRuntimeConfig()
    const clientId = config.public[providerConfig.value.configKey]

    if (!clientId) {
      throw new Error(`${props.integration.name} OAuth client ID not configured. Please contact support.`)
    }

    // Create OAuth URL with provider-specific parameters
    const oauthParams = new URLSearchParams({
      client_id: clientId,
      redirect_uri: `${window.location.origin}${providerConfig.value.callbackPath}`,
      scope: providerConfig.value.scopes.join(' '),
      response_type: 'code',
      state: `${props.integration.id}-${Date.now()}`, // Include integration ID in state
    })

    // Add provider-specific parameters
    if (props.integration.id === 'google-calendar') {
      oauthParams.set('access_type', 'offline')
      oauthParams.set('prompt', 'consent')
    }

    const oauthUrl = `${providerConfig.value.authUrl}?${oauthParams.toString()}`

    // Open OAuth popup window
    const popup = window.open(
      oauthUrl,
      'oauth-popup',
      'width=500,height=600,scrollbars=yes,resizable=yes',
    )

    if (!popup) {
      throw new Error('Popup blocked. Please allow popups for this site.')
    }

    // Listen for OAuth callback
    const oauthResult = await waitForOAuthCallback(popup)

    if (oauthResult.success && oauthResult.data) {
      oauthData.value = {
        accessToken: oauthResult.data.access_token,
        refreshToken: oauthResult.data.refresh_token,
        email: oauthResult.data.email,
        expiresAt: new Date(Date.now() + (oauthResult.data.expires_in * 1000)),
        primaryCalendarId: oauthResult.data.primaryCalendarId,
        calendarsTotal: oauthResult.data.calendarsTotal,
      }

      // Auto-fill connection name with email if not provided
      if (!formData.value.connectionName && oauthResult.data.email) {
        formData.value.connectionName = `${oauthResult.data.email} Calendar`
      }
    }
    else {
      throw new Error(oauthResult.error || 'OAuth authentication failed')
    }
  }
  catch (error: any) {
    console.error('OAuth flow failed:', error)
    // Show error to user
    errors.value.general = error.message || 'Authentication failed'
  }
  finally {
    isAuthenticating.value = false
  }
}

// Wait for OAuth callback from popup
function waitForOAuthCallback(popup: Window): Promise<{
  success: boolean
  data?: any
  error?: string
}> {
  return new Promise((resolve) => {
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        clearInterval(checkClosed)
        resolve({ success: false, error: 'Authentication was cancelled' })
      }
    }, 1000)

    // Listen for messages from the OAuth callback
    const handleMessage = (event: MessageEvent) => {
      // Validate origin for security
      if (event.origin !== window.location.origin)
        return

      if (event.data.type === 'oauth-success') {
        clearInterval(checkClosed)
        window.removeEventListener('message', handleMessage)
        popup.close()
        resolve({ success: true, data: event.data.data })
      }
      else if (event.data.type === 'oauth-error') {
        clearInterval(checkClosed)
        window.removeEventListener('message', handleMessage)
        popup.close()
        resolve({ success: false, error: event.data.error })
      }
    }

    window.addEventListener('message', handleMessage)
  })
}

// Submit handler
async function handleSubmit() {
  if (!props.integration || !validateForm() || !oauthData.value)
    return

  isSubmitting.value = true

  try {
    const connectionData: ConnectionFormData = {
      integration: props.integration,
      credentials: {
        connectionName: formData.value.connectionName,
        oauth: {
          accessToken: oauthData.value.accessToken,
          refreshToken: oauthData.value.refreshToken,
          email: oauthData.value.email,
          expiresAt: oauthData.value.expiresAt,
          scopes: providerConfig.value?.scopes || [],
        },
      },
      testConnection: formData.value.testConnection,
    }

    emit('submit', connectionData)
  }
  finally {
    isSubmitting.value = false
  }
}

// Reset form when modal closes
watch(() => props.open, (isOpen) => {
  if (!isOpen) {
    formData.value = {
      connectionName: '',
      testConnection: true,
    }
    errors.value = {}
    oauthData.value = null
    isAuthenticating.value = false
  }
})

// Get OAuth provider instructions
function getInstructions() {
  switch (props.integration?.id) {
    case 'google-calendar':
      return {
        title: 'Connect Your Google Calendar',
        steps: [
          'Click "Authenticate with Google" below',
          'Sign in to your Google account in the popup window',
          'Grant permission to access your Google Calendar',
          'Return to this page to complete the connection',
        ],
        permissions: [
          'Read calendar events',
          'Create and edit calendar events',
          'Delete calendar events',
          'Manage event reminders and attachments',
          'Access your email address for identification',
        ],
      }
    case 'microsoft-calendar':
      return {
        title: 'Connect Your Microsoft Calendar',
        steps: [
          'Click "Authenticate with Microsoft" below',
          'Sign in to your Microsoft account in the popup window',
          'Grant permission to access your Outlook Calendar',
          'Return to this page to complete the connection',
        ],
        permissions: [
          'Read calendar events',
          'Create and edit calendar events',
          'Delete calendar events',
          'Access your profile information',
        ],
      }
    default:
      return {
        title: 'Connect Your Calendar',
        steps: ['Follow the authentication flow to connect your calendar'],
        permissions: [],
      }
  }
}

const instructions = computed(() => getInstructions())

// Get provider-specific icon
const providerIcon = computed(() => {
  switch (props.integration?.id) {
    case 'google-calendar':
      return 'simple-icons:googlecalendar'
    case 'microsoft-calendar':
      return 'simple-icons:microsoftoutlook'
    default:
      return 'lucide:calendar'
  }
})

// Get provider-specific button text
const authButtonText = computed(() => {
  switch (props.integration?.id) {
    case 'google-calendar':
      return isAuthenticating.value ? 'Authenticating...' : 'Authenticate with Google'
    case 'microsoft-calendar':
      return isAuthenticating.value ? 'Authenticating...' : 'Authenticate with Microsoft'
    default:
      return isAuthenticating.value ? 'Authenticating...' : 'Authenticate'
  }
})
</script>

<template>
  <BaseModal
    :open="open"
    size="md"
    @close="emit('close')"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <div class="flex size-12 items-center justify-center rounded-lg bg-muted-100 dark:bg-muted-800">
          <Icon
            v-if="integration?.icon"
            :name="integration.icon"
            class="size-6 text-muted-600 dark:text-muted-400"
          />
        </div>
        <div>
          <BaseHeading as="h3" size="lg" weight="semibold">
            Connect {{ integration?.name }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Secure OAuth authentication
          </BaseParagraph>
        </div>
      </div>
    </template>

    <div class="space-y-6">
      <!-- Instructions -->
      <div class="bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
        <BaseHeading as="h4" size="sm" weight="medium" class="mb-3">
          {{ instructions.title }}
        </BaseHeading>
        <ol class="list-decimal list-inside space-y-1 text-sm text-muted-600 dark:text-muted-400">
          <li v-for="(step, index) in instructions.steps" :key="index">
            {{ step }}
          </li>
        </ol>
      </div>

      <!-- Permissions -->
      <div v-if="instructions.permissions.length > 0" class="bg-amber-50 dark:bg-amber-950 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
        <div class="flex items-start gap-2 mb-3">
          <Icon name="lucide:shield-check" class="size-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
          <BaseHeading as="h4" size="sm" weight="medium" class="text-amber-800 dark:text-amber-200">
            Required Permissions
          </BaseHeading>
        </div>
        <ul class="space-y-1 text-sm text-amber-700 dark:text-amber-300">
          <li v-for="permission in instructions.permissions" :key="permission" class="flex items-start gap-2">
            <span class="text-amber-500 mt-1">•</span>
            <span>{{ permission }}</span>
          </li>
        </ul>
      </div>

      <!-- Authentication Status -->
      <div v-if="!oauthData" class="text-center py-8">
        <div class="mb-4">
          <Icon
            :name="providerIcon"
            class="size-16 mx-auto text-muted-400"
          />
        </div>

        <BaseHeading as="h4" size="sm" weight="medium" class="mb-2">
          Ready to Connect
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-500 mb-6">
          Click the button below to authenticate with {{ integration?.name }}
        </BaseParagraph>

        <BaseButton
          variant="primary"
          :disabled="isAuthenticating"
          class="mx-auto"
          @click="initiateOAuthFlow"
        >
          <Icon
            v-if="isAuthenticating"
            name="lucide:loader-2"
            class="size-4 animate-spin mr-2"
          />
          <Icon
            v-else
            :name="providerIcon"
            class="size-4 mr-2"
          />
          {{ authButtonText }}
        </BaseButton>
      </div>

      <!-- Authenticated State -->
      <div v-else class="space-y-4">
        <div class="bg-success-50 dark:bg-success-950 border border-success-200 dark:border-success-800 rounded-lg p-4">
          <div class="flex items-center gap-3 mb-3">
            <Icon name="lucide:check-circle" class="size-5 text-success-600 dark:text-success-400" />
            <BaseHeading as="h4" size="sm" weight="medium" class="text-success-800 dark:text-success-200">
              Calendar Connected!
            </BaseHeading>
          </div>
          <div class="text-sm text-success-700 dark:text-success-300">
            <p><strong>Connected Email:</strong> {{ oauthData.email }}</p>
            <p v-if="oauthData.calendarsTotal">
              <strong>Calendars:</strong> {{ oauthData.calendarsTotal }} calendar{{ oauthData.calendarsTotal !== 1 ? 's' : '' }} available
            </p>
            <p class="text-xs mt-1 text-success-600 dark:text-success-400">
              Token expires: {{ oauthData.expiresAt?.toLocaleDateString() }}
            </p>
          </div>
        </div>

        <!-- Connection Name -->
        <form class="space-y-4" @submit.prevent="handleSubmit">
          <div>
            <BaseLabel for="connectionName" class="mb-2">
              Connection Name *
            </BaseLabel>
            <TairoInput
              id="connectionName"
              v-model="formData.connectionName"
              placeholder="e.g., My Google Calendar, Work Calendar"
              :error="!!errors.connectionName"
              @input="() => validateForm()"
            />
            <BaseParagraph
              v-if="errors.connectionName"
              size="xs"
              class="text-destructive-500 mt-1"
            >
              {{ errors.connectionName }}
            </BaseParagraph>
            <BaseParagraph size="xs" class="text-muted-500 mt-1">
              A friendly name to identify this calendar connection
            </BaseParagraph>
          </div>

          <!-- Test Connection -->
          <div class="flex items-center gap-2">
            <BaseCheckbox
              id="testConnection"
              v-model="formData.testConnection"
            />
            <BaseLabel for="testConnection" class="text-sm">
              Test connection before saving
            </BaseLabel>
          </div>
        </form>

        <!-- Re-authenticate Button -->
        <div class="flex items-center justify-center pt-4 border-t border-muted-200 dark:border-muted-700">
          <BaseButton
            size="xs"
            variant="outline"
            @click="() => { oauthData = null; errors = {} }"
          >
            <Icon name="lucide:refresh-cw" class="size-3 mr-1" />
            Re-authenticate
          </BaseButton>
        </div>
      </div>

      <!-- General Error -->
      <div v-if="errors.general" class="bg-destructive-50 dark:bg-destructive-950 border border-destructive-200 dark:border-destructive-800 rounded-lg p-3">
        <div class="flex items-start gap-2">
          <Icon name="lucide:alert-circle" class="size-4 text-destructive-600 dark:text-destructive-400 mt-0.5 flex-shrink-0" />
          <div class="text-sm text-destructive-700 dark:text-destructive-300">
            {{ errors.general }}
          </div>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="bg-info-50 dark:bg-info-950 border border-info-200 dark:border-info-800 rounded-lg p-3">
        <div class="flex items-start gap-2">
          <Icon name="lucide:shield-check" class="size-4 text-info-600 dark:text-info-400 mt-0.5 flex-shrink-0" />
          <div class="text-xs text-info-700 dark:text-info-300">
            <strong>Secure OAuth:</strong> We use industry-standard OAuth 2.0 authentication.
            Your credentials are never stored on our servers - only secure access tokens are saved.
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <BaseButton
          variant="outline"
          @click="emit('close')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          variant="primary"
          :disabled="isSubmitting || loading || !oauthData"
          @click="handleSubmit"
        >
          <Icon
            v-if="isSubmitting || loading"
            name="lucide:loader-2"
            class="size-4 animate-spin mr-2"
          />
          {{ formData.testConnection ? 'Test & Connect' : 'Connect' }}
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
