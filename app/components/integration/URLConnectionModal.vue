<script setup lang="ts">
import type { ConnectionFormData, Integration } from '~/types/ui'
import { z } from 'zod'

interface Props {
  open: boolean
  integration?: Integration
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: ConnectionFormData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form validation schema
const urlSchema = z.object({
  url: z.string()
    .min(1, 'URL is required')
    .url('Invalid URL format')
    .refine(
      (url) => {
        // Basic validation for different providers
        if (props.integration?.id === 'ollama') {
          try {
            const parsed = new URL(url)
            return parsed.protocol === 'http:' || parsed.protocol === 'https:'
          }
          catch {
            return false
          }
        }
        return true
      },
      {
        message: 'Invalid URL for this provider',
      },
    ),
  testConnection: z.boolean().default(true),
})

type FormData = z.infer<typeof urlSchema>

// Form state
const formData = ref<FormData>({
  url: '',
  testConnection: true,
})

const errors = ref<Partial<Record<keyof FormData, string>>>({})
const isSubmitting = ref(false)

// Set default URL for Ollama
watch(() => props.integration?.id, (integrationId) => {
  if (integrationId === 'ollama' && !formData.value.url) {
    formData.value.url = 'http://localhost:11434'
  }
})

// Validation
function validateForm(): boolean {
  try {
    urlSchema.parse(formData.value)
    errors.value = {}
    return true
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      errors.value = error.errors.reduce((acc, err) => {
        const path = err.path[0] as keyof FormData
        acc[path] = err.message
        return acc
      }, {} as Record<keyof FormData, string>)
    }
    return false
  }
}

// Submit handler
async function handleSubmit() {
  if (!props.integration || !validateForm())
    return

  isSubmitting.value = true

  try {
    const connectionData: ConnectionFormData = {
      integration: props.integration,
      credentials: {
        url: formData.value.url,
      },
      testConnection: formData.value.testConnection,
    }

    emit('submit', connectionData)
  }
  finally {
    isSubmitting.value = false
  }
}

// Reset form when modal closes
watch(() => props.open, (isOpen) => {
  if (!isOpen) {
    formData.value = {
      url: props.integration?.id === 'ollama' ? 'http://localhost:11434' : '',
      testConnection: true,
    }
    errors.value = {}
  }
})

// Provider-specific instructions
function getInstructions() {
  switch (props.integration?.id) {
    case 'ollama':
      return {
        title: 'Connect to your Ollama Server',
        steps: [
          'Install Ollama on your machine or server',
          'Start the Ollama service',
          'Verify it\'s running on port 11434 (default)',
          'Enter the server URL below',
        ],
        defaultUrl: 'http://localhost:11434',
        docsUrl: 'https://ollama.com/docs',
        examples: [
          'http://localhost:11434 (Local installation)',
          'http://*************:11434 (Local network)',
          'https://ollama.yourserver.com (Remote server)',
        ],
      }
    default:
      return {
        title: 'Connect to your Service',
        steps: ['Enter the URL of your service'],
        docsUrl: props.integration?.docsUrl,
        examples: ['https://api.yourservice.com'],
      }
  }
}

const instructions = computed(() => getInstructions())
</script>

<template>
  <BaseModal
    :open="open"
    size="md"
    @close="emit('close')"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <div class="flex size-12 items-center justify-center rounded-lg bg-muted-100 dark:bg-muted-800">
          <Icon
            v-if="integration?.icon"
            :name="integration.icon"
            class="size-6 text-muted-600 dark:text-muted-400"
          />
        </div>
        <div>
          <BaseHeading as="h3" size="lg" weight="semibold">
            Connect {{ integration?.name }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Enter the server URL to connect
          </BaseParagraph>
        </div>
      </div>
    </template>

    <div class="space-y-6">
      <!-- Instructions -->
      <div class="bg-muted-50 dark:bg-muted-900 rounded-lg p-4">
        <BaseHeading as="h4" size="sm" weight="medium" class="mb-3">
          {{ instructions.title }}
        </BaseHeading>
        <ol class="list-decimal list-inside space-y-1 text-sm text-muted-600 dark:text-muted-400 mb-4">
          <li v-for="(step, index) in instructions.steps" :key="index">
            {{ step }}
          </li>
        </ol>

        <!-- Examples -->
        <div v-if="instructions.examples" class="mb-3">
          <BaseParagraph size="sm" weight="medium" class="mb-2">
            Example URLs:
          </BaseParagraph>
          <div class="space-y-1">
            <div
              v-for="(example, index) in instructions.examples"
              :key="index"
              class="text-xs font-mono text-muted-600 dark:text-muted-400 bg-muted-100 dark:bg-muted-800 px-2 py-1 rounded"
            >
              {{ example }}
            </div>
          </div>
        </div>

        <div class="flex gap-2">
          <BaseButton
            size="xs"
            variant="outline"
            :to="instructions.docsUrl"
            external
            target="_blank"
          >
            <Icon name="lucide:external-link" class="size-3 mr-1" />
            View Documentation
          </BaseButton>

          <!-- Ollama-specific quick setup button -->
          <BaseButton
            v-if="integration?.id === 'ollama'"
            size="xs"
            variant="outline"
            to="https://ollama.com/download"
            external
            target="_blank"
          >
            <Icon name="lucide:download" class="size-3 mr-1" />
            Download Ollama
          </BaseButton>
        </div>
      </div>

      <!-- Form -->
      <form class="space-y-4" @submit.prevent="handleSubmit">
        <!-- Server URL Input -->
        <div>
          <BaseLabel for="serverUrl" class="mb-2">
            Server URL
          </BaseLabel>
          <TairoInput
            id="serverUrl"
            v-model="formData.url"
            type="url"
            placeholder="http://localhost:11434"
            :error="!!errors.url"
            @input="() => validateForm()"
          />
          <BaseParagraph
            v-if="errors.url"
            size="xs"
            class="text-destructive-500 mt-1"
          >
            {{ errors.url }}
          </BaseParagraph>
          <BaseParagraph size="xs" class="text-muted-500 mt-1">
            Include the protocol (http:// or https://) and port if needed
          </BaseParagraph>
        </div>

        <!-- Test Connection Checkbox -->
        <div class="flex items-center gap-2">
          <BaseCheckbox
            id="testConnection"
            v-model="formData.testConnection"
          />
          <BaseLabel for="testConnection" class="text-sm">
            Test connection before saving
          </BaseLabel>
        </div>

        <!-- Security Notice for HTTP -->
        <div
          v-if="formData.url && formData.url.startsWith('http://')"
          class="bg-warning-50 dark:bg-warning-950 border border-warning-200 dark:border-warning-800 rounded-lg p-3"
        >
          <div class="flex items-start gap-2">
            <Icon name="lucide:alert-triangle" class="size-4 text-warning-600 dark:text-warning-400 mt-0.5 flex-shrink-0" />
            <div class="text-xs text-warning-700 dark:text-warning-300">
              <strong>HTTP Connection:</strong> You're using an unencrypted connection.
              Consider using HTTPS for production environments.
            </div>
          </div>
        </div>

        <!-- General Security Notice -->
        <div class="bg-info-50 dark:bg-info-950 border border-info-200 dark:border-info-800 rounded-lg p-3">
          <div class="flex items-start gap-2">
            <Icon name="lucide:shield-check" class="size-4 text-info-600 dark:text-info-400 mt-0.5 flex-shrink-0" />
            <div class="text-xs text-info-700 dark:text-info-300">
              <strong>Connection Storage:</strong> The server URL will be encrypted and stored securely
              within your workspace.
            </div>
          </div>
        </div>
      </form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <BaseButton
          variant="outline"
          @click="emit('close')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          variant="primary"
          :disabled="isSubmitting || loading"
          @click="handleSubmit"
        >
          <Icon
            v-if="isSubmitting || loading"
            name="lucide:loader-2"
            class="size-4 animate-spin mr-2"
          />
          {{ formData.testConnection ? 'Test & Connect' : 'Connect' }}
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
