<script setup lang="ts">
import type { ConnectionFormData, Integration } from '~/types/ui'
import { AuthType, IntegrationCategory } from '~/types/ui'
import APIKeyModal from './APIKeyModal.vue'
import OAuthCalendarModal from './OAuthCalendarModal.vue'
import OAuthEmailModal from './OAuthEmailModal.vue'
import SMTPConnectionModal from './SMTPConnectionModal.vue'
import URLConnectionModal from './URLConnectionModal.vue'

interface Props {
  open: boolean
  integration?: Integration
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: ConnectionFormData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Handle different auth types
function handleSubmit(data: ConnectionFormData) {
  emit('submit', data)
}
</script>

<template>
  <!-- SMTP Email Connection Modal -->
  <SMTPConnectionModal
    v-if="integration?.id === 'smtp' && integration?.category === IntegrationCategory.EMAIL"
    :open="open"
    :integration="integration"
    :loading="loading"
    @close="emit('close')"
    @submit="handleSubmit"
  />

  <!-- OAuth Calendar Connection Modal -->
  <OAuthCalendarModal
    v-else-if="integration?.authType === AuthType.OAUTH && integration?.category === IntegrationCategory.CALENDAR"
    :open="open"
    :integration="integration"
    :loading="loading"
    @close="emit('close')"
    @submit="handleSubmit"
  />

  <!-- OAuth Email Connection Modal (Google Email) -->
  <OAuthEmailModal
    v-else-if="integration?.authType === AuthType.OAUTH && integration?.category === IntegrationCategory.EMAIL"
    :open="open"
    :integration="integration"
    :loading="loading"
    @close="emit('close')"
    @submit="handleSubmit"
  />

  <!-- API Key Modal (OpenAI, Anthropic, Grok, Gemini, SMTP fallback) -->
  <APIKeyModal
    v-else-if="integration?.authType === AuthType.API_KEY"
    :open="open"
    :integration="integration"
    :loading="loading"
    @close="emit('close')"
    @submit="handleSubmit"
  />

  <!-- URL Connection Modal (Ollama) -->
  <URLConnectionModal
    v-else-if="integration?.authType === AuthType.URL"
    :open="open"
    :integration="integration"
    :loading="loading"
    @close="emit('close')"
    @submit="handleSubmit"
  />

  <!-- Built-in Integration Modal -->
  <BaseModal
    v-else-if="integration?.authType === 'built_in'"
    :open="open"
    size="sm"
    @close="emit('close')"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <div class="flex size-12 items-center justify-center rounded-lg bg-muted-100 dark:bg-muted-800">
          <Icon
            v-if="integration?.icon"
            :name="integration.icon"
            class="size-6 text-muted-600 dark:text-muted-400"
          />
        </div>
        <div>
          <BaseHeading as="h3" size="lg" weight="semibold">
            {{ integration?.name }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Built-in integration
          </BaseParagraph>
        </div>
      </div>
    </template>

    <div class="space-y-4">
      <div class="bg-success-50 dark:bg-success-950 border border-success-200 dark:border-success-800 rounded-lg p-4">
        <div class="flex items-center gap-2 mb-2">
          <Icon name="lucide:check-circle" class="size-5 text-success-600 dark:text-success-400" />
          <BaseHeading as="h4" size="sm" weight="medium">
            Ready to Use
          </BaseHeading>
        </div>
        <BaseParagraph size="sm" class="text-success-700 dark:text-success-300">
          This integration is built into the platform and doesn't require additional configuration.
        </BaseParagraph>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <BaseButton
          variant="outline"
          @click="emit('close')"
        >
          Close
        </BaseButton>
        <BaseButton
          variant="primary"
          @click="() => handleSubmit({
            integration: integration!,
            credentials: {},
            testConnection: false,
          })"
        >
          Enable Integration
        </BaseButton>
      </div>
    </template>
  </BaseModal>

  <!-- Fallback Modal -->
  <BaseModal
    v-else
    :open="open"
    size="sm"
    @close="emit('close')"
  >
    <template #header>
      <BaseHeading as="h3" size="lg" weight="semibold">
        Integration Not Supported
      </BaseHeading>
    </template>

    <BaseParagraph>
      This integration type is not yet supported. Please contact support for assistance.
    </BaseParagraph>

    <template #footer>
      <div class="flex justify-end">
        <BaseButton
          variant="outline"
          @click="emit('close')"
        >
          Close
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
