<script setup lang="ts">
import Bold from '@tiptap/extension-bold'
import BulletList from '@tiptap/extension-bullet-list'
import Document from '@tiptap/extension-document'
import Heading from '@tiptap/extension-heading'
import Italic from '@tiptap/extension-italic'
import Link from '@tiptap/extension-link'
import ListItem from '@tiptap/extension-list-item'
import OrderedList from '@tiptap/extension-ordered-list'
import Paragraph from '@tiptap/extension-paragraph'
import Strike from '@tiptap/extension-strike'
import Text from '@tiptap/extension-text'
import { Editor, EditorContent } from '@tiptap/vue-3'

interface EmailAddress {
  name?: string
  email: string
}

interface Attachment {
  filename: string
  content: string // base64
  contentType: string
  size: number
}

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// Composables
const { sendEmail, loading } = useEmail()
const { integrations, emailIntegrations } = useIntegrations()
const { showSuccess, showError } = useToaster()

// Reactive state
const isOpen = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

const selectedIntegration = ref('')
const recipients = ref({
  to: '',
  cc: '',
  bcc: '',
})
const subject = ref('')
const attachments = ref<Attachment[]>([])
const showCc = ref(false)
const showBcc = ref(false)

// Form validation
const errors = ref<Record<string, string>>({})

// Rich text editor
const editor = ref<Editor | null>(null)

// Computed
const availableIntegrations = computed(() => {
  return emailIntegrations.value.filter(integration =>
    integration.connected,
  ).map(integration => ({
    id: integration.id,
    name: integration.name || integration.id,
    icon: getProviderIcon(integration.emailConfig?.provider || 'SMTP'),
  }))
})

const canSend = computed(() => {
  return selectedIntegration.value
    && recipients.value.to.trim()
    && subject.value.trim()
    && editor.value?.getHTML()
    && !loading.value
})

// Methods
function validateForm() {
  errors.value = {}

  if (!selectedIntegration.value) {
    errors.value.integration = 'Please select an email account'
  }

  if (!recipients.value.to.trim()) {
    errors.value.to = 'Please enter at least one recipient'
  }

  if (!subject.value.trim()) {
    errors.value.subject = 'Please enter a subject'
  }

  if (!editor.value?.getHTML() || editor.value.getHTML() === '<p></p>') {
    errors.value.body = 'Please enter a message'
  }

  return Object.keys(errors.value).length === 0
}

function parseEmailAddresses(emailString: string): EmailAddress[] {
  if (!emailString.trim())
    return []

  return emailString
    .split(',')
    .map(email => email.trim())
    .filter(Boolean)
    .map((email) => {
      // Simple email parsing - can be enhanced
      const match = email.match(/^(.+?)\s*<(.+?)>$/)
      if (match) {
        return { name: match[1].replace(/['"]/g, ''), email: match[2] }
      }
      return { email }
    })
}

async function handleSend() {
  if (!validateForm())
    return

  try {
    const htmlBody = editor.value!.getHTML()
    const textBody = editor.value!.getText()

    await sendEmail({
      integrationId: selectedIntegration.value,
      to: parseEmailAddresses(recipients.value.to),
      cc: recipients.value.cc ? parseEmailAddresses(recipients.value.cc) : undefined,
      bcc: recipients.value.bcc ? parseEmailAddresses(recipients.value.bcc) : undefined,
      subject: subject.value,
      htmlBody,
      textBody,
      attachments: attachments.value.length > 0 ? attachments.value : undefined,
    })

    // Reset form and close modal
    resetForm()
    isOpen.value = false
  }
  catch (error) {
    console.error('Failed to send email:', error)
  }
}

function handleDiscard() {
  if (hasUnsavedChanges()) {
    if (confirm('Are you sure you want to discard this email?')) {
      resetForm()
      isOpen.value = false
    }
  }
  else {
    resetForm()
    isOpen.value = false
  }
}

function hasUnsavedChanges() {
  return recipients.value.to.trim()
    || recipients.value.cc.trim()
    || recipients.value.bcc.trim()
    || subject.value.trim()
    || (editor.value?.getHTML() && editor.value.getHTML() !== '<p></p>')
    || attachments.value.length > 0
}

function resetForm() {
  selectedIntegration.value = ''
  recipients.value = { to: '', cc: '', bcc: '' }
  subject.value = ''
  attachments.value = []
  showCc.value = false
  showBcc.value = false
  errors.value = {}
  editor.value?.commands.clearContent()
}

function saveDraft() {
  // TODO: Implement draft saving
  showSuccess('Draft saved locally')
}

function insertLink() {
  const url = window.prompt('URL')
  if (url) {
    editor.value?.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
  }
}

function handleFileUpload(event: Event) {
  const files = (event.target as HTMLInputElement).files
  if (!files)
    return

  Array.from(files).forEach((file) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = (e.target?.result as string)?.split(',')[1] // Remove data URL prefix
      attachments.value.push({
        filename: file.name,
        content,
        contentType: file.type,
        size: file.size,
      })
    }
    reader.readAsDataURL(file)
  })
}

function removeAttachment(index: number) {
  attachments.value.splice(index, 1)
}

function formatFileSize(bytes: number): string {
  if (bytes === 0)
    return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}

function getProviderIcon(provider: string): string {
  switch (provider) {
    case 'GOOGLE_EMAIL':
      return 'simple-icons:gmail'
    case 'MICROSOFT_EMAIL':
      return 'simple-icons:microsoftoutlook'
    case 'SMTP':
      return 'lucide:mail'
    default:
      return 'lucide:mail'
  }
}

// Lifecycle
onMounted(() => {
  editor.value = new Editor({
    extensions: [
      Document,
      Paragraph,
      Text,
      Bold,
      Italic,
      Strike,
      Heading.configure({
        levels: [1, 2, 3],
      }),
      BulletList,
      OrderedList,
      ListItem,
      Link.configure({
        openOnClick: false,
      }),
    ],
    content: '',
    editorProps: {
      attributes: {
        class: 'prose prose-sm max-w-none focus:outline-none dark:prose-invert',
      },
    },
  })
})

onBeforeUnmount(() => {
  editor.value?.destroy()
})

// Auto-select first available integration
watch(() => availableIntegrations.value, (integrations) => {
  if (integrations.length > 0 && !selectedIntegration.value) {
    selectedIntegration.value = integrations[0].id
  }
}, { immediate: true })
</script>

<template>
  <BaseModal
    v-model="isOpen"
    size="2xl"
    :blur="false"
  >
    <template #header>
      <div class="flex items-center gap-2">
        <Icon name="lucide:mail" class="h-5 w-5" />
        <h3 class="font-heading text-muted-900 text-lg font-medium leading-6 dark:text-white">
          Compose Email
        </h3>
      </div>
    </template>

    <div class="mx-auto w-full max-w-4xl p-4">
      <form class="space-y-4" @submit.prevent="handleSend">
        <!-- Provider Selection -->
        <TairoSelect
          v-model="selectedIntegration"
          label="Send from"
          placeholder="Select email account"
          :error="errors.integration"
        >
          <BaseSelectItem
            v-for="integration in availableIntegrations"
            :key="integration.id"
            :value="integration.id"
          >
            <Icon :name="integration.icon" class="h-4 w-4 mr-2" />
            {{ integration.name }}
          </BaseSelectItem>
        </TairoSelect>

        <!-- Recipients -->
        <div class="space-y-3">
          <!-- To Field -->
          <BaseInput
            v-model="recipients.to"
            label="To"
            placeholder="<EMAIL>"
            :error="errors.to"
          />

          <!-- Show CC/BCC toggle -->
          <div class="flex gap-4 text-sm">
            <button
              type="button"
              class="text-primary-500 hover:text-primary-600"
              @click="showCc = !showCc"
            >
              {{ showCc ? 'Hide' : 'Add' }} CC
            </button>
            <button
              type="button"
              class="text-primary-500 hover:text-primary-600"
              @click="showBcc = !showBcc"
            >
              {{ showBcc ? 'Hide' : 'Add' }} BCC
            </button>
          </div>

          <!-- CC Field -->
          <BaseInput
            v-if="showCc"
            v-model="recipients.cc"
            label="CC"
            placeholder="<EMAIL>"
          />

          <!-- BCC Field -->
          <BaseInput
            v-if="showBcc"
            v-model="recipients.bcc"
            label="BCC"
            placeholder="<EMAIL>"
          />
        </div>

        <!-- Subject -->
        <BaseInput
          v-model="subject"
          label="Subject"
          placeholder="Email subject"
          :error="errors.subject"
        />

        <!-- Rich Text Editor -->
        <div class="space-y-2">
          <BaseLabel>Message</BaseLabel>

          <!-- Editor Toolbar -->
          <div
            v-if="editor"
            class="border border-muted-200 dark:border-muted-700 rounded-t-lg px-3 py-2 bg-muted-50 dark:bg-muted-800 flex flex-wrap gap-1"
          >
            <button
              type="button"
              :class="{ 'bg-primary-500 text-white': editor.isActive('bold') }"
              class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700"
              @click="editor.chain().focus().toggleBold().run()"
            >
              <Icon name="lucide:bold" class="h-4 w-4" />
            </button>
            <button
              type="button"
              :class="{ 'bg-primary-500 text-white': editor.isActive('italic') }"
              class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700"
              @click="editor.chain().focus().toggleItalic().run()"
            >
              <Icon name="lucide:italic" class="h-4 w-4" />
            </button>
            <button
              type="button"
              :class="{ 'bg-primary-500 text-white': editor.isActive('strike') }"
              class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700"
              @click="editor.chain().focus().toggleStrike().run()"
            >
              <Icon name="lucide:strikethrough" class="h-4 w-4" />
            </button>
            <div class="w-px h-6 bg-muted-300 dark:bg-muted-600 mx-1" />
            <button
              type="button"
              :class="{ 'bg-primary-500 text-white': editor.isActive('heading', { level: 1 }) }"
              class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700"
              @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
            >
              <Icon name="lucide:heading-1" class="h-4 w-4" />
            </button>
            <button
              type="button"
              :class="{ 'bg-primary-500 text-white': editor.isActive('heading', { level: 2 }) }"
              class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700"
              @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
            >
              <Icon name="lucide:heading-2" class="h-4 w-4" />
            </button>
            <div class="w-px h-6 bg-muted-300 dark:bg-muted-600 mx-1" />
            <button
              type="button"
              :class="{ 'bg-primary-500 text-white': editor.isActive('bulletList') }"
              class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700"
              @click="editor.chain().focus().toggleBulletList().run()"
            >
              <Icon name="lucide:list" class="h-4 w-4" />
            </button>
            <button
              type="button"
              :class="{ 'bg-primary-500 text-white': editor.isActive('orderedList') }"
              class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700"
              @click="editor.chain().focus().toggleOrderedList().run()"
            >
              <Icon name="lucide:list-ordered" class="h-4 w-4" />
            </button>
            <div class="w-px h-6 bg-muted-300 dark:bg-muted-600 mx-1" />
            <button
              type="button"
              :class="{ 'bg-primary-500 text-white': editor.isActive('link') }"
              class="p-1.5 rounded hover:bg-muted-200 dark:hover:bg-muted-700"
              @click="insertLink"
            >
              <Icon name="lucide:link" class="h-4 w-4" />
            </button>
          </div>

          <!-- Editor Content -->
          <div
            class="min-h-[200px] max-h-[400px] overflow-auto border border-muted-200 dark:border-muted-700 rounded-b-lg"
            :class="errors.body ? 'border-red-500' : ''"
          >
            <EditorContent
              :editor="editor"
              class="prose prose-sm max-w-none p-4 focus:outline-none dark:prose-invert"
            />
          </div>

          <p v-if="errors.body" class="text-red-500 text-sm">
            {{ errors.body }}
          </p>
        </div>

        <!-- Attachments -->
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <BaseLabel>Attachments</BaseLabel>
            <button
              type="button"
              class="text-primary-500 hover:text-primary-600 text-sm"
              @click="$refs.fileInput.click()"
            >
              Add file
            </button>
          </div>

          <input
            ref="fileInput"
            type="file"
            multiple
            class="hidden"
            @change="handleFileUpload"
          >

          <div v-if="attachments.length > 0" class="space-y-2">
            <div
              v-for="(attachment, index) in attachments"
              :key="index"
              class="flex items-center gap-2 p-2 bg-muted-50 dark:bg-muted-800 rounded"
            >
              <Icon name="lucide:paperclip" class="h-4 w-4" />
              <span class="text-sm flex-1">{{ attachment.filename }}</span>
              <span class="text-xs text-muted-500">{{ formatFileSize(attachment.size) }}</span>
              <button
                type="button"
                class="text-red-500 hover:text-red-600"
                @click="removeAttachment(index)"
              >
                <Icon name="lucide:x" class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between pt-4">
          <BaseButton
            type="button"
            color="muted"
            @click="handleDiscard"
          >
            Discard
          </BaseButton>

          <div class="flex gap-2">
            <BaseButton
              type="button"
              color="muted"
              :disabled="loading"
              @click="saveDraft"
            >
              Save Draft
            </BaseButton>
            <BaseButton
              type="submit"
              color="primary"
              :loading="loading"
              :disabled="!canSend"
            >
              <Icon name="lucide:send" class="h-4 w-4" />
              Send
            </BaseButton>
          </div>
        </div>
      </form>
    </div>
  </BaseModal>
</template>

<style scoped>
:deep(.ProseMirror) {
  outline: none;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}
</style>
