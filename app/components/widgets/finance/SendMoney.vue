<script setup lang="ts">
import type { SendMoneyData } from '~/types/widgets'

interface Props {
  data: SendMoneyData
}

const props = withDefaults(defineProps<Props>(), {})

// Local reactive state
const selectedBank = shallowRef(props.data.selectedTo || props.data.toAccounts[0])
const selectedCurrency = ref(props.data.selectedCurrency || 'usd')
const amount = ref<number>(0)

// Form submission handler
function handleSubmit() {
  console.log('Send money submitted:', {
    bank: selectedBank.value,
    currency: selectedCurrency.value,
    amount: amount.value,
  })
}
</script>

<template>
  <form
    method="POST"
    action=""
    class="col-span-12 lg:col-span-6"
    @submit.prevent="handleSubmit"
  >
    <div class="relative h-full">
      <BaseCard rounded="md" class="flex h-full flex-col p-4 md:p-6">
        <div class="mb-4 flex items-center justify-between">
          <BaseHeading
            as="h3"
            size="md"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ data.title }}</span>
          </BaseHeading>
          <div>
            <NuxtLink
              :to="data.seeAllLink?.href || '#'"
              class="text-muted-400 hover:text-primary-500 font-sans text-sm underline-offset-4 transition-colors duration-300 hover:underline"
            >
              {{ data.seeAllLink?.text || 'See All' }}
            </NuxtLink>
          </div>
        </div>
        <div>
          <div class="mb-4 flex gap-3">
            <BaseAvatar
              v-for="(contact, index) in data.contacts"
              :key="index"
              :src="contact.src"
              :text="contact.text"
              size="xs"
              :class="contact.class"
            />
          </div>
          <NuxtLink
            :to="data.contactsLink?.href || '#'"
            class="text-muted-500 dark:text-muted-400 hover:text-primary-500 font-sans text-sm underline-offset-4 hover:underline"
          >
            {{ data.contactsLink?.text || 'See all contacts' }}
          </NuxtLink>
        </div>
        <div class="mt-auto">
          <div class="space-y-3">
            <div>
              <BaseSelect v-model="selectedBank" rounded="sm">
                <TairoSelectItem
                  v-for="item in data.toAccounts"
                  :key="item.id"
                  :value="item"
                  :media="item.media"
                  :name="item.name"
                  :text="item.text"
                />
              </BaseSelect>
            </div>
            <div class="grid grid-cols-12 w-full gap-2">
              <BaseSelect
                v-model="selectedCurrency"
                rounded="sm"
                label="Currency"
                class="col-span-3"
              >
                <BaseSelectItem
                  v-for="currency in data.currencies"
                  :key="currency.value"
                  :value="currency.value"
                >
                  {{ currency.symbol }}
                </BaseSelectItem>
              </BaseSelect>
              <div class="col-span-9">
                <BaseInputNumber
                  v-model="amount"
                  placeholder="0.00"
                  rounded="sm"
                  label="Amount"
                  :min="0"
                />
              </div>
            </div>
          </div>
          <p class="text-muted-500 dark:text-muted-400 my-3 font-sans text-xs leading-tight">
            {{ data.transferNote }}
          </p>
          <div>
            <BaseButton
              type="submit"
              rounded="md"
              variant="primary"
              class="w-full"
            >
              {{ data.submitButtonText || 'Send Money' }}
            </BaseButton>
          </div>
        </div>
      </BaseCard>
    </div>
  </form>
</template>
