<script setup lang="ts">
interface AccountAction {
  label: string
  to: string
  variant?: 'primary' | 'default' | 'muted'
}

interface AccountSummaryConfig {
  accountLabel?: string // e.g. Main Account
  accountType?: string // e.g. Checking account
  accountNumber?: string // masked PAN
  actions?: AccountAction[]
  availableLabel?: string // e.g. Available Funds
  availableAmount?: number // numeric amount
  currencyPrefix?: string // e.g. $
  showLogo?: boolean
}

const props = withDefaults(defineProps<{ config?: AccountSummaryConfig }>(), {
  config: () => ({
    accountLabel: 'Main Account',
    accountType: 'Checking account',
    accountNumber: '0000 0000 0000 0000',
    actions: [
      { label: 'Transfer Money', to: '/layouts/send', variant: 'primary' },
      { label: 'Link Accounts', to: '/layouts/accounts/linked', variant: 'default' },
    ],
    availableLabel: 'Available Funds',
    availableAmount: 0,
    currencyPrefix: '$',
    showLogo: true,
  }),
})
</script>

<template>
  <BaseCard
    rounded="md"
    class="h-full p-4 md:p-6 xl:p-8"
  >
    <div class="flex h-full flex-col justify-between gap-y-6 sm:flex-row sm:gap-y-0">
      <div class="flex flex-col">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 mb-4">
          {{ config.accountLabel }}
        </BaseParagraph>
        <BaseHeading weight="medium" class="text-muted-900 dark:text-muted-100">
          {{ config.accountType }}
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 mb-6">
          {{ config.accountNumber }}
        </BaseParagraph>
        <div class="mt-auto flex flex-col items-center gap-2 sm:flex-row">
          <BaseButton
            v-for="(action, idx) in config.actions"
            :key="idx"
            :to="action.to"
            rounded="md"
            :variant="action.variant || 'default'"
            class="w-full sm:w-auto"
          >
            {{ action.label }}
          </BaseButton>
        </div>
      </div>
      <div class="flex flex-col">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 md:mb-4">
          {{ config.availableLabel }}
        </BaseParagraph>
        <BaseHeading as="h5" size="2xl" weight="medium" class="mb-6">
          {{ config.currencyPrefix }}{{ Number(config.availableAmount || 0).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}
        </BaseHeading>
        <div v-if="config.showLogo" class="hidden md:block ms-auto mt-auto">
          <!-- Allow override via slot -->
          <slot name="logo">
            <TairoLogo class="text-muted-400 size-12" />
          </slot>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
