<script setup lang="ts">
import type { QuickTransferData } from '~/types/widgets'

interface Props {
  data: QuickTransferData
}

const props = withDefaults(defineProps<Props>(), {})

// Local reactive state
const selectedBank = shallowRef(props.data.selectedFrom || props.data.fromAccounts[0])

// Form submission handler
function handleSubmit() {
  // Handle form submission - could emit event or call parent method
  console.log('Quick transfer submitted:', selectedBank.value)
}
</script>

<template>
  <form
    method="POST"
    action=""
    class="relative"
    @submit.prevent="handleSubmit"
  >
    <BaseCard rounded="md" class="flex flex-col p-4 md:p-6">
      <div class="mb-4 flex items-center justify-between">
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ data.title }}</span>
        </BaseHeading>
      </div>
      <div>
        <BaseSelect v-model="selectedBank">
          <TairoSelectItem
            v-for="item in data.fromAccounts"
            :key="item.id"
            :value="item"
            :media="item.media"
            :name="item.name"
            :text="item.text"
          />
        </BaseSelect>
        <div class="mt-2">
          <p class="text-muted-600 dark:text-muted-400 font-sans text-xs leading-tight">
            {{ data.description }}
          </p>
          <div class="mt-4 flex justify-end">
            <BaseButton
              type="submit"
              class="w-full"
              rounded="md"
            >
              {{ data.submitButtonText || 'Confirm and send' }}
            </BaseButton>
          </div>
        </div>
      </div>
    </BaseCard>
  </form>
</template>
