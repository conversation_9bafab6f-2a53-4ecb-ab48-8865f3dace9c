<script setup lang="ts">
import type { TransactionItem } from '~/types/widgets'

interface TransactionsListData {
  title?: string
  transactions: TransactionItem[]
  viewAllLink?: {
    text: string
    href?: string
  }
}

withDefaults(defineProps<{
  data?: TransactionsListData
}>(), {
  data: () => ({
    title: 'Transactions',
    transactions: [],
    viewAllLink: {
      text: 'View All',
      href: '#',
    },
  }),
})
</script>

<template>
  <BaseCard rounded="md" class="flex h-full flex-col py-4 px-2 md:py-6 md:px-4">
    <div class="mb-8 px-2 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <div v-if="data.viewAllLink">
        <NuxtLink
          :to="data.viewAllLink.href"
          class="text-muted-400 hover:text-primary-500 font-sans text-sm underline-offset-4 transition-colors duration-300 hover:underline"
        >
          {{ data.viewAllLink.text }}
        </NuxtLink>
      </div>
    </div>
    <div class="space-y-2">
      <!-- Transaction Item -->
      <div
        v-for="item in data.transactions"
        :key="item.id || item.title"
        class="py-2 px-2 flex items-center gap-2 hover:bg-muted-200/50 dark:hover:bg-muted-900/50 transition-colors duration-100 rounded-lg"
      >
        <BaseIconBox
          :class="item.iconColor"
          size="sm"
          rounded="full"
          variant="none"
        >
          <Icon :name="item.icon" class="size-5" />
        </BaseIconBox>
        <div>
          <BaseHeading
            as="h4"
            size="sm"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ item.title }}</span>
          </BaseHeading>
          <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400">
            <span>{{ item.subtitle }}</span>
          </BaseParagraph>
        </div>
        <div class="ms-auto">
          <span
            class="text-muted-900 dark:text-muted-100 font-sans text-sm font-semibold"
          >
            {{ formatPrice(item.amount) }}
          </span>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
