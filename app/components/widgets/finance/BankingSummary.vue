<script setup lang="ts">
import type { BankingSummaryData } from '~/types/widgets'

interface Props {
  data: BankingSummaryData
}

defineProps<Props>()

defineEmits<{
  settings: []
  create: []
}>()
</script>

<template>
  <BaseCard
    rounded="md"
    variant="muted"
    class="lg:landscape:flex-row flex flex-col gap-y-10 p-4 sm:p-6 md:p-10 xl:landscape:flex-row"
  >
    <!-- Monthly Summary & Chart Section -->
    <div
      class="lg:landscape:w-3/5 lg:landscape:border-r border-muted-200 dark:border-muted-800/80 w-full xl:landscape:w-3/5 xl:landscape:border-e"
    >
      <div class="flex size-full flex-col gap-16 sm:flex-row">
        <!-- Monthly Summary -->
        <div class="shrink-0">
          <BaseHeading
            as="h3"
            size="md"
            weight="semibold"
            lead="tight"
            class="text-muted-800 mb-6 dark:text-white"
          >
            <span>{{ data.summaryTitle }}</span>
          </BaseHeading>
          <div
            class="border-muted-300 divide-muted-300 dark:border-muted-800/80 dark:divide-muted-800/80 flex flex-col divide-y rounded-lg border"
          >
            <div class="p-4">
              <div
                class="text-muted-400 mb-1 font-sans text-xs font-medium uppercase"
              >
                <span>{{ data.summary.income.label }}</span>
              </div>
              <div class="text-success-500 font-sans text-sm font-semibold">
                <span>+ {{ formatPrice(data.summary.income.value) }}</span>
              </div>
            </div>
            <div class="p-4">
              <div
                class="text-muted-400 mb-1 font-sans text-xs font-medium uppercase"
              >
                <span>{{ data.summary.expenses.label }}</span>
              </div>
              <div class="text-destructive-500 font-sans text-sm font-semibold">
                <span>- {{ formatPrice(data.summary.expenses.value) }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- Chart -->
        <div class="lg:landscape:pe-12 flex h-full grow flex-col xl:landscape:pe-12">
          <div class="mt-auto">
            <component :is="data.chartComponent" v-bind="data.chartProps" />
          </div>
        </div>
      </div>
    </div>

    <!-- Transactions Section -->
    <div class="lg:landscape:w-2/5 w-full xl:landscape:w-2/5">
      <div class="lg:landscape:ps-12 h-full xl:landscape:ps-12">
        <div class="flex size-full flex-col">
          <div class="mb-6 flex items-center justify-between">
            <BaseHeading
              as="h3"
              size="md"
              weight="medium"
              lead="tight"
              class="text-muted-900 mb-6 dark:text-white"
            >
              <span>{{ data.transactionsTitle }}</span>
            </BaseHeading>
          </div>
          <div class="mt-auto">
            <div class="space-y-4">
              <!-- Transaction Items -->
              <div
                v-for="(transaction, index) in data.transactions"
                :key="index"
                class="flex items-center gap-2"
              >
                <BaseIconBox
                  :class="transaction.iconClass"
                  size="md"
                  rounded="full"
                  variant="none"
                >
                  <Icon :name="transaction.icon" class="size-5" />
                </BaseIconBox>
                <div>
                  <BaseHeading
                    as="h4"
                    size="sm"
                    weight="medium"
                    lead="tight"
                    class="text-muted-900 dark:text-white"
                  >
                    <span>{{ transaction.title }}</span>
                  </BaseHeading>
                  <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
                    <span>{{ transaction.timestamp }}</span>
                  </BaseParagraph>
                </div>
                <div class="ms-auto">
                  <span
                    class="text-muted-900 dark:text-muted-100 font-sans text-sm font-semibold"
                  >
                    {{ transaction.isIncome ? '+' : '-' }} {{ formatPrice(transaction.amount) }}
                  </span>
                </div>
              </div>
            </div>
            <div class="mt-6 flex items-center gap-2">
              <BaseButton
                class="w-full"
                @click="$emit('settings')"
              >
                <span>{{ data.actions?.settings || 'Settings' }}</span>
              </BaseButton>
              <BaseButton
                variant="primary"
                class="w-full"
                @click="$emit('create')"
              >
                <span>{{ data.actions?.create || 'Create' }}</span>
              </BaseButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
