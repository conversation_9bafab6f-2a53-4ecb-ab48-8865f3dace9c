<script setup lang="ts">
interface AccountBalanceData {
  title?: string
  balance: number
  change: number
  changeLabel?: string
  changeIcon?: string
  isIncrease?: boolean
}

withDefaults(defineProps<{
  data?: AccountBalanceData
}>(), {
  data: () => ({
    title: 'Account Balance',
    balance: 9543.12,
    change: 149.32,
    changeLabel: 'Today, Sep 25',
    changeIcon: 'lucide:arrow-up',
    isIncrease: true,
  }),
})
</script>

<template>
  <BaseCard rounded="md">
    <div class="flex flex-col gap-4 px-8 pt-8 text-center">
      <BaseHeading
        as="h4"
        size="xs"
        weight="medium"
        lead="none"
        class="text-muted-700 dark:text-muted-100 uppercase"
      >
        {{ data.title }}
      </BaseHeading>
      <p>
        <span
          class="text-muted-900 font-sans text-4xl font-medium dark:text-white"
        >
          {{ formatPrice(data.balance) }}
        </span>
      </p>
      <div class="flex items-center justify-center gap-x-2">
        <Icon
          :name="data.changeIcon"
          :class="`iconify size-4 ${data.isIncrease ? 'text-success-500' : 'text-danger-500'}`"
        />
        <span class="text-muted-600 dark:text-muted-400 font-sans text-sm">
          {{ formatPrice(data.change) }} {{ data.changeLabel }}
        </span>
      </div>
    </div>
    <ChartAreaBalance />
  </BaseCard>
</template>
