<script setup lang="ts">
interface StandingOrdersConfig {
  title?: string
  description?: string
  buttonText?: string
  buttonTo?: string
  bgVariant?: string // Tailwind classes for background
}

withDefaults(defineProps<{ config?: StandingOrdersConfig }>(), {
  config: () => ({
    title: 'Standing Orders',
    description:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Si longus, levis dictata sunt. Quare attende, quaeso',
    buttonText: 'Manage Orders',
    buttonTo: '/layouts/send',
    bgVariant: 'bg-primary-800 border-primary-800',
  }),
})
</script>

<template>
  <BaseCard
    rounded="md"
    variant="none"
    :class="`${config.bgVariant} h-full p-4 md:p-6 xl:p-8`"
  >
    <div class="flex flex-col justify-between gap-y-16 sm:flex-row sm:gap-y-0">
      <div class="flex flex-col">
        <BaseHeading weight="medium" class="mb-3 text-white">
          {{ config.title }}
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-100 mb-6">
          {{ config.description }}
        </BaseParagraph>
        <div class="mt-auto flex items-center gap-2">
          <BaseButton :to="config.buttonTo" rounded="md">
            {{ config.buttonText }}
          </BaseButton>
        </div>
      </div>
      <div class="relative min-h-[180px] min-w-[160px] shrink-0">
        <div class="absolute bottom-0 end-0 min-w-[250px] max-w-[250px] sm:-end-10 sm:min-w-[220px]">
          <slot name="illustration">
            <VectorIllustrationCalendar class="text-primary-500" />
          </slot>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
