<script setup lang="ts">
interface InvestConfig {
  mainCard: {
    title?: string
    description?: string
    linkText?: string
    linkUrl?: string
    image?: string
    imageAlt?: string
  }
  infoCard: {
    title?: string
    description?: string
    linkText?: string
    linkUrl?: string
  }
}

const props = withDefaults(defineProps<{
  config?: InvestConfig
}>(), {
  config: () => ({
    mainCard: {
      title: 'Explore investments',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Bork Idem adhuc; Igitur neque stultorum quisquam beatus.',
      linkText: 'Learn more about it',
      linkUrl: '#',
      image: '/img/illustrations/ui/invest.svg',
      imageAlt: 'Investment illustration',
    },
    infoCard: {
      title: 'Need more info?',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Bork Idem adhuc; Igitur neque stultorum quisquam beatus.',
      linkText: 'Learn more about it',
      linkUrl: '#',
    },
  }),
})
</script>

<template>
  <div class="grid grid-cols-12 gap-6">
    <div class="col-span-12 md:col-span-8">
      <BaseCard
        rounded="lg"
        class="relative flex flex-col gap-4 overflow-hidden sm:flex-row"
      >
        <div class="flex flex-col p-6">
          <BaseHeading
            as="h3"
            weight="medium"
            size="lg"
            class="text-muted-800 dark:text-muted-100 mb-2"
          >
            {{ config.mainCard.title }}
          </BaseHeading>
          <BaseParagraph
            size="sm"
            class="text-muted-500 dark:text-muted-400 mb-4"
          >
            {{ config.mainCard.description }}
          </BaseParagraph>
          <div class="mb-1 mt-auto">
            <LinkArrow :to="config.mainCard.linkUrl!" :label="config.mainCard.linkText!" />
          </div>
        </div>
        <div
          class="flex h-44 w-full shrink-0 items-center justify-center sm:w-1/2"
        >
          <img
            :src="config.mainCard.image"
            class="xs:-bottom-48 xs:start-5 absolute h-full w-auto object-contain object-top sm:end-0 sm:top-0 sm:w-64 lg:w-72 lg:object-cover"
            :alt="config.mainCard.imageAlt"
          >
        </div>
      </BaseCard>
    </div>
    <!-- Invest widget -->
    <div class="col-span-12 md:col-span-4">
      <BaseCard
        rounded="lg"
        class="relative flex h-full flex-col gap-4 sm:flex-row"
      >
        <div class="flex flex-col p-6">
          <BaseHeading
            as="h3"
            weight="medium"
            size="lg"
            class="text-muted-800 dark:text-muted-100 mb-2"
          >
            {{ config.infoCard.title }}
          </BaseHeading>
          <BaseParagraph
            size="sm"
            class="text-muted-500 dark:text-muted-400 mb-4"
          >
            {{ config.infoCard.description }}
          </BaseParagraph>
          <div class="mb-1 mt-auto">
            <LinkArrow :to="config.infoCard.linkUrl!" :label="config.infoCard.linkText!" />
          </div>
        </div>
      </BaseCard>
    </div>
  </div>
</template>
