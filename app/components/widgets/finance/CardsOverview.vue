<script setup lang="ts">
import type { BankingCardsOverviewData } from '~/types/widgets'

interface Props {
  data: BankingCardsOverviewData
}

const props = defineProps<Props>()

defineEmits<{
  addCard: []
}>()

const activeCard = ref(0)
const selectedBank = ref()

// Initialize selected bank when data changes
watchEffect(() => {
  if (props.data?.banks?.length && !selectedBank.value) {
    selectedBank.value = props.data.banks[0]
  }
})
</script>

<template>
  <BaseCard
    rounded="md"
    class="lg:landscape:flex-row flex flex-col gap-y-10 overflow-hidden p-4 sm:p-6 md:p-10 xl:landscape:flex-row"
  >
    <!-- My Cards Section -->
    <div class="lg:landscape:w-3/5 w-full xl:landscape:w-3/5">
      <div class="mb-12 flex items-center justify-between">
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ props.data.title }}</span>
        </BaseHeading>
        <div />
      </div>
      <div
        class="lg:landscape:pe-12 flex flex-col gap-y-4 sm:flex-row sm:gap-x-8 xl:landscape:pe-12"
      >
        <div>
          <BaseButton size="icon-md" rounded="md" @click="$emit('addCard')">
            <Icon name="lucide:plus" class="size-4" />
          </BaseButton>
        </div>
        <div
          class="nui-slimscroll grid max-h-[164px] w-full gap-4 overflow-y-auto px-6 sm:grid-cols-2"
        >
          <CreditCard
            v-for="(card, index) in props.data.cards"
            :key="index"
            :balance="card.balance"
            :ending-number="card.endingNumber"
            :type="card.type"
            :active="activeCard === index"
            @click="activeCard = index"
          />
        </div>
      </div>
      <div class="mt-4 flex justify-center text-center">
        <p
          class="lg:landscape:mx-0 text-muted-400 mx-auto max-w-xs text-center font-sans text-xs xl:landscape:mx-0"
        >
          {{ props.data.description }}
        </p>
      </div>
    </div>

    <!-- Balance Section -->
    <div class="lg:landscape:w-2/5 w-full xl:landscape:w-2/5">
      <div
        class="lg:landscape:ps-12 lg:landscape:border-l border-muted-200 dark:border-muted-800/80 flex h-full flex-col xl:landscape:border-s xl:landscape:ps-12"
      >
        <div class="mb-4 flex items-center justify-between">
          <BaseHeading
            as="h4"
            size="md"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ props.data.balanceTitle || 'Balance' }}</span>
          </BaseHeading>
          <div
            class="lg:landscape:max-w-[230px] hidden max-w-[260px] grow sm:block"
          >
            <BaseSelect v-model="selectedBank">
              <TairoSelectItem
                v-for="item in props.data.banks"
                :key="item.id"
                :value="item"
                :media="item.media"
                :name="item.name"
                :text="item.text"
              />
            </BaseSelect>
          </div>
        </div>
        <div
          class="lg:portrait:flex lg:landscape:items-end lg:portrait:justify-between lg:portrait:mt-10 mt-auto"
        >
          <div class="lg:portrait:mb-0 mb-6 leading-relaxed">
            <span
              class="text-muted-900 dark:text-muted-100 block font-sans text-3xl font-semibold"
            >
              {{ formatPrice(props.data.currentBalance) }}
            </span>
            <span class="text-muted-400 block font-sans text-sm">
              {{ props.data.cardNumber }}
            </span>
          </div>
          <div class="flex items-center gap-12">
            <div class="card-balance-stat">
              <div
                class="text-muted-400 mb-1 font-sans text-xs font-medium uppercase"
              >
                <span>Income</span>
              </div>
              <div class="flex items-center gap-2">
                <div
                  class="bg-muted-100 dark:bg-muted-900 flex size-10 items-center justify-center rounded-full"
                >
                  <Icon
                    name="lucide:arrow-right"
                    class="text-success-500 size-4 -rotate-45"
                  />
                </div>
                <div
                  class="text-muted-500 dark:text-muted-400 font-sans text-sm"
                >
                  <span>+ {{ formatPrice(props.data.income) }}</span>
                </div>
              </div>
            </div>
            <div class="card-balance-stat">
              <div
                class="text-muted-400 mb-1 font-sans text-xs font-medium uppercase"
              >
                <span>Expense</span>
              </div>
              <div class="flex items-center gap-2">
                <div
                  class="bg-muted-100 dark:bg-muted-900 flex size-10 items-center justify-center rounded-full"
                >
                  <Icon
                    name="lucide:arrow-right"
                    class="text-destructive-500 size-4 rotate-45"
                  />
                </div>
                <div
                  class="text-muted-500 dark:text-muted-400 font-sans text-sm"
                >
                  <span>+ {{ formatPrice(props.data.expense) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
