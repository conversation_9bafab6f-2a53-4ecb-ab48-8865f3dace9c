<script setup lang="ts">
interface BankTileButton {
  to?: string
  icon?: string
  label?: string
  size?: 'sm' | 'md' | 'icon-sm' | 'icon'
  rounded?: 'md' | 'lg' | 'full'
}

interface BankTileConfig {
  bankName: string
  maskedNumber?: string
  amount: number | string
  currencyPrefix?: string
  logoSrc?: string
  button?: BankTileButton
}

const props = withDefaults(defineProps<{ config?: BankTileConfig }>(), {
  config: () => ({
    bankName: 'Bank',
    maskedNumber: '**** **** 0000',
    amount: 0,
    currencyPrefix: '$',
    logoSrc: '',
    button: { icon: 'lucide:arrow-right', size: 'icon-sm', rounded: 'lg' },
  }),
})

function formattedAmount(value: number | string, prefix?: string) {
  const num = typeof value === 'number' ? value : Number.parseFloat(String(value).replace(/[^0-9.-]/g, ''))
  if (Number.isFinite(num)) {
    return `${prefix || ''}${num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }
  return `${prefix || ''}${value}`
}
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <div class="mb-6 flex justify-between">
      <div class="flex flex-col">
        <BaseHeading weight="medium" size="md" class="text-muted-900 dark:text-muted-100 mb-&">
          {{ config.bankName }}
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          {{ config.maskedNumber }}
        </BaseParagraph>
      </div>
      <div v-if="config.logoSrc">
        <img :src="config.logoSrc" alt="Bank logo" class="size-8">
      </div>
      <div v-else>
        <slot name="logo">
          <!-- fallback icon -->
          <Icon name="lucide:building-2" class="size-8 text-muted-400" />
        </slot>
      </div>
    </div>
    <div class="flex justify-between">
      <div>
        <BaseHeading as="h5" size="xl">
          {{ formattedAmount(config.amount, config.currencyPrefix) }}
        </BaseHeading>
      </div>
      <div>
        <BaseButton
          :to="config.button?.to"
          :size="(config.button?.size as any) || 'icon-sm'"
          :rounded="(config.button?.rounded as any) || 'lg'"
        >
          <Icon :name="config.button?.icon || 'lucide:arrow-right'" />
          <template v-if="config.button?.label">
            <span class="ml-2">{{ config.button?.label }}</span>
          </template>
        </BaseButton>
      </div>
    </div>
  </BaseCard>
</template>
