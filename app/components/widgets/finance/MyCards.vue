<script setup lang="ts">
import type { MyCardsData } from '~/types/widgets'

interface Props {
  data: MyCardsData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="flex flex-col py-4 md:py-6">
    <div class="mb-6 flex items-center justify-between px-4 md:px-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseButton rounded="full" size="icon-sm">
        <Icon name="lucide:plus" class="size-3" />
      </BaseButton>
    </div>

    <!-- Cards Section -->
    <div
      v-for="(card, index) in data.cards"
      :key="index"
      class="flex items-center gap-4 px-6" :class="[
        index === 0 ? 'mb-8' : 'mb-2',
      ]"
    >
      <CreditCardSmall :class="card.color" />
      <div>
        <BaseParagraph size="xs" weight="medium">
          <span class="text-muted-600 dark:text-muted-400">{{ card.label }}</span>
        </BaseParagraph>
        <BaseHeading
          as="h4"
          size="lg"
          weight="semibold"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ card.balance.formatted || `$${card.balance.value}` }}</span>
        </BaseHeading>
      </div>
      <div class="ms-auto">
        <BaseDropdown
          label="Actions"
          placement="bottom-end"
          size="md"
          class="z-20"
        >
          <template #button>
            <BaseButton
              rounded="full"
              size="icon-sm"
              class="shrink-0 text-muted-500 dark:text-muted-400"
            >
              <Icon name="lucide:more-horizontal" class="size-3" />
            </BaseButton>
          </template>
          <BaseDropdownItem
            v-for="action in card.actions"
            :key="action.title"
            :to="action.href"
            :title="action.title"
            :text="action.description"
          >
            <template #start>
              <Icon :name="action.icon" class="me-2 block size-5" />
            </template>
          </BaseDropdownItem>
        </BaseDropdown>
      </div>
    </div>

    <!-- Information Section -->
    <div
      v-if="data.information"
      class="border-muted-200 dark:border-muted-700 mb-8 space-y-5 border-b-2 border-dashed px-6 pb-6"
    >
      <div class="flex items-center justify-between">
        <BaseHeading
          as="h5"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-800 dark:text-white"
        >
          <span>Information</span>
        </BaseHeading>
        <NuxtLink
          :to="data.information.editLink?.href || '#'"
          class="text-primary-500 font-sans text-sm underline-offset-4 hover:underline"
        >
          {{ data.information.editLink?.text || 'Edit' }}
        </NuxtLink>
      </div>

      <div
        v-for="info in data.information.items"
        :key="info.label"
        class="flex items-center justify-between"
      >
        <BaseHeading
          as="h5"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-800 dark:text-white"
        >
          <span>{{ info.label }}</span>
        </BaseHeading>
        <div class="flex items-center gap-1">
          <span
            v-if="info.status?.color"
            :class="`block size-2 rounded-full bg-${info.status.color}-500`"
          />
          <Icon
            v-if="info.icon"
            :name="info.icon"
            :class="info.iconClass || 'text-muted-400 size-10'"
          />
          <BaseParagraph
            :size="info.textSize || 'sm'"
            class="text-muted-500 dark:text-muted-400"
          >
            {{ info.value }}
          </BaseParagraph>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
