<script setup lang="ts">
import type { OverallProgressData } from '~/types/widgets'

interface Props {
  data: OverallProgressData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="flex flex-col p-4 md:p-6">
    <div class="mb-4 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <div>
        <BaseText size="sm" class="text-muted-400">
          {{ data.level }}
        </BaseText>
      </div>
    </div>
    <div>
      <BaseProgress
        size="xs"
        variant="primary"
        :model-value="data.progress"
      />
      <div class="mt-2">
        <p class="text-muted-600 dark:text-muted-400 font-sans text-xs leading-tight">
          {{ data.description }}
        </p>
        <div class="mt-2 flex justify-end">
          <NuxtLink
            :to="data.viewDetailsLink?.href || '#'"
            class="text-primary-500 font-sans text-sm underline-offset-4 hover:underline"
          >
            {{ data.viewDetailsLink?.text || 'View details' }}
          </NuxtLink>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
