<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { computed, ref } from 'vue'

interface Account {
  id: number | string
  type: string
  label: string
  number: string
  balance: number
}

interface Person {
  name: string
  avatar: string
}

interface SubmitPayload {
  amount: number | null | undefined
  account: Account | null
  person: string | null
}

const props = withDefaults(defineProps<{
  title?: string
  accounts: Account[]
  people?: Person[]
  card?: {
    status?: string
    name?: string
    number?: string
    brand?: string
  }
  buttonLabel?: string
}>(), {
  title: 'Quick Transfer',
  people: () => [
    { name: '<PERSON>', avatar: '/img/avatars/8.svg' },
    { name: '<PERSON>', avatar: '/img/avatars/24.svg' },
    { name: '<PERSON><PERSON><PERSON>', avatar: '/img/avatars/3.svg' },
    { name: '<PERSON>', avatar: '/img/avatars/12.svg' },
  ],
  card: () => ({
    status: 'active',
    name: '<PERSON>',
    number: '•••• •••• •••• 4479',
    brand: 'mastercard',
  }),
  buttonLabel: 'Send Money',
})

const emit = defineEmits<{ submit: [payload: SubmitPayload] }>()

const selectedPerson = ref<string | null>(props.people?.[1]?.name ?? null)
const selectedAccount = ref<Account | null>(props.accounts?.[0] ?? null)
const amount = ref<number | null>()

const target = ref<HTMLElement | null>(null)
const open = ref(false)

function openDropdown() {
  open.value = true
}

onClickOutside(target, () => (open.value = false))

function setAccount(account: Account) {
  selectedAccount.value = account
  open.value = false
}

function onSubmit() {
  emit('submit', { amount: amount.value, account: selectedAccount.value, person: selectedPerson.value })
}

const accountText = computed(() => {
  const acc = selectedAccount.value
  if (!acc)
    return ''
  return `${acc.type} ${acc.label}`
})
</script>

<template>
  <form action="" class="space-y-4" @submit.prevent="onSubmit">
    <BaseCard rounded="md" class="p-4 md:p-6">
      <div class="my-6">
        <CreditCardReal
          :status="card.status"
          :name="card.name"
          :number="card.number"
          :brand="card.brand"
        />
      </div>
      <div>
        <BaseHeading size="md" weight="medium" class="text-muted-800 dark:text-muted-100">
          {{ title }}
        </BaseHeading>
        <RadioGroupRoot v-model="selectedPerson" class="mt-6 flex justify-between gap-3">
          <div
            class="border-muted-200 hover:border-primary-500 dark:border-muted-700 flex size-12 items-center justify-center rounded-full border-2 transition-color duration-100"
          >
            <BaseButton size="icon-md" rounded="full">
              <Icon name="lucide:plus" class="size-4" />
            </BaseButton>
          </div>
          <BaseTooltip
            v-for="person in people"
            :key="person.name"
            :content="person.name"
          >
            <RadioGroupItem :value="person.name" class="rounded-full outline-none focus-visible:nui-focus">
              <RadioGroupIndicator
                force-mount
                class="border-muted-200 data-[state=checked]:border-primary-500 dark:border-muted-700 flex size-12 items-center justify-center rounded-full border-2"
              >
                <BaseAvatar :src="person.avatar" :alt="person.name" size="sm" />
              </RadioGroupIndicator>
            </RadioGroupItem>
          </BaseTooltip>
        </RadioGroupRoot>
      </div>
      <div class="mt-6 space-y-4">
        <!-- Dropdown -->
        <div ref="target" class="relative z-20 w-full">
          <button
            type="button"
            class="click-blur dark:bg-muted-950 border-muted-200 dark:border-muted-800 w-full rounded-xl border bg-white p-4"
            @click="openDropdown()"
          >
            <span class="flex w-full items-center gap-3 text-start">
              <TairoLogo class="text-primary-500 size-8" />
              <div>
                <BaseText size="sm" class="text-muted-800 dark:text-muted-200 block capitalize">
                  {{ accountText }}
                </BaseText>
                <BaseText size="xs" class="text-muted-500 dark:text-muted-400 block">
                  ${{ selectedAccount?.balance.toFixed(2) }}
                </BaseText>
              </div>
              <Icon
                name="lucide:chevron-down"
                class="text-muted-400 ms-auto size-4 transition-transform duration-300"
                :class="open && 'rotate-180'"
              />
            </span>
          </button>
          <Transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <div
              v-if="open"
              class="border-muted-200 dark:border-muted-800 dark:bg-muted-950 shadow-muted-400/10 dark:shadow-muted-800/10 absolute start-0 top-20 w-full rounded-xl border bg-white p-2 shadow-xl"
            >
              <!-- Accounts -->
              <ul>
                <li v-for="account in accounts" :key="account.id">
                  <button
                    type="button"
                    class="hover:bg-muted-100 dark:hover:bg-muted-900 flex w-full items-center gap-3 rounded-lg px-4 py-2 text-start transition-colors duration-300"
                    @click="setAccount(account)"
                  >
                    <TairoLogo class="text-muted-400 dark:text-muted-100 size-8" />
                    <span class="block">
                      <span class="font-heading text-muted-800 dark:text-muted-200 block text-sm capitalize">
                        {{ account.type }} {{ account.label }}
                      </span>
                      <span class="font-heading text-muted-500 dark:text-muted-400 block text-xs">
                        ${{ account.balance.toFixed(2) }}
                      </span>
                    </span>
                  </button>
                </li>
              </ul>
            </div>
          </Transition>
        </div>
        <!-- Input -->
        <div class="relative">
          <BaseInputNumber v-model="amount" placeholder="Transfer amount" />
        </div>
        <div>
          <BaseButton type="submit" rounded="md" variant="primary" class="w-full">
            {{ buttonLabel }}
          </BaseButton>
        </div>
      </div>
    </BaseCard>
  </form>
</template>
