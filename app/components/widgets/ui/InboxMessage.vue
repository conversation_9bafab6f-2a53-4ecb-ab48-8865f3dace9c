<script setup lang="ts">
import type { UIInboxMessageData } from '~/types/widgets'

interface Props {
  data: UIInboxMessageData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-4 md:p-6">
    <InboxMessage
      :picture="data.picture"
      :name="data.name"
      :title="data.title"
      :text="data.text"
      :time="data.time"
      :rounded="data.rounded || 'lg'"
    />
  </BaseCard>
</template>
