<script setup lang="ts">
import type { UIPictureData } from '~/types/widgets'

interface Props {
  data: UIPictureData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-3">
    <Picture
      :src="data.src"
      :alt="data.alt"
      :rounded="data.rounded || 'lg'"
      :height="data.height"
      :width="data.width"
      :loading="data.loading"
    />
  </BaseCard>
</template>
