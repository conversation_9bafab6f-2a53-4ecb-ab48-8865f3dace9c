<script setup lang="ts">
import type { UICalendarData } from '~/types/widgets'
import { hydrateOnVisible } from 'vue'
import 'v-calendar/dist/style.css'
import '~/assets/vcalendar.css'

interface Props {
  data?: UICalendarData
}

const props = withDefaults(defineProps<Props>(), {})

const Calendar = defineAsyncComponent({
  loader: () => import('v-calendar').then(mod => mod.Calendar),
  hydrate: hydrateOnVisible(),
})

const calendarAttributes = computed(() => [
  {
    key: 'today',
    highlight: true,
    order: 0,
    dates: [new Date()],
  },
  ...(props.data?.attributes || []),
])
</script>

<template>
  <BaseCard rounded="lg" class="p-2">
    <Calendar
      :attributes="calendarAttributes"
      :title-position="data?.titlePosition || 'left'"
      :expanded="data?.expanded ?? true"
      :borderless="data?.borderless ?? true"
      :transparent="data?.transparent ?? true"
      :trim-weeks="data?.trimWeeks ?? true"
      :class="data?.calendarClass || 'max-w-full rounded-xl'"
    />
  </BaseCard>
</template>
