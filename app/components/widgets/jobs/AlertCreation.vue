<template>
  <div class="bg-muted-200 dark:bg-muted-950/60 mb-12 rounded-xl p-6">
    <!-- Title -->
    <div class="mb-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 mb-2 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseParagraph size="xs">
        <span class="text-muted-500 dark:text-muted-400">
          {{ data.description }}
        </span>
      </BaseParagraph>
    </div>
    
    <!-- Form -->
    <form class="space-y-2" @submit.prevent="handleCreateAlert">
      <BaseInput
        v-model.trim="localKeywords"
        rounded="lg"
        :placeholder="data.inputPlaceholder || 'Job keywords'"
      />
      <BaseButton
        rounded="lg"
        variant="primary"
        class="w-full"
        type="submit"
      >
        {{ data.buttonText || 'Create alert' }}
      </BaseButton>
    </form>
  </div>
</template>

<script setup lang="ts">
import type { JobAlertData } from '~/types/widgets'

interface Props {
  data: JobAlertData
  modelValue?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'create-alert', keywords: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
})

const emit = defineEmits<Emits>()

// Local reactive state
const localKeywords = ref(props.modelValue)

// Watch for external model changes
watch(() => props.modelValue, (newValue) => {
  localKeywords.value = newValue
})

// Emit model updates
watch(localKeywords, (newValue) => {
  emit('update:modelValue', newValue)
})

// Handle alert creation
function handleCreateAlert() {
  const keywords = localKeywords.value.trim()
  
  if (!keywords) {
    return // Don't create empty alerts
  }
  
  emit('create-alert', keywords)
  
  // Call callback if provided
  if (props.data.onCreateAlert) {
    props.data.onCreateAlert(keywords)
  }
  
  // Clear input after successful creation
  localKeywords.value = ''
}
</script>

<style scoped>
/* Additional styling if needed */
</style>
