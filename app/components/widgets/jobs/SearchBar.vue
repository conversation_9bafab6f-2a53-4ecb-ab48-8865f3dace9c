<template>
  <BaseCard
    rounded="lg"
    class="md:portrait:py-6 md:portrait:px-4 md:portrait:grid md:portrait:grid-cols-12 lg:landscape:divide-x divide-muted-200 dark:divide-muted-800 mb-10 flex w-full flex-col items-center py-2 sm:flex-row sm:py-0 2xl:landscape:divide-x"
  >
    <!-- Keywords Search Input -->
    <div class="md:portrait:ps-4 md:portrait:col-span-6 w-full py-2 pe-4 ps-4 sm:w-auto sm:grow sm:ps-2">
      <TairoInput
        v-model.trim="localKeywords"
        rounded="lg"
        :placeholder="data.searchPlaceholder || 'Job keywords'"
        icon="lucide:search"
      />
    </div>
    
    <!-- Location Input -->
    <div class="md:portrait:col-span-6 w-full flex-1 px-4 py-2 sm:w-auto">
      <TairoInput
        v-model.trim="localLocation"
        rounded="lg"
        :placeholder="data.locationPlaceholder || 'Location'"
        icon="lucide:map-pin"
      />
    </div>
    
    <!-- Job Type Select -->
    <div class="md:portrait:col-span-6 w-full flex-1 px-4 py-2 sm:w-auto">
      <BaseSelect
        v-model="localJobType"
        rounded="lg"
        icon="lucide:briefcase"
        label=""
        hide-label
        placeholder="Select a type"
      >
        <BaseSelectItem 
          v-for="option in data.typeOptions" 
          :key="option.value" 
          :value="option.value"
        >
          {{ option.label }}
        </BaseSelectItem>
      </BaseSelect>
    </div>
    
    <!-- Salary Range Select -->
    <div class="md:portrait:col-span-6 w-full flex-1 px-4 py-2 sm:w-auto">
      <BaseSelect
        v-model="localSalaryRange"
        rounded="lg"
        icon="lucide:dollar-sign"
        label=""
        placeholder="Select a range"
      >
        <BaseSelectItem 
          v-for="option in data.salaryRangeOptions" 
          :key="option.value" 
          :value="option.value"
        >
          {{ option.label }}
        </BaseSelectItem>
      </BaseSelect>
    </div>
    
    <!-- Search Button -->
    <div class="md:portrait:col-span-12 w-full px-4 py-2 sm:w-auto">
      <BaseButton
        rounded="lg"
        variant="primary"
        class="md:portrait:w-full w-full sm:w-32"
        @click="handleSearch"
      >
        {{ data.searchButtonText || 'Search' }}
      </BaseButton>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import type { JobSearchData } from '~/types/widgets'

interface Props {
  data: JobSearchData
  modelValue?: {
    keywords?: string
    location?: string
    jobType?: string
    salaryRange?: string
  }
}

interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void
  (e: 'search', value: {
    keywords: string
    location: string
    type: string
    salaryRange: string
  }): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    keywords: '',
    location: '',
    jobType: 'all',
    salaryRange: 'all',
  }),
})

const emit = defineEmits<Emits>()

// Local reactive state
const localKeywords = ref(props.modelValue?.keywords || '')
const localLocation = ref(props.modelValue?.location || '')
const localJobType = ref(props.modelValue?.jobType || 'all')
const localSalaryRange = ref(props.modelValue?.salaryRange || 'all')

// Watch for external model changes
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    localKeywords.value = newValue.keywords || ''
    localLocation.value = newValue.location || ''
    localJobType.value = newValue.jobType || 'all'
    localSalaryRange.value = newValue.salaryRange || 'all'
  }
}, { deep: true })

// Emit model updates
watch([localKeywords, localLocation, localJobType, localSalaryRange], () => {
  emit('update:modelValue', {
    keywords: localKeywords.value,
    location: localLocation.value,
    jobType: localJobType.value,
    salaryRange: localSalaryRange.value,
  })
}, { deep: true })

// Handle search action
function handleSearch() {
  const searchData = {
    keywords: localKeywords.value,
    location: localLocation.value,
    type: localJobType.value,
    salaryRange: localSalaryRange.value,
  }
  
  emit('search', searchData)
  
  // Call callback if provided
  if (props.data.onSearch) {
    props.data.onSearch(searchData)
  }
}
</script>

<style scoped>
/* Additional styling if needed */
</style>
