<script setup lang="ts">
import type { KpiTileData } from '~/types/widgets'

interface KpiTileProps {
  data?: KpiTileData
}

withDefaults(defineProps<KpiTileProps>(), {
  data: () => ({
    title: 'Metric',
    icon: {
      name: 'solar:chart-square-bold-duotone',
      color: 'bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2',
    },
    value: 0,
    trend: {
      value: 0,
      percentage: 0,
      isIncrease: true,
      period: 'since last month',
    },
  }),
})
</script>

<template>
  <BaseCard rounded="md" class="p-4">
    <div class="mb-1 flex items-center justify-between">
      <BaseHeading
        as="h5"
        size="sm"
        weight="medium"
        lead="tight"
        class="text-muted-500 dark:text-muted-400"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseIconBox
        size="xs"
        :class="data.icon.color"
        rounded="full"
        variant="none"
      >
        <Icon :name="data.icon.name" class="size-5" />
      </BaseIconBox>
    </div>
    <div class="mb-2">
      <BaseHeading
        as="h4"
        size="3xl"
        weight="bold"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ typeof data.value === 'number' ? data.value.toLocaleString() : data.value }}</span>
      </BaseHeading>
    </div>
    <div
      v-if="data.trend"
      :class="`flex items-center gap-1 font-sans text-sm ${
        data.trend.isIncrease ? 'text-success-500' : 'text-destructive-500'
      }`"
    >
      <span>{{ data.trend.isIncrease ? '+' : '' }}{{ data.trend.percentage || data.trend.value }}%</span>
      <Icon
        :name="data.trend.isIncrease ? 'lucide:trending-up' : 'lucide:trending-down'"
        class="size-5"
      />
      <span class="text-muted-400 text-xs">{{ data.trend.period || data.trend.label }}</span>
    </div>
  </BaseCard>
</template>
