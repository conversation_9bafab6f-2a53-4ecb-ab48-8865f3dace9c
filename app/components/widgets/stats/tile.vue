<script setup lang="ts">
interface StatTileData {
  title: string
  value: string | number
  icon: string
  iconBgClass?: string
  iconTextClass?: string
  change?: string
  changeType?: 'increase' | 'decrease' | 'neutral'
  changeLabel?: string
}

const props = withDefaults(defineProps<{
  stat?: StatTileData
}>(), {
  stat: () => ({
    title: 'Transactions',
    value: '7,549',
    icon: 'solar:chart-square-bold-duotone',
    iconBgClass: 'bg-primary-100 dark:bg-primary-500/20',
    iconTextClass: 'text-primary-500 dark:text-primary-400',
    change: '+7.8%',
    changeType: 'increase' as const,
    changeLabel: 'since last month',
  }),
})

const changeClasses = computed(() => {
  switch (props.stat?.changeType) {
    case 'increase':
      return 'text-success-500'
    case 'decrease':
      return 'text-danger-500'
    default:
      return 'text-muted-500'
  }
})

const changeIcon = computed(() => {
  switch (props.stat?.changeType) {
    case 'increase':
      return 'lucide:trending-up'
    case 'decrease':
      return 'lucide:trending-down'
    default:
      return 'lucide:minus'
  }
})
</script>

<template>
  <BaseCard rounded="md" class="p-4">
    <div class="mb-1 flex items-center justify-between">
      <BaseHeading
        as="h5"
        size="sm"
        weight="medium"
        lead="tight"
        class="text-muted-500 dark:text-muted-400"
      >
        <span>{{ stat.title }}</span>
      </BaseHeading>
      <BaseIconBox
        size="xs"
        :class="`${stat.iconBgClass} ${stat.iconTextClass} dark:border-2 dark:border-current`"
        rounded="full"
        variant="none"
      >
        <Icon :name="stat.icon" class="size-5" />
      </BaseIconBox>
    </div>
    <div class="mb-2">
      <BaseHeading
        as="h4"
        size="3xl"
        weight="bold"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ stat.value }}</span>
      </BaseHeading>
    </div>
    <div
      v-if="stat.change"
      :class="`${changeClasses} flex items-center gap-1 font-sans text-sm`"
    >
      <span>{{ stat.change }}</span>
      <Icon :name="changeIcon" class="size-5" />
      <span v-if="stat.changeLabel" class="text-muted-400 text-xs">{{ stat.changeLabel }}</span>
    </div>
  </BaseCard>
</template>
