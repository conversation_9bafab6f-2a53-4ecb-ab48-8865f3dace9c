<script setup lang="ts">
withDefaults(defineProps<{
  title?: string
}>(), {
  title: 'Your Quick Stats',
})
</script>

<template>
  <!-- Quick stats -->
  <BaseCard rounded="md" class="p-6 h-full flex flex-col">
    <div class="mb-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="semibold"
        lead="tight"
        class="text-muted-800 dark:text-white"
      >
        <span>{{ title }}</span>
      </BaseHeading>
    </div>
    <div class="grid gap-4 grid-cols-2 mt-auto">
      <div class="col-span-2">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          We've selected some of your most important stats to keep you updated. You can always view more in the
          <BaseLink to="/dashboards" class="text-primary-600 dark:text-primary-400">
            reports section.
          </BaseLink>
        </BaseParagraph>
      </div>
      <!-- Grid item -->
      <div
        class="ring-muted-900/5 dark:ring-muted-800 ring-1 flex flex-col items-center text-center shadow-sm gap-2 rounded-md p-4"
      >
        <BaseIconBox
          size="sm"
          class="bg-primary-100 text-primary-500 dark:bg-primary-500/20 dark:text-primary-400 dark:border-primary-500 dark:border-2"
          rounded="full"
          variant="none"
        >
          <Icon name="ph:nut-duotone" class="size-5" />
        </BaseIconBox>
        <div>
          <BaseHeading
            as="h2"
            size="md"
            weight="semibold"
            lead="tight"
            class="text-muted-800 dark:text-white"
          >
            <span>2,870</span>
          </BaseHeading>
          <BaseParagraph size="sm">
            <span class="text-muted-500 dark:text-muted-400">
              Sales this month
            </span>
          </BaseParagraph>
        </div>
      </div>
      <!-- Grid item -->
      <div
        class="ring-muted-900/5 dark:ring-muted-800 ring-1 flex flex-col items-center text-center shadow-sm gap-2 rounded-md p-4"
      >
        <BaseIconBox
          size="sm"
          class="bg-amber-100 text-amber-500 dark:border-2 dark:border-amber-500 dark:bg-amber-500/20 dark:text-amber-400"
          rounded="full"
          variant="none"
        >
          <Icon name="ph:handshake-duotone" class="size-5" />
        </BaseIconBox>
        <div>
          <BaseHeading
            as="h2"
            size="md"
            weight="semibold"
            lead="tight"
            class="text-muted-800 dark:text-white"
          >
            <span>159</span>
          </BaseHeading>
          <BaseParagraph size="sm">
            <span class="text-muted-500 dark:text-muted-400">
              New users
            </span>
          </BaseParagraph>
        </div>
      </div>
      <!-- Grid item -->
      <div
        class="ring-muted-900/5 dark:ring-muted-800 ring-1 flex flex-col items-center text-center shadow-sm gap-2 rounded-md p-4"
      >
        <BaseIconBox
          size="sm"
          class="bg-green-100 text-green-500 dark:border-2 dark:border-green-500 dark:bg-green-500/20 dark:text-green-400"
          rounded="full"
          variant="none"
        >
          <Icon name="ph:sketch-logo-duotone" class="size-5" />
        </BaseIconBox>
        <div>
          <BaseHeading
            as="h2"
            size="md"
            weight="semibold"
            lead="tight"
            class="text-muted-800 dark:text-white"
          >
            <span>$429.18</span>
          </BaseHeading>
          <BaseParagraph size="sm">
            <span class="text-muted-500 dark:text-muted-400">
              Earned today
            </span>
          </BaseParagraph>
        </div>
      </div>
      <!-- Grid item -->
      <div
        class="ring-muted-900/5 dark:ring-muted-800 ring-1 flex flex-col items-center text-center shadow-sm gap-2 rounded-md p-4"
      >
        <BaseIconBox
          size="sm"
          class="bg-indigo-100 text-indigo-500 dark:border-2 dark:border-indigo-500 dark:bg-indigo-500/20 dark:text-indigo-400"
          rounded="full"
          variant="none"
        >
          <Icon name="ph:bank-duotone" class="size-5" />
        </BaseIconBox>
        <div>
          <BaseHeading
            as="h2"
            size="md"
            weight="semibold"
            lead="tight"
            class="text-muted-800 dark:text-white"
          >
            <span>$6816.32</span>
          </BaseHeading>
          <BaseParagraph size="sm">
            <span class="text-muted-500 dark:text-muted-400">
              Total balance
            </span>
          </BaseParagraph>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
