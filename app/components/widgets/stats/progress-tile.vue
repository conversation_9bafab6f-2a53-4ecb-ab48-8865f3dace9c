<script setup lang="ts">
import { computed } from 'vue'

interface ProgressStatTileData {
  title: string
  value: string | number
  change?: string
  // Optional explicit class for the change text (e.g. 'text-orange-500')
  changeClass?: string
  // Circular progress value (0-100)
  progress: number
  // Overlay icon name
  icon: string
  // Color class applied to the icon and circle wrapper (e.g. 'text-orange-500')
  colorClass?: string
  // Variant for BaseProgressCircle (limited to supported variants)
  circleVariant?: 'none' | 'primary' | 'default' | 'dark'
  // Circle dimensions
  size?: number
  thickness?: number
}

const props = withDefaults(defineProps<{
  stat?: ProgressStatTileData
}>(), {
  stat: () => ({
    title: 'Stat Title',
    value: '0',
    change: undefined,
    changeClass: undefined,
    progress: 0,
    icon: 'lucide:circle',
    colorClass: 'text-primary-500',
    circleVariant: 'none' as const,
    size: 75,
    thickness: 1,
  }),
})

const circleClass = computed(() => {
  // When variant is 'none', apply muted track colors like seen in designs
  const base = props.stat?.colorClass ? props.stat.colorClass : ''
  if (props.stat?.circleVariant === 'none') {
    return `${base} *:first:text-muted-200 *:dark:first:text-muted-900`.trim()
  }
  return base
})

const changeTextClass = computed(() => {
  return props.stat?.changeClass || props.stat?.colorClass || 'text-muted-500'
})
</script>

<template>
  <BaseCard rounded="md" class="p-2" data-testid="progress-tile">
    <div class="flex items-center">
      <div :class="`${stat.colorClass} relative`" data-testid="progress-circle-wrap">
        <BaseProgressCircle
          :max="100"
          :model-value="stat.progress"
          :size="stat.size"
          :thickness="stat.thickness"
          :variant="stat.circleVariant"
          :class="circleClass"
        />
        <Icon
          :name="stat.icon"
          class="absolute start-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2"
        />
      </div>
      <div>
        <BaseParagraph
          size="xs"
          weight="medium"
          class="uppercase text-muted-600 dark:text-muted-400"
        >
          {{ stat.title }}
        </BaseParagraph>
        <BaseHeading size="xl" weight="medium" class="text-muted-900 dark:text-white">
          {{ stat.value }}
        </BaseHeading>
      </div>
      <div class="ms-auto me-2">
        <BaseParagraph v-if="stat.change" size="sm" weight="medium" :class="changeTextClass" data-testid="change">
          {{ stat.change }}
        </BaseParagraph>
      </div>
    </div>
  </BaseCard>
</template>
