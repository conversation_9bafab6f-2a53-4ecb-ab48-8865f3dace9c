<script setup lang="ts">
import type { CardInfo } from '~/types/billing'

interface MonthOption {
  value: string
  label: string
}

interface YearOption {
  value: string
  label: string
}

interface Props {
  title?: string
  cardInfo: CardInfo
  monthOptions?: MonthOption[]
  yearOptions?: YearOption[]
  rounded?: string
  cardClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Payment information',
  monthOptions: () => [
    { value: '01', label: '01' },
    { value: '02', label: '02' },
    { value: '03', label: '03' },
    { value: '04', label: '04' },
    { value: '05', label: '05' },
    { value: '06', label: '06' },
    { value: '07', label: '07' },
    { value: '08', label: '08' },
    { value: '09', label: '09' },
    { value: '10', label: '10' },
    { value: '11', label: '11' },
    { value: '12', label: '12' },
  ],
  yearOptions: () => [
    { value: '23', label: '23' },
    { value: '24', label: '24' },
    { value: '25', label: '25' },
    { value: '26', label: '26' },
    { value: '27', label: '27' },
    { value: '28', label: '28' },
    { value: '29', label: '29' },
    { value: '30', label: '30' },
  ],
  rounded: 'md',
  cardClass: 'p-4 md:p-6',
})

const emit = defineEmits<{
  'update:cardInfo': [cardInfo: CardInfo]
}>()

function updateCardInfo(field: keyof CardInfo, value: string | undefined) {
  const updatedCardInfo = { ...props.cardInfo, [field]: value }
  emit('update:cardInfo', updatedCardInfo)
}
</script>

<template>
  <BaseCard :rounded="rounded" :class="cardClass">
    <div class="mb-4">
      <BaseHeading
        as="h4"
        size="sm"
        weight="semibold"
      >
        {{ title }}
      </BaseHeading>
    </div>
    <CreditCardReal
      :name="cardInfo.name"
      :number="cardInfo.number"
      :expiry-month="cardInfo.expiryMonth"
      :expiry-year="cardInfo.expiryMonth"
      :cvc="cardInfo.cvc"
    />
    <div class="mt-5">
      <form>
        <div class="grid grid-cols-12 gap-4">
          <BaseInput
            :model-value="cardInfo.name"
            label="Name on card"
            placeholder="ex: John Doe"
            class="col-span-12"
            @update:model-value="updateCardInfo('name', $event)"
          />
          <BaseInput
            :model-value="cardInfo.number"
            label="Card number"
            placeholder="ex: 4242 4242 4242 4242"
            class="col-span-12"
            @update:model-value="updateCardInfo('number', $event)"
          />
          <div class="col-span-12">
            <div class="grid gap-4 sm:grid-cols-3">
              <BaseSelect
                :model-value="cardInfo.expiryMonth"
                label="Exp. month"
                placeholder="Month"
                @update:model-value="updateCardInfo('expiryMonth', $event)"
              >
                <BaseSelectItem
                  v-for="option in monthOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </BaseSelectItem>
              </BaseSelect>
              <BaseSelect
                :model-value="cardInfo.expiryYear"
                label="Exp. year"
                placeholder="Year"
                @update:model-value="updateCardInfo('expiryYear', $event)"
              >
                <BaseSelectItem
                  v-for="option in yearOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </BaseSelectItem>
              </BaseSelect>
              <BaseInput
                :model-value="cardInfo.cvc"
                label="CVC"
                placeholder="ex: 239"
                @update:model-value="updateCardInfo('cvc', $event)"
              />
            </div>
          </div>
        </div>
      </form>
    </div>
  </BaseCard>
</template>
