<script setup lang="ts">
import type { SeatInfo } from '~/types/billing'

interface Props {
  title?: string
  usedSeats: number
  totalSeats: number
  seats: SeatInfo[]
  managementTooltip?: string
  rounded?: string
  cardClass?: string
}

withDefaults(defineProps<Props>(), {
  title: 'Used seats',
  managementTooltip: 'Manage plan seats',
  rounded: 'md',
  cardClass: 'p-4 md:p-6',
})

const emit = defineEmits<{
  manageSeats: []
}>()

function handleManageSeats() {
  emit('manageSeats')
}
</script>

<template>
  <BaseCard :rounded="rounded" :class="cardClass">
    <div class="mb-4 flex items-center justify-between">
      <BaseHeading
        as="h4"
        size="sm"
        weight="medium"
      >
        {{ title }}
      </BaseHeading>
      <div>
        <BaseText
          size="xs"
          lead="tight"
          class="text-muted-400"
        >
          {{ totalSeats - usedSeats }}/{{ totalSeats }} remaining
        </BaseText>
      </div>
    </div>
    <div
      class="flex items-center gap-2 lg:justify-between"
    >
      <div
        class="flex items-center gap-2"
      >
        <BaseTooltip
          v-for="seat in seats"
          :key="seat.name"
          :content="seat.tooltipContent"
        >
          <BaseAvatar
            v-if="seat.avatar"
            :src="seat.avatar"
            size="sm"
          />
          <BaseAvatar
            v-else
            size="sm"
            :text="seat.text || seat.name.substring(0, 2).toUpperCase()"
            :class="getRandomColor()"
          />
        </BaseTooltip>
      </div>
      <div>
        <BaseTooltip :content="managementTooltip">
          <button
            type="button"
            class="flex items-center justify-center border-muted-200 dark:border-muted-800 hover:border-primary-500 dark:hover:border-primary-500 text-muted-400 dark:text-muted-600 hover:text-primary-500 dark:hover:text-primary-500 size-10 rounded-full border-2 border-dashed transition-all duration-300 hover:border-solid"
            @click="handleManageSeats"
          >
            <Icon name="solar:pen-2-linear" class="size-4" />
          </button>
        </BaseTooltip>
      </div>
    </div>
  </BaseCard>
</template>
