<script setup lang="ts">
import type { BillingInterval } from '~/types/billing'

interface CycleOption {
  label: string
  value: BillingInterval
}

interface Props {
  title?: string
  modelValue: BillingInterval
  options?: CycleOption[]
  invoicesLinkText?: string
  rounded?: string
  cardClass?: string
}

withDefaults(defineProps<Props>(), {
  title: 'Billing cycle',
  options: () => [
    { label: 'Monthly', value: 'monthly' },
    { label: 'Semestral', value: 'semestral' },
    { label: 'Yearly', value: 'yearly' },
  ],
  invoicesLinkText: 'My invoices',
  rounded: 'md',
  cardClass: 'p-4 md:p-6',
})

const emit = defineEmits<{
  'update:modelValue': [value: BillingInterval]
  'invoicesClick': []
}>()

function updateValue(value: BillingInterval) {
  emit('update:modelValue', value)
}

function handleInvoicesClick() {
  emit('invoicesClick')
}
</script>

<template>
  <BaseCard :rounded="rounded" :class="cardClass">
    <div class="mb-8 flex items-center justify-between">
      <BaseHeading
        as="h4"
        size="sm"
        weight="semibold"
      >
        {{ title }}
      </BaseHeading>
      <div>
        <NuxtLink
          class="text-primary-500 font-sans text-xs underline underline-offset-4"
          @click="handleInvoicesClick"
        >
          {{ invoicesLinkText }}
        </NuxtLink>
      </div>
    </div>
    <BaseRadioGroup :model-value="modelValue" class="flex items-center gap-6" @update:model-value="updateValue">
      <BaseRadio
        v-for="option in options"
        :key="option.value"
        :label="option.label"
        :value="option.value"
      />
    </BaseRadioGroup>
  </BaseCard>
</template>
