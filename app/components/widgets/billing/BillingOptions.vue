<script setup lang="ts">
import type { BillingNotification } from '~/types/billing'

interface Props {
  title?: string
  notifications: BillingNotification[]
  rounded?: string
  cardClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Billing options',
  rounded: 'md',
  cardClass: 'p-4 md:p-6',
})

const emit = defineEmits<{
  'update:notifications': [notifications: BillingNotification[]]
}>()

function updateNotification(id: string, enabled: boolean) {
  const updatedNotifications = props.notifications.map(notification =>
    notification.id === id ? { ...notification, enabled } : notification,
  )
  emit('update:notifications', updatedNotifications)
}
</script>

<template>
  <BaseCard :rounded="rounded" :class="cardClass">
    <div class="mb-8 flex items-center justify-between">
      <BaseHeading
        as="h4"
        size="sm"
        weight="semibold"
      >
        {{ title }}
      </BaseHeading>
      <div>
        <BaseText
          size="xs"
          lead="tight"
          class="text-muted-400"
        />
      </div>
    </div>
    <div class="space-y-6">
      <BaseSwitchBall
        v-for="notification in notifications"
        :id="`billing-ball-${notification.id}`"
        :key="notification.id"
        :model-value="notification.enabled"
        :label="notification.label"
        :sublabel="notification.sublabel"
        variant="primary"
        @update:model-value="updateNotification(notification.id, $event)"
      />
    </div>
  </BaseCard>
</template>
