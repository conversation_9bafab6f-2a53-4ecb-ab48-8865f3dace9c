<script setup lang="ts">
import type { BillingPlan } from '~/types/billing'

interface Props {
  plan: BillingPlan
  selectedColor?: string
  logoClass?: string
  rounded?: string
  cardClass?: string
}

withDefaults(defineProps<Props>(), {
  selectedColor: 'text-primary-500',
  logoClass: 'size-12 shrink-0',
  rounded: 'md',
  cardClass: 'flex h-full flex-col p-4 md:p-6',
})
</script>

<template>
  <BaseCard :rounded="rounded" :class="cardClass">
    <div class="flex gap-12">
      <TairoLogo :class="[logoClass, selectedColor]" />
      <div>
        <BaseText
          size="xl"
          lead="tight"
          weight="semibold"
        >
          ${{ plan.price.monthly }}
          <span class="text-sm text-muted-400 font-normal">/per month</span>
        </BaseText>
        <BaseText
          size="xs"
          lead="tight"
          class="mb-2"
          :class="selectedColor"
        >
          Billed ${{ plan.price.yearly }} yearly
        </BaseText>
        <BaseParagraph
          size="xs"
          class="text-muted-500 dark:text-muted-400"
        >
          {{ plan.description }}
        </BaseParagraph>
      </div>
    </div>

    <div class="my-8">
      <BaseParagraph
        size="sm"
        lead="tight"
        class="text-muted-500 dark:text-muted-400"
      >
        Tairo has incredible features and each plan perfectly adapts to
        your company, wether it is a small business or a bigger one. Tairo
        can also scale smoothly with you, as your business grows.
      </BaseParagraph>
    </div>

    <div class="grid grid-cols-2 gap-4 font-sans text-xs">
      <div>
        <ul>
          <li
            v-for="item in plan.features"
            :key="item"
            class="flex items-center gap-2"
            :class="selectedColor"
          >
            <Icon name="lucide:check" class="size-3 text-current" />
            <span class="text-muted-400">{{ item }}</span>
          </li>
        </ul>
      </div>
      <div>
        <ul>
          <li
            v-for="item in plan.benefits"
            :key="item"
            class="flex items-center gap-2"
            :class="selectedColor"
          >
            <Icon name="lucide:check" class="size-3 text-current" />
            <span class="text-muted-400">{{ item }}</span>
          </li>
        </ul>
      </div>
    </div>
  </BaseCard>
</template>
