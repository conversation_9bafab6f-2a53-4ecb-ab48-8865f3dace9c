<script setup lang="ts">
interface Tool {
  name: string
  level: number
  mastery: string
  logo?: string
}

interface Props {
  tools: Tool[]
  title?: string
  rounded?: string
  cardClass?: string
}

withDefaults(defineProps<Props>(), {
  title: 'Tools',
  rounded: 'md',
  cardClass: 'p-8',
})
</script>

<template>
  <BaseCard v-if="tools.length" :rounded="rounded" :class="cardClass">
    <div class="mb-8 flex items-center gap-2">
      <h4
        class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
      >
        {{ title }}
      </h4>
    </div>
    <div class="space-y-6">
      <div
        v-for="item in tools"
        :key="item.name"
        class="flex w-full items-center gap-2"
      >
        <div
          class="border-muted-200 dark:border-muted-600 dark:bg-muted-700 relative flex size-[50px] shrink-0 items-center justify-center rounded-full border bg-white"
        >
          <img
            v-if="item.logo"
            :src="item.logo"
            :alt="item.name"
            class="size-8 rounded-full"
          >
          <Icon
            v-else
            name="solar:settings-linear"
            class="size-6 text-muted-400"
          />
          <BaseProgressCircle
            :size="68"
            :thickness="1.5"
            :model-value="item.level"
            variant="primary"
            class="absolute -start-2.5 -top-2.5"
          />
        </div>
        <div>
          <BaseHeading
            tag="h3"
            size="sm"
            weight="medium"
          >
            {{ item.name }}
          </BaseHeading>
          <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
            <span>{{ item.mastery }}</span>
          </BaseParagraph>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
