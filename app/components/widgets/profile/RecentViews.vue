<script setup lang="ts">
import { getRandomColor } from '../../../app/utils/colors'

interface RecentView {
  name: string
  role: string
  src: string
  badge?: string
  viewUrl?: string
}

interface Props {
  recentViews: RecentView[]
  title?: string
  viewButtonTooltip?: string
  rounded?: string
  cardClass?: string
}

withDefaults(defineProps<Props>(), {
  title: 'Recent Views',
  viewButtonTooltip: 'View Profile',
  rounded: 'md',
  cardClass: 'p-4 md:p-8',
})
</script>

<template>
  <BaseCard v-if="recentViews.length" :rounded="rounded" :class="cardClass">
    <div class="mb-8 flex items-center gap-2">
      <h4
        class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
      >
        {{ title }}
      </h4>
    </div>
    <div class="space-y-6">
      <div
        v-for="item in recentViews"
        :key="item.name"
        class="flex w-full items-center gap-2"
      >
        <BaseAvatar
          :src="item.src"
          size="xs"
          :badge-src="item.badge"
          :class="getRandomColor()"
        />
        <div>
          <BaseHeading
            tag="h3"
            size="sm"
            weight="medium"
          >
            {{ item.name }}
          </BaseHeading>
          <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
            <span>{{ item.role }}</span>
          </BaseParagraph>
        </div>
        <div class="ms-auto">
          <BaseTooltip :content="viewButtonTooltip">
            <BaseButton
              :to="item.viewUrl || '#'"
              size="icon-sm"
              rounded="full"
            >
              <Icon name="lucide:arrow-right" class="size-4" />
            </BaseButton>
          </BaseTooltip>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
