<script setup lang="ts">
interface Props {
  modelValue: boolean
  title?: string
  description?: string
  rounded?: string
  cardClass?: string
}

withDefaults(defineProps<Props>(), {
  title: 'Notifications',
  description: 'Enable or disable this setting to manage if your network should be notified when you make changes to your profile.',
  rounded: 'md',
  cardClass: 'p-4 md:p-8',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

function updateValue(value: boolean) {
  emit('update:modelValue', value)
}
</script>

<template>
  <BaseCard :rounded="rounded" :class="cardClass">
    <div class="mb-8 flex items-center gap-2">
      <h4
        class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
      >
        {{ title }}
      </h4>
      <div class="ms-auto">
        <BaseSwitchBall :model-value="modelValue" variant="primary" @update:model-value="updateValue" />
      </div>
    </div>
    <div>
      <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400">
        {{ description }}
      </BaseParagraph>
    </div>
  </BaseCard>
</template>
