<script setup lang="ts">
interface Recommendation {
  name: string
  role: string
  src: string
  text: string
  date: string
  badge?: string
}

interface Props {
  recommendations: Recommendation[]
  title?: string
  rounded?: string
  cardClass?: string
}

withDefaults(defineProps<Props>(), {
  title: 'Recommendations',
  rounded: 'md',
  cardClass: 'p-4 md:p-8',
})
</script>

<template>
  <BaseCard v-if="recommendations.length" :rounded="rounded" :class="cardClass">
    <div class="mb-8 flex items-center gap-2">
      <h4
        class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
      >
        {{ title }}
      </h4>
    </div>
    <div class="grid gap-4 sm:grid-cols-2">
      <div
        v-for="item in recommendations"
        :key="item.name"
        class="bg-muted-100 dark:bg-muted-700/60 rounded-lg p-5"
      >
        <div class="flex flex-col py-4">
          <BaseAvatar
            :src="item.src"
            size="lg"
            :badge-src="item.badge"
            class="mx-auto"
          />
          <div class="py-4 text-center">
            <BaseHeading
              tag="h3"
              size="md"
              weight="medium"
            >
              {{ item.name }}
            </BaseHeading>
            <BaseText size="xs" class="text-muted-400 dark:text-muted-300 mb-4">
              <span>{{ item.role }}</span>
            </BaseText>
            <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 max-w-xs mx-auto">
              <span>{{ item.text }}</span>
            </BaseParagraph>
            <div class="mt-4">
              <BaseText size="xs" class="text-primary-500">
                <span>{{ item.date }}</span>
              </BaseText>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
