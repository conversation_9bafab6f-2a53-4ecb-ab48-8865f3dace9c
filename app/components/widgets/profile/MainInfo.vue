<script setup lang="ts">
interface Experience {
  company: string
  position: string
  period: string
  logo?: string
}

interface Language {
  name: string
  level: number
  mastery: string
  icon?: string
}

interface Skill {
  name: string
  level: number
  experience: number
  logo?: string
  icon?: string
}

interface Props {
  bio: string
  experiences?: Experience[]
  languages?: Language[]
  skills?: Skill[]
  rounded?: string
  cardClass?: string
}

withDefaults(defineProps<Props>(), {
  experiences: () => [],
  languages: () => [],
  skills: () => [],
  rounded: 'md',
  cardClass: 'p-4 md:p-8',
})
</script>

<template>
  <BaseCard :rounded="rounded" :class="cardClass">
    <!-- Bio -->
    <div class="border-muted-200 dark:border-muted-800/80 border-b pb-8">
      <div class="mb-4 flex items-center gap-2">
        <h4
          class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
        >
          About me
        </h4>
      </div>
      <div class="relative">
        <BaseParagraph
          size="sm"
          class="text-muted-500 dark:text-muted-400"
        >
          {{ bio }}
        </BaseParagraph>
      </div>
    </div>

    <!-- Experience -->
    <div v-if="experiences.length" class="border-muted-200 dark:border-muted-800/80 border-b py-8">
      <div class="mb-8 flex items-center gap-2">
        <h4
          class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
        >
          Experiences
        </h4>
      </div>
      <div class="grid gap-x-4 gap-y-8 sm:grid-cols-2">
        <div
          v-for="item in experiences"
          :key="item.company"
          class="flex w-full items-center gap-2"
        >
          <img
            v-if="item.logo"
            :src="item.logo"
            :alt="item.company"
            class="border-muted-200 dark:border-muted-600 dark:bg-muted-700 max-w-[50px] rounded-full border bg-white"
          >
          <div
            v-else
            class="border-muted-200 dark:border-muted-600 dark:bg-muted-700 flex size-[50px] items-center justify-center rounded-full border bg-white"
          >
            <Icon name="solar:buildings-2-linear" class="size-6 text-muted-400" />
          </div>
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
            >
              {{ item.company }}
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              <span>{{ item.period }}</span>
            </BaseParagraph>
            <BaseParagraph size="xs" class="text-primary-500">
              <span>{{ item.position }}</span>
            </BaseParagraph>
          </div>
        </div>
      </div>
    </div>

    <!-- Languages -->
    <div v-if="languages.length" class="border-muted-200 dark:border-muted-800/80 border-b py-8">
      <div class="mb-8 flex items-center gap-2">
        <h4
          class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
        >
          Languages
        </h4>
      </div>
      <div class="grid gap-x-4 gap-y-8 sm:grid-cols-2">
        <div
          v-for="item in languages"
          :key="item.name"
          class="flex w-full items-center gap-2"
        >
          <div
            class="border-muted-200 dark:border-muted-600 dark:bg-muted-700 relative flex size-[50px] shrink-0 items-center justify-center rounded-full border bg-white"
          >
            <img
              v-if="item.icon"
              :src="item.icon"
              :alt="item.name"
              class="size-8 rounded-full"
            >
            <Icon
              v-else
              name="solar:chat-dots-linear"
              class="size-6 text-muted-400"
            />
            <BaseProgressCircle
              :size="68"
              :thickness="1.5"
              :model-value="item.level"
              variant="primary"
              class="absolute -start-2.5 -top-2.5"
            />
          </div>
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
            >
              {{ item.name }}
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              <span>{{ item.mastery }}</span>
            </BaseParagraph>
          </div>
        </div>
      </div>
    </div>

    <!-- Skills -->
    <div v-if="skills.length" class="py-8">
      <div class="mb-8 flex items-center gap-2">
        <h4
          class="text-muted-600 dark:text-muted-400 font-sans text-xs font-medium uppercase"
        >
          Skills
        </h4>
      </div>
      <div class="space-y-8">
        <div
          v-for="item in skills"
          :key="item.name"
          class="flex w-full items-center gap-2"
        >
          <div
            class="border-muted-200 dark:border-muted-600 dark:bg-muted-700 relative flex size-[50px] shrink-0 items-center justify-center rounded-full border bg-white"
          >
            <img
              v-if="item.logo"
              :src="item.logo"
              :alt="item.name"
              class="size-8 rounded-full"
            >
            <Icon
              v-else
              :name="item.icon || 'solar:code-linear'"
              class="text-muted-500 dark:text-muted-400 size-6"
            />
            <BaseProgressCircle
              :size="68"
              :thickness="1.5"
              :model-value="item.level"
              variant="primary"
              class="absolute -start-2.5 -top-2.5"
            />
          </div>
          <div>
            <BaseHeading
              tag="h3"
              size="sm"
              weight="medium"
            >
              {{ item.name }}
            </BaseHeading>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              <span>{{ item.experience }} years of experience</span>
            </BaseParagraph>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
