<script setup lang="ts">
import type { RecipientMetricCardData } from '~/types/widgets'

export interface Props {
  data: RecipientMetricCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard
    rounded="md"
    shadow="hover"
    class="flex h-full flex-col items-center justify-center p-6"
  >
    <div class="text-center">
      <!-- Payment method icons -->
      <div v-if="data.type === 'payment-method'">
        <Icon
          v-if="data.paymentMethod === 'paypal'"
          name="logos:paypal"
          class="mb-4 text-4xl mx-auto"
        />
        <Icon
          v-else-if="data.paymentMethod === 'stripe'"
          name="bi:stripe"
          class="mb-4 text-4xl mx-auto text-indigo-600"
        />
      </div>

      <div>
        <!-- Payment method title -->
        <BaseHeading
          v-if="data.type === 'payment-method'"
          weight="medium"
          size="sm"
          lead="none"
          class="line-clamp-1 capitalize text-muted-900 dark:text-white"
        >
          {{ data.title }}
        </BaseHeading>

        <!-- Amount display -->
        <BaseHeading
          v-else-if="data.type === 'amount'"
          weight="medium"
          size="2xl"
          lead="none"
          class="mb-3"
        >
          {{ data.formattedAmount }}
        </BaseHeading>

        <!-- Regular title -->
        <BaseHeading
          v-else
          weight="medium"
          size="lg"
          lead="none"
          class="mb-3"
        >
          {{ data.title }}
        </BaseHeading>

        <BaseParagraph size="xs" class="text-muted-400 line-clamp-1">
          {{ data.subtitle }}
        </BaseParagraph>
      </div>
    </div>
  </BaseCard>
</template>
