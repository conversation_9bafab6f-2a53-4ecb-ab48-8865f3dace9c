<script setup lang="ts">
import type { RecipientDetailsCardData } from '~/types/widgets'

export interface Props {
  data: RecipientDetailsCardData
  notifications?: boolean
  onNotificationsToggle?: (value: boolean) => void
}

defineProps<Props>()
defineEmits<{
  'update:notifications': [value: boolean]
}>()
</script>

<template>
  <BaseCard
    rounded="md"
    shadow="hover"
    class="p-6"
  >
    <div class="mb-6">
      <BaseHeading
        weight="medium"
        size="lg"
        lead="none"
        class="line-clamp-1"
      >
        {{ data.title || 'Additional info' }}
      </BaseHeading>
      <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 line-clamp-1">
        {{ data.subtitle || 'More details about this contact' }}
      </BaseParagraph>
    </div>

    <div class="mb-6 space-y-4">
      <div
        v-for="field in data.fields"
        :key="field.label"
        class="flex items-center justify-between"
      >
        <div class="text-muted-600 dark:text-muted-400 flex items-center gap-1">
          <Icon :name="field.icon" class="size-5" />
          <BaseParagraph size="sm">
            {{ field.label }}
          </BaseParagraph>
        </div>

        <!-- Tag for status field -->
        <BaseTag
          v-if="field.type === 'status'"
          rounded="full"
          :variant="field.value === 'active' ? 'primary' : 'muted'"
          size="sm"
        >
          {{ field.value }}
        </BaseTag>

        <!-- Regular text for other fields -->
        <BaseParagraph
          v-else
          size="sm"
          weight="medium"
          class="text-muted-800 dark:text-muted-100 line-clamp-1"
          :class="{ capitalize: field.type === 'language' }"
        >
          {{ field.value }}
        </BaseParagraph>
      </div>
    </div>

    <!-- Notifications toggle -->
    <div
      v-if="data.showNotifications"
      class="border-muted-200 dark:border-muted-800 flex items-center justify-between border-t pt-4"
    >
      <BaseText
        size="sm"
        class="text-muted-600 dark:text-muted-400"
      >
        Notifications
      </BaseText>
      <div>
        <BaseSwitchBall
          :model-value="notifications"
          variant="primary"
          @update:model-value="$emit('update:notifications', $event)"
        />
      </div>
    </div>
  </BaseCard>
</template>
