<script setup lang="ts">
import type { RecipientTransactionsCardData } from '~/types/widgets'

export interface Props {
  data: RecipientTransactionsCardData
  onSendMoney?: () => void
}

defineProps<Props>()

function statusColor(itemStatus: string) {
  switch (itemStatus) {
    case 'complete':
      return 'primary'
    case 'in progress':
      return 'dark'
    case 'processing':
      return 'default'
    case 'cancelled':
      return 'muted'
    default:
      return 'default'
  }
}

function getMethodIcon(method: string) {
  switch (method) {
    case 'credit card':
      return 'solar:card-linear'
    case 'cheque':
      return 'solar:document-linear'
    case 'transfer':
      return 'solar:transfer-horizontal-linear'
    default:
      return 'solar:card-linear'
  }
}
</script>

<template>
  <BaseCard
    rounded="md"
    shadow="hover"
    class="p-4 md:p-8"
  >
    <div class="mb-8 flex items-center justify-between">
      <BaseHeading
        as="h4"
        size="xs"
        weight="medium"
        lead="none"
        class="text-muted-600 dark:text-muted-400 uppercase"
      >
        {{ data.title || 'History' }}
      </BaseHeading>
      <div>
        <BaseButton
          rounded="md"
          size="sm"
          @click="onSendMoney"
        >
          {{ data.actionButtonText || 'Send Money' }}
        </BaseButton>
      </div>
    </div>

    <div class="mt-7 overflow-x-auto">
      <!-- Empty state -->
      <div v-if="data.transactions.length === 0">
        <BasePlaceholderPage
          :title="data.emptyTitle || 'Nothing to show'"
          :subtitle="data.emptyMessage || 'Looks like there are no transactions to show for this recipient.'"
        />
      </div>

      <!-- Transactions table -->
      <table v-else class="w-full whitespace-nowrap">
        <thead>
          <tr>
            <th class="w-1/5" />
            <th class="w-2/5" />
            <th />
            <th />
            <th />
            <th />
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="transaction in data.transactions"
            :key="transaction.id"
            tabindex="0"
          >
            <!-- Date -->
            <td class="py-2">
              <BaseText
                size="sm"
                lead="none"
                class="text-muted-600 dark:text-muted-400"
              >
                {{ transaction.date }}
              </BaseText>
            </td>

            <!-- Description -->
            <td class="py-2">
              <BaseText
                size="sm"
                weight="medium"
                lead="none"
                class="text-muted-600 dark:text-muted-300"
              >
                {{ transaction.type === 'in' ? 'Money received' : 'Money sent' }}
              </BaseText>
            </td>

            <!-- Amount -->
            <td class="px-4 py-2">
              <BaseText
                size="sm"
                weight="semibold"
                lead="none"
                class="text-muted-800 dark:text-muted-100"
              >
                {{ transaction.formattedAmount }}
              </BaseText>
            </td>

            <!-- Status -->
            <td class="px-4 py-2">
              <BaseTag
                rounded="full"
                :variant="statusColor(transaction.status)"
                size="sm"
              >
                {{ transaction.status }}
              </BaseTag>
            </td>

            <!-- Method -->
            <td class="px-4 py-2">
              <div class="text-muted-400 flex items-center gap-2">
                <Icon
                  :name="getMethodIcon(transaction.method)"
                  class="size-5"
                />
                <BaseText
                  size="sm"
                  weight="medium"
                  lead="none"
                  class="text-muted-400"
                >
                  {{ transaction.method }}
                </BaseText>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </BaseCard>
</template>
