<script setup lang="ts">
import type { RecipientProfileCardData } from '~/types/widgets'

export interface Props {
  data: RecipientProfileCardData
  onEdit?: () => void
}

defineProps<Props>()
</script>

<template>
  <BaseCard
    rounded="md"
    shadow="hover"
    class="p-6"
  >
    <div
      class="border-muted-200 dark:border-muted-800 mb-6 flex items-center gap-4 border-b pb-8"
    >
      <BaseAvatar
        :src="data.picture"
        :alt="data.name"
        size="md"
        rounded="none"
        mask="blob"
        class="bg-muted-100 dark:bg-muted-900"
      />
      <div>
        <BaseHeading
          weight="medium"
          size="lg"
          lead="none"
          class="line-clamp-1"
        >
          {{ data.name }}
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 line-clamp-1">
          {{ data.category }}
        </BaseParagraph>
      </div>
    </div>

    <div class="mb-6">
      <BaseHeading
        weight="medium"
        size="md"
        lead="none"
        class="mb-2"
      >
        Address
      </BaseHeading>
      <BaseParagraph
        v-for="item in data.address"
        :key="item"
        size="xs"
        class="text-muted-500 dark:text-muted-400"
      >
        {{ item }}
      </BaseParagraph>
      <BaseParagraph
        size="xs"
        class="text-muted-500 dark:text-muted-400"
      >
        {{ data.country }}
      </BaseParagraph>
    </div>

    <div>
      <BaseButton
        rounded="md"
        variant="primary"
        class="w-full"
        @click="onEdit"
      >
        {{ data.editButtonText || 'Edit Contact' }}
      </BaseButton>
    </div>
  </BaseCard>
</template>
