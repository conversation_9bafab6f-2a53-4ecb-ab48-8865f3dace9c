<script setup lang="ts">
import type { PersonalMetricCardData } from '~/types/widgets'

interface Props {
  data: PersonalMetricCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="flex items-center gap-2 p-3">
    <BaseIconBox
      size="sm"
      :class="data.iconClass"
      rounded="full"
      variant="none"
    >
      <Icon :name="data.icon" class="size-6" />
    </BaseIconBox>
    <div>
      <BaseHeading
        as="h2"
        size="sm"
        weight="semibold"
        lead="tight"
        class="text-muted-800 dark:text-white"
      >
        <span>{{ data.value }}</span>
      </BaseHeading>
      <BaseParagraph size="xs">
        <span class="text-muted-500 dark:text-muted-400">{{ data.label }}</span>
      </BaseParagraph>
    </div>
  </BaseCard>
</template>
