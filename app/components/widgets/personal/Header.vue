<script setup lang="ts">
import type { PersonalHeaderData } from '~/types/widgets'

interface Props {
  data: PersonalHeaderData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <div class="flex flex-col items-center md:flex-row">
      <div
        class="lg:landscape:flex-row lg:landscape:items-center flex flex-col items-center gap-4 text-center md:items-start md:text-start xl:landscape::flex-row xl:landscape::items-center"
      >
        <BaseAvatar
          :src="data.avatarSrc"
          size="xl"
          :badge-src="data.badgeSrc"
        />
        <div class="text-center md:text-start">
          <BaseHeading
            as="h2"
            size="xl"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ data.greeting }}, {{ data.displayName }}</span>
          </BaseHeading>
          <BaseParagraph>
            <span class="text-muted-600 dark:text-muted-400">{{ data.subtitle }}</span>
          </BaseParagraph>
        </div>
      </div>

      <div
        class="w-full md:w-auto lg:landscape:flex-row lg:landscape:items-center md:ms-auto flex flex-col gap-6 text-center md:text-start xl:landscape:flex-row xl:landscape:items-center"
      >
        <!-- Metric Section -->
        <div v-if="data.metric" class="flex-1">
          <BaseHeading
            as="h3"
            size="3xl"
            weight="semibold"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>
              {{ data.metric.value }}
              <small class="text-base font-medium">{{ data.metric.label }}</small>
            </span>
          </BaseHeading>
          <BaseParagraph>
            <span class="text-muted-600 dark:text-muted-400 text-sm">
              {{ data.metric.description }}
            </span>
          </BaseParagraph>
        </div>

        <!-- Promotional Card Section -->
        <BaseCard
          v-if="data.promotionalCard"
          variant="none"
          rounded="md"
          class="shadow-primary-500/20 relative flex flex-1 items-center justify-center shadow-xl p-8 md:p-5" :class="[
            data.promotionalCard.gradient || 'bg-gradient-to-br from-primary-900 to-primary-800',
          ]"
        >
          <div class="relative z-20 flex flex-col gap-3">
            <BaseParagraph size="sm">
              <span :class="data.promotionalCard.textClass || 'text-primary-50'">
                {{ data.promotionalCard.text }}
              </span>
            </BaseParagraph>
            <NuxtLink
              v-if="data.promotionalCard.link"
              :to="data.promotionalCard.link.href"
              :class="data.promotionalCard.linkClass || 'font-sans text-sm text-white underline-offset-4 hover:underline'"
            >
              {{ data.promotionalCard.link.text }}
            </NuxtLink>
          </div>
          <div
            v-if="data.promotionalCard.icon"
            class="absolute bottom-0 end-2 z-10 flex size-14 items-center justify-center"
          >
            <Icon
              :name="data.promotionalCard.icon.name"
              :class="data.promotionalCard.icon.class || 'text-primary-400/50 size-10'"
            />
          </div>
        </BaseCard>
      </div>
    </div>
  </BaseCard>
</template>
