<script setup lang="ts">
import type { InterviewCardData } from '~/types/widgets'

interface Props {
  data: InterviewCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="flex items-center gap-3 p-4">
    <BaseAvatar :src="data.avatar" size="sm" />
    <div>
      <BaseHeading
        as="h4"
        size="sm"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.name }}</span>
      </BaseHeading>
      <BaseParagraph size="xs">
        <span class="text-muted-600 dark:text-muted-400">{{ data.timeSlot }}</span>
      </BaseParagraph>
    </div>
    <div class="ms-auto flex items-center">
      <BaseButton
        rounded="lg"
        variant="muted"
        size="icon-md"
        class="scale-75"
      >
        <Icon name="lucide:arrow-right" class="size-5" />
      </BaseButton>
    </div>
  </BaseCard>
</template>
