<script setup lang="ts">
import type { InterviewWelcomeData } from '~/types/widgets'

interface Props {
  data: InterviewWelcomeData
}

defineProps<Props>()
</script>

<template>
  <BaseCard
    variant="none"
    class="bg-muted-200 dark:bg-muted-950/60 flex h-full flex-col border-0 p-8"
    rounded="lg"
  >
    <div class="mb-5">
      <BaseHeading
        as="h2"
        size="3xl"
        weight="medium"
        lead="tight"
        class="text-muted-900 mb-2 dark:text-white"
      >
        <span>Hi, {{ data.displayName }}</span>
      </BaseHeading>
      <BaseParagraph size="sm">
        <span class="text-muted-600 dark:text-muted-400">
          {{ data.description }}
        </span>
      </BaseParagraph>
    </div>
    <div class="mb-4 mt-auto flex items-center gap-2">
      <div class="text-4xl">
        <span>{{ data.emoji }}</span>
      </div>
      <div>
        <BaseParagraph size="xs">
          <span class="text-muted-600 dark:text-muted-400 mb-2">{{ data.progressLabel }}</span>
        </BaseParagraph>
        <BaseHeading
          as="h4"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ data.progressStatus }}</span>
        </BaseHeading>
      </div>
    </div>
    <div>
      <BaseButton
        variant="primary"
        rounded="lg"
        class="h-11 w-full"
      >
        <span>{{ data.ctaText }}</span>
      </BaseButton>
    </div>
  </BaseCard>
</template>
