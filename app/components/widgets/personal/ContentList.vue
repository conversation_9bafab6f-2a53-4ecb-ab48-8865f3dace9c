<script setup lang="ts">
import type { PersonalContentListData } from '~/types/widgets'

interface Props {
  data: PersonalContentListData
}

const props = defineProps<Props>()
</script>

<template>
  <BaseCard :rounded="data.rounded || 'md'" :class="data.cardClass || 'p-4 md:p-6'">
    <!-- Header -->
    <div class="mb-8 flex items-center justify-between">
      <BaseHeading
        as="h3"
        :size="data.titleSize || 'md'"
        weight="semibold"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseText
        v-if="data.viewAllLink"
        size="sm"
      >
        <BaseLink :to="data.viewAllLink.href" class="not-hover:text-muted-400">
          {{ data.viewAllLink.text || 'View all' }}
        </BaseLink>
      </BaseText>
    </div>

    <!-- Content -->
    <div :class="data.contentClass || 'pb-2'">
      <component
        :is="data.contentComponent"
        v-bind="data.contentProps"
      />
    </div>
  </BaseCard>
</template>
