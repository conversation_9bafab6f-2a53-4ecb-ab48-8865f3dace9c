<script setup lang="ts">
interface AchievementData {
  title: string
  subtitle: string
  buttonText: string
  buttonLink?: string
  image: string
  badgeSmall: string
  badgeMedium: string
  count?: number
}

withDefaults(defineProps<{
  achievement?: AchievementData
}>(), {
  achievement: () => ({
    title: 'You\'ve unlocked 2 new Achievements',
    subtitle: 'Congrats, your efforts have been rewarded. Keep up like this!',
    buttonText: 'View Achievements',
    buttonLink: '#',
    image: '/img/illustrations/widgets/1.svg',
    badgeSmall: '/img/illustrations/widgets/3.svg',
    badgeMedium: '/img/illustrations/widgets/2.svg',
    count: 2,
  }),
})
</script>

<template>
  <BaseCard rounded="md" class="p-6 h-full">
    <InfoBadges
      class="h-full"
      :image="achievement.image"
      :badge-small="achievement.badgeSmall"
      :badge-medium="achievement.badgeMedium"
    >
      <div class="text-start">
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-800 mb-1 dark:text-white"
        >
          <span>{{ achievement.title }}</span>
        </BaseHeading>
        <BaseParagraph size="sm">
          <span class="text-muted-600 dark:text-muted-400">{{ achievement.subtitle }}</span>
        </BaseParagraph>
        <div class="mt-4">
          <BaseButton rounded="md" class="w-full">
            <span>{{ achievement.buttonText }}</span>
          </BaseButton>
        </div>
      </div>
    </InfoBadges>
  </BaseCard>
</template>
