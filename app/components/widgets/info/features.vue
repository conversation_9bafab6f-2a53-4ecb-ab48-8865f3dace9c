<script setup lang="ts">
interface Feature {
  title: string
  description: string
  image: string
  linkUrl?: string
  linkText?: string
}

interface FeaturesData {
  title?: string
  subtitle?: string
  features: Feature[]
}

withDefaults(defineProps<{
  data?: FeaturesData
}>(), {
  data: () => ({
    title: 'New Features',
    subtitle: 'Some nice features we\'ve just released',
    features: [
      {
        title: 'Recurring payments',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Bork Idem adhuc; Igitur neque stultorum quisquam beatus.',
        image: '/img/illustrations/ui/recurring.svg',
        linkUrl: '#',
        linkText: 'Learn more about it',
      },
      {
        title: 'Credit balance',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Bork Idem adhuc; Igitur neque stultorum quisquam beatus.',
        image: '/img/illustrations/ui/cashback.svg',
        linkUrl: '#',
        linkText: 'Learn more about it',
      },
    ],
  }),
})
</script>

<template>
  <div
    class="bg-muted-200 dark:bg-muted-950/50 rounded-xl p-4 sm:p-6"
  >
    <div class="mb-6 flex items-center justify-between">
      <div>
        <BaseHeading
          as="h3"
          weight="medium"
          size="xl"
          class="text-muted-900 dark:text-muted-100"
        >
          {{ data.title }}
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          {{ data.subtitle }}
        </BaseParagraph>
      </div>
      <div>
        <slot name="actions" />
      </div>
    </div>
    <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
      <div v-for="feature in data.features" :key="feature.title" class="relative">
        <BaseCard
          rounded="md"
          class="flex flex-col gap-4 p-4 sm:flex-row sm:items-center"
        >
          <div
            class="bg-muted-100 dark:bg-muted-900 flex w-full shrink-0 items-center justify-center rounded-xl sm:size-32"
          >
            <img
              :src="feature.image"
              class="w-full"
              :alt="feature.title"
            >
          </div>
          <div class="flex flex-col">
            <BaseHeading
              as="h3"
              weight="medium"
              size="md"
              class="text-muted-800 dark:text-muted-100 mb-2"
            >
              {{ feature.title }}
            </BaseHeading>
            <BaseParagraph
              size="xs"
              class="text-muted-500 dark:text-muted-400 mb-4"
            >
              {{ feature.description }}
            </BaseParagraph>
            <div v-if="feature.linkUrl" class="mb-1 mt-auto">
              <LinkArrow :to="feature.linkUrl" :label="feature.linkText || 'Learn more'" />
            </div>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>
