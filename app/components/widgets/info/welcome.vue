<script setup lang="ts">
interface WelcomeConfig {
  accountTitle?: string
  greeting?: string
  description?: string
  buttonText?: string
  buttonAction?: string
  userName?: string
}

const props = withDefaults(defineProps<{
  config?: WelcomeConfig
}>(), {
  config: () => ({
    accountTitle: 'Account',
    greeting: 'Welcome back!',
    description: 'Everything seems ok and up-to-date with your account since your last visit. Would you like to fund it?',
    buttonText: 'Fund my Account',
    buttonAction: 'fund-account',
    userName: 'there',
  }),
})

const emit = defineEmits<{
  action: [type: string]
}>()

function handleAction() {
  emit('action', props.config.buttonAction!)
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="h-full p-10"
  >
    <div class="flex h-full flex-col justify-between gap-5">
      <BaseHeading
        as="h4"
        size="xs"
        weight="medium"
        lead="none"
        class="text-muted-700 dark:text-muted-100 uppercase"
      >
        {{ config.userName }}'s {{ config.accountTitle }}
      </BaseHeading>

      <h2
        class="font-heading text-muted-900 text-4xl font-medium dark:text-white"
      >
        {{ config.greeting }}, {{ config.userName }}! 👋
      </h2>
      <BaseParagraph class="text-muted-600 dark:text-muted-400">
        {{ config.description }}
      </BaseParagraph>
      <BaseButton
        variant="primary"
        rounded="md"
        size="lg"
        class="w-full"
        @click="handleAction"
      >
        {{ config.buttonText }}
      </BaseButton>
    </div>
  </BaseCard>
</template>
