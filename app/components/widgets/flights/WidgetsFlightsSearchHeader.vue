<script setup lang="ts">
import type { FlightSearchHeaderData } from '~/types/widgets'
import type { Duration } from '~/utils/bundles/date-fns'
import { format, isSameDay, sub } from '~/utils/bundles/date-fns'

defineProps<{
  data: FlightSearchHeaderData
}>()

// Date range picker logic
const ranges = [
  { label: 'Last 7 days', duration: { days: 7 } },
  { label: 'Last 14 days', duration: { days: 14 } },
  { label: 'Last 30 days', duration: { days: 30 } },
  { label: 'Last 3 months', duration: { months: 3 } },
  { label: 'Last 6 months', duration: { months: 6 } },
  { label: 'Last year', duration: { years: 1 } },
]

const selected = ref({
  start: sub(new Date(), { days: 14 }),
  end: new Date(),
})

function isRangeSelected(duration: Duration) {
  return isSameDay(selected.value.start, sub(new Date(), duration)) && isSameDay(selected.value.end, new Date())
}

function selectRange(duration: Duration) {
  selected.value = { start: sub(new Date(), duration), end: new Date() }
}
</script>

<template>
  <div
    class="bg-primary-800 flex flex-col items-stretch rounded-lg p-8 sm:flex-row"
  >
    <div class="w-full sm:w-3/5">
      <div class="flex w-full flex-col gap-4">
        <div class="flex flex-col gap-4 sm:flex-row sm:items-center">
          <Icon :name="data.icon" class="size-9 text-white" />
          <div>
            <BaseHeading
              as="h2"
              weight="medium"
              size="xl"
              class="text-white"
            >
              <span>{{ data.route }}</span>
            </BaseHeading>
            <BaseParagraph size="sm" class="text-white">
              <span>{{ data.subtitle }}</span>
            </BaseParagraph>
          </div>
        </div>
        <BaseDropdown>
          <template #button>
            <BaseButton rounded="md" class="w-68">
              <Icon name="solar:calendar-linear" class="size-5 scale-95 me-1" />
              <span>{{ format(selected.start, 'd MMM, yyy') }} - {{ format(selected.end, 'd MMM, yyy') }}</span>
              <Icon name="lucide:chevron-down" class="size-4 transition-transform duration-300 in-data-[state=open]:rotate-180" />
            </BaseButton>
          </template>
          <div class="flex items-center">
            <div class="hidden sm:flex flex-col gap-1 py-4 px-2 shrink-0 sm:border-e border-muted-200 dark:border-muted-800">
              <button
                v-for="(range, index) in ranges"
                :key="index"
                type="button"
                class="text-start font-sans text-sm whitespace-nowrap rounded-md py-1 px-4"
                :class="[isRangeSelected(range.duration) ? 'bg-muted-100 text-muted-900' : 'hover:bg-muted-50 text-muted-600']"
                truncate
                @click="selectRange(range.duration)"
              >
                {{ range.label }}
              </button>
            </div>

            <LazyAddonDatepicker v-model="selected" locale="en" />
          </div>
        </BaseDropdown>
      </div>
    </div>
    <div class="xs:min-h-[190px] relative w-full hidden sm:block sm:w-2/5">
      <img
        class="xs:mx-auto absolute -bottom-12 w-full max-w-[380px] sm:end-0"
        :src="data.illustration"
        :alt="data.illustrationAlt"
      >
    </div>
  </div>
</template>
