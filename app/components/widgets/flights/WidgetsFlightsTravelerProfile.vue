<script setup lang="ts">
import type { FlightTravelerProfileData } from '~/types/widgets'

defineProps<{
  data: FlightTravelerProfileData
}>()
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <div class="flex flex-col items-center w-full gap-4">
      <BaseAvatar
        :src="data.avatar"
        size="lg"
      />
      <div class="text-center">
        <BaseHeading
          as="h4"
          weight="medium"
          size="md"
          class="text-muted-900 dark:text-muted-100"
        >
          {{ data.name }}
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400">
          {{ data.subtitle }}
        </BaseParagraph>
        <div class="pt-4 pb-2 flex items-center justify-center w-full divide-x divide-muted-200 dark:divide-muted-800">
          <div
            v-for="(stat, index) in data.stats"
            :key="index"
            class="flex flex-col gap-1 flex-1 px-4 2xl:px-6"
          >
            <BaseText size="xs" weight="medium" class="uppercase text-muted-600 dark:text-muted-400">
              {{ stat.label }}
            </BaseText>
            <BaseText size="md" weight="semibold" class="text-muted-900 dark:text-muted-100">
              {{ stat.value }}
            </BaseText>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
