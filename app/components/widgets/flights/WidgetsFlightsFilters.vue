<script setup lang="ts">
import type { FlightFiltersData } from '~/types/widgets'

defineProps<{
  data: FlightFiltersData
}>()

// Reactive filter state
const selectedFilters = ref<string[]>([])
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <BaseButton class="w-full" rounded="md" variant="primary">
      <span>{{ data.ctaText }}</span>
    </BaseButton>
    <div class="mt-6">
      <div class="space-y-10">
        <div
          v-for="(section, sectionIndex) in data.sections"
          :key="sectionIndex"
        >
          <h5
            class="text-muted-500 dark:text-muted-400 mb-4 font-sans text-xs font-semibold uppercase"
          >
            {{ section.title }}
          </h5>
          <BaseCheckboxGroup v-model="selectedFilters" class="flex flex-col gap-4">
            <BaseCheckbox
              v-for="(option, optionIndex) in section.options"
              :key="optionIndex"
              :value="option.value"
              :label="option.label"
            />
          </BaseCheckboxGroup>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
