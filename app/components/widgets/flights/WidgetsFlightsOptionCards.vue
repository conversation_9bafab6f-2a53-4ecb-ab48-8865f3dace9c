<script setup lang="ts">
import type { FlightOptionCardData } from '~/types/widgets'

defineProps<{
  options: FlightOptionCardData[]
}>()
</script>

<template>
  <div class="grid gap-4 sm:grid-cols-3">
    <BaseCard
      v-for="(option, index) in options"
      :key="index"
      :variant="option.isPrimary ? 'none' : 'default'"
      rounded="md"
      :class="option.isPrimary
        ? 'bg-primary-600 dark:bg-primary-600 p-4 md:p-6'
        : 'p-4 md:p-6'"
    >
      <div class="flex w-full items-center gap-3">
        <span
          :class="option.isPrimary
            ? 'block font-sans text-3xl font-semibold text-white'
            : 'text-muted-900 dark:text-muted-100 block font-sans text-3xl font-semibold'"
        >
          {{ option.price }}
        </span>
        <div>
          <span
            :class="option.isPrimary
              ? 'text-muted-200 block font-sans text-[0.65rem] font-medium uppercase leading-snug'
              : 'text-muted-600 dark:text-muted-400 block font-sans text-[0.65rem] font-medium uppercase leading-snug'"
          >
            {{ option.label }}
          </span>
          <span
            :class="option.isPrimary
              ? 'text-muted-100 block font-sans text-xs leading-none'
              : 'text-muted-900 dark:text-muted-100 block font-sans text-xs leading-none'"
          >
            {{ option.duration }}
          </span>
        </div>
      </div>
    </BaseCard>
  </div>
</template>
