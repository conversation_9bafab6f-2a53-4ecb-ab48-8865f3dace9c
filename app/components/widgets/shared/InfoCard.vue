<script setup lang="ts">
import type { InfoCardData } from '~/types/widgets'

interface Props {
  data: InfoCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="relative flex flex-col gap-4 sm:flex-row">
    <div class="flex flex-col p-6">
      <div
        class="dark:bg-muted-900 border-muted-200 dark:border-muted-800 mb-4 flex size-14 items-center justify-center rounded-full border bg-white"
      >
        <div
          class="dark:bg-muted-900 border-muted-200 dark:border-muted-800 flex size-10 items-center justify-center rounded-full border bg-white"
        >
          <Icon :name="data.icon" class="text-primary-500 size-6" />
        </div>
      </div>
      <BaseHeading
        as="h3"
        weight="medium"
        size="lg"
        class="text-muted-900 dark:text-muted-100 mb-2"
      >
        {{ data.title }}
      </BaseHeading>
      <BaseParagraph
        size="sm"
        class="text-muted-500 dark:text-muted-400 mb-4"
      >
        {{ data.description }}
      </BaseParagraph>
      <div class="mb-1 mt-auto">
        <LinkArrow :to="data.link" :label="data.linkLabel" />
      </div>
    </div>
  </BaseCard>
</template>
