<script setup lang="ts">
import type { CreativeInfoBadgesData } from '~/types/widgets'

interface Props {
  data: CreativeInfoBadgesData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-6">
    <InfoBadges
      :image="data.image"
      :badge-small="data.badgeSmall"
      :badge-medium="data.badgeMedium"
      :title="data.title"
      :text="data.text"
    />
  </BaseCard>
</template>
