<script setup lang="ts">
import type { CreativeGenericData } from '~/types/widgets'

interface Props {
  data: CreativeGenericData
}

defineProps<Props>()
</script>

<template>
  <BaseCard :rounded="data.rounded || 'lg'" :class="data.cardClass || 'p-4'">
    <component
      :is="data.contentComponent"
      v-bind="data.contentProps"
      :rounded="data.contentRounded"
    />
  </BaseCard>
</template>
