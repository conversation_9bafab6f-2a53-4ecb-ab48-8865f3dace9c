<script setup lang="ts">
import type { CreativeTeamListData } from '~/types/widgets'

interface Props {
  data: CreativeTeamListData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-5">
    <!-- Title -->
    <div class="mb-8 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseButton
        size="sm"
        rounded="md"
      >
        {{ data.actionLabel || 'View all' }}
      </BaseButton>
    </div>
    <component :is="data.contentComponent" />
  </BaseCard>
</template>
