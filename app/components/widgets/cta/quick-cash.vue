<script setup lang="ts">
withDefaults(defineProps<{
  title?: string
  description?: string
  icon?: string
  linkLabel?: string
  linkTo?: string
}>(), {
  title: 'Try Quick Cash',
  description: 'Quick Cash is a new feature that allows you to send money to your friends and family in a matter of seconds.',
  icon: 'solar:bag-smile-bold-duotone',
  linkLabel: 'Learn more about it',
  linkTo: '#',
})
</script>

<template>
  <BaseCard rounded="md" class="relative flex flex-col gap-4 sm:flex-row">
    <div class="flex flex-col p-6">
      <div class="dark:bg-muted-900 border-muted-200 dark:border-muted-800 mb-4 flex size-14 items-center justify-center rounded-full border bg-white">
        <div class="dark:bg-muted-900 border-muted-200 dark:border-muted-800 flex size-10 items-center justify-center rounded-full border bg-white">
          <Icon :name="icon" class="text-primary-500 size-6" />
        </div>
      </div>
      <BaseHeading as="h3" weight="medium" size="lg" class="text-muted-900 dark:text-muted-100 mb-2">
        {{ title }}
      </BaseHeading>
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-4">
        {{ description }}
      </BaseParagraph>
      <div class="mb-1 mt-auto">
        <LinkArrow :to="linkTo" :label="linkLabel" />
      </div>
    </div>
  </BaseCard>
</template>
