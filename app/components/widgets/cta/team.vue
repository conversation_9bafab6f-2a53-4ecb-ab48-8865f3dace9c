<script setup lang="ts">
interface CtaData {
  title: string
  subtitle: string
  linkText: string
  linkUrl: string
  iconName: string
  gradientFrom?: string
  gradientTo?: string
}

withDefaults(defineProps<{
  cta?: CtaData
}>(), {
  cta: () => ({
    title: 'You\'re doing great!',
    subtitle: 'Start using our team and project management tools',
    linkText: 'Learn More',
    linkUrl: '#',
    iconName: 'ph:crown-duotone',
    gradientFrom: 'from-primary-900',
    gradientTo: 'to-primary-800',
  }),
})
</script>

<template>
  <BaseCard
    variant="none"
    rounded="md"
    :class="`${cta.gradientFrom} ${cta.gradientTo} relative flex h-full items-center justify-center bg-gradient-to-br p-6`"
  >
    <div class="relative z-20 flex flex-col gap-3 py-10 text-center">
      <BaseHeading
        as="h4"
        size="lg"
        weight="semibold"
        lead="tight"
        class="text-white"
      >
        <span>{{ cta.title }}</span>
      </BaseHeading>
      <BaseParagraph size="md" class="mx-auto max-w-[280px]">
        <span class="text-white/80">
          {{ cta.subtitle }}
        </span>
      </BaseParagraph>
      <NuxtLink
        class="font-sans text-sm text-white underline-offset-4 hover:underline"
        :to="cta.linkUrl"
      >
        {{ cta.linkText }}
      </NuxtLink>
    </div>
    <div
      class="absolute bottom-4 end-4 z-10 flex size-14 items-center justify-center"
    >
      <Icon
        :name="cta.iconName"
        class="text-primary-600/50 size-14"
      />
    </div>
  </BaseCard>
</template>
