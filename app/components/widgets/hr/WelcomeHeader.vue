<script setup lang="ts">
import type { HRWelcomeHeaderData } from '~/types/widgets'

interface Props {
  data: HRWelcomeHeaderData
}

defineProps<Props>()
</script>

<template>
  <div class="bg-primary-800 rounded-2xl px-6 py-12">
    <div class="flex w-full flex-col items-center gap-y-4 sm:flex-row">
      <div class="flex flex-1 flex-col gap-y-2 px-4">
        <BaseAvatar
          :src="data.avatar"
          size="lg"
          class="border-primary-200/50 ring-primary-200/50 ring-offset-primary-600 mb-3 border ring-2 ring-offset-4"
        />
        <BaseHeading
          as="h2"
          size="2xl"
          weight="semibold"
          lead="none"
          class="text-white"
        >
          <span>{{ data.greeting }}</span>
        </BaseHeading>
      </div>
      <div class="flex h-full flex-1 flex-col px-4 sm:px-6">
        <BaseHeading
          as="h2"
          size="md"
          weight="semibold"
          lead="tight"
          class="mb-1 text-white"
        >
          <span>{{ data.newRookies.title }}</span>
        </BaseHeading>
        <BaseParagraph
          size="xs"
          lead="tight"
          class="mb-3"
        >
          <span class="text-white">
            {{ data.newRookies.description }}
          </span>
        </BaseParagraph>
        <div class="mt-auto flex gap-2">
          <BaseAvatar
            v-for="(avatar, index) in data.newRookies.avatars"
            :key="index"
            size="sm"
            rounded="none"
            :src="avatar"
            class="nui-mask nui-mask-blob"
          />
          <BaseButton size="icon-md" rounded="lg">
            <Icon name="lucide:plus" class="size-4" />
          </BaseButton>
        </div>
      </div>
      <div class="border-primary-300/60 flex h-full flex-1 flex-col px-4 sm:border-l sm:px-6">
        <BaseHeading
          as="h2"
          size="md"
          weight="semibold"
          lead="tight"
          class="mb-1 text-white"
        >
          <span>{{ data.jobFeed.title }}</span>
        </BaseHeading>
        <BaseParagraph
          size="xs"
          lead="tight"
          class="mb-3"
        >
          <span class="text-white">
            {{ data.jobFeed.description }}
          </span>
        </BaseParagraph>
        <div class="mt-auto">
          <BaseButton class="w-full">
            <span>{{ data.jobFeed.ctaText }}</span>
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>
