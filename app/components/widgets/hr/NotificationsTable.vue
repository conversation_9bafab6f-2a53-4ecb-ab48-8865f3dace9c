<script setup lang="ts">
import type { HRNotificationsTableData } from '~/types/widgets'

interface Props {
  data: HRNotificationsTableData
}

defineProps<Props>()
</script>

<template>
  <div class="mt-6 grid grid-cols-12 gap-x-6 gap-y-12">
    <!-- Text -->
    <div class="col-span-12 sm:col-span-5">
      <div class="flex h-full max-w-[240px] flex-col justify-center gap-2">
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-800 dark:text-muted-100 mb-1"
        >
          <span>{{ data.title }}</span>
        </BaseHeading>
        <BaseParagraph size="xs" lead="tight">
          <span class="text-muted-500 dark:text-muted-400">
            {{ data.description }}
          </span>
        </BaseParagraph>
        <NuxtLink
          v-if="data.learnMoreLink"
          :to="data.learnMoreLink"
          class="text-primary-600 dark:text-primary-400 font-sans font-medium text-xs underline underline-offset-4"
        >
          <span>Learn More</span>
        </NuxtLink>
      </div>
    </div>
    <!-- Table -->
    <div class="col-span-12 sm:col-span-7">
      <div class="space-y-2">
        <FlexTableRow
          v-for="(item, index) in data.items"
          :key="index"
          rounded="sm"
        >
          <template #start>
            <FlexTableStart
              label="type"
              :hide-label="index > 0"
              :title="item.title"
              :subtitle="item.subtitle"
              :icon="item.icon"
            />
          </template>
          <template #end>
            <FlexTableCell
              label="count"
              :hide-label="index > 0"
              class="w-full sm:w-10"
            >
              <span class="text-muted-500 dark:text-muted-400 font-sans text-sm">
                {{ item.count }}
              </span>
            </FlexTableCell>
            <FlexTableCell
              label="status"
              :hide-label="index > 0"
              class="w-full sm:w-16"
            >
              <BaseTag
                :variant="item.statusVariant"
                rounded="full"
                size="sm"
              >
                {{ item.status }}
              </BaseTag>
            </FlexTableCell>
            <FlexTableCell label="action" :hide-label="index > 0">
              <a
                href="#"
                class="text-primary-500 font-sans font-medium hover:underline underline-offset-4 text-xs sm:pe-2"
              >
                Action
              </a>
            </FlexTableCell>
          </template>
        </FlexTableRow>
      </div>
    </div>
  </div>
</template>
