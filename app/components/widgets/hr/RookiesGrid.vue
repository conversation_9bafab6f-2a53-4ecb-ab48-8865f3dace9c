<script setup lang="ts">
import type { HRRookiesGridData } from '~/types/widgets'

interface Props {
  data: HRRookiesGridData
}

defineProps<Props>()
</script>

<template>
  <div>
    <!-- Header -->
    <div class="mt-6 flex items-center justify-between gap-6 sm:mt-4">
      <div>
        <BaseHeading
          as="h3"
          size="lg"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-muted-100 mb-1"
        >
          <span>{{ data.title }}</span>
        </BaseHeading>
      </div>
      <div class="flex gap-2 sm:justify-end">
        <NuxtLink
          v-if="data.viewAllLink"
          :to="data.viewAllLink"
          class="hover:bg-muted-200 dark:bg-muted-700 dark:hover:bg-muted-900 text-primary-500 rounded-lg bg-white px-4 py-2 font-sans text-sm font-medium underline-offset-4 transition-colors duration-300 hover:underline"
        >
          View All
        </NuxtLink>
      </div>
    </div>
    <!-- Grid -->
    <div class="grid gap-6 sm:grid-cols-3">
      <!-- Item -->
      <div v-for="rookie in data.rookies" :key="rookie.name" class="relative">
        <BaseCard class="p-6" rounded="lg">
          <div class="flex flex-col">
            <div class="mx-auto mb-4 flex items-center justify-center">
              <BaseAvatar
                size="lg"
                :src="rookie.avatar"
                :badge-src="rookie.stack"
                :alt="rookie.name"
              />
            </div>
            <div class="text-center">
              <BaseHeading
                as="h4"
                size="md"
                weight="medium"
                lead="tight"
                class="text-muted-800 dark:text-muted-100"
              >
                <span>
                  {{ rookie.name }}
                </span>
              </BaseHeading>
              <BaseParagraph size="xs">
                <span class="text-muted-400">
                  {{ rookie.role }}
                </span>
              </BaseParagraph>
              <div class="mt-3">
                <BaseButton
                  rounded="lg"
                  class="w-full"
                >
                  <span>{{ data.ctaText }}</span>
                </BaseButton>
              </div>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>
