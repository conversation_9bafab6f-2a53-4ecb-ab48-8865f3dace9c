<script setup lang="ts">
import type { WriterHeaderData } from '~/types/widgets'

interface Props {
  data: WriterHeaderData
}

defineProps<Props>()
</script>

<template>
  <div class="col-span-12">
    <div
      class="bg-primary-800 flex flex-col items-center rounded-2xl p-4 sm:flex-row"
    >
      <div class="relative h-[150px] w-[320px] shrink-0 sm:h-[175px]">
        <img
          class="pointer-events-none absolute start-6 top-0 sm:-start-10"
          :src="data.illustration"
          :alt="data.illustrationAlt"
        >
      </div>
      <div class="mt-6 grow sm:mt-0">
        <div class="pb-4 text-center sm:pb-0 sm:text-start">
          <BaseHeading tag="h1" size="2xl" class="text-white">
            <span>{{ data.greeting }}</span>
          </BaseHeading>
          <BaseParagraph
            size="sm"
            class="max-w-xs text-white/90"
          >
            <span>{{ data.description }}</span>
          </BaseParagraph>
          <div class="mt-2">
            <BaseButton
              size="sm"
              class="w-full sm:w-auto"
            >
              <Icon :name="data.ctaIcon" class="size-4" />
              <span>{{ data.ctaText }}</span>
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
