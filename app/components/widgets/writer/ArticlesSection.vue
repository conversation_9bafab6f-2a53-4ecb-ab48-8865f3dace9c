<script setup lang="ts">
import type { WriterArticlesSectionData } from '~/types/widgets'

interface Props {
  data: WriterArticlesSectionData
}

const props = defineProps<Props>()

const activeFilter = ref(props.data.defaultFilter || props.data.filters[0]?.value)

const filteredArticles = computed(() => {
  return props.data.articles.filter(article =>
    article.category === activeFilter.value || !activeFilter.value,
  )
})
</script>

<template>
  <div class="bg-muted-200 dark:bg-muted-800/70 rounded-2xl p-6">
    <!-- Title -->
    <div class="mb-8 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ props.data.title }}</span>
      </BaseHeading>
      <div class="flex scale-90 gap-2 sm:justify-end">
        <BaseButton
          v-for="filter in props.data.filters"
          :key="filter.value"
          size="sm"
          :variant="activeFilter === filter.value ? 'primary' : 'default'"
          @click="activeFilter = filter.value"
        >
          {{ filter.label }}
        </BaseButton>
      </div>
    </div>
    <!-- Posts -->
    <div class="grid grid-cols-1 md:portrait:grid-cols-2 lg:grid-cols-2 xl:landscape:grid-cols-1 flex flex-col gap-6">
      <!-- Posts for active filter -->
      <template v-for="article in filteredArticles" :key="article.id">
        <NuxtLink :to="article.link" class="flex flex-col">
          <img
            :src="article.image"
            :alt="article.imageAlt"
            class="bg-muted-200 rounded-xl"
          >
          <BaseCard
            class="shadow-muted-300/30 dark:shadow-muted-900/20 -mt-8 rounded-2xl! p-4 md:p-6 shadow-xl"
          >
            <div class="mb-3">
              <BaseHeading
                as="h4"
                size="md"
                weight="medium"
                lead="tight"
                class="text-muted-900 mb-1 dark:text-white"
              >
                <span>{{ article.title }}</span>
              </BaseHeading>
              <BaseParagraph size="xs">
                <span class="text-muted-600 dark:text-muted-400">{{ article.excerpt }}</span>
              </BaseParagraph>
            </div>
            <div class="flex gap-3">
              <BaseAvatar
                :src="article.author.avatar"
                :text="article.author.initials"
                size="xs"
                class="bg-primary-100 dark:bg-primary-500/20 text-primary-500 shrink-0"
              />
              <div>
                <BaseHeading
                  as="h4"
                  size="xs"
                  weight="medium"
                  lead="tight"
                  class="text-muted-900 dark:text-white"
                >
                  <span>{{ article.author.name }}</span>
                </BaseHeading>
                <BaseParagraph size="xs">
                  <span class="text-muted-600 dark:text-muted-400">{{ article.author.role }}</span>
                </BaseParagraph>
              </div>
            </div>
          </BaseCard>
        </NuxtLink>
      </template>
    </div>
  </div>
</template>
