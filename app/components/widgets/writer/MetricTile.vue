<script setup lang="ts">
import type { WriterMetricTileData } from '~/types/widgets'

interface Props {
  data: WriterMetricTileData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-4">
    <BaseParagraph
      size="xs"
      weight="medium"
      class="text-muted-400 uppercase"
    >
      <span>{{ data.label }}</span>
    </BaseParagraph>
    <span
      class="text-muted-900 dark:text-muted-100 block font-sans text-2xl font-bold"
    >
      {{ data.value }}
    </span>
  </BaseCard>
</template>
