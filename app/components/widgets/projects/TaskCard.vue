<script setup lang="ts">
import type { ProjectTaskCardData } from '~/types/widgets'

export interface Props {
  data: ProjectTaskCardData
  onClick?: () => void
}

defineProps<Props>()

function getStatusIcon(status: number) {
  switch (status) {
    case 0:
      return 'solar:add-circle-bold-duotone'
    case 1:
      return 'solar:stopwatch-bold-duotone'
    case 2:
    case 3:
      return 'solar:shield-warning-bold-duotone'
    case 5:
      return 'solar:check-circle-bold-duotone'
    default:
      return 'solar:add-circle-bold-duotone'
  }
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="hover:border-primary-500! flex cursor-pointer flex-col"
    @click="onClick"
  >
    <div class="flex flex-col items-center p-5 sm:flex-row text-start">
      <div class="flex flex-col gap-3 sm:flex-row">
        <Icon
          :name="getStatusIcon(data.status)"
          class="text-muted-400 size-6 shrink-0"
        />
        <div class="leading-none">
          <h4
            class="text-muted-800 dark:text-muted-100 mb-2 font-sans text-base font-medium leading-tight"
          >
            {{ data.name }}
          </h4>
          <p class="text-muted-400 line-clamp-2 font-sans text-xs">
            {{ data.description }}
          </p>
        </div>
      </div>
    </div>

    <div
      class="bg-muted-50 dark:bg-muted-900/50 mt-auto flex items-center justify-between rounded-b-lg px-5 py-3"
    >
      <div class="flex max-w-[180px] grow items-center gap-2">
        <BaseTooltip :content="data.assignee.tooltip">
          <BaseAvatar
            size="xxs"
            :src="data.assignee.src"
          />
        </BaseTooltip>
        <BaseProgress
          :model-value="data.completion"
          size="xs"
          variant="primary"
        />
      </div>
      <div class="text-muted-400 flex items-center gap-4">
        <div class="flex items-center gap-1 text-sm">
          <Icon name="solar:paperclip-linear" class="size-4" />
          <span class="font-sans">
            {{ data.filesCount }}
          </span>
        </div>
        <div class="flex items-center gap-1 text-sm">
          <Icon name="solar:chat-square-call-linear" class="size-4" />
          <span class="font-sans">
            {{ data.commentsCount }}
          </span>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
