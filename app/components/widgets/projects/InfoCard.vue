<script setup lang="ts">
import type { ProjectInfoCardData } from '~/types/widgets'

export interface Props {
  data: ProjectInfoCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <h4
      class="text-muted-600 dark:text-muted-400 mb-6 font-sans text-xs font-medium uppercase"
    >
      {{ data.title }}
    </h4>

    <!-- Customer type with progress -->
    <div v-if="data.type === 'customer'">
      <div class="mb-4 flex items-center gap-2">
        <BaseTooltip :content="data.customer!.name">
          <BaseAvatar
            :src="data.customer!.logo"
            size="md"
            class="bg-muted-100 dark:bg-muted-700"
          />
        </BaseTooltip>
        <div>
          <BaseHeading
            tag="h5"
            size="lg"
            weight="medium"
            lead="none"
            class="line-clamp-1 text-muted-900 dark:text-white"
          >
            {{ data.customer!.name }}
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
            {{ data.customer!.text }}
          </BaseParagraph>
        </div>
      </div>

      <!-- Progress bar -->
      <div v-if="data.customer!.progress !== undefined" class="w-full space-y-1">
        <div class="flex items-center justify-between">
          <h4
            class="text-muted-700 dark:text-muted-100 font-sans text-sm font-medium"
          >
            Progress
          </h4>
          <div>
            <span class="text-muted-400 font-sans text-sm">
              {{ data.customer!.progress }}%
            </span>
          </div>
        </div>
        <BaseProgress
          size="xs"
          variant="primary"
          :model-value="data.customer!.progress"
        />
      </div>
    </div>

    <!-- Tools/Stacks list type -->
    <div v-else-if="data.type === 'list' && data.items" class="space-y-8">
      <div
        v-for="item in data.items"
        :key="item.name"
        class="flex items-center gap-2"
      >
        <BaseTooltip :content="item.name">
          <BaseAvatar
            :src="item.icon"
            size="xs"
            class="bg-muted-100 dark:bg-muted-700"
          />
        </BaseTooltip>
        <div>
          <BaseHeading
            tag="h5"
            size="sm"
            weight="medium"
            lead="none"
            class="line-clamp-1 text-muted-900 dark:text-white"
          >
            {{ item.name }}
          </BaseHeading>
          <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400">
            {{ item.description }}
          </BaseParagraph>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
