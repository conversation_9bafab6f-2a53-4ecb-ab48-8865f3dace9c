<script setup lang="ts">
import type { ProjectTeamCardData } from '~/types/widgets'

export interface Props {
  data: ProjectTeamCardData
  isOwner?: boolean
  href?: string
}

defineProps<Props>()
</script>

<template>
  <BaseCard
    rounded="md"
    class="hover:border-primary-500! relative"
  >
    <NuxtLink :to="href || '#'">
      <Icon
        v-if="isOwner"
        name="uiw:star-on"
        class="text-primary-500 absolute end-6 top-6"
      />
      <div class="flex flex-col items-center p-4 sm:flex-row">
        <div class="flex gap-3">
          <BaseAvatar
            :src="data.avatar"
            :badge-src="data.badge"
            :text="data.name"
            size="md"
            class="bg-muted-500/20 text-muted-500"
          />
          <div class="leading-none">
            <h4
              class="text-muted-900 dark:text-muted-100 font-sans text-base font-medium"
            >
              {{ data.name }}
            </h4>
            <p class="text-muted-600 dark:text-muted-400 mb-2 font-sans text-xs">
              {{ data.role }}
            </p>
            <p class="text-muted-500 dark:text-muted-400 font-sans text-xs">
              {{ data.bio }}
            </p>
          </div>
        </div>
      </div>
    </NuxtLink>
  </BaseCard>
</template>
