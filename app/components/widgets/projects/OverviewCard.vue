<script setup lang="ts">
import type { ProjectOverviewCardData } from '~/types/widgets'

export interface Props {
  data: ProjectOverviewCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="space-y-12 p-10">
    <!-- Project Header -->
    <div
      class="border-muted-200 dark:border-muted-800/80 flex flex-col items-center justify-between gap-8 border-b pb-12 sm:flex-row"
    >
      <div>
        <BaseHeading
          tag="h2"
          size="2xl"
          weight="medium"
          class="text-muted-900 dark:text-white"
        >
          {{ data.name }}
        </BaseHeading>
        <BaseParagraph
          size="lg"
          class="text-muted-600 dark:text-muted-400"
        >
          {{ data.category }}
        </BaseParagraph>
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400 py-4">
          {{ data.description }}
        </BaseParagraph>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <BaseTooltip :content="`${data.owner.name} owns this project`">
              <BaseAvatar :src="data.owner.avatar" />
            </BaseTooltip>
            <div>
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ data.owner.name }}
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-400">
                Project owner
              </BaseParagraph>
            </div>
          </div>
          <BaseAvatarGroup
            :avatars="data.team"
            :limit="3"
          />
        </div>
      </div>
      <div class="w-full shrink-0 sm:w-72">
        <img
          :src="data.image"
          :alt="data.name"
          class="rounded-lg"
        >
      </div>
    </div>

    <!-- Project Features Grid -->
    <div
      class="border-muted-200 dark:border-muted-800/80 grid gap-4 border-b pb-12 sm:grid-cols-4"
    >
      <div
        v-for="feature in data.features"
        :key="feature.title"
      >
        <Icon
          :name="feature.icon"
          class="text-primary-500 mb-2 size-6"
        />
        <div>
          <BaseHeading
            tag="h3"
            size="sm"
            weight="medium"
            class="text-muted-900 dark:text-white"
          >
            {{ feature.title }}
          </BaseHeading>
          <BaseParagraph
            size="xs"
            lead="tight"
            class="text-muted-600 dark:text-muted-400"
          >
            {{ feature.description }}
          </BaseParagraph>
        </div>
      </div>
    </div>

    <!-- Recent Files -->
    <div v-if="data.files && data.files.length > 0">
      <h4
        class="text-muted-600 dark:text-muted-400 mb-6 font-sans text-xs font-medium uppercase"
      >
        Recent files
      </h4>
      <div class="grid gap-8 pb-6 sm:grid-cols-2">
        <div
          v-for="(file, index) in data.files"
          :key="index"
          rounded="lg"
        >
          <div class="flex w-full items-center gap-2">
            <img
              :src="file.icon"
              :alt="file.name"
              class="max-w-[46px]"
            >
            <div>
              <BaseHeading
                tag="h3"
                size="sm"
                weight="medium"
                class="text-muted-900 dark:text-white"
              >
                {{ file.name }}
              </BaseHeading>
              <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400">
                <span>{{ file.size }}</span>
                <span class="px-1 text-base leading-tight">
                  &middot;
                </span>
                <span>v{{ file.version }}</span>
              </BaseParagraph>
            </div>
            <div class="ms-auto">
              <BaseTooltip content="Download file">
                <BaseButton
                  rounded="full"
                  size="icon-sm"
                >
                  <Icon name="lucide:arrow-down" />
                </BaseButton>
              </BaseTooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
