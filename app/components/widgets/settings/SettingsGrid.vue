<script setup lang="ts">
import type { SettingsGridData } from '~/types/widgets'

interface Props {
  data: SettingsGridData
}

defineProps<Props>()
</script>

<template>
  <div :class="data.gridClass || 'mx-auto mt-6 grid w-full max-w-4xl grid-cols-2 gap-4 sm:grid-cols-4 lg:landscape:grid-cols-5'">
    <BaseCard
      v-for="(item, index) in data.items"
      :key="index"
      rounded="lg"
      class="hover:border-primary-500! group"
    >
      <NuxtLink :to="item.link" class="block py-6 px-4">
        <div class="text-center">
          <Icon
            :name="item.icon"
            class="group-hover:text-primary-500 text-muted-400 size-6 transition-all duration-300 group-hover:rotate-6 mx-auto"
          />
          <BaseHeading
            tag="h3"
            :size="item.titleSize || 'sm'"
            weight="semibold"
            class="mt-2 text-[0.65rem]! uppercase"
          >
            {{ item.title }}
          </BaseHeading>
          <BaseText size="xs" class="text-muted-400">
            {{ item.description }}
          </BaseText>
        </div>
      </NuxtLink>
    </BaseCard>
  </div>
</template>
