<script setup lang="ts">
import type { AiPromptSuggestion, WidgetsAiPromptSuggestionData } from '~/types/ai'
import { computed } from 'vue'

const props = withDefaults(defineProps<{
  data?: WidgetsAiPromptSuggestionData
}>(), {
  data: () => ({
    title: 'Suggested prompts',
    items: [],
    columns: { base: 2, md: 4 },
    clickable: true,
  }),
})

const emit = defineEmits<{
  select: [item: AiPromptSuggestion]
}>()

function onSelect(item: AiPromptSuggestion) {
  if (!props.data.clickable)
    return
  emit('select', item)
}

const gridClass = computed(() => {
  const c = props.data.columns || {}
  return [
    'grid',
    'gap-4',
    `grid-cols-${c.base ?? 2}`,
    c.sm ? `sm:grid-cols-${c.sm}` : '',
    c.md ? `md:grid-cols-${c.md}` : '',
    c.lg ? `lg:grid-cols-${c.lg}` : '',
    c.xl ? `xl:grid-cols-${c.xl}` : '',
  ].filter(Boolean).join(' ')
})

function getIconColorClass(color?: string) {
  switch (color) {
    case 'primary':
      return 'text-primary-500'
    case 'success':
      return 'text-success-500'
    case 'warning':
      return 'text-warning-500'
    case 'info':
      return 'text-info-500'
    case 'danger':
      return 'text-danger-500'
    case 'yellow':
      return 'text-yellow-400'
    default:
      return 'text-primary-500'
  }
}

function getHoverClass(color?: string) {
  switch (color) {
    case 'primary':
      return 'hover:border-primary-500'
    case 'success':
      return 'hover:border-success-500'
    case 'warning':
      return 'hover:border-warning-500'
    case 'info':
      return 'hover:border-info-500'
    case 'danger':
      return 'hover:border-danger-500'
    case 'yellow':
      return 'hover:border-yellow-500'
    default:
      return 'hover:border-primary-500'
  }
}
</script>

<template>
  <div v-if="data.items.length > 0" :class="gridClass" class="max-w-4xl">
    <BaseCard
      v-for="item in data.items"
      :key="item.id"
      rounded="lg"
      shadow="flat"
      class="cursor-pointer p-4 transition-colors"
      :class="[
        props.data.clickable ? 'cursor-pointer' : '',
        getHoverClass(item.color),
      ]"
      role="button"
      tabindex="0"
      data-testid="ai-prompt-card"
      :aria-label="item.description || item.title"
      @click="onSelect(item)"
      @keyup.enter="onSelect(item)"
    >
      <div class="mb-2">
        <Icon
          v-if="item.icon"
          :name="item.icon"
          class="size-5" :class="[getIconColorClass(item.color)]"
          aria-hidden="true"
        />
      </div>
      <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-300">
        {{ item.description || item.title }}
      </BaseParagraph>
    </BaseCard>
  </div>
</template>
