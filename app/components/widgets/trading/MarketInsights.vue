<script setup lang="ts">
import type { TradingMarketInsightsData } from '~/types/widgets'

interface Props {
  data: TradingMarketInsightsData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <div class="mb-6 flex items-center gap-2">
      <component :is="data.icon" class="text-primary-500 size-9" />
      <div>
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          lead="snug"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ data.title }}</span>
        </BaseHeading>
        <BaseParagraph
          size="xs"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          {{ data.subtitle }}
        </BaseParagraph>
      </div>
    </div>
    <div class="space-y-5">
      <div
        v-for="(item, index) in data.insights"
        :key="index"
        class="flex items-center justify-between"
      >
        <BaseParagraph
          size="sm"
          class="text-muted-600 text-muted-400"
        >
          {{ item.label }}
        </BaseParagraph>
        <BaseParagraph
          size="sm"
          weight="semibold"
          class="text-muted-900 dark:text-muted-100"
        >
          {{ item.value }}
        </BaseParagraph>
      </div>
    </div>
  </BaseCard>
</template>
