<script setup lang="ts">
import type { TradingTrendingStocksData } from '~/types/widgets'

interface Props {
  data: TradingTrendingStocksData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <LinkArrow :to="data.viewAllLink" :label="data.viewAllLabel" />
    </div>
    <div class="divide-muted-200 dark:divide-muted-800 mb-2 divide-y">
      <div
        v-for="(stock, index) in data.stocks"
        :key="index"
        class="flex items-center gap-2 py-4 last:pb-0"
      >
        <div class="shrink-0">
          <img
            :src="stock.logo"
            :alt="stock.name"
            class="size-8"
          >
        </div>
        <div>
          <BaseHeading
            as="h4"
            size="sm"
            weight="medium"
            lead="snug"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ stock.name }}</span>
          </BaseHeading>
        </div>
        <div class="ms-auto flex items-center gap-1">
          <BaseHeading
            as="h5"
            size="md"
            weight="medium"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ stock.changeSymbol }}{{ formatPrice(stock.change) }}</span>
            <span
              class="ps-2 text-xs font-semibold" :class="[
                stock.isPositive ? 'text-success-500' : 'text-destructive-500',
              ]"
            >
              ({{ stock.changeSymbol }}{{ stock.percentage }}%)
            </span>
          </BaseHeading>
          <Icon
            :name="stock.isPositive ? 'lucide:trending-up' : 'lucide:trending-down'"
            class="size-4" :class="[
              stock.isPositive ? 'text-success-500' : 'text-destructive-500',
            ]"
          />
        </div>
      </div>
    </div>
  </BaseCard>
</template>
