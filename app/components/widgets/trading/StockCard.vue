<script setup lang="ts">
import type { TradingStockCardData } from '~/types/widgets'

interface Props {
  data: TradingStockCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="flex gap-3 p-5">
    <div class="shrink-0">
      <img
        :src="data.logo"
        :alt="data.name"
        class="size-6"
      >
    </div>
    <div class="space-y-1">
      <BaseParagraph
        size="xs"
        class="text-muted-500 dark:text-muted-400"
      >
        {{ data.label }}
      </BaseParagraph>
      <BaseHeading
        as="h5"
        size="md"
        weight="medium"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.changeSymbol }}{{ formatPrice(data.change) }}</span>
        <span
          class="ps-2 text-sm font-semibold" :class="[
            data.isPositive ? 'text-success-500' : 'text-destructive-500',
          ]"
        >
          ({{ data.changeSymbol }}{{ data.percentage }}%)
        </span>
      </BaseHeading>
    </div>
  </BaseCard>
</template>
