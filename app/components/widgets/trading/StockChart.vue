<script setup lang="ts">
import type { TradingBankData, TradingStockChartData } from '~/types/widgets'

interface Props {
  data: TradingStockChartData
}

interface Emits {
  (e: 'bank-changed', bank: TradingBankData): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const target = ref(null)
const open = ref(false)
const selectedBank = ref(props.data.banks[0])

function openDropdown() {
  open.value = true
}

function setAccount(account: TradingBankData) {
  selectedBank.value = account
  open.value = false
  emit('bank-changed', account)
}

onClickOutside(target, () => (open.value = false))
</script>

<template>
  <div class="relative">
    <div class="absolute end-2 top-2 w-52">
      <div ref="target" class="relative z-10 w-full">
        <button
          type="button"
          class="click-blur dark:bg-muted-950 border-muted-200 dark:border-muted-800 w-full rounded-full border bg-white p-3"
          @click="openDropdown()"
        >
          <span class="flex w-full items-center gap-3 text-start">
            <img
              :src="selectedBank?.logo"
              :alt="selectedBank?.name"
              class="size-6 shrink-0"
            >
            <div>
              <BaseText
                size="sm"
                class="text-muted-900 dark:text-muted-200 block capitalize"
              >
                {{ selectedBank?.name }}
              </BaseText>
            </div>
            <Icon
              name="lucide:chevron-down"
              class="text-muted-400 ms-auto size-4 transition-transform duration-300"
              :class="open && 'rotate-180'"
            />
          </span>
        </button>
        <Transition
          enter-active-class="transition duration-100 ease-out"
          enter-from-class="transform scale-95 opacity-0"
          enter-to-class="transform scale-100 opacity-100"
          leave-active-class="transition duration-75 ease-in"
          leave-from-class="transform scale-100 opacity-100"
          leave-to-class="transform scale-95 opacity-0"
        >
          <div
            v-if="open"
            class="border-muted-200 dark:border-muted-800 dark:bg-muted-950 shadow-muted-400/10 dark:shadow-muted-800/10 absolute start-0 top-14 w-full rounded-xl border bg-white p-2 shadow-xl"
          >
            <ul>
              <li v-for="bank in data.banks" :key="bank.id">
                <button
                  type="button"
                  class="hover:bg-muted-100 dark:hover:bg-muted-900 flex w-full items-center gap-3 rounded-lg px-4 py-2 text-start transition-colors duration-300"
                  @click="setAccount(bank)"
                >
                  <img
                    :src="bank.logo"
                    :alt="bank.name"
                    class="size-6 shrink-0"
                  >
                  <span class="block">
                    <span
                      class="font-heading text-muted-900 dark:text-muted-200 block text-sm capitalize"
                    >
                      {{ bank.name }}
                    </span>
                  </span>
                </button>
              </li>
            </ul>
          </div>
        </Transition>
      </div>
    </div>
    <ChartAreaStockPrice
      :series="selectedBank?.series"
      class="[--color-chart-gradient:var(--color-muted-50)] dark:[--color-chart-gradient:var(--color-muted-900)]"
    />
  </div>
</template>
