<script setup lang="ts">
import type { TradingMarketOrderData } from '~/types/widgets'

interface Props {
  data: TradingMarketOrderData
}

interface Emits {
  (e: 'update:shares', value: number): void
  (e: 'buy-shares'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <div class="mb-6 flex items-center gap-2">
      <div>
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          lead="snug"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ data.title }}</span>
        </BaseHeading>
        <BaseParagraph
          size="xs"
          weight="medium"
          class="text-muted-500 dark:text-muted-400"
        >
          {{ data.subtitle }}
        </BaseParagraph>
      </div>
    </div>
    <div class="space-y-5">
      <div class="flex items-center justify-between">
        <BaseParagraph
          size="sm"
          class="text-muted-600 text-muted-400"
        >
          {{ data.sharesLabel }}
        </BaseParagraph>
        <div class="max-w-[140px]">
          <BaseInputNumber
            :model-value="data.shares"
            placeholder="0.00"
            :min="0"
            :step="0.01"
            @update:model-value="$emit('update:shares', $event)"
          />
        </div>
      </div>
      <div
        v-for="(item, index) in data.priceDetails"
        :key="index"
        class="flex items-center justify-between"
      >
        <BaseParagraph
          size="sm"
          class="text-muted-600 text-muted-400"
        >
          {{ item.label }}
        </BaseParagraph>
        <BaseParagraph
          size="sm"
          weight="semibold"
          class="text-muted-900 dark:text-muted-100"
        >
          {{ item.value }}
        </BaseParagraph>
      </div>
      <hr class="bg-transprent border-muted-200 dark:border-muted-800 border-t">
      <div class="flex items-center justify-between">
        <BaseParagraph
          size="sm"
          class="text-muted-600 text-muted-400"
        >
          {{ data.totalLabel }}
        </BaseParagraph>
        <BaseParagraph
          size="sm"
          weight="semibold"
          class="text-muted-900 dark:text-muted-100"
        >
          {{ data.totalValue }}
        </BaseParagraph>
      </div>
      <div>
        <BaseButton
          rounded="md"
          variant="primary"
          class="w-full"
          @click="$emit('buy-shares')"
        >
          {{ data.buttonText }}
        </BaseButton>
      </div>
    </div>
  </BaseCard>
</template>
