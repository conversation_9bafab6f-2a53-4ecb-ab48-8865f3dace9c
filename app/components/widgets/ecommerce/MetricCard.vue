<script setup lang="ts">
import type { EcommerceMetricData } from '~/types/widgets'

interface Props {
  data: EcommerceMetricData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="p-4">
    <div class="flex items-center justify-between">
      <div>
        <BaseHeading
          as="h4"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-600 dark:text-muted-400"
        >
          <span>{{ data.title }}</span>
        </BaseHeading>
        <BaseText
          size="xl"
          weight="semibold"
          class="text-muted-900 dark:text-muted-100"
        >
          {{ data.value }}
        </BaseText>
      </div>
      <Icon
        :name="data.icon"
        :class="data.iconClass"
        class="size-8"
      />
    </div>
    <div class="pt-4">
      <component :is="data.chartComponent" />
    </div>
  </BaseCard>
</template>
