<script setup lang="ts">
import type { NotificationsWidgetData } from '~/types/widgets'

interface Props {
  data: NotificationsWidgetData
}

defineProps<Props>()
</script>

<template>
  <BaseCard
    :class="data.cardClass || 'p-4 md:p-6'"
    :rounded="data.rounded || 'md'"
  >
    <div v-if="data.title" class="mb-4 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="sm"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        {{ data.title }}
      </BaseHeading>
      <BaseButtonAction
        v-if="data.viewAllLink"
        size="sm"
        color="muted"
        :to="data.viewAllLink.href"
      >
        {{ data.viewAllLink.text || 'View all' }}
      </BaseButtonAction>
    </div>

    <component
      :is="data.notificationComponent || 'NotificationsCompact'"
      v-bind="data.notificationProps"
    />
  </BaseCard>
</template>
