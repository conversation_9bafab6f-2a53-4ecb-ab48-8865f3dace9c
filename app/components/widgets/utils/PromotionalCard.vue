<script setup lang="ts">
import type { PromotionalCardData } from '~/types/widgets'

interface Props {
  data: PromotionalCardData
}

const props = defineProps<Props>()
</script>

<template>
  <BaseCard
    variant="none"
    :rounded="props.data.rounded || 'md'"
    class="relative flex h-full items-center justify-center p-6" :class="[
      props.data.gradient || 'bg-gradient-to-br from-primary-900 to-primary-800',
      props.data.cardClass,
    ]"
  >
    <!-- Content -->
    <div class="relative z-20 flex flex-col gap-3 py-10 text-center">
      <BaseHeading
        as="h4"
        :size="props.data.titleSize || 'lg'"
        weight="semibold"
        lead="tight"
        :class="props.data.titleClass || 'text-white'"
      >
        <span>{{ props.data.title }}</span>
      </BaseHeading>

      <BaseParagraph
        :size="props.data.descriptionSize || 'md'"
        class="mx-auto" :class="[
          props.data.maxWidth || 'max-w-[280px]',
          props.data.descriptionClass || 'text-white/80',
        ]"
      >
        <span>{{ props.data.description }}</span>
      </BaseParagraph>

      <NuxtLink
        v-if="props.data.link"
        :to="props.data.link.href"
        class="font-sans text-sm underline-offset-4 hover:underline" :class="[
          props.data.linkClass || 'text-white',
        ]"
      >
        {{ props.data.link.text }}
      </NuxtLink>
    </div>

    <!-- Icon/Decoration -->
    <div
      v-if="props.data.icon"
      class="absolute z-10 flex items-center justify-center" :class="[
        props.data.iconPosition || 'bottom-4 end-4',
        props.data.iconSize || 'size-14',
      ]"
    >
      <Icon
        :name="props.data.icon.name"
        :class="[
          props.data.icon.size || 'size-14',
          props.data.icon.class || 'text-primary-600/50',
        ]"
      />
    </div>
  </BaseCard>
</template>
