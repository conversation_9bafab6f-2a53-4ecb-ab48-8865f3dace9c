<script setup lang="ts">
import type { DatePickerWidgetData } from '~/types/widgets'

interface Props {
  data: DatePickerWidgetData
  modelValue?: Date
}

interface Emits {
  (e: 'update:modelValue', value: Date): void
  (e: 'change', value: Date): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => new Date(),
})

const emit = defineEmits<Emits>()

const dateValue = computed({
  get() {
    return props.modelValue
  },
  set(value: Date) {
    emit('update:modelValue', value)
    emit('change', value)
  },
})
</script>

<template>
  <BaseCard :rounded="data.rounded || 'md'" class="p-2">
    <LazyAddonDatepicker
      v-model="dateValue"
      :locale="data.locale || 'en'"
      :label="data.label"
      :placeholder="data.placeholder"
      :disabled="data.disabled"
      :required="data.required"
    />
  </BaseCard>
</template>
