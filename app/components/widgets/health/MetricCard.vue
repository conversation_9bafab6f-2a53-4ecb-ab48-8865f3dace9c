<script setup lang="ts">
import type { HealthMetricCardData } from '~/types/widgets'

interface Props {
  data: HealthMetricCardData
}

defineProps<Props>()
</script>

<template>
  <div class="flex flex-col">
    <div class="mb-3 flex items-center gap-2">
      <BaseIconBox
        size="md"
        class="bg-primary-500/10"
        variant="none"
        rounded="none"
        mask="blob"
      >
        <Icon :name="data.icon" class="text-primary-500 size-5" />
      </BaseIconBox>
      <div class="flex items-center gap-1 font-sans">
        <BaseText weight="semibold" class="text-muted-800 dark:text-muted-100">
          {{ data.value }}
        </BaseText>
        <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
          {{ data.unit }}
        </BaseText>
      </div>
    </div>
    <div>
      <BaseHeading
        tag="h3"
        size="sm"
        weight="medium"
        class="text-muted-800 dark:text-muted-100"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 max-w-[260px]">
        {{ data.description }}
      </BaseParagraph>
    </div>
  </div>
</template>
