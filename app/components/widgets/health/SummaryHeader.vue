<script setup lang="ts">
import type { HealthSummaryHeaderData } from '~/types/widgets'

interface Props {
  data: HealthSummaryHeaderData
}

defineProps<Props>()
</script>

<template>
  <div class="bg-primary-800 flex flex-col items-center rounded-2xl p-4 sm:flex-row">
    <div class="relative h-[168px] w-[280px] shrink-0">
      <img
        class="pointer-events-none absolute -start-6 -top-20 sm:-start-10"
        :src="data.illustration"
        :alt="data.illustrationAlt"
      >
    </div>
    <div class="mt-6 grow sm:mt-0">
      <div class="text-center sm:text-start">
        <BaseHeading tag="h1" class="text-white opacity-90">
          <span>{{ data.title }}</span>
        </BaseHeading>
        <BaseParagraph size="sm" class="text-white opacity-70">
          <span>{{ data.description }}</span>
        </BaseParagraph>
        <div class="mt-6 flex flex-wrap gap-y-6 pb-4 text-center sm:mt-4 sm:gap-x-8 sm:pb-0 sm:text-start">
          <div
            v-for="(metric, index) in data.metrics"
            :key="index"
            class="min-w-[33.3%] sm:min-w-0"
          >
            <BaseHeading
              tag="h4"
              weight="medium"
              size="sm"
              class="text-white opacity-90"
            >
              <span>{{ metric.value }}</span>
            </BaseHeading>
            <BaseParagraph size="xs" class="text-white opacity-70">
              <span>{{ metric.label }}</span>
            </BaseParagraph>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
