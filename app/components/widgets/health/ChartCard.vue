<script setup lang="ts">
import type { HealthChartCardData } from '~/types/widgets'

interface Props {
  data: HealthChartCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-4 md:p-6">
    <div class="mb-2 flex items-center gap-2">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseTooltip v-if="data.tooltip" :content="data.tooltip">
        <Icon name="solar:question-circle-linear" class="size-4 text-muted-400" />
      </BaseTooltip>
    </div>
    <div>
      <BaseParagraph
        size="xs"
        class="text-muted-600 dark:text-muted-400 max-w-[240px]"
      >
        <span>{{ data.description }}</span>
        <NuxtLink
          v-if="data.readMoreLink"
          :to="data.readMoreLink"
          class="text-primary-500 underline-offset-4 hover:underline"
        >
          Read how
        </NuxtLink>
      </BaseParagraph>
    </div>
    <component
      :is="data.chartComponent"
      :class="data.chartClass"
    />
  </BaseCard>
</template>
