<script setup lang="ts">
import type { InvoiceLineItem } from '~/types/invoice'

export interface Props {
  invoiceNumber: string
  invoiceDate: string
  dueDate: string
  items: InvoiceLineItem[]
  editable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editable: false,
})

const emit = defineEmits<{
  'update:items': [items: InvoiceLineItem[]]
  'add-item': []
  'remove-item': [index: number]
}>()

// Local reactive copy of items for editing
const localItems = ref([...props.items])

// Watch for external changes to items prop
watch(() => props.items, (newItems) => {
  localItems.value = [...newItems]
}, { deep: true })

// Computed totals for each line item
const itemTotals = computed(() => {
  return localItems.value.map(item => item.hours * item.rate)
})

// Overall subtotal
const subtotal = computed(() => {
  return itemTotals.value.reduce((sum, total) => sum + total, 0)
})

// Format currency
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

// Format date
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

// Add new item
function addItem() {
  const newItem: InvoiceLineItem = {
    id: `item-${Date.now()}`,
    description: '',
    hours: 0,
    rate: 0,
  }
  localItems.value.push(newItem)
  emit('update:items', [...localItems.value])
  emit('add-item')
}

// Remove item
function removeItem(index: number) {
  if (localItems.value.length > 1) {
    localItems.value.splice(index, 1)
    emit('update:items', [...localItems.value])
    emit('remove-item', index)
  }
}

// Update item and emit changes
function updateItem() {
  emit('update:items', [...localItems.value])
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="p-4 md:p-6"
    data-testid="invoice-details-card"
  >
    <div class="mb-6">
      <div class="flex items-start justify-between mb-6">
        <div>
          <BaseHeading
            weight="medium"
            size="xl"
            lead="none"
            class="mb-2"
          >
            Invoice {{ invoiceNumber }}
          </BaseHeading>
          <BaseParagraph
            size="sm"
            class="text-muted-500 dark:text-muted-400"
          >
            Invoice Date: {{ formatDate(invoiceDate) }}
          </BaseParagraph>
          <BaseParagraph
            size="sm"
            class="text-muted-500 dark:text-muted-400"
          >
            Due Date: {{ formatDate(dueDate) }}
          </BaseParagraph>
        </div>
      </div>

      <!-- Line Items -->
      <div class="space-y-4">
        <BaseHeading
          weight="medium"
          size="lg"
          lead="none"
          class="mb-4"
        >
          Line Items
        </BaseHeading>

        <!-- Headers for larger screens -->
        <div class="hidden md:grid md:grid-cols-12 gap-4 pb-2 border-b border-muted-200 dark:border-muted-800">
          <div class="col-span-5">
            <BaseParagraph size="sm" weight="medium" class="text-muted-600 dark:text-muted-400">
              Description
            </BaseParagraph>
          </div>
          <div class="col-span-2">
            <BaseParagraph size="sm" weight="medium" class="text-muted-600 dark:text-muted-400">
              Hours
            </BaseParagraph>
          </div>
          <div class="col-span-2">
            <BaseParagraph size="sm" weight="medium" class="text-muted-600 dark:text-muted-400">
              Rate
            </BaseParagraph>
          </div>
          <div class="col-span-2">
            <BaseParagraph size="sm" weight="medium" class="text-muted-600 dark:text-muted-400">
              Total
            </BaseParagraph>
          </div>
          <div v-if="editable" class="col-span-1">
            <BaseParagraph size="sm" weight="medium" class="text-muted-600 dark:text-muted-400">
              Action
            </BaseParagraph>
          </div>
        </div>

        <!-- Line Items -->
        <div
          v-for="(item, index) in localItems"
          :key="item.id"
          class="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 border border-muted-200 dark:border-muted-800 rounded-lg"
        >
          <!-- Description -->
          <div class="md:col-span-5">
            <label class="block md:hidden text-sm font-medium text-muted-600 dark:text-muted-400 mb-1">
              Description
            </label>
            <BaseInput
              v-if="editable"
              v-model="item.description"
              placeholder="Enter description"
              @input="updateItem"
            />
            <BaseParagraph v-else size="sm">
              {{ item.description }}
            </BaseParagraph>
          </div>

          <!-- Hours -->
          <div class="md:col-span-2">
            <label class="block md:hidden text-sm font-medium text-muted-600 dark:text-muted-400 mb-1">
              Hours
            </label>
            <BaseInput
              v-if="editable"
              v-model.number="item.hours"
              type="number"
              placeholder="0"
              min="0"
              step="0.1"
              @input="updateItem"
            />
            <BaseParagraph v-else size="sm">
              {{ item.hours }}
            </BaseParagraph>
          </div>

          <!-- Rate -->
          <div class="md:col-span-2">
            <label class="block md:hidden text-sm font-medium text-muted-600 dark:text-muted-400 mb-1">
              Rate
            </label>
            <BaseInput
              v-if="editable"
              v-model.number="item.rate"
              type="number"
              placeholder="0.00"
              min="0"
              step="0.01"
              @input="updateItem"
            />
            <BaseParagraph v-else size="sm">
              {{ formatCurrency(item.rate) }}
            </BaseParagraph>
          </div>

          <!-- Total -->
          <div class="md:col-span-2">
            <label class="block md:hidden text-sm font-medium text-muted-600 dark:text-muted-400 mb-1">
              Total
            </label>
            <BaseParagraph size="sm" weight="medium">
              {{ formatCurrency(itemTotals[index]) }}
            </BaseParagraph>
          </div>

          <!-- Actions -->
          <div v-if="editable" class="md:col-span-1">
            <BaseButton
              v-if="localItems.length > 1"
              variant="danger"
              size="sm"
              @click="removeItem(index)"
            >
              <Icon name="lucide:trash-2" class="size-4" />
            </BaseButton>
          </div>
        </div>

        <!-- Add Item Button -->
        <div v-if="editable" class="flex justify-center pt-4">
          <BaseButton
            variant="outline"
            @click="addItem"
          >
            <Icon name="lucide:plus" class="size-4 mr-2" />
            Add Line Item
          </BaseButton>
        </div>

        <!-- Subtotal -->
        <div class="flex justify-end pt-6 border-t border-muted-200 dark:border-muted-800">
          <div class="text-right">
            <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 mb-1">
              Subtotal
            </BaseParagraph>
            <BaseHeading weight="medium" size="lg" class="text-primary-500">
              {{ formatCurrency(subtotal) }}
            </BaseHeading>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
