<script setup lang="ts">
export interface Props {
  total: number
  dueDate: string
  pdfAttachment: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:pdfAttachment': [value: boolean]
}>()

// Format currency
const formattedTotal = computed(() => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(props.total)
})

// Format date
const formattedDueDate = computed(() => {
  return new Date(props.dueDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
})

function handlePdfToggle(value: boolean) {
  emit('update:pdfAttachment', value)
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="p-4 md:p-6"
    data-testid="invoice-amount-simple-card"
  >
    <div class="mb-6">
      <BaseHeading
        weight="medium"
        size="md"
        lead="none"
        class="line-clamp-1"
      >
        Invoice amount
      </BaseHeading>
      <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 line-clamp-1">
        Expressed in USD
      </BaseParagraph>
    </div>
    <div class="border-muted-200 dark:border-muted-800/80 border-b pb-4">
      <BaseHeading
        weight="semibold"
        size="2xl"
        lead="none"
        class="mb-3"
      >
        {{ formattedTotal }}
        <BaseText size="xs" class="text-muted-400 inline">
          (Tax incl.)
        </BaseText>
      </BaseHeading>
      <BaseTag
        rounded="full"
        variant="primary"
      >
        Due on {{ formattedDueDate }}
      </BaseTag>
    </div>
    <div class="flex items-center justify-between pt-4">
      <BaseText
        size="sm"
        class="text-muted-600 dark:text-muted-400"
      >
        Attach PDF in mail
      </BaseText>
      <div>
        <BaseSwitchBall
          :model-value="pdfAttachment"
          variant="primary"
          @update:model-value="handlePdfToggle"
        />
      </div>
    </div>
  </BaseCard>
</template>
