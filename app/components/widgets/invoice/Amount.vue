<script setup lang="ts">
import type { WidgetsInvoiceAmountData } from '~/types/invoice'

export interface Props {
  data: WidgetsInvoiceAmountData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  pay: [amount: number]
}>()

// Computed properties for formatting
const formattedSubtotal = computed(() => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(props.data.subtotal)
})

const formattedTax = computed(() => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(props.data.taxAmount)
})

const formattedTotal = computed(() => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(props.data.total)
})

const formattedTaxRate = computed(() => {
  return `${props.data.taxRate}%`
})

function handlePay() {
  emit('pay', props.data.total)
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="p-4 md:p-6"
    data-testid="invoice-amount-card"
  >
    <div class="mb-6">
      <BaseHeading
        weight="medium"
        size="lg"
        lead="none"
        class="mb-4"
      >
        Invoice Amount
      </BaseHeading>
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <BaseParagraph
            size="sm"
            class="text-muted-500 dark:text-muted-400"
          >
            Subtotal
          </BaseParagraph>
          <BaseParagraph
            size="sm"
            weight="medium"
            class="text-muted-800 dark:text-muted-200"
          >
            {{ formattedSubtotal }}
          </BaseParagraph>
        </div>
        <div class="flex items-center justify-between">
          <BaseParagraph
            size="sm"
            class="text-muted-500 dark:text-muted-400"
          >
            Tax ({{ formattedTaxRate }})
          </BaseParagraph>
          <BaseParagraph
            size="sm"
            weight="medium"
            class="text-muted-800 dark:text-muted-200"
          >
            {{ formattedTax }}
          </BaseParagraph>
        </div>
        <div
          class="border-muted-200 dark:border-muted-800 flex items-center justify-between border-t pt-4"
        >
          <BaseHeading
            weight="medium"
            size="md"
            lead="none"
          >
            Total
          </BaseHeading>
          <BaseHeading
            weight="medium"
            size="md"
            lead="none"
            class="text-primary-500"
          >
            {{ formattedTotal }}
          </BaseHeading>
        </div>
      </div>
    </div>
    <div>
      <BaseButton
        rounded="md"
        variant="primary"
        class="w-full"
        @click="handlePay"
      >
        Pay Invoice
      </BaseButton>
    </div>
  </BaseCard>
</template>
