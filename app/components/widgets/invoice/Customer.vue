<script setup lang="ts">
import type { WidgetsInvoiceCustomerData } from '~/types/invoice'

export interface Props {
  data: WidgetsInvoiceCustomerData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  edit: [customerId: string]
}>()

function handleEdit() {
  if (props.data.editUrl) {
    navigateTo(props.data.editUrl)
  }
  else {
    emit('edit', props.data.customer.id)
  }
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="p-4 md:p-6"
    data-testid="invoice-customer-card"
  >
    <div
      class="border-muted-200 dark:border-muted-800 mb-6 flex items-center gap-4 border-b pb-8"
    >
      <BaseAvatar
        :src="data.customer.avatarUrl"
        :alt="data.customer.name"
        size="md"
        rounded="none"
        mask="blob"
        class="bg-muted-100 dark:bg-muted-900"
      />
      <div>
        <BaseHeading
          weight="medium"
          size="md"
          lead="none"
          class="line-clamp-1"
        >
          {{ data.customer.name }}
        </BaseHeading>
        <BaseParagraph size="xs" class="text-muted-400 line-clamp-1">
          {{ data.customer.email }}
        </BaseParagraph>
      </div>
    </div>
    <div class="mb-6">
      <BaseParagraph
        size="sm"
        class="text-muted-500 dark:text-muted-400"
      >
        {{ data.customer.address.street }}
      </BaseParagraph>
      <BaseParagraph
        size="sm"
        class="text-muted-500 dark:text-muted-400"
      >
        {{ data.customer.address.city }}, {{ data.customer.address.country }}
      </BaseParagraph>
    </div>
    <div>
      <BaseButton
        rounded="md"
        variant="primary"
        class="w-full"
        @click="handleEdit"
      >
        Edit Customer
      </BaseButton>
    </div>
  </BaseCard>
</template>
