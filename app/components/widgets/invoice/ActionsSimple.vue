<script setup lang="ts">
export interface Props {
  canPreview?: boolean
  canDownload?: boolean
  canSend?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  canPreview: true,
  canDownload: true,
  canSend: true,
})

const emit = defineEmits<{
  preview: []
  download: []
  send: []
}>()

function handlePreview() {
  emit('preview')
}

function handleDownload() {
  emit('download')
}

function handleSend() {
  emit('send')
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="p-4 md:p-6 space-y-4"
    data-testid="invoice-actions-simple-card"
  >
    <div class="flex gap-4">
      <BaseButton
        v-if="canPreview"
        rounded="md"
        class="w-full"
        @click="handlePreview"
      >
        <Icon name="solar:eye-linear" class="size-4" />
        <span>Preview</span>
      </BaseButton>
      <BaseButton
        v-if="canDownload"
        rounded="md"
        class="w-full"
        @click="handleDownload"
      >
        <Icon name="solar:download-linear" class="size-4" />
        <span>Download</span>
      </BaseButton>
    </div>
    <!-- Submit -->
    <div v-if="canSend">
      <BaseButton
        rounded="md"
        variant="primary"
        class="w-full"
        @click="handleSend"
      >
        <span>Send Invoice</span>
      </BaseButton>
    </div>
  </BaseCard>
</template>
