<script setup lang="ts">
import type { WidgetsInvoiceActionsData } from '~/types/invoice'

export interface Props {
  data: WidgetsInvoiceActionsData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  download: []
  print: []
  email: []
  duplicate: []
  delete: []
}>()

function handleDownload() {
  emit('download')
}

function handlePrint() {
  emit('print')
}

function handleEmail() {
  emit('email')
}

function handleDuplicate() {
  emit('duplicate')
}

function handleDelete() {
  emit('delete')
}
</script>

<template>
  <BaseCard
    rounded="md"
    class="p-4 md:p-6"
    data-testid="invoice-actions-card"
  >
    <div class="mb-6">
      <BaseHeading
        weight="medium"
        size="lg"
        lead="none"
        class="mb-4"
      >
        Invoice Actions
      </BaseHeading>
      <BaseParagraph
        size="sm"
        class="text-muted-500 dark:text-muted-400 mb-6"
      >
        {{ data.description }}
      </BaseParagraph>
    </div>

    <div class="space-y-3">
      <BaseButton
        v-if="data.canDownload"
        rounded="md"
        variant="outline"
        class="w-full"
        @click="handleDownload"
      >
        <Icon name="lucide:download" class="size-4 mr-2" />
        Download PDF
      </BaseButton>

      <BaseButton
        v-if="data.canPrint"
        rounded="md"
        variant="outline"
        class="w-full"
        @click="handlePrint"
      >
        <Icon name="lucide:printer" class="size-4 mr-2" />
        Print Invoice
      </BaseButton>

      <BaseButton
        v-if="data.canEmail"
        rounded="md"
        variant="outline"
        class="w-full"
        @click="handleEmail"
      >
        <Icon name="lucide:mail" class="size-4 mr-2" />
        Email Invoice
      </BaseButton>

      <BaseButton
        v-if="data.canDuplicate"
        rounded="md"
        variant="outline"
        class="w-full"
        @click="handleDuplicate"
      >
        <Icon name="lucide:copy" class="size-4 mr-2" />
        Duplicate Invoice
      </BaseButton>

      <BaseButton
        v-if="data.canDelete"
        rounded="md"
        variant="danger"
        class="w-full mt-4"
        @click="handleDelete"
      >
        <Icon name="lucide:trash-2" class="size-4 mr-2" />
        Delete Invoice
      </BaseButton>
    </div>
  </BaseCard>
</template>
