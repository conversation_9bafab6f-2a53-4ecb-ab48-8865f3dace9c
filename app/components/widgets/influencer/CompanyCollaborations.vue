<script setup lang="ts">
import type { CompanyCollaborationsData } from '~/types/widgets'

interface Props {
  data: CompanyCollaborationsData
}

defineProps<Props>()
</script>

<template>
  <div>
    <!-- Title -->
    <div class="mb-6 flex flex-col md:flex-row w-full md:items-center md:justify-between gap-y-4">
      <div>
        <BaseHeading
          as="h3"
          size="lg"
          weight="medium"
          lead="tight"
          class="mb-2"
        >
          <span class="text-muted-900 dark:text-muted-100">
            {{ data.title }}
          </span>
        </BaseHeading>
        <BaseParagraph
          size="sm"
          class="text-muted-500 dark:text-muted-400 max-w-sm"
        >
          <span>
            {{ data.description }}
          </span>
        </BaseParagraph>
      </div>
      <div class="shrink-0">
        <BaseButton rounded="md">
          {{ data.ctaText }}
        </BaseButton>
      </div>
    </div>
    <!-- Company grid -->
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      <div
        v-for="(company, index) in data.companies"
        :key="index"
        class="relative h-full"
      >
        <BaseCard rounded="md" class="flex h-full flex-col p-4 md:p-6">
          <div class="flex size-full gap-4">
            <BaseTooltip :content="company.name">
              <Icon :name="company.logo" class="size-8 shrink-0" />
            </BaseTooltip>
            <div class="flex h-full flex-col">
              <BaseHeading
                as="h4"
                size="md"
                weight="semibold"
                lead="tight"
                class="after:text-muted-800 mb-4 dark:text-white"
              >
                <span>{{ company.name }}</span>
              </BaseHeading>
              <BaseParagraph size="sm">
                <span class="text-muted-500 dark:text-muted-400 line-clamp-4">
                  {{ company.description }}
                </span>
              </BaseParagraph>
            </div>
          </div>
          <div
            class="divide-muted-200 dark:divide-muted-700 mt-auto flex items-center justify-center divide-x py-4"
          >
            <div
              v-for="(stat, statIndex) in company.stats"
              :key="statIndex"
              class="my-4 flex-1 px-4 text-center font-sans"
            >
              <span class="text-muted-800 dark:text-muted-100 block font-semibold">
                {{
                  stat.label === 'Income'
                    ? `$${stat.value}k`
                    : stat.value
                }}
              </span>
              <span class="text-muted-400 block text-xs">
                {{ stat.label }}
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between">
            <div>
              <BaseAvatarGroup
                :avatars="company.followers"
                :limit="3"
                size="xs"
              />
            </div>
            <div class="flex gap-2">
              <BaseButton
                rounded="md"
                size="sm"
              >
                Details
              </BaseButton>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>
