<script setup lang="ts">
import type { SocialMediaStatsData } from '~/types/widgets'

interface Props {
  data: SocialMediaStatsData
}

defineProps<Props>()

// Format large numbers (e.g., 129700 -> "129.7K")
function formatValue(value: number): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  }
  else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  return value.toString()
}
</script>

<template>
  <div>
    <!-- Title -->
    <div class="mb-6 flex flex-col md:flex-row w-full md:items-center md:justify-between gap-y-4">
      <div>
        <BaseHeading
          as="h3"
          size="lg"
          weight="medium"
          lead="tight"
          class="mb-2"
        >
          <span class="text-muted-900 dark:text-muted-100">
            {{ data.title }}
          </span>
        </BaseHeading>
        <BaseParagraph
          size="sm"
          class="text-muted-500 dark:text-muted-400 max-w-sm"
        >
          <span>
            {{ data.description }}
          </span>
        </BaseParagraph>
      </div>
      <div class="shrink-0">
        <BaseButton rounded="md">
          {{ data.ctaText }}
        </BaseButton>
      </div>
    </div>
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Grid item -->
      <div
        v-for="stat in data.stats"
        :key="stat.label"
        class="lg:landscape:col-span-4 col-span-12 sm:col-span-6 2xl:landscape:col-span-3"
      >
        <BaseCard rounded="md" class="p-4">
          <div class="flex items-center justify-between">
            <BaseParagraph
              size="xs"
              weight="medium"
              class="text-muted-600 dark:text-muted-400"
            >
              <span>{{ stat.label }}</span>
            </BaseParagraph>
            <BaseIconBox
              size="sm"
              class="bg-muted-200 dark:bg-muted-800 text-muted-500 dark:text-muted-100"
              variant="none"
              rounded="none"
              mask="blob"
            >
              <Icon :name="stat.icon" class="size-5" />
            </BaseIconBox>
          </div>
          <div class="pb-4 pt-2">
            <span
              class="text-muted-800 dark:text-muted-100 font-sans text-xl xl:text-2xl font-semibold leading-none"
            >
              {{ formatValue(stat.value) }}
            </span>
            <small
              class="block text-muted-500 dark:text-muted-400 text-xs font-medium"
            >
              {{ stat.action }}
            </small>
          </div>
          <div class="mb-2 flex items-center gap-2 font-sans">
            <div
              class="text-sm flex items-center font-semibold"
              :class="stat.growth < 0 ? 'text-destructive-500' : 'text-success-500'"
            >
              <Icon v-if="stat.growth < 0" name="lucide:arrow-down-right" class="size-4" />
              <Icon v-else name="lucide:arrow-up-right" class="size-4" />
              <span>{{ stat.growth > 0 ? '+' : '' }}{{ stat.growth }}%</span>
            </div>
            <span class="text-muted-400 text-xs">
              {{ stat.growthText }}
            </span>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>
