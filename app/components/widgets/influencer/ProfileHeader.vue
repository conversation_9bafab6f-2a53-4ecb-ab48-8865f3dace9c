<script setup lang="ts">
import type { InfluencerProfileHeaderData } from '~/types/widgets'

interface Props {
  data: InfluencerProfileHeaderData
}

defineProps<Props>()
</script>

<template>
  <div
    class="relative flex w-full flex-col lg:flex-row p-6 lg:p-8 2xl:p-10 bg-muted-200/80 dark:bg-muted-950 rounded-2xl"
  >
    <div class="relative z-10 flex w-full flex-col gap-6 lg:flex-row">
      <div class="lg:landscape:mx-0 mx-auto xl:landscape:mx-0">
        <BaseAvatar
          :src="data.avatar"
          :badge-src="data.badgeSrc"
          size="2xl"
          alt="avatar"
        />
      </div>
      <div class="text-center lg:text-start">
        <BaseHeading
          as="h2"
          size="xl"
          weight="semibold"
          class="flex items-center justify-center gap-2 lg:justify-start"
        >
          <span class="text-muted-800 dark:text-white">{{ data.username }}</span>
          <Icon v-if="data.verified" name="uiw:star-on" class="size-4 text-yellow-400" />
        </BaseHeading>
        <span class="text-muted-600 dark:text-muted-400 mb-4 block font-sans text-base">
          {{ data.realName }}
        </span>
        <div class="mb-6 flex items-center gap-x-6">
          <div
            v-for="stat in data.stats"
            :key="stat.label"
            class="flex flex-1 flex-col gap-x-2 font-sans lg:flex-auto lg:flex-row"
          >
            <span class="text-muted-800 dark:text-muted-100 font-semibold">
              {{ stat.value }}
            </span>
            <span class="text-muted-400 text-xs sm:text-sm lg:text-base">
              {{ stat.label }}
            </span>
          </div>
        </div>
        <BaseProse class="prose-sm mx-auto mb-6 max-w-xl lg:mx-0">
          <p class="line-clamp-3">
            {{ data.bio }}
            <a v-if="data.bioLink" :href="data.bioLink.href">{{ data.bioLink.text }}</a>
          </p>
        </BaseProse>
        <div class="flex justify-center gap-4 lg:justify-start">
          <div
            v-for="badge in data.badges"
            :key="badge.icon"
            class="dark:bg-muted-700 shadow-muted-300/40 dark:shadow-muted-900/20 flex size-10 items-center justify-center rounded-full bg-white shadow-xl"
          >
            <div
              class="flex size-8 items-center justify-center rounded-full"
              :class="badge.bgColor"
            >
              <Icon :name="badge.icon" class="size-5" :class="badge.iconColor" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Action -->
    <div class="mt-4 flex shrink-0 justify-center lg:ms-auto lg:mt-0 lg:justify-start">
      <BaseButton class="mx-auto w-52 lg:mx-0 lg:w-auto">
        <Icon name="lucide:plus" class="size-4" />
        <span>{{ data.ctaText }}</span>
      </BaseButton>
    </div>
  </div>
</template>
