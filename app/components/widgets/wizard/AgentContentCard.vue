<script setup lang="ts">
import type { WizardAgentContentCardData } from '~/types/widgets'

interface Props {
  data: WizardAgentContentCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" :class="data.cardClass || 'group relative p-6'">
    <!-- Edit Button -->
    <div v-if="data.editButton" class="absolute end-3 top-3 z-10">
      <BaseButton
        size="icon-sm"
        rounded="full"
        :class="data.editButton.class || 'hover:border-primary-500 hover:text-primary-500 dark:hover:border-primary-500 dark:hover:text-primary-500 pointer-events-none opacity-0 group-hover:pointer-events-auto group-hover:opacity-100'"
        :to="data.editButton.to"
      >
        <Icon name="lucide:edit-2" class="pointer-events-none size-3" />
      </BaseButton>
    </div>

    <!-- Header -->
    <BaseHeading
      v-if="data.title && data.titleType !== 'text'"
      size="xs"
      class="mb-4 uppercase"
      :class="data.titleClass"
    >
      <span class="text-muted-500 dark:text-muted-400">{{ data.title }}</span>
    </BaseHeading>

    <BaseText
      v-if="data.title && data.titleType === 'text'"
      size="sm"
      weight="semibold"
      :class="data.titleClass || 'text-muted-800 dark:text-muted-200 mb-4 uppercase tracking-wide'"
    >
      {{ data.title }}
    </BaseText>

    <!-- Content Area -->
    <div v-if="data.content" :class="data.contentClass">
      <!-- Text Content -->
      <BaseParagraph
        v-if="data.contentType === 'text'"
        size="sm"
        :class="data.textClass || 'text-muted-500 dark:text-muted-400'"
      >
        {{ data.content }}
      </BaseParagraph>

      <!-- Code Content -->
      <div v-else-if="data.contentType === 'code'" class="p-4 bg-muted-800 dark:bg-muted-950 rounded-lg">
        <BaseText
          size="xs"
          class="text-green-400 font-mono leading-relaxed whitespace-pre-line"
        >
          {{ data.content }}
        </BaseText>
      </div>

      <!-- Badge List Content -->
      <div v-else-if="data.contentType === 'badges'" class="space-y-4">
        <div v-for="section in data.badgeSections" :key="section.label">
          <BaseText size="xs" class="text-muted-500 mb-2">
            {{ section.label }}{{ section.count ? ` (${section.count})` : '' }}:
          </BaseText>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="badge in section.items"
              :key="badge"
              :class="section.badgeClass || 'inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-500/20 dark:text-blue-300'"
            >
              {{ badge }}
            </span>
          </div>
        </div>
      </div>

      <!-- List Content -->
      <div v-else-if="data.contentType === 'list' && data.listItems" class="space-y-4">
        <div
          v-for="item in data.listItems"
          :key="item.name"
          class="flex items-center gap-2"
        >
          <BaseAvatar v-if="item.picture" size="xs" :src="item.picture" />
          <img
            v-else-if="item.logo"
            :src="item.logo"
            class="size-8"
            alt=""
          >
          <div class="flex flex-col">
            <h3 class="text-muted-800 dark:text-muted-100 font-sans text-sm font-medium">
              {{ item.name }}
            </h3>
            <p v-if="item.role" class="text-muted-500 dark:text-muted-400 font-sans text-xs">
              {{ item.role }}
            </p>
            <p v-if="item.description" class="text-muted-400 text-xs">
              {{ item.description }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else>
      <BaseParagraph size="sm" class="text-muted-400">
        {{ data.emptyMessage || 'No content available' }}
      </BaseParagraph>
    </div>

    <!-- Footer Text -->
    <BaseText
      v-if="data.footerText"
      size="xs"
      :class="data.footerClass || 'text-muted-500 mt-2'"
    >
      {{ data.footerText }}
    </BaseText>
  </BaseCard>
</template>
