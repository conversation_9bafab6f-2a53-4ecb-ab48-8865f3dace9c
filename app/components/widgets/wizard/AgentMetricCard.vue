<script setup lang="ts">
import type { WizardAgentMetricCardData } from '~/types/widgets'

interface Props {
  data: WizardAgentMetricCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" :class="data.cardClass || 'group relative p-6'">
    <!-- Edit Button -->
    <div v-if="data.editButton" class="absolute end-3 top-3 z-10">
      <BaseButton
        size="icon-sm"
        rounded="full"
        :class="data.editButton.class || 'hover:border-primary-500 hover:text-primary-500 dark:hover:border-primary-500 dark:hover:text-primary-500 pointer-events-none opacity-0 group-hover:pointer-events-auto group-hover:opacity-100'"
        :to="data.editButton.to"
      >
        <Icon name="lucide:edit-2" class="pointer-events-none size-3" />
      </BaseButton>
    </div>

    <div class="flex flex-col items-center justify-center text-center" :class="data.heightClass">
      <div>
        <span
          class="text-muted-800 dark:text-muted-100 block font-sans font-semibold mb-2"
          :class="data.valueClass || (data.valueSize === 'lg' ? 'text-2xl' : 'text-xl')"
        >
          {{ data.value || data.fallback || '--' }}
        </span>
        <BaseHeading size="xs" class="scale-90 uppercase" :class="data.labelClass || 'mb-4'">
          <span class="text-muted-500 dark:text-muted-400">{{ data.label }}</span>
        </BaseHeading>
      </div>
    </div>
  </BaseCard>
</template>
