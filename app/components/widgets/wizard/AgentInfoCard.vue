<script setup lang="ts">
import type { WizardAgentInfoCardData } from '~/types/widgets'

interface Props {
  data: WizardAgentInfoCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="group relative p-6">
    <!-- Edit Button -->
    <div v-if="data.editButton" class="absolute end-3 top-3 z-10">
      <BaseButton
        size="icon-sm"
        rounded="full"
        :class="data.editButton.class || 'hover:border-primary-500 hover:text-primary-500 dark:hover:border-primary-500 dark:hover:text-primary-500 pointer-events-none opacity-0 group-hover:pointer-events-auto group-hover:opacity-100'"
        :to="data.editButton.to"
      >
        <Icon name="lucide:edit-2" class="pointer-events-none size-3" />
      </BaseButton>
    </div>

    <div class="flex w-full items-center gap-2">
      <!-- Avatar or Conditional Icons -->
      <BaseAvatar
        v-if="data.avatarSrc"
        :src="data.avatarSrc"
        size="sm"
        class="bg-muted-100 dark:bg-muted-700"
      />

      <!-- Conditional Icon Display -->
      <template v-else-if="data.iconConditions">
        <BaseIconBox
          v-for="condition in data.iconConditions"
          v-show="condition.condition"
          :key="condition.icon"
          size="sm"
          :class="condition.iconClass"
          variant="none"
          rounded="none"
          mask="blob"
        >
          <Icon :name="condition.icon" class="size-5" />
        </BaseIconBox>
      </template>

      <!-- Default Single Icon -->
      <BaseIconBox
        v-else
        size="sm"
        :class="data.iconClass || 'bg-primary-500/10 text-primary-600'"
        variant="none"
        rounded="none"
        mask="blob"
      >
        <Icon :name="data.icon" class="size-5" />
      </BaseIconBox>

      <div>
        <div class="text-muted-400 text-xs">
          <span>{{ data.label }}</span>
        </div>
        <div class="text-muted-800 dark:text-muted-100 text-sm font-medium" :class="data.value?.includes?.('capitalize') ? 'capitalize' : ''">
          <span>{{ data.value || data.fallback || 'Not specified' }}</span>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
