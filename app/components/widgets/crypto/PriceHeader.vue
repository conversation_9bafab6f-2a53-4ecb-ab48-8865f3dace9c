<script setup lang="ts">
import type { CryptoPriceHeaderData } from '~/types/widgets'

interface Props {
  data: CryptoPriceHeaderData
  modelValue?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'periodChange': [period: string]
}>()

const activePeriod = computed({
  get: () => props.modelValue || props.data.defaultPeriod || 'week',
  set: (value: string) => emit('update:modelValue', value),
})

function handlePeriodChange(period: string) {
  activePeriod.value = period
  emit('periodChange', period)
}
</script>

<template>
  <div class="mb-6 flex flex-col justify-between gap-y-4 sm:flex-row sm:items-center">
    <div>
      <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
        {{ props.data.description }}
      </BaseParagraph>
      <BaseHeading
        size="3xl"
        weight="bold"
        class="text-muted-900 dark:text-white"
      >
        <span
          :class="`after:${props.data.changeColor} after:relative after:-end-2 after:-top-3 after:text-sm after:content-['${props.data.changeText}']`"
        >
          {{ formatPrice(props.data.price) }}
        </span>
      </BaseHeading>
    </div>
    <div class="flex gap-2 sm:justify-end">
      <BaseButton
        v-for="period in props.data.periods"
        :key="period.value"
        size="sm"
        rounded="md"
        :variant="activePeriod === period.value ? 'primary' : 'default'"
        @click="handlePeriodChange(period.value)"
      >
        {{ period.label }}
      </BaseButton>
    </div>
  </div>
</template>
