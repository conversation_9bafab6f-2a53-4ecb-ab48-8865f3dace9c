<script setup lang="ts">
import type { CryptoContentWidgetData } from '~/types/widgets'

interface Props {
  data: CryptoContentWidgetData
}

interface Emits {
  (e: 'view-all'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

function handleViewAll() {
  emit('view-all')
  // If there's a custom handler, call it
  if (props.data.onViewAll) {
    props.data.onViewAll()
  }
}
</script>

<template>
  <BaseCard class="p-6">
    <!-- Header with title and view all button -->
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-muted-800 dark:text-muted-100">
        {{ data.title }}
      </h3>
      <BaseButtonAction
        color="muted"
        size="sm"
        @click="handleViewAll"
      >
        {{ data.viewAllLabel || 'View all' }}
      </BaseButtonAction>
    </div>

    <!-- Content slot for dynamic component -->
    <div class="content-wrapper">
      <component
        :is="data.contentComponent"
        v-bind="data.contentProps"
      />
    </div>
  </BaseCard>
</template>
