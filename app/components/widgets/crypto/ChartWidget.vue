<script setup lang="ts">
import type { CryptoChartWidgetData } from '~/types/widgets'

interface Props {
  data: CryptoChartWidgetData
}

defineProps<Props>()

defineEmits<{
  action: []
}>()
</script>

<template>
  <BaseCard :class="data.cardClass || 'p-4 md:p-6'">
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseButton
        v-if="data.action"
        size="sm"
        rounded="md"
        @click="$emit('action')"
      >
        {{ data.action.label }}
      </BaseButton>
    </div>
    <component
      :is="data.chartComponent"
      :class="data.chartClass"
      v-bind="data.chartProps"
    />
  </BaseCard>
</template>
