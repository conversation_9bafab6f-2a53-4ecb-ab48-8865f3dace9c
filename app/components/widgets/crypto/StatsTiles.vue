<script setup lang="ts">
import type { CryptoStatsTilesData } from '~/types/widgets'

interface Props {
  data: CryptoStatsTilesData
}

const props = defineProps<Props>()
</script>

<template>
  <div class="grid gap-4 sm:grid-cols-3">
    <div
      v-for="(stat, index) in data.stats"
      :key="index"
      class="relative"
    >
      <BaseCard class="space-y-1 p-4">
        <BaseParagraph
          size="xs"
          class="text-muted-600 dark:text-muted-400"
        >
          {{ stat.label }}
        </BaseParagraph>
        <BaseHeading
          size="lg"
          weight="semibold"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ stat.formattedValue || formatPrice(stat.value) }}{{ stat.suffix || '' }}</span>
        </BaseHeading>
      </BaseCard>
    </div>
  </div>
</template>
