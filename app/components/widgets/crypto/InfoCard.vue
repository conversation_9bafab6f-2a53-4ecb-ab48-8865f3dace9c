<script setup lang="ts">
import type { CryptoInfoCardData } from '~/types/widgets'

interface Props {
  data: CryptoInfoCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard class="p-6">
    <div class="flex items-start gap-4">
      <!-- Icon Section -->
      <div class="flex-shrink-0">
        <div class="h-12 w-12 bg-primary-100 rounded-full flex items-center justify-center">
          <Icon :name="data.icon" class="h-6 w-6 text-primary-500" />
        </div>
      </div>

      <!-- Content Section -->
      <div class="flex-1">
        <h3 class="text-lg font-semibold text-muted-800 dark:text-muted-100 mb-1">
          {{ data.title }}
        </h3>
        <p class="text-sm text-muted-500 dark:text-muted-400 mb-4 leading-relaxed">
          {{ data.description }}
        </p>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-2">
          <BaseButton
            v-for="action in data.actions"
            :key="action.label"
            :color="action.color"
            :variant="action.variant"
            size="sm"
            class="text-xs"
          >
            {{ action.label }}
          </BaseButton>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
