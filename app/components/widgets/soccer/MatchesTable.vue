<script setup lang="ts">
import type { SoccerMatchesTableData } from '~/types/widgets'

export interface Props {
  data: SoccerMatchesTableData
}

defineProps<Props>()

function capitalize(str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
</script>

<template>
  <BaseCard rounded="lg" class="p-8">
    <div class="mb-6 flex gap-x-8 font-sans">
      <button
        v-for="filter in data.filters"
        :key="filter.value"
        class="text-sm pb-4 border-b-2" :class="[
          filter.active
            ? 'border-primary-500 text-muted-800 dark:text-muted-100'
            : 'text-muted-400 dark:text-muted-500 border-transparent',
        ]"
      >
        {{ filter.label }}
      </button>
    </div>
    <div class="overflow-x-auto">
      <div class="inline-block min-w-full align-middle">
        <div class="overflow-hidden">
          <table
            class="min-w-full table-fixed divide-y divide-muted-200 text-start dark:divide-muted-800"
          >
            <thead>
              <tr>
                <th class="py-3 text-start font-sans text-xs uppercase">
                  Time
                </th>
                <th
                  class="xs:px-8 py-3 text-center font-sans text-xs uppercase"
                >
                  Match
                </th>
                <th class="xs:px-4 py-3 text-start font-sans text-xs uppercase">
                  Stadium
                </th>
                <th class="xs:px-4 py-3 font-sans text-xs uppercase">
                  Details
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="match in data.matches" :key="match.id">
                <td class="py-4 align-middle">
                  <BaseTooltip :content="capitalize(match.status)">
                    <div class="text-muted-500 dark:text-muted-400 flex items-center gap-2 font-sans text-sm">
                      <div
                        class="size-2 rounded-full"
                        :class="
                          match.status === 'live'
                            ? 'bg-rose-500'
                            : 'bg-muted-300 dark:bg-muted-600'
                        "
                      />
                      <span>{{ match.time }}</span>
                    </div>
                  </BaseTooltip>
                </td>
                <td class="xs:px-8 py-4">
                  <div class="flex items-center justify-center gap-4">
                    <div
                      class="flex w-[120px] items-center justify-end gap-2"
                    >
                      <BaseHeading
                        as="h4"
                        size="sm"
                        weight="medium"
                        lead="none"
                        class="text-muted-600 dark:text-muted-400"
                      >
                        <span>{{ match.match.home.name }}</span>
                      </BaseHeading>
                      <img
                        class="size-8"
                        :src="match.match.home.logo"
                        :alt="match.match.home.name"
                      >
                    </div>
                    <div
                      class="text-muted-800 dark:text-muted-100 flex items-center justify-center gap-2 text-center font-sans"
                    >
                      <span class="text-sm font-bold">
                        {{
                          match.match.home.score !== undefined
                            ? match.match.home.score
                            : '-'
                        }}
                      </span>
                      <span class="text-xs font-bold">:</span>
                      <span class="text-sm font-bold">
                        {{
                          match.match.away.score !== undefined
                            ? match.match.away.score
                            : '-'
                        }}
                      </span>
                    </div>
                    <div class="flex w-[120px] items-center gap-2">
                      <img
                        class="size-8"
                        :src="match.match.away.logo"
                        :alt="match.match.away.name"
                      >
                      <BaseHeading
                        as="h4"
                        size="sm"
                        weight="medium"
                        lead="none"
                        class="text-muted-600 dark:text-muted-400"
                      >
                        <span>{{ match.match.away.name }}</span>
                      </BaseHeading>
                    </div>
                  </div>
                </td>
                <td class="xs:px-4 py-4">
                  <div
                    class="text-muted-500 dark:text-muted-400 font-sans text-sm"
                  >
                    <span class="whitespace-nowrap">
                      {{ match.stadium }}
                    </span>
                  </div>
                </td>
                <td class="xs:px-4 py-4 text-end">
                  <BaseButton size="icon-sm" variant="muted">
                    <Icon
                      name="lucide:more-horizontal"
                      class="size-4"
                    />
                  </BaseButton>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
