<script setup lang="ts">
import type { SoccerContestPromoData } from '~/types/widgets'

export interface Props {
  data: SoccerContestPromoData
}

defineProps<Props>()
</script>

<template>
  <div class="col-span-12">
    <div
      class="flex flex-col items-center rounded-2xl sm:flex-row" :class="[
        data.bgClass || 'bg-primary-800',
      ]"
    >
      <div class="mt-6 grow pe-6 ps-6 sm:mt-0 sm:pe-0 sm:ps-10">
        <div class="pb-4 text-center sm:pb-0 sm:text-start">
          <BaseHeading tag="h2" size="xl" class="text-white mb-2">
            <span>{{ data.title }}</span>
          </BaseHeading>
          <BaseParagraph size="sm" class="text-white/90 max-w-sm mb-3">
            <span>{{ data.description }}</span>
          </BaseParagraph>
          <div>
            <BaseButton
              size="sm"
              variant="default"
              class="w-full sm:w-auto"
            >
              <span>{{ data.ctaText }}</span>
              <Icon
                v-if="data.ctaIcon"
                :name="data.ctaIcon"
                class="size-4"
              />
            </BaseButton>
          </div>
        </div>
      </div>
      <div class="relative h-[280px] w-[320px] shrink-0 sm:h-[225px]">
        <img
          class="pointer-events-none absolute bottom-0 end-6 sm:-end-4"
          :src="data.illustration"
          :alt="data.illustrationAlt"
        >
      </div>
    </div>
  </div>
</template>
