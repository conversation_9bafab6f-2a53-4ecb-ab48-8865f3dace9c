<script setup lang="ts">
import type { SoccerLiveMatchData } from '~/types/widgets'

export interface Props {
  data: SoccerLiveMatchData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-4 md:p-8">
    <!-- Title -->
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.tournament }}</span>
      </BaseHeading>
    </div>

    <!-- Subtitle -->
    <div class="mb-6 flex items-center justify-between">
      <div>
        <BaseHeading
          as="h4"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-900 mb-1 dark:text-white"
        >
          <span>{{ data.group }}</span>
        </BaseHeading>
        <BaseParagraph size="xs">
          <span class="text-muted-600 dark:text-muted-400">{{ data.matchDescription }}</span>
        </BaseParagraph>
      </div>
      <div v-if="data.liveTag">
        <BaseTag
          rounded="full"
          variant="primary"
          class="inline-flex items-center justify-center gap-1"
          size="sm"
        >
          <Icon :name="data.liveTag.icon" class="size-3" />
          <span>{{ data.liveTag.text }}</span>
        </BaseTag>
      </div>
    </div>

    <!-- Results -->
    <div class="mb-6 flex items-center justify-between">
      <div class="flex flex-col text-center">
        <img
          class="mx-auto size-12"
          :src="data.match.home.logo"
          :alt="`${data.match.home.name} logo`"
        >
        <BaseHeading
          as="h4"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-600 dark:text-muted-400 mt-2"
        >
          <span>{{ data.match.home.name }}</span>
        </BaseHeading>
      </div>
      <div
        class="text-muted-900 dark:text-muted-100 flex items-center justify-center gap-2 text-center font-sans"
      >
        <span class="text-5xl font-bold">
          {{ data.match.home.score !== undefined ? data.match.home.score : '-' }}
        </span>
        <span class="text-4xl font-bold">:</span>
        <span class="text-5xl font-bold">
          {{ data.match.away.score !== undefined ? data.match.away.score : '-' }}
        </span>
      </div>
      <div class="flex flex-col text-center">
        <img
          class="mx-auto size-12"
          :src="data.match.away.logo"
          :alt="`${data.match.away.name} logo`"
        >
        <BaseHeading
          as="h4"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-600 dark:text-muted-400 mt-2"
        >
          <span>{{ data.match.away.name }}</span>
        </BaseHeading>
      </div>
    </div>

    <!-- Action -->
    <div>
      <BaseButton
        variant="primary"
        rounded="lg"
        class="h-12! w-full"
      >
        <span>{{ data.ctaText }}</span>
      </BaseButton>
    </div>
  </BaseCard>
</template>
