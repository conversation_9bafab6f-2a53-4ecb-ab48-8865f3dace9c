<script setup lang="ts">
import type { SoccerAvailableLeaguesData } from '~/types/widgets'

export interface Props {
  data: SoccerAvailableLeaguesData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-4 md:p-8">
    <!-- Title -->
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <component
      :is="data.listComponent"
      v-bind="data.listProps"
    />
  </BaseCard>
</template>
