<script setup lang="ts">
import type { HobbyActivityListData } from '~/types/widgets'
import { getRandomColor } from '~/utils/colors'

defineProps<{
  data: HobbyActivityListData
}>()
</script>

<template>
  <div class="col-span-12 sm:col-span-6">
    <div class="my-4">
      <BaseHeading
        as="h2"
        size="lg"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseParagraph size="sm">
        <span class="text-muted-600 dark:text-muted-500">
          {{ data.subtitle }}
        </span>
      </BaseParagraph>
    </div>
    <!-- List -->
    <div class="space-y-2">
      <!-- List item -->
      <BaseCard
        v-for="(item, index) in data.activities"
        :key="index"
        rounded="lg"
        class="flex items-center gap-3 p-3"
      >
        <div
          class="flex size-10 shrink-0 items-center justify-center rounded-full"
          :class="data.getRandomColor ? data.getRandomColor() : getRandomColor()"
        >
          <Icon :name="item.icon" class="size-5" />
        </div>
        <div>
          <BaseHeading
            as="h4"
            size="sm"
            weight="medium"
            lead="tight"
            class="text-muted-800 dark:text-white"
          >
            <span>{{ item.name }}</span>
          </BaseHeading>
          <BaseParagraph size="xs">
            <span class="text-muted-400">
              {{ item.date }}
            </span>
          </BaseParagraph>
        </div>
        <div class="ms-auto flex items-center">
          <BaseButton
            rounded="lg"
            size="icon-md"
            variant="muted"
            class="scale-75"
          >
            <Icon name="lucide:arrow-right" class="size-5" />
          </BaseButton>
        </div>
      </BaseCard>
    </div>
  </div>
</template>
