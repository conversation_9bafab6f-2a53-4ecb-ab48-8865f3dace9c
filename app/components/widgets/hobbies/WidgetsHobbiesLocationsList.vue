<script setup lang="ts">
import type { HobbyLocationsListData } from '~/types/widgets'

defineProps<{
  data: HobbyLocationsListData
}>()
</script>

<template>
  <BaseCard class="p-4 md:p-6" rounded="lg">
    <div class="mb-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <div class="space-y-1">
      <!-- Item -->
      <NuxtLink
        v-for="(location, index) in data.locations"
        :key="index"
        :to="location.link"
        class="hover:bg-muted-100 dark:hover:bg-muted-700/50 flex items-center gap-3 rounded-xl px-2 py-3 transition-colors duration-300"
      >
        <img
          :src="location.image"
          alt="Location thumbnail"
          class="max-w-[48px]"
        >
        <div>
          <BaseHeading
            as="h4"
            size="sm"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ location.name }}</span>
          </BaseHeading>
          <BaseParagraph size="xs">
            <span class="text-muted-600 dark:text-muted-400">{{ location.location }} · {{ location.level }}</span>
          </BaseParagraph>
        </div>
        <div class="ms-auto flex items-center">
          <BaseButton
            rounded="lg"
            size="icon-md"
            variant="muted"
            class="scale-75"
          >
            <Icon name="lucide:arrow-right" class="size-5" />
          </BaseButton>
        </div>
      </NuxtLink>
    </div>
  </BaseCard>
</template>
