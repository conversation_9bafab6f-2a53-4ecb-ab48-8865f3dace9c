<script setup lang="ts">
import type { HobbyCategoryData } from '~/types/widgets'

defineProps<{
  categories: HobbyCategoryData[]
}>()
</script>

<template>
  <div class="grid grid-cols-12 gap-4">
    <div
      v-for="(category, index) in categories"
      :key="index"
      class="col-span-6 sm:col-span-3"
    >
      <NuxtLink
        :to="category.link"
        class="group relative flex w-full flex-col overflow-hidden rounded-2xl"
      >
        <img
          class="h-80 w-full object-cover object-center"
          :src="category.image"
          alt="Hobby cover"
        >
        <div
          class="bg-muted-900 absolute inset-0 z-10 size-full opacity-0 transition-opacity duration-300 group-hover:opacity-50"
        />
        <div
          class="absolute inset-0 z-20 flex size-full flex-col justify-between p-6"
        >
          <div class="flex items-center justify-between">
            <h3
              class="-translate-y-2 font-sans tracking-wider text-white opacity-0 transition-all delay-100 duration-300 group-hover:translate-y-0 group-hover:opacity-100"
            >
              {{ category.title }}
            </h3>
            <Icon
              :name="category.icon"
              class="size-5 -translate-y-2 text-white opacity-0 transition-all delay-300 duration-300 group-hover:translate-y-0 group-hover:opacity-100"
            />
          </div>
          <div class="flex items-center justify-between">
            <h3
              class="translate-y-2 font-sans text-sm text-white underline underline-offset-4 opacity-0 transition-all delay-500 duration-300 group-hover:translate-y-0 group-hover:opacity-100"
            >
              {{ category.ctaText || 'View activities' }}
            </h3>
            <Icon
              name="lucide:arrow-right"
              class="size-4 translate-y-2 text-white opacity-0 transition-all delay-700 duration-300 group-hover:translate-y-0 group-hover:opacity-100"
            />
          </div>
        </div>
      </NuxtLink>
    </div>
  </div>
</template>
