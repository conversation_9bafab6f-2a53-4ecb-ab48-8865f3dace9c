<script setup lang="ts">
import type { CompanyTeamTableData } from '~/types/widgets'

interface Props {
  data: CompanyTeamTableData
}

const props = defineProps<Props>()

const selected = ref<string[]>([])

const isAllVisibleSelected = computed(() => {
  return selected.value.length === props.data.team?.length
})

function toggleAllVisibleSelection() {
  if (isAllVisibleSelected.value) {
    selected.value = []
  }
  else {
    selected.value = props.data.team?.map(item => item.id) ?? []
  }
}
</script>

<template>
  <TairoTable rounded="md">
    <template #header>
      <TairoTableHeading uppercase class="p-4">
        <div class="flex items-center">
          <BaseCheckbox
            :model-value="selected.length > 0 && !isAllVisibleSelected ? 'indeterminate' : isAllVisibleSelected"
            name="team-table-main"
            rounded="sm"
            @click="toggleAllVisibleSelection"
          />
        </div>
      </TairoTableHeading>
      <TairoTableHeading uppercase class="p-4">
        Collaborator
      </TairoTableHeading>
      <TairoTableHeading uppercase class="p-4">
        Expertise
      </TairoTableHeading>
      <TairoTableHeading uppercase class="p-4">
        Rate
      </TairoTableHeading>
      <TairoTableHeading uppercase class="p-4">
        Status
      </TairoTableHeading>
      <TairoTableHeading uppercase class="p-4">
        <span class="sr-only">View</span>
      </TairoTableHeading>
    </template>

    <BaseCheckboxGroup v-model="selected" as-child>
      <TairoTableRow v-for="member in props.data.team" :key="member.id">
        <TairoTableCell class="p-4">
          <div class="flex items-center">
            <BaseCheckbox :value="member.id" />
          </div>
        </TairoTableCell>
        <TairoTableCell class="p-4">
          <div class="flex items-center">
            <BaseAvatar :src="member.src" size="xs" />
            <div class="ms-3 leading-none">
              <h4 class="font-heading text-sm font-semibold">
                {{ member.name }}
              </h4>
              <p class="text-muted-600 dark:text-muted-400 font-sans text-xs">
                {{ member.role }}
              </p>
            </div>
          </div>
        </TairoTableCell>
        <TairoTableCell light class="p-4">
          {{ member.expertise }}
        </TairoTableCell>
        <TairoTableCell>${{ member.rate }}/hour</TairoTableCell>
        <TairoTableCell class="p-4">
          <BaseTag
            v-if="member.status === 'Available'"
            rounded="full"
            variant="none"
            class="font-medium bg-green-500/10 text-green-500"
          >
            {{ member.status }}
          </BaseTag>
          <BaseTag
            v-else-if="member.status === 'New'"
            rounded="full"
            variant="none"
            class="font-medium bg-blue-500/10 text-blue-500"
          >
            {{ member.status }}
          </BaseTag>
          <BaseTag
            v-else-if="member.status === 'Hired'"
            rounded="full"
            class="font-medium"
          >
            {{ member.status }}
          </BaseTag>
        </TairoTableCell>
        <TairoTableCell class="flex justify-end p-4">
          <BaseButton size="sm" rounded="md">
            {{ props.data.actionLabel || 'View' }}
          </BaseButton>
        </TairoTableCell>
      </TairoTableRow>
    </BaseCheckboxGroup>
  </TairoTable>
</template>
