<script setup lang="ts">
import type { CompanyPendingTicketsData } from '~/types/widgets'

interface Props {
  data: CompanyPendingTicketsData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <!-- Title -->
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <LinkArrow
        v-if="data.viewAllLink"
        :to="data.viewAllLink.href"
        :label="data.viewAllLink.label"
        class="me-1"
      />
    </div>
    <component :is="data.contentComponent" v-bind="data.contentProps" />
  </BaseCard>
</template>
