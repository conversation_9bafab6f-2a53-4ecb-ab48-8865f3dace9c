<script setup lang="ts">
import type { CompanyStatsHeaderData } from '~/types/widgets'

interface Props {
  data: CompanyStatsHeaderData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6">
    <div
      class="divide-muted-200 dark:divide-muted-800 flex flex-wrap sm:flex-nowrap w-full items-center sm:divide-x"
    >
      <!-- Stats Items -->
      <div
        v-for="(stat, index) in data.stats"
        :key="index"
        class="w-1/2 sm:w-auto sm:flex-1"
      >
        <div class="flex flex-col p-4 text-center sm:py-0">
          <Icon
            :name="stat.icon"
            class="text-primary-500 mx-auto size-8 mb-1"
          />
          <h4
            class="text-muted-900 dark:text-muted-100 font-sans text-xl font-semibold"
          >
            {{ stat.value }}
          </h4>
          <p class="text-muted-600 dark:text-muted-400 font-sans text-xs">
            {{ stat.label }}
          </p>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
