<script setup lang="ts">
import type { ListTitledData } from '~/types/widgets'

interface Props {
  data: ListTitledData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-4 md:p-6">
    <!-- Title -->
    <div class="mb-8 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseButton
        variant="default"
        size="sm"
        rounded="md"
      >
        {{ data.actionLabel || 'View all' }}
      </BaseButton>
    </div>
    <component
      :is="data.listComponent"
      v-bind="data.listProps"
      :rounded="data.rounded"
      :color="data.color"
    />
  </BaseCard>
</template>
