<script setup lang="ts">
interface TeamMember {
  src?: string
  text?: string
  bgClass?: string
  textClass?: string
}

interface TeamEfficiencyData {
  title: string
  members: TeamMember[]
}

withDefaults(defineProps<{
  teamData?: TeamEfficiencyData
}>(), {
  teamData: () => ({
    title: 'Team Efficiency',
    members: [
      { src: '/img/avatars/4.svg' },
      { text: 'H', bgClass: 'bg-yellow-400/10 dark:bg-yellow-400/20', textClass: 'text-yellow-600' },
      { src: '/img/avatars/3.svg' },
    ],
  }),
})
</script>

<template>
  <BaseCard rounded="md" class="relative flex flex-col h-full p-6">
    <div class="mb-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="semibold"
        lead="tight"
        class="text-muted-800 dark:text-white"
      >
        <span>{{ teamData.title }}</span>
      </BaseHeading>
    </div>
    <div
      class="absolute inset-x-0 top-24 flex items-center justify-center gap-4"
    >
      <BaseAvatar
        v-for="(member, index) in teamData.members"
        :key="index"
        :src="member.src"
        :text="member.text"
        :class="member.bgClass ? `${member.bgClass} ${member.textClass}` : ''"
      />
    </div>
    <div class="mt-auto">
      <ChartRadialGaugeAlt />
    </div>
  </BaseCard>
</template>
