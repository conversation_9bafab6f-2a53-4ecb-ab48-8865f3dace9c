<script setup lang="ts">
import type { PersonalScoreData } from '~/types/widgets'

interface Props {
  data: PersonalScoreData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="flex flex-col p-4 md:p-6">
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <div class="py-16">
      <component :is="data.chartComponent" class="-mt-14" />
    </div>
    <div class="mt-auto text-center">
      <BaseParagraph size="sm">
        <span class="text-muted-500 dark:text-muted-400">
          {{ data.description }}
        </span>
      </BaseParagraph>
    </div>
  </BaseCard>
</template>
