<script setup lang="ts">
interface MetricItem {
  label: string
  value: string | number
}

interface SummaryCardConfig {
  title: string
  metrics?: MetricItem[]
}

withDefaults(defineProps<{ config?: SummaryCardConfig }>(), {
  config: () => ({
    title: '',
    metrics: [],
  }),
})
</script>

<template>
  <BaseCard rounded="md" class="h-full flex-col p-4 md:p-6 xl:p-8">
    <div class="mb-8 items-center justify-between sm:flex">
      <BaseHeading
        as="h4"
        size="xs"
        weight="medium"
        lead="none"
        class="text-muted-700 dark:text-muted-100 uppercase"
      >
        {{ config.title }}
      </BaseHeading>
    </div>
    <div v-if="config.metrics?.length" class="border-muted-200 dark:border-muted-800 mb-4 flex justify-between border-b pb-4">
      <div v-for="(m, i) in config.metrics" :key="i">
        <BaseParagraph size="xs" weight="medium" class="text-muted-600 dark:text-muted-400 mb-1">
          {{ m.label }}
        </BaseParagraph>
        <BaseHeading as="h5" size="md">
          {{ m.value }}
        </BaseHeading>
      </div>
    </div>
    <div class="mt-auto sm:px-4">
      <slot />
    </div>
  </BaseCard>
</template>
