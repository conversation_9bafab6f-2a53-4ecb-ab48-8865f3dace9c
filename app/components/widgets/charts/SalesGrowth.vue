<script setup lang="ts">
import type { SalesGrowthData } from '~/types/widgets'

interface Props {
  data: SalesGrowthData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="flex h-full flex-col p-6">
    <div class="mb-5 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <div class="mb-6">
      <ChartRadialGrowth />
    </div>
    <div class="mt-auto">
      <div class="flex justify-center gap-2">
        <Icon :name="data.channel.icon" class="size-8" />
        <div>
          <BaseHeading
            as="h5"
            size="sm"
            weight="medium"
            lead="tight"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ data.channel.name }}</span>
          </BaseHeading>
          <BaseParagraph size="xs">
            <span class="text-muted-600 dark:text-muted-400">{{ data.channel.description }}</span>
          </BaseParagraph>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
