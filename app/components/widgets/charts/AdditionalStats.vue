<script setup lang="ts">
import type { AdditionalStatsData } from '~/types/widgets'

interface Props {
  data: AdditionalStatsData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="flex-1 p-6">
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <div class="flex justify-between">
      <div
        v-for="(stat, index) in data.stats"
        :key="index"
        class="flex flex-col gap-2 text-center w-1/3"
      >
        <component :is="stat.chartComponent" />
        <div class="-mt-6">
          <BaseHeading
            as="h5"
            size="md"
            weight="semibold"
            lead="tight"
            class="text-muted-800 dark:text-white"
          >
            <span>{{ stat.value }}</span>
          </BaseHeading>
          <BaseParagraph size="xs">
            <span class="text-muted-600 dark:text-muted-400">{{ stat.label }}</span>
          </BaseParagraph>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
