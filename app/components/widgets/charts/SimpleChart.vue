<script setup lang="ts">
import type { SimpleChartWidgetData } from '~/types/widgets'

interface Props {
  data: SimpleChartWidgetData
}

defineProps<Props>()
</script>

<template>
  <BaseCard :rounded="data.rounded || 'md'" :class="data.cardClass || 'relative p-4 md:p-6'">
    <div v-if="data.title" :class="data.titleClass || 'mb-6'">
      <BaseHeading
        :as="data.titleTag || 'h3'"
        :size="data.titleSize || 'md'"
        :weight="data.titleWeight || 'medium'"
        :lead="data.titleLead || 'tight'"
        :class="data.titleHeadingClass || 'text-muted-900 dark:text-white'"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <component
      :is="data.chartComponent"
      :class="data.chartClass"
      v-bind="data.chartProps"
    />
  </BaseCard>
</template>
