<script setup lang="ts">
import type { GoalOverviewData } from '~/types/widgets'

interface Props {
  data: GoalOverviewData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="flex h-full flex-col p-6">
    <div class="mb-10 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <div class="mb-6">
      <ChartRadialGoal />
    </div>
    <div class="mt-auto">
      <div
        class="border-muted-200 dark:border-muted-700 flex w-full border-t pt-4 text-center"
      >
        <div
          class="border-muted-200 dark:border-muted-700 flex-1 border-r px-2"
        >
          <span class="text-muted-400 font-sans text-xs">
            Completed
          </span>
          <p
            class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
          >
            {{ data.completed }}
          </p>
        </div>
        <div class="flex-1 px-2">
          <span class="text-muted-400 font-sans text-xs">
            In Progress
          </span>
          <p
            class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium"
          >
            {{ data.inProgress }}
          </p>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
