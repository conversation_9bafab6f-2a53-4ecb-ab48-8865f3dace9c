<script setup lang="ts">
import type { ProfitChartData } from '~/types/widgets'

interface Props {
  data: ProfitChartData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="relative p-6">
    <div class="mb-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <ChartBarMulti />
  </BaseCard>
</template>
