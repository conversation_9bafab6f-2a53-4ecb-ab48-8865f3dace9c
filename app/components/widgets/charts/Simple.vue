<script setup lang="ts">
import type { SimpleChartData } from '~/types/widgets'

interface Props {
  data?: SimpleChartData
  title?: string
  chartComponent?: string
  actionLabel?: string
  actionHref?: string
}

const props = withDefaults(defineProps<Props>(), {})

// Support both data prop and individual props for flexibility
const chartTitle = computed(() => props.title || props.data?.title)
const component = computed(() => props.chartComponent || props.data?.chartComponent)
</script>

<template>
  <BaseCard rounded="lg" class="flex flex-col h-full p-6">
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="semibold"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ chartTitle }}</span>
      </BaseHeading>
      <BaseText
        v-if="actionLabel"
        size="sm"
      >
        <BaseLink :to="actionHref" class="not-hover:text-muted-400">
          {{ actionLabel }}
        </BaseLink>
      </BaseText>
    </div>
    <div class="mt-auto">
      <component :is="component" />
    </div>
  </BaseCard>
</template>
