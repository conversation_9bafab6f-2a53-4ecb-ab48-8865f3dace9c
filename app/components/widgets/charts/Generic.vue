<script setup lang="ts">
import type { GenericChartData } from '~/types/widgets'

interface Props {
  data: GenericChartData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard class="p-6">
    <div class="mb-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="semibold"
        lead="tight"
        class="text-muted-800 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
    </div>
    <component :is="data.chartComponent" v-bind="data.chartProps || {}" />
  </BaseCard>
</template>
