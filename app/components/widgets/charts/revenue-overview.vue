<script setup lang="ts">
interface RevenueOverviewData {
  title?: string
  metrics?: {
    label: string
    value: string
  }[]
  chartComponent?: string
  actionButton?: {
    label: string
    onClick?: () => void
  }
}

withDefaults(defineProps<{
  data?: RevenueOverviewData
}>(), {
  data: () => ({
    title: 'Revenue',
    metrics: [
      { label: 'This month', value: '$75,689' },
      { label: 'Last month', value: '$59,724' },
    ],
    chartComponent: 'ChartLineRevenue',
    actionButton: {
      label: 'Details',
    },
  }),
})
</script>

<template>
  <BaseCard rounded="md" class="p-4 md:p-6 flex flex-col h-full">
    <div class="mb-2 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseButton
        v-if="data.actionButton"
        size="sm"
        rounded="md"
        @click="data.actionButton.onClick"
      >
        {{ data.actionButton.label }}
      </BaseButton>
    </div>
    <div v-if="data.metrics?.length" class="flex gap-8">
      <div v-for="metric in data.metrics" :key="metric.label">
        <span class="text-muted-600 dark:text-muted-400 font-sans text-xs">{{ metric.label }}</span>
        <p class="text-muted-900 dark:text-muted-100 font-sans text-lg font-medium">
          {{ metric.value }}
        </p>
      </div>
    </div>
    <div class="mt-auto">
      <!-- Dynamic chart component rendering -->
      <component :is="data.chartComponent" />
    </div>
  </BaseCard>
</template>
