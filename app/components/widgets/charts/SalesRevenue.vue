<script setup lang="ts">
import type { SalesRevenueData } from '~/types/widgets'

interface Props {
  data: SalesRevenueData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard class="flex-1 p-6">
    <div class="flex items-center justify-center">
      <div class="flex-1">
        <BaseHeading
          as="h3"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-900 mb-4 dark:text-white"
        >
          <span>{{ data.title }}</span>
        </BaseHeading>
        <BaseHeading
          as="h4"
          size="lg"
          weight="medium"
          lead="tight"
          class="text-muted-900 mb-2 dark:text-white"
        >
          <span>{{ data.revenue }}</span>
        </BaseHeading>
        <BaseParagraph size="xs">
          <span class="text-muted-600 dark:text-muted-400">
            {{ data.description }}
          </span>
        </BaseParagraph>
      </div>
      <div class="flex-1">
        <ChartRadialSalesRevenue />
      </div>
    </div>
  </BaseCard>
</template>
