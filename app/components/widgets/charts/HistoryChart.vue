<script setup lang="ts">
import type { HistoryChartData } from '~/types/widgets'

interface Props {
  data: HistoryChartData
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<template>
  <BaseCard rounded="md" class="flex h-full flex-col p-4 md:p-6">
    <div class="mb-4 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <div>
        <NuxtLink
          :to="data.viewReportsLink?.href || '#'"
          class="text-muted-400 hover:text-primary-500 font-sans text-sm underline-offset-4 transition-colors duration-300 hover:underline"
        >
          {{ data.viewReportsLink?.text || 'View reports' }}
        </NuxtLink>
      </div>
    </div>
    <ChartAreaIncomeHistory />
  </BaseCard>
</template>
