<script setup lang="ts">
interface ExpenseReportData {
  title: string
}

withDefaults(defineProps<{
  reportData?: ExpenseReportData
}>(), {
  reportData: () => ({
    title: 'Global expense report',
  }),
})
</script>

<template>
  <BaseCard rounded="md" class="flex flex-col h-full p-6">
    <div class="mb-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="semibold"
        lead="tight"
        class="text-muted-800 dark:text-white"
      >
        <span>{{ reportData.title }}</span>
      </BaseHeading>
    </div>
    <div class="mt-auto">
      <ChartBarProfit />
    </div>
  </BaseCard>
</template>
