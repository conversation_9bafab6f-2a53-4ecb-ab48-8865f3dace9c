<script setup lang="ts">
import type { StockCardData } from '~/types/widgets'

interface Props {
  data: StockCardData
}

defineProps<Props>()
</script>

<template>
  <BaseCard rounded="lg" class="p-4 md:p-6">
    <div class="mb-2 flex items-center justify-between">
      <div class="flex items-center gap-2">
        <span
          class="block size-2 rounded-full"
          :class="data.isOpen ? 'bg-emerald-500' : 'bg-muted-300 dark:bg-muted-700/60'"
        />
        <span class="text-muted-400 font-sans text-xs font-medium">
          {{ data.isOpen ? 'Open to transactions' : 'Closed to transactions' }}
        </span>
      </div>
      <BaseDropdown
        label="Actions"
        placement="bottom-end"
        size="md"
        class="z-20"
      >
        <template #button>
          <BaseButton
            rounded="full"
            size="icon-sm"
            class="shrink-0 text-muted-500 dark:text-muted-400"
          >
            <Icon name="lucide:more-horizontal" class="size-3" />
          </BaseButton>
        </template>
        <BaseDropdownItem
          v-for="action in data.actions"
          :key="action.title"
          :to="action.href"
          :title="action.title"
          :text="action.description"
        >
          <template #start>
            <Icon :name="action.icon" class="me-2 block size-5" />
          </template>
        </BaseDropdownItem>
      </BaseDropdown>
    </div>
    <div class="mb-4">
      <component
        :is="data.chartComponent"
        class="text-muted-200 dark:text-muted-700 mx-auto mb-4"
      />
    </div>
    <div class="flex items-center gap-2">
      <BaseIconBox
        rounded="full"
        size="xs"
        :class="data.company.iconColors"
        variant="none"
      >
        <Icon :name="data.company.icon" class="size-4" />
      </BaseIconBox>
      <div>
        <BaseHeading
          as="h4"
          size="sm"
          weight="semibold"
          lead="snug"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ data.company.symbol }}</span>
        </BaseHeading>
        <BaseParagraph lead="none" size="sm">
          <span class="text-muted-600 dark:text-muted-400">{{ data.company.name }}</span>
        </BaseParagraph>
      </div>
      <div class="ms-auto flex items-center gap-1">
        <span class="text-muted-600 dark:text-muted-400 font-sans text-sm font-medium">
          {{ formatPrice(data.price) }}
        </span>
      </div>
    </div>
  </BaseCard>
</template>
