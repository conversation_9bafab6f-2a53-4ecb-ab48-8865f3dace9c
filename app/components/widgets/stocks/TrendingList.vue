<script setup lang="ts">
import type { StockTrendingListData } from '~/types/widgets'

interface Props {
  data: StockTrendingListData
}

interface Emits {
  (e: 'view-all'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<template>
  <BaseCard rounded="lg" class="py-4 px-2 md:py-6 md:px-4">
    <div class="mb-8 px-2 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseButton
        size="sm"
        rounded="md"
        @click="$emit('view-all')"
      >
        {{ data.viewAllLabel }}
      </BaseButton>
    </div>
    <div class="space-y-2">
      <div
        v-for="item in data.stocks"
        :key="item.id"
        class="py-2 px-2 flex items-center gap-2 hover:bg-muted-200/50 dark:hover:bg-muted-900/50 transition-colors duration-100 rounded-lg"
      >
        <BaseIconBox
          rounded="full"
          size="xs"
          :class="item.iconColors"
          variant="none"
        >
          <Icon :name="item.icon" class="size-3" />
        </BaseIconBox>
        <div>
          <BaseHeading
            as="h4"
            size="sm"
            weight="medium"
            lead="snug"
            class="text-muted-900 dark:text-white"
          >
            <span>{{ item.name }}</span>
          </BaseHeading>
          <BaseParagraph lead="none" size="xs">
            <span class="text-muted-600 dark:text-muted-400">{{ item.company }}</span>
          </BaseParagraph>
        </div>
        <div class="ms-auto flex items-center gap-1">
          <span class="text-muted-600 dark:text-muted-400 font-sans text-sm font-medium">
            {{ formatPrice(item.price) }}
          </span>
        </div>
      </div>
    </div>
  </BaseCard>
</template>
