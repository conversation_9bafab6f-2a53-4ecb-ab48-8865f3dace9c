<script setup lang="ts">
import type { StockProfitEvolutionData } from '~/types/widgets'

interface Props {
  data: StockProfitEvolutionData
}

interface Emits {
  (e: 'view-all'): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<template>
  <BaseCard class="relative p-4 md:p-6 h-full flex flex-col" rounded="lg">
    <div class="mb-6 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ data.title }}</span>
      </BaseHeading>
      <BaseButton
        size="sm"
        rounded="md"
        @click="$emit('view-all')"
      >
        {{ data.viewAllLabel }}
      </BaseButton>
    </div>
    <div class="mt-auto">
      <component :is="data.chartComponent" />
    </div>
  </BaseCard>
</template>
