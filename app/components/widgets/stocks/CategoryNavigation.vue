<script setup lang="ts">
import type { StockCategoryNavigationData } from '~/types/widgets'

interface Props {
  data: StockCategoryNavigationData
}

defineProps<Props>()
</script>

<template>
  <div class="flex justify-around gap-6 overflow-x-auto pb-6 lg:overflow-visible">
    <NuxtLink
      v-for="(category, index) in data.categories"
      :key="index"
      :to="category.href"
      class="group flex flex-1 flex-col text-center"
    >
      <div
        class="nui-mask nui-mask-hexed bg-muted-200 dark:bg-muted-700 mx-auto flex size-16 scale-90 items-center justify-center transition-all duration-300 group-hover:-translate-y-1 group-hover:scale-90"
        :class="category.hoverColors"
      >
        <div
          class="nui-mask nui-mask-hexed dark:bg-muted-800 flex size-16 scale-95 items-center justify-center bg-white"
        >
          <Icon
            :name="category.icon"
            class="size-7" :class="[
              category.iconColor,
            ]"
          />
        </div>
      </div>
      <BaseHeading
        as="h5"
        size="md"
        weight="medium"
        lead="tight"
        class="text-muted-400 dark:text-muted-400 group-hover:text-muted-600 dark:group-hover:text-muted-200"
      >
        <span class="font-sans text-sm">{{ category.name }}</span>
      </BaseHeading>
    </NuxtLink>
  </div>
</template>
