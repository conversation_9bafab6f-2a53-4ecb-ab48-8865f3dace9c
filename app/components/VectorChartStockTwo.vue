<template>
  <svg
    id="stock-chart-2"
    data-vivus
    viewBox="0 0 250 60"
    width="250"
    height="90"
  >
    <path
      vector-effect="non-scaling-stroke"
      d="M209.3,32.3c12.6,0.2,25.9,15.3,40.7,5"
      fill="none"
      stroke-width="2"
      stroke="currentColor"
      stroke-linejoin="miter"
      stroke-linecap="round"
      stroke-miterlimit="3"
    />
    <text
      transform="matrix(1 0 0 1 195 20)"
      stroke="none"
      fill="rgb(30 64 175)"
    >
      -5%
    </text>
    <linearGradient
      id="_lgradient_2"
      x1="0%"
      y1="50%"
      x2="100%"
      y2="50%"
    >
      <stop offset="0" style="stop-color: rgb(30 64 175)" />
      <stop offset="1" style="stop-color: rgb(98 127 222)" />
    </linearGradient>
    <path
      fill="none"
      vector-effect="non-scaling-stroke"
      stroke-width="2"
      stroke="url(#_lgradient_2)"
      stroke-linejoin="miter"
      stroke-linecap="round"
      stroke-miterlimit="3"
      d="M0,20c0,0,13.4,7.4,17.6,20.9S31.8,76,49.8,74.1S83.9,48,100.4,55.2c0,0,27,14.9,38.5,11.9
        5-3,4-13.2,13.4-11c9.4,2.3,10.5-18.4,17.1-15.2c6.5,3.1,8.2,12.5,13.4,6.2s6.5-26.1,25.4-14.8"
    />
    <path
      d="M206.6,32.2c0-1.5,1.2-2.7,2.7-2.7c1.5,0,2.7,1.2,2.7,2.7c0,1.5-1.2,2.7-2.7,2.7
        C207.9,34.9,206.6,33.7,206.6,32.2z"
      fill="rgb(30 64 175)"
    />
  </svg>
</template>
