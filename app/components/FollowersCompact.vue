<script setup lang="ts">
import type { FollowersCompactProps } from '../app/types/ui'

const props = withDefaults(
  defineProps<FollowersCompactProps>(),
  {
    followers: () => [
      {
        id: 1,
        name: '<PERSON>',
        image: '/img/avatars/3.svg',
        username: 'clarke_smith',
        isFollowing: false,
        followers: 1245,
      },
      {
        id: 2,
        name: '<PERSON>',
        image: '/img/avatars/6.svg',
        username: 'john_rowner',
        isFollowing: true,
        followers: 892,
      },
      {
        id: 3,
        name: '<PERSON>',
        image: '/img/avatars/9.svg',
        username: 'maggie_pitts',
        isFollowing: false,
        followers: 2156,
      },
    ],
    showFollowButton: true,
    avatarSize: 'xs',
  },
)
</script>

<template>
  <div>
    <div class="mb-4 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="sm"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>Followers</span>
      </BaseHeading>
      <BaseAvatarGroup
        :avatars="props.followers"
        size="xs"
        :limit="3"
      />
    </div>
    <div>
      <BaseParagraph size="xs">
        <span class="text-muted-600 dark:text-muted-400">
          Great News!
          <NuxtLink
            to="#"
            class="text-primary-500 underline-offset-2 hover:underline"
          >
            Clarke
          </NuxtLink>
          ,
          <NuxtLink
            to="#"
            class="text-primary-500 underline-offset-2 hover:underline"
          >
            John
          </NuxtLink>
          and
          <NuxtLink
            to="#"
            class="text-primary-500 underline-offset-2 hover:underline"
          >
            Maggie
          </NuxtLink>
          are now following you. Take some time to look at their profile.
        </span>
      </BaseParagraph>
    </div>
  </div>
</template>
