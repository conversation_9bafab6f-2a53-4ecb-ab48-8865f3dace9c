<script setup lang="ts" generic="T extends { id: string } = { id: string }">
import type { DataViewConfig, DataViewMode } from '~/types/ui/data-view'
import { useDataViewAdapter } from '~/composables/useDataViewAdapter'
import { useFeatureFlag } from '~/composables/useFeatureFlag'

interface Props {
  /** Collection name for data API operations */
  collectionName: string
  /** DataView configuration object */
  config: DataViewConfig<T>
  /** Display mode: list, grid, or table */
  mode?: DataViewMode
  /** CSS class to apply to the root element */
  class?: string
  /** Feature flag key for controlled rollout */
  featureFlag?: string
  /** Fallback content when feature is disabled */
  fallbackContent?: string
}

interface Emits {
  /** Emitted when data is loaded or refreshed */
  (e: 'data-loaded', data: T[]): void
  /** Emitted when selection changes */
  (e: 'selection-changed', selectedItems: T[]): void
  /** Emitted when a row action is executed */
  (e: 'row-action', action: string, item: T): void
  /** Emitted when a bulk action is executed */
  (e: 'bulk-action', action: string, items: T[]): void
  /** Emitted when filters change */
  (e: 'filters-changed', filters: any[]): void
  /** Emitted when sort changes */
  (e: 'sort-changed', sort: { field: string, direction: 'asc' | 'desc' } | null): void
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'list',
  class: '',
  featureFlag: '',
  fallbackContent: '',
})

const emit = defineEmits<Emits>()

// Feature flag support for safe rollout
const { isEnabled: checkFlag } = useFeatureFlag()
const isEnabled = computed(() => {
  if (!props.featureFlag)
    return true
  return checkFlag(props.featureFlag, false)
})

// Initialize data view adapter with configuration
const adapter = useDataViewAdapter({
  collectionName: props.collectionName,
  config: props.config,
  controllerOptions: {
    enableSelection: props.config.selection?.enabled || false,
    enableFiltering: props.config.filters ? props.config.filters.length > 0 : true,
    enableSorting: props.config.sorting?.enabled !== false,
    enablePagination: props.config.pagination?.enabled !== false,
    enableSearch: props.config.toolbar?.showSearch !== false,
    defaultPageSize: props.config.pagination?.pageSize || 25,
    autoLoad: props.config.autoLoad !== false,
  },
})

// Reactive mode selection with fallback
const currentMode = ref<DataViewMode>(props.mode)

// Watch for data changes and emit events
watch(
  () => adapter.data.value,
  (newData) => {
    emit('data-loaded', newData)
  },
  { deep: true },
)

watch(
  () => adapter.selectedItems.value,
  (selectedItems) => {
    emit('selection-changed', selectedItems)
  },
  { deep: true },
)

watch(
  () => adapter.filters.value,
  (filters) => {
    emit('filters-changed', filters)
  },
  { deep: true },
)

watch(
  () => adapter.sort.value,
  (sort) => {
    emit('sort-changed', sort)
  },
  { deep: true },
)

// Mode switching
function switchMode(newMode: DataViewMode) {
  currentMode.value = newMode
}

// Expose adapter methods for programmatic control
defineExpose({
  // Data operations
  refresh: adapter.refresh,
  loadData: adapter.loadData,

  // Filter operations
  setFilter: adapter.setFilter,
  removeFilter: adapter.removeFilter,
  clearFilters: adapter.clearFilters,

  // Sort operations
  setSort: adapter.setSort,
  clearSort: adapter.clearSort,

  // Pagination operations
  goToPage: adapter.goToPage,
  nextPage: adapter.nextPage,
  previousPage: adapter.previousPage,

  // Selection operations
  selectRow: adapter.selectRow,
  unselectRow: adapter.unselectRow,
  selectAll: adapter.selectAll,
  unselectAll: adapter.unselectAll,

  // Search operations
  search: adapter.search,
  clearSearch: adapter.clearSearch,

  // Data access
  getData: () => adapter.data.value,
  getSelectedItems: () => adapter.selectedItems.value,

  // Mode switching
  switchMode,
  getCurrentMode: () => currentMode.value,
})

// Handle row actions
async function handleRowAction(actionId: string, item: T) {
  const action = props.config.rowActions?.find(a => a.id === actionId)
  if (action) {
    await adapter.executeRowAction(action, item)
    emit('row-action', actionId, item)
  }
}

// Handle bulk actions
async function handleBulkAction(actionId: string) {
  const action = props.config.bulkActions?.find(a => a.id === actionId)
  if (action) {
    const items = adapter.selectedItems.value
    await adapter.executeBulkAction(action)
    emit('bulk-action', actionId, items)
  }
}
</script>

<template>
  <div
    v-if="isEnabled"
    class="data-view-container" :class="[
      `data-view-mode-${currentMode}`,
      props.class,
    ]"
  >
    <!-- Toolbar Section -->
    <DataViewToolbar
      v-if="config.toolbar?.enabled !== false"
      :config="config.toolbar || {}"
      :search-query="adapter.searchQuery.value"
      :filters="adapter.filters.value"
      :selected-count="adapter.selectedItems.value.length"
      :total-count="adapter.totalCount.value"
      :current-mode="currentMode"
      :available-modes="config.modes || ['list']"
      :bulk-actions="config.bulkActions"
      @search="adapter.search"
      @clear-search="adapter.clearSearch"
      @filter-changed="(field, operator, value) => adapter.setFilter(field, operator, value)"
      @filter-removed="adapter.removeFilter"
      @clear-filters="adapter.clearFilters"
      @mode-changed="switchMode"
      @bulk-action="handleBulkAction"
    >
      <!-- Passthrough toolbar slots for customization -->
      <template #search="slotProps">
        <slot name="toolbar-search" v-bind="slotProps" />
      </template>
      <template #filters="slotProps">
        <slot name="toolbar-filters" v-bind="slotProps" />
      </template>
      <template #actions="slotProps">
        <slot name="toolbar-actions" v-bind="slotProps" />
      </template>
    </DataViewToolbar>

    <!-- Loading State -->
    <DataViewSkeleton
      v-if="adapter.loading.value && !adapter.hasData.value"
      :mode="currentMode"
      :columns="config.columns?.length || 3"
      :rows="config.pagination?.pageSize || 25"
    />

    <!-- Content Section -->
    <div v-else-if="adapter.hasData.value" class="data-view-content">
      <!-- List Mode -->
      <DataViewList
        v-if="currentMode === 'list'"
        :items="adapter.data.value"
        :columns="config.columns || []"
        :row-actions="config.rowActions"
        :selected-items="adapter.selectedRows.value"
        :selectable="config.selection?.enabled || false"
        :loading="adapter.loading.value"
        @row-click="(item, index) => $emit('row-click', item, index)"
        @row-action="handleRowAction"
        @selection-changed="(id, selected) => selected ? adapter.selectRow(id) : adapter.unselectRow(id)"
      >
        <!-- Passthrough list slots -->
        <template #item="slotProps">
          <slot name="list-item" v-bind="slotProps" />
        </template>
        <template #item-actions="slotProps">
          <slot name="list-item-actions" v-bind="slotProps" />
        </template>
      </DataViewList>

      <!-- Grid Mode -->
      <DataViewGrid
        v-else-if="currentMode === 'grid'"
        :items="adapter.data.value"
        :columns="config.columns || []"
        :row-actions="config.rowActions"
        :selected-items="adapter.selectedRows.value"
        :selectable="config.selection?.enabled || false"
        :loading="adapter.loading.value"
        :grid-config="config.grid"
        @row-click="(item, index) => $emit('row-click', item, index)"
        @row-action="handleRowAction"
        @selection-changed="(id, selected) => selected ? adapter.selectRow(id) : adapter.unselectRow(id)"
      >
        <!-- Passthrough grid slots -->
        <template #card="slotProps">
          <slot name="grid-card" v-bind="slotProps" />
        </template>
        <template #card-actions="slotProps">
          <slot name="grid-card-actions" v-bind="slotProps" />
        </template>
      </DataViewGrid>

      <!-- Table Mode -->
      <DataViewTable
        v-else-if="currentMode === 'table'"
        :items="adapter.data.value"
        :columns="config.columns || []"
        :row-actions="config.rowActions"
        :selected-items="adapter.selectedRows.value"
        :selectable="config.selection?.enabled || false"
        :sortable="config.sorting?.enabled !== false"
        :sort="adapter.sort.value"
        :loading="adapter.loading.value"
        @row-click="(item, index) => $emit('row-click', item, index)"
        @row-action="handleRowAction"
        @sort-changed="(field, direction) => adapter.setSort(field, direction)"
        @selection-changed="(id, selected) => selected ? adapter.selectRow(id) : adapter.unselectRow(id)"
      >
        <!-- Passthrough table slots -->
        <template #header="slotProps">
          <slot name="table-header" v-bind="slotProps" />
        </template>
        <template #cell="slotProps">
          <slot name="table-cell" v-bind="slotProps" />
        </template>
        <template #row-actions="slotProps">
          <slot name="table-row-actions" v-bind="slotProps" />
        </template>
      </DataViewTable>
    </div>

    <!-- Empty State -->
    <DataViewEmpty
      v-else-if="!adapter.loading.value"
      :search-query="adapter.searchQuery.value"
      :filters="adapter.filters.value"
      :has-data="adapter.hasData.value"
      @clear-search="adapter.clearSearch"
      @clear-filters="adapter.clearFilters"
    >
      <!-- Passthrough empty state slots -->
      <template #icon="slotProps">
        <slot name="empty-icon" v-bind="slotProps" />
      </template>
      <template #title="slotProps">
        <slot name="empty-title" v-bind="slotProps" />
      </template>
      <template #description="slotProps">
        <slot name="empty-description" v-bind="slotProps" />
      </template>
      <template #actions="slotProps">
        <slot name="empty-actions" v-bind="slotProps" />
      </template>
    </DataViewEmpty>

    <!-- Pagination Section -->
    <DataViewPagination
      v-if="config.pagination?.enabled !== false && adapter.hasData.value"
      :current-page="adapter.pagination.value.page"
      :page-size="adapter.pagination.value.pageSize"
      :total-items="adapter.pagination.value.totalItems"
      :total-pages="adapter.pagination.value.totalPages"
      :loading="adapter.loading.value"
      @page-changed="adapter.goToPage"
      @page-size-changed="adapter.changePageSize"
      @previous="adapter.previousPage"
      @next="adapter.nextPage"
    >
      <!-- Passthrough pagination slots -->
      <template #summary="slotProps">
        <slot name="pagination-summary" v-bind="slotProps" />
      </template>
      <template #controls="slotProps">
        <slot name="pagination-controls" v-bind="slotProps" />
      </template>
    </DataViewPagination>
  </div>

  <!-- Fallback content when feature flag is disabled -->
  <div v-else-if="fallbackContent">
    <!-- eslint-disable-next-line vue/no-v-html -->
    <div v-html="fallbackContent" />
  </div>

  <!-- Default fallback slot -->
  <div v-else>
    <slot name="fallback">
      <!-- Feature disabled fallback -->
      <BaseCard class="p-8 text-center">
        <BaseText class="text-muted-500">
          DataView component is not enabled for this context.
        </BaseText>
      </BaseCard>
    </slot>
  </div>
</template>

<style scoped>
.data-view-container {
  @apply w-full space-y-4;
}

.data-view-content {
  @apply min-h-0 flex-1;
}

.data-view-mode-list .data-view-content {
  @apply space-y-2;
}

.data-view-mode-grid .data-view-content {
  @apply grid gap-4;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.data-view-mode-table .data-view-content {
  @apply overflow-auto;
}

/* Visual parity support for legacy classes */
.dv-grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.dv-grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.dv-grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.dv-grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.dv-gap-4 {
  gap: 1rem;
}
.dv-gap-6 {
  gap: 1.5rem;
}
.dv-p-4 {
  padding: 1rem;
}
.dv-p-6 {
  padding: 1.5rem;
}
</style>
