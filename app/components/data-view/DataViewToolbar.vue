<script setup lang="ts">
import type {
  DataViewBulkAction,
  DataViewFilter,
  DataViewMode,
  DataViewToolbarConfig,
} from '~/types/ui/data-view'

interface Props {
  /** Toolbar configuration */
  config: DataViewToolbarConfig
  /** Current search query */
  searchQuery: string
  /** Active filters */
  filters: DataViewFilter[]
  /** Number of selected items */
  selectedCount: number
  /** Total number of items */
  totalCount: number
  /** Current display mode */
  currentMode: DataViewMode
  /** Available display modes */
  availableModes: DataViewMode[]
  /** Available bulk actions */
  bulkActions?: DataViewBulkAction<any>[]
  /** Custom CSS classes */
  class?: string
}

interface Emits {
  /** Emitted when search query changes */
  (e: 'search', query: string): void
  /** Emitted when search is cleared */
  (e: 'clear-search'): void
  /** Emitted when a filter is changed */
  (e: 'filter-changed', field: string, operator: string, value: any): void
  /** Emitted when a filter is removed */
  (e: 'filter-removed', field: string): void
  /** Emitted when all filters are cleared */
  (e: 'clear-filters'): void
  /** Emitted when display mode changes */
  (e: 'mode-changed', mode: DataViewMode): void
  /** Emitted when a bulk action is executed */
  (e: 'bulk-action', actionId: string): void
  /** Emitted when refresh is triggered */
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  bulkActions: () => [],
  class: '',
})

const emit = defineEmits<Emits>()

// Search input model
const searchInput = ref(props.searchQuery)

// Watch for external search query changes
watch(() => props.searchQuery, (newQuery) => {
  searchInput.value = newQuery
})

// Debounced search
let searchTimeout: NodeJS.Timeout | null = null
function handleSearchInput(query: string) {
  if (searchTimeout)
    clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    emit('search', query)
  }, 300)
}

// Handle search clear
function clearSearch() {
  searchInput.value = ''
  emit('clear-search')
}

// Handle mode change
function changeMode(mode: DataViewMode) {
  emit('mode-changed', mode)
}

// Handle bulk action
function executeBulkAction(actionId: string) {
  emit('bulk-action', actionId)
}

// Handle refresh
function handleRefresh() {
  emit('refresh')
}

// Get mode icon
function getModeIcon(mode: DataViewMode) {
  switch (mode) {
    case 'list':
      return 'lucide:list'
    case 'grid':
      return 'lucide:grid-3x3'
    case 'table':
      return 'lucide:table'
    default:
      return 'lucide:layout-grid'
  }
}

// Get mode label
function getModeLabel(mode: DataViewMode) {
  switch (mode) {
    case 'list':
      return 'List View'
    case 'grid':
      return 'Grid View'
    case 'table':
      return 'Table View'
    default:
      return mode.charAt(0).toUpperCase() + mode.slice(1)
  }
}

// Show bulk actions bar when items are selected
const showBulkActions = computed(() => {
  return props.selectedCount > 0 && props.bulkActions && props.bulkActions.length > 0
})
</script>

<template>
  <div class="data-view-toolbar" :class="[props.class]">
    <!-- Main Toolbar -->
    <BaseCard rounded="md" class="p-4">
      <div class="flex items-center justify-between gap-4">
        <!-- Left Side: Search and Filters -->
        <div class="flex items-center gap-4 flex-1 min-w-0">
          <!-- Search Input -->
          <div
            v-if="config.showSearch !== false"
            class="relative max-w-sm w-full"
          >
            <slot name="search" :search-query="searchInput" :clear-search="clearSearch">
              <BaseInput
                v-model="searchInput"
                icon="lucide:search"
                placeholder="Search..."
                @input="handleSearchInput(searchInput)"
              >
                <template v-if="searchInput" #action>
                  <BaseButton
                    size="xs"
                    variant="ghost"
                    @click="clearSearch"
                  >
                    <Icon name="lucide:x" class="h-4 w-4" />
                  </BaseButton>
                </template>
              </BaseInput>
            </slot>
          </div>

          <!-- Filters -->
          <div
            v-if="config.showFilters !== false"
            class="flex items-center gap-2"
          >
            <slot name="filters" :filters="filters" :clear-filters="() => emit('clear-filters')">
              <!-- Active Filters Display -->
              <div v-if="filters.length > 0" class="flex items-center gap-2">
                <BaseTag
                  v-for="filter in filters"
                  :key="`${filter.field}-${filter.operator}`"
                  size="sm"
                  variant="outline"
                  closable
                  @close="emit('filter-removed', filter.field)"
                >
                  <span class="text-xs">
                    {{ filter.field }}: {{ filter.value }}
                  </span>
                </BaseTag>

                <BaseButton
                  v-if="filters.length > 1"
                  size="xs"
                  variant="ghost"
                  @click="emit('clear-filters')"
                >
                  Clear all
                </BaseButton>
              </div>

              <!-- Add Filter Button (placeholder) -->
              <BaseDropdown v-else>
                <template #button>
                  <BaseButton size="sm" variant="outline">
                    <Icon name="lucide:filter" class="h-4 w-4 mr-2" />
                    Filters
                  </BaseButton>
                </template>

                <div class="p-3 text-sm text-muted-600 dark:text-muted-400">
                  <p>Filter options will be available</p>
                  <p>in the next iteration.</p>
                </div>
              </BaseDropdown>
            </slot>
          </div>
        </div>

        <!-- Right Side: Actions and Mode Switcher -->
        <div class="flex items-center gap-3">
          <!-- Custom Actions Slot -->
          <slot name="actions" :selected-count="selectedCount" :total-count="totalCount">
            <!-- Refresh Button -->
            <BaseButton
              size="sm"
              variant="ghost"
              @click="handleRefresh"
            >
              <Icon name="lucide:refresh-cw" class="h-4 w-4" />
            </BaseButton>
          </slot>

          <!-- View Mode Switcher -->
          <div
            v-if="config.showModeToggle !== false && availableModes.length > 1"
            class="flex items-center border border-muted-200 dark:border-muted-700 rounded-md p-1 bg-muted-50 dark:bg-muted-800"
          >
            <BaseButton
              v-for="mode in availableModes"
              :key="mode"
              size="xs"
              :variant="currentMode === mode ? 'solid' : 'ghost'"
              class="px-2 py-1.5" :class="[
                currentMode === mode
                  ? 'bg-white dark:bg-muted-900 shadow-sm'
                  : 'hover:bg-white/50 dark:hover:bg-muted-700',
              ]"
              :title="getModeLabel(mode)"
              @click="changeMode(mode)"
            >
              <Icon :name="getModeIcon(mode)" class="h-4 w-4" />
            </BaseButton>
          </div>
        </div>
      </div>

      <!-- Results Summary -->
      <div
        v-if="config.showSummary !== false"
        class="mt-3 pt-3 border-t border-muted-200 dark:border-muted-700"
      >
        <div class="flex items-center justify-between text-sm text-muted-600 dark:text-muted-400">
          <div>
            <span v-if="selectedCount > 0">
              {{ selectedCount }} of {{ totalCount }} items selected
            </span>
            <span v-else>
              {{ totalCount }} {{ totalCount === 1 ? 'item' : 'items' }} total
            </span>
          </div>

          <div v-if="filters.length > 0" class="text-xs">
            {{ filters.length }} {{ filters.length === 1 ? 'filter' : 'filters' }} applied
          </div>
        </div>
      </div>
    </BaseCard>

    <!-- Bulk Actions Bar -->
    <Transition
      enter-active-class="transition-all duration-200 ease-out"
      enter-from-class="opacity-0 -translate-y-2 scale-95"
      enter-to-class="opacity-100 translate-y-0 scale-100"
      leave-active-class="transition-all duration-150 ease-in"
      leave-from-class="opacity-100 translate-y-0 scale-100"
      leave-to-class="opacity-0 -translate-y-2 scale-95"
    >
      <BaseCard
        v-if="showBulkActions"
        rounded="md"
        class="p-4 bg-primary-50 dark:bg-primary-900/10 border-primary-200 dark:border-primary-800"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <Icon name="lucide:check-circle" class="h-5 w-5 text-primary-600 dark:text-primary-400" />
            <span class="text-sm font-medium text-primary-900 dark:text-primary-100">
              {{ selectedCount }} {{ selectedCount === 1 ? 'item' : 'items' }} selected
            </span>
          </div>

          <div class="flex items-center gap-2">
            <!-- Bulk Action Buttons -->
            <BaseButton
              v-for="action in bulkActions.filter(a => a.visible !== false)"
              :key="action.id"
              size="sm"
              :variant="action.variant === 'danger' ? 'solid' : 'outline'"
              :color="action.variant === 'danger' ? 'danger' : 'primary'"
              @click="executeBulkAction(action.id)"
            >
              <Icon v-if="action.icon" :name="action.icon" class="h-4 w-4 mr-2" />
              {{ action.label }}
            </BaseButton>

            <!-- Clear Selection -->
            <BaseButton
              size="sm"
              variant="ghost"
              @click="$emit('clear-selection')"
            >
              <Icon name="lucide:x" class="h-4 w-4 mr-2" />
              Clear
            </BaseButton>
          </div>
        </div>
      </BaseCard>
    </Transition>
  </div>
</template>

<style scoped>
.data-view-toolbar {
  @apply space-y-3;
}

/* Ensure search input takes appropriate width */
.data-view-toolbar .relative.max-w-sm {
  min-width: 200px;
}

@media (max-width: 768px) {
  .data-view-toolbar .relative.max-w-sm {
    min-width: 150px;
    max-width: 200px;
  }
}

/* Mode switcher styling */
.data-view-toolbar .flex.items-center.border {
  background: rgba(var(--color-muted-50), 0.5);
}

.dark .data-view-toolbar .flex.items-center.border {
  background: rgba(var(--color-muted-800), 0.5);
}

/* Bulk actions styling */
.data-view-toolbar .bg-primary-50 {
  backdrop-filter: blur(8px);
}

/* Animation improvements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
