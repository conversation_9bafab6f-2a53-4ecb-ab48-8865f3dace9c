<script setup lang="ts">
const { toasts, dismiss, pause, resume } = useToast()

// Icon mapping for toast types
const iconMap = {
  info: 'ph:info',
  success: 'ph:check-circle',
  warning: 'ph:warning',
  error: 'ph:x-circle',
}

// Color mapping for toast types
const colorMap = {
  info: {
    bg: 'bg-blue-50 dark:bg-blue-900/20',
    border: 'border-blue-200 dark:border-blue-800',
    icon: 'text-blue-500',
    text: 'text-blue-900 dark:text-blue-100',
  },
  success: {
    bg: 'bg-green-50 dark:bg-green-900/20',
    border: 'border-green-200 dark:border-green-800',
    icon: 'text-green-500',
    text: 'text-green-900 dark:text-green-100',
  },
  warning: {
    bg: 'bg-yellow-50 dark:bg-yellow-900/20',
    border: 'border-yellow-200 dark:border-yellow-800',
    icon: 'text-yellow-500',
    text: 'text-yellow-900 dark:text-yellow-100',
  },
  error: {
    bg: 'bg-red-50 dark:bg-red-900/20',
    border: 'border-red-200 dark:border-red-800',
    icon: 'text-red-500',
    text: 'text-red-900 dark:text-red-100',
  },
}
</script>

<template>
  <!-- Toast Container -->
  <Teleport to="body">
    <div
      v-if="toasts.length > 0"
      class="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full"
    >
      <TransitionGroup
        enter-active-class="transform transition-all duration-300"
        enter-from-class="translate-x-full opacity-0"
        enter-to-class="translate-x-0 opacity-100"
        leave-active-class="transform transition-all duration-200"
        leave-from-class="translate-x-0 opacity-100 scale-100"
        leave-to-class="translate-x-full opacity-0 scale-95"
        move-class="transition-all duration-200"
      >
        <div
          v-for="toast in toasts"
          :key="toast.id"
          class="relative flex items-start p-4 rounded-lg shadow-lg border backdrop-blur-sm"
          :class="[colorMap[toast.type].bg, colorMap[toast.type].border]"
          @mouseenter="pause(toast.id)"
          @mouseleave="resume(toast.id)"
        >
          <!-- Icon -->
          <div class="flex-shrink-0">
            <Icon
              :name="iconMap[toast.type]"
              class="w-5 h-5"
              :class="colorMap[toast.type].icon"
            />
          </div>

          <!-- Content -->
          <div class="ml-3 flex-1">
            <!-- Title -->
            <h3
              v-if="toast.title"
              class="text-sm font-semibold"
              :class="colorMap[toast.type].text"
            >
              {{ toast.title }}
            </h3>

            <!-- Message -->
            <p
              class="text-sm"
              :class="[
                colorMap[toast.type].text,
                { 'mt-1': toast.title },
              ]"
            >
              {{ toast.message }}
            </p>

            <!-- Actions -->
            <div
              v-if="toast.actions && toast.actions.length > 0"
              class="mt-3 flex space-x-2"
            >
              <button
                v-for="action in toast.actions"
                :key="action.label"
                class="text-xs font-medium px-3 py-1 rounded-md border border-current hover:bg-current hover:text-white transition-colors"
                :class="colorMap[toast.type].text"
                @click="action.handler"
              >
                {{ action.label }}
              </button>
            </div>
          </div>

          <!-- Close button -->
          <button
            class="flex-shrink-0 ml-2 p-1 rounded-md hover:bg-black/10 transition-colors"
            :class="colorMap[toast.type].text"
            @click="dismiss(toast.id)"
          >
            <Icon name="ph:x" class="w-4 h-4" />
          </button>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<style scoped>
/* Additional animation styles if needed */
</style>
