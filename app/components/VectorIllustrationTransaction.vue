<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 500 500"
  >
    <defs>
      <clipPath id="clip-path">
        <path
          fill="currentColor"
          stroke="var(--illustration-contour)"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M321,234.74c-3.42-5.65-19.85-5.55-21.34-3.05,0,0,8.14,26.32,17.21,23.59S321,234.74,321,234.74Z"
        />
      </clipPath>
      <clipPath id="clip-path-2">
        <path
          fill="currentColor"
          stroke="var(--illustration-contour)"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M301.36,248.13a8.09,8.09,0,0,0-1.74,5c0,2.46,4.68,51.47,19.13,68.54,3,3.58,6.49,5.75,10.43,5.8,12.23.13,18.15-9.57,25.94-32.08q1.45-4.2,3-9C365.7,263.1,375,214.64,375,214.64s-16.5,3.83-26.28,13.53c-4.28,4.24-8.74,16.27-11.59,25.11-1.4,4.34-2.41,7.91-2.84,9.41l-.2.77a22.84,22.84,0,0,0-1.73-4.39s0,0,0,0a.24.24,0,0,1-.06-.11,53.63,53.63,0,0,0-7.75-10.67L321,234.74s-1.26,2.47-6.95,3.34-14.39-6.38-14.39-6.38Z"
        />
      </clipPath>
      <clipPath id="clip-path-3">
        <path
          fill="currentColor"
          stroke="var(--illustration-contour)"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M440,358.86s2.08-16.42,3.35-21.78c5.71-24.15,25.83-80.08,22.59-96.63-3.34-17-23.73-31.9-56.64-33.53-30.53-1.53-55.7,9.3-61.71,21.87-5.75,12-5.51,94.87-4.05,132.26h83.85A41.45,41.45,0,0,0,440,358.86Z"
        />
      </clipPath>
      <clipPath id="clip-path-4">
        <rect
          fill="currentColor"
          stroke="var(--illustration-contour)"
          stroke-linecap="round"
          stroke-linejoin="round"
          x="299.62"
          y="171.98"
          width="57.65"
          height="28.55"
          transform="translate(45.27 436.01) rotate(-70.97)"
        />
      </clipPath>
      <clipPath id="clip-path-5">
        <rect
          fill="currentColor"
          stroke="var(--illustration-contour)"
          stroke-linecap="round"
          stroke-linejoin="round"
          x="276.13"
          y="84.82"
          width="57.65"
          height="28.55"
          transform="translate(322.39 -221.08) rotate(75.12)"
        />
      </clipPath>
      <clipPath id="clip-path-6">
        <path
          fill="currentColor"
          stroke="var(--illustration-contour)"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M365.53,221.45c-1.57-2.83-6-1.46-11.23,6.72s-7.34,17.72-6.08,21.2,18.1,7.69,18.1,7.69l3.47-8.81Z"
        />
      </clipPath>
    </defs>
    <g id="Background_Simple" data-name="Background Simple">
      <path
        fill="currentColor"
        fill-opacity="20%"
        d="M79.34,74.54,80,74c57.53-51.81,134-54.15,189.63,1.62,29,29.07,80.55,23.51,117.41,34,127.11,36.25,105.13,208.71,42.34,292.08-56.65,75.22-179.42,92.22-261.9,55.21C95,424.38,43.94,351.72,25.78,275.74q-2.73-11.43-4.47-23.06C11.76,188,30.11,119.31,79.34,74.54Z"
      />
    </g>
    <g id="Bubble">
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M282.2,417.89a51,51,0,0,1-99.31,2.94"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M281.51,394.86A50.58,50.58,0,0,1,283,412.19"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M199.42,368.87a51,51,0,0,1,79.2,17.91"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="185.27 360.83 183.85 357.03 197.29 367.25"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M182.22,397.89a50.76,50.76,0,0,1,9.2-20.59l-4.48-12"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M181.26,410.05a51.63,51.63,0,0,1,.13-6.21"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M234.63,382.49a25.63,25.63,0,1,1-9.07.55"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M228.34,436.2a28.42,28.42,0,0,1-24.93-31.53"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M258.85,416.22a28.47,28.47,0,0,1-21.65,19.64"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M205.22,397.47a28.43,28.43,0,0,1,54.66,13.81"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-width="2px"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="248.15"
        y1="396.86"
        x2="248.45"
        y2="396.49"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-width="2px"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="243.85"
        y1="401.99"
        x2="245.99"
        y2="399.43"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-width="2px"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="216.29 407.03 227.98 421 241.98 404.24"
      />
    </g>
    <g id="Character">
      <rect
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        x="251.84"
        y="135.39"
        width="225.66"
        height="225.66"
        rx="46.19"
      />
      <path
        fill="currentColor"
        d="M321,234.74c-3.42-5.65-19.85-5.55-21.34-3.05,0,0,8.14,26.32,17.21,23.59S321,234.74,321,234.74Z"
      />
      <g class="clip-path-0" clip-path="url(#clip-path)">
        <path
          class="opacity-30"
          fill="white"
          fill-opacity="70%"
          d="M321,234.74c-3.42-5.65-19.85-5.55-21.34-3.05,0,0,8.14,26.32,17.21,23.59S321,234.74,321,234.74Z"
        />
      </g>
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M321,234.74c-3.42-5.65-19.85-5.55-21.34-3.05,0,0,8.14,26.32,17.21,23.59S321,234.74,321,234.74Z"
      />
      <path
        fill="currentColor"
        d="M301.36,248.13a8.09,8.09,0,0,0-1.74,5c0,2.46,4.68,51.47,19.13,68.54,3,3.58,6.49,5.75,10.43,5.8,12.23.13,18.15-9.57,25.94-32.08q1.45-4.2,3-9C365.7,263.1,375,214.64,375,214.64s-16.5,3.83-26.28,13.53c-4.28,4.24-8.74,16.27-11.59,25.11-1.4,4.34-2.41,7.91-2.84,9.41l-.2.77a22.84,22.84,0,0,0-1.73-4.39s0,0,0,0a.24.24,0,0,1-.06-.11,53.63,53.63,0,0,0-7.75-10.67L321,234.74s-1.26,2.47-6.95,3.34-14.39-6.38-14.39-6.38Z"
      />
      <g class="clip-path-1" clip-path="url(#clip-path-2)">
        <path
          class="opacity-30"
          fill="white"
          fill-opacity="70%"
          d="M318.75,321.63c3,3.58,6.49,5.75,10.43,5.8,12.23.13,18.15-9.57,25.94-32.08L355,243a38.82,38.82,0,0,0-17.82,10.24c-1.4,4.34-2.41,7.91-2.84,9.41,3.45,7.17,9.44,23.64,2.56,41.19C332.39,315.26,325.08,319.89,318.75,321.63Z"
        />
      </g>
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M301.36,248.13a8.09,8.09,0,0,0-1.74,5c0,2.46,4.68,51.47,19.13,68.54,3,3.58,6.49,5.75,10.43,5.8,12.23.13,18.15-9.57,25.94-32.08q1.45-4.2,3-9C365.7,263.1,375,214.64,375,214.64s-16.5,3.83-26.28,13.53c-4.28,4.24-8.74,16.27-11.59,25.11-1.4,4.34-2.41,7.91-2.84,9.41l-.2.77a22.84,22.84,0,0,0-1.73-4.39s0,0,0,0a.24.24,0,0,1-.06-.11,53.63,53.63,0,0,0-7.75-10.67L321,234.74s-1.26,2.47-6.95,3.34-14.39-6.38-14.39-6.38Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M340.26,279.43c.88,4.59.91,9.57-1.21,13.85"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M334.09,263.46a53.23,53.23,0,0,1,5.52,13.14"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M341.88,269.25s-1.72-4.74-6.37-5.79"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M318.14,252a16.08,16.08,0,0,0,3.12-2.19"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M301.36,248.13c3.64,3.89,9.32,6.44,14.62,4.75"
      />
      <path
        fill="currentColor"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M332.27,258.93a.59.59,0,0,0,.**********,0,0,1-.06-.11Z"
      />
      <path
        fill="currentColor"
        d="M440,358.86s2.08-16.42,3.35-21.78c5.71-24.15,25.83-80.08,22.59-96.63-3.34-17-23.73-31.9-56.64-33.53-30.53-1.53-55.7,9.3-61.71,21.87-5.75,12-5.51,94.87-4.05,132.26h83.85A41.45,41.45,0,0,0,440,358.86Z"
      />
      <g class="clip-path-3" clip-path="url(#clip-path-3)">
        <path
          class="opacity-30"
          fill="white"
          fill-opacity="70%"
          d="M433.48,259c-15.81,21.68-33.05,54.11-33.05,54.11l-47.09-58.69S364.12,329,370,337.07s5.62,11.78,5.62,11.78,14.93,16.07,26.13,13.89c9.49-1.85,43.64-40,54.29-84.13Z"
        />
      </g>
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M440,358.86s2.08-16.42,3.35-21.78c5.71-24.15,25.83-80.08,22.59-96.63-3.34-17-23.73-31.9-56.64-33.53-30.53-1.53-55.7,9.3-61.71,21.87-5.75,12-5.51,94.87-4.05,132.26h83.85A41.45,41.45,0,0,0,440,358.86Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M422.62,210.23s-16.42,7-34.18,1.21c0,0,2.29-5.56,2.3-10.14,0-2.3-.57-4.37-2.3-5.35,0,0,.81-.88,2.17-2.28h0c6.73-6.85,27.16-26.1,33.81-14.62C424.43,179.05,417.46,179.71,422.62,210.23Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M404.06,189s-2.7,9.89-13.32,12.33c0-2.3-.57-4.37-2.3-5.35,0,0,.81-.88,2.17-2.28h0C397.12,191.12,404.06,189,404.06,189Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M429.05,162.89c7.05-28.09-8.14-37.71-17.7-41.39-15.33-5.89-34.67.81-39.2,16.42s-.45,19.16-.66,25.54c-.15,4.48-3.12,6.69.15,17.92,2.16,7.41,6.18,15.92,10.85,16.19,12.66.74,24.65-6.47,24-9.86"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M389.27,149.13s5.67-2,8.69,4.22"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M370,148.2s4.22-3.57,8.44,1.49"
      />
      <ellipse
        fill="var(--illustration-contour)"
        cx="391.8"
        cy="161.02"
        rx="1.41"
        ry="2.36"
      />
      <path
        fill="var(--illustration-contour)"
        d="M374.22,159.73c0,1.3.63,2.36,1.4,2.36S377,161,377,159.73s-.63-2.37-1.41-2.37S374.22,158.42,374.22,159.73Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M378.44,154.31s.88,6.51.49,7.78-4.79,5.24-5,7,3.68,3.55,3.68,3.55"
      />
      <path
        fill="var(--illustration-contour)"
        d="M375.62,176.38s8.79,2.21,16.18,0c0,0-3.48,10.9-10.36,9.79-4.56-.73-3.4-7.88-3.4-7.88Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M399.85,133.38a11.6,11.6,0,0,0-8.69-1.32c-5.05,1.18-5.85,11.81-5.85,11.81s-2.7-2.39-3.17-5.34l-1.59,4.56-4.22-4-1.41,4.75-1.67-3.42L370,146.77s-4.87-6.29-4.12-11.14c2.06-13.24,12.57-24.12,34.55-23,11.06.59,15.19,8.18,15.19,8.18s10.82-1.47,15.62,4.15c16.34,19.11,6.1,39.87-9.42,59.53l-5.32-18.74-5.15-.59a54.12,54.12,0,0,0,0-5.77,28.9,28.9,0,0,0-1.91-7.25l-1.91.85.8-3.59L404,151.68s1.84-14.77,0-17.27A3,3,0,0,0,399.85,133.38Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M413.26,167.8s5.15-9.59,10.53-7.25,1.64,13.81-3,17.55-7.49,0-7.49,0"
      />
      <g class="opacity-20">
        <path
          class="cls-13"
          d="M404.53,129.43a9.44,9.44,0,0,0-8.23-5.79c-6.3-.41-10.83,6.42-11,11.18"
        />
        <path
          class="cls-13"
          d="M408.79,129.22s4.32-3.31,6.82,0c4.24,5.6.73,19.11.73,19.11"
        />
        <path
          class="cls-13"
          d="M414.67,125.27s4.49-2.76,8.34,2.56,3.43,18.27,3.43,18.27"
        />
        <path class="cls-13" d="M427.4,124.56s7.78,3.27,8.22,18.95" />
        <path
          class="cls-13"
          d="M409.31,125.27s-4.37-7.43-17.33-4.94-14.91,15.73-14.91,15.73"
        />
        <path
          class="cls-13"
          d="M412.54,123s-5.22-7.25-21.08-5.8-22.24,15.32-20.63,21.94"
        />
      </g>
      <path
        stroke="var(--illustration-contour)"
        fill="var(--color-yellow-500)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M282.55,215.9s18.87-14.52,27.09-16,36.52,4.71,36.52,4.71v33.51s-17.7,1.94-25.65,7.76-35.31,6.75-35.31,6.75Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M300.26,205.52a50.81,50.81,0,0,1,1.1-6.63c.67-2.35,5.23-2.1,6.26-1.27a12.63,12.63,0,0,1,2,2.28l-2,.61C307.18,200.66,306.73,209.19,300.26,205.52Z"
      />
      <rect
        fill="currentColor"
        x="299.62"
        y="171.98"
        width="57.65"
        height="28.55"
        transform="translate(45.27 436.01) rotate(-70.97)"
      />
      <g class="clip-path-4" clip-path="url(#clip-path-4)">
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M305.55,208.85l27,9.31,18.8-54.5-27-9.31ZM348.5,165l-17.34,50.28-22.77-7.85,17.34-50.28Z"
        />
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M328.36,161.45,326,168.18a1.45,1.45,0,0,1-1.84.89h0a1.44,1.44,0,0,1-.89-1.83l2.32-6.73a1.44,1.44,0,0,1,1.84-.9h0A1.45,1.45,0,0,1,328.36,161.45Z"
        />
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M333.58,205.27,331.26,212a1.44,1.44,0,0,1-1.83.89h0a1.44,1.44,0,0,1-.9-1.83l2.32-6.73a1.45,1.45,0,0,1,1.84-.89h0A1.44,1.44,0,0,1,333.58,205.27Z"
        />
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M320,183.34a8.94,8.94,0,1,0,11.37-5.54A8.94,8.94,0,0,0,320,183.34Zm13.15,**********-.4,1.17-.67-.23a2.12,2.12,0,0,1-2.79.78l-.63-.21.47-1.36.72.24c.66.23,1,0,1.15-.43s0-.83-.63-1.06-1.22-.12-2.31.58c-1.34.86-2.21,1-3.25.65a2.12,2.12,0,0,1-1.72-2.29l-.68-.23.41-1.18.68.24a2.1,2.1,0,0,1,2.77-.78l.29.1-.47,1.36-.38-.13c-.66-.22-1,0-1.15.41s0,.81.65,1,1.2.12,2.31-.58c1.35-.85,2.21-1,3.25-.65A2.12,2.12,0,0,1,333.14,187.22Z"
        />
      </g>
      <rect
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x="299.62"
        y="171.98"
        width="57.65"
        height="28.55"
        transform="translate(45.27 436.01) rotate(-70.97)"
      />
      <rect
        fill="currentColor"
        x="276.13"
        y="84.82"
        width="57.65"
        height="28.55"
        transform="translate(322.39 -221.08) rotate(75.12)"
      />
      <g class="clip-path-5" clip-path="url(#clip-path-5)">
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M311.34,67.57,283.75,74.9l14.81,55.72,27.59-7.34Zm-11.2,60.32-13.66-51.4,23.28-6.19,13.66,51.4Z"
        />
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M318.86,119.63,317,112.75a1.43,1.43,0,0,1,1-1.76h0a1.43,1.43,0,0,1,1.76,1l1.83,6.88a1.44,1.44,0,0,1-1,1.77h0A1.46,1.46,0,0,1,318.86,119.63Z"
        />
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M290.08,86.18l-1.83-6.88a1.45,1.45,0,0,1,1-1.77h0a1.46,1.46,0,0,1,1.77,1l1.83,6.88a1.44,1.44,0,0,1-1,1.76h0A1.43,1.43,0,0,1,290.08,86.18Z"
        />
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M313.59,96.8a8.94,8.94,0,1,0-6.34,10.94A8.93,8.93,0,0,0,313.59,96.8Zm-13.07,4.12-.69.18-.32-1.2.69-.19c-.07-1.11.6-1.86,1.87-2.2l.65-.17.37,1.39-.74.2c-.67.17-.83.53-.71,1s.45.7,1.11.52,1.08-.57,1.6-1.77c.63-1.45,1.26-2.07,2.33-2.35a2.1,2.1,0,0,1,2.7.94l.7-.18.32,1.2-.7.19c.07,1.1-.59,1.84-1.86,2.18l-.3.08-.37-1.39.39-.1c.67-.18.85-.52.73-1s-.45-.67-1.12-.5-1.07.57-1.59,1.77c-.65,1.46-1.27,2.07-2.34,2.36A2.12,2.12,0,0,1,300.52,100.92Z"
        />
      </g>
      <rect
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x="276.13"
        y="84.82"
        width="57.65"
        height="28.55"
        transform="translate(322.39 -221.08) rotate(75.12)"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="var(--color-yellow-500)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M282.55,215.9s27.37-9.23,34.73-10.55,28.88-.74,28.88-.74v33.51s-22.47,6.82-25.94,9.55-35,5-35,5Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M319.9,219.89c-1.44,7.75-.41,18.15-5.54,24.45"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M322.28,208.14c-.55,2.54-1.08,5.07-1.59,7.62"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="var(--color-yellow-500)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M349.52,97a6.13,6.13,0,1,1-6.13-6.13A6.13,6.13,0,0,1,349.52,97Z"
      />
      <path
        class="opacity-50"
        fill="white"
        fill-opacity="70%"
        d="M346.81,100.79a5.13,5.13,0,1,0-7.24-.4A5.14,5.14,0,0,0,346.81,100.79Zm-5.51-5.61-.27-.3.54-.48.27.3a1.2,1.2,0,0,1,1.61.39l.25.28-.61.55-.29-.33c-.27-.29-.49-.28-.7-.09s-.24.4,0,.7.57.4,1.31.35a2,2,0,0,1,1.82.58,1.23,1.23,0,0,1,.22,1.63l.27.31-.53.47-.28-.3a1.21,1.21,0,0,1-1.6-.39l-.12-.13.62-.55.15.17c.27.3.49.3.69.12s.23-.41,0-.7-.57-.4-1.32-.36a1.93,1.93,0,0,1-1.81-.58A1.22,1.22,0,0,1,341.3,95.18Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="var(--color-yellow-500)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M320.49,48.53a6.13,6.13,0,1,1-6.13-6.13A6.14,6.14,0,0,1,320.49,48.53Z"
      />
      <path
        class="opacity-50"
        fill="white"
        fill-opacity="70%"
        d="M317.78,52.35a5.13,5.13,0,1,0-7.24-.4A5.13,5.13,0,0,0,317.78,52.35Zm-5.51-5.61-.27-.3.53-.48.27.3a1.22,1.22,0,0,1,1.62.39l.25.28-.61.55-.29-.32c-.27-.3-.5-.29-.7-.1s-.24.4,0,.7.58.4,1.32.35a2,2,0,0,1,1.81.58,1.21,1.21,0,0,1,.22,1.63l.28.31-.53.47-.28-.3a1.21,1.21,0,0,1-1.6-.39l-.12-.13.61-.55.16.17c.26.3.48.3.69.12s.22-.41,0-.7-.57-.4-1.32-.35a1.94,1.94,0,0,1-1.81-.59A1.21,1.21,0,0,1,312.27,46.74Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="var(--color-yellow-500)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M292,167a6.13,6.13,0,1,1,7.41-4.49A6.13,6.13,0,0,1,292,167Z"
      />
      <path
        class="opacity-50"
        fill="white"
        fill-opacity="70%"
        d="M289,163.43a5.13,5.13,0,1,0,2.11-6.94A5.12,5.12,0,0,0,289,163.43Zm6.76-4,.36-.19.33.63-.35.19a1.23,1.23,0,0,1-.76,1.48l-.34.18-.39-.73.39-.21c.35-.18.39-.41.26-.65s-.34-.33-.69-.15-.53.47-.66,1.2a1.94,1.94,0,0,1-1,1.62,1.22,1.22,0,0,1-1.63-.17l-.37.19-.33-.63.36-.19a1.21,1.21,0,0,1,.76-1.47l.15-.08.39.73-.2.11c-.36.18-.41.39-.28.64s.***********.52-.46.65-1.2a2,2,0,0,1,1-1.62A1.22,1.22,0,0,1,295.73,159.42Z"
      />
      <path
        fill="currentColor"
        d="M365.53,221.45c-1.57-2.83-6-1.46-11.23,6.72s-7.34,17.72-6.08,21.2,18.1,7.69,18.1,7.69l3.47-8.81Z"
      />
      <g class="clip-path-6" clip-path="url(#clip-path-6)">
        <path
          class="opacity-50"
          fill="white"
          fill-opacity="70%"
          d="M365.53,221.45c-1.57-2.83-6-1.46-11.23,6.72s-7.34,17.72-6.08,21.2,18.1,7.69,18.1,7.69l3.47-8.81Z"
        />
      </g>
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M365.53,221.45c-1.57-2.83-6-1.46-11.23,6.72s-7.34,17.72-6.08,21.2,18.1,7.69,18.1,7.69l3.47-8.81Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M372.65,238.58,363,217.44s-6.3-9-7.54-12.44-15.08-6.11-18.21-6.11-6.43,5.59-6.43,5.59h6.5s2.43,2.55,2.73,4.07-8.6,9.12-9.87,9.84a46.85,46.85,0,0,0-8.16,4.46c-3.54,2.62-11.51,4.67-13,7s-.43,2.79.63,3.33a9.33,9.33,0,0,0,2.85.67s-.64,2.67,0,4.07,4.34,1.27,4.34,1.27a2.54,2.54,0,0,0,2,2.24,18.1,18.1,0,0,1,3.19.85s7.65,1.29,11.09.57,13.85-6.28,16.4-6,9.6,16.58,9.6,16.58S371.12,251.37,372.65,238.58Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M312.49,233.88s10-2.16,10.66-2.93,7.66-2.94,7.66-2.94"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M316.83,239.22s5.62-1.14,7.57-1.4,10-3.94,10-3.94"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M322,242.31s7.91-.67,9.56-1.44,6.49-2.29,6.49-2.29"
      />
      <path
        fill="currentColor"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M438.9,229.27c-11.74,7.73-18.83,26.42-18.83,26.42l-18.32,34.09S386.34,251.84,382,251.35l-16.44-29.9s1.94,17.53-4.91,25-12.4,3-12.4,3l5.12,14.13,1.27,6.72s21.62,64.84,28.68,68.86,23.15,5.81,36.78-4.17,36.95-47.73,39.16-60.08"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M375.35,255.84c.6-.89,1.14-1.81,1.65-2.75"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M361.5,264.7a18.66,18.66,0,0,0,12.13-6.56"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M353.34,263.5a16.3,16.3,0,0,0,5.76,1.28"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M406.55,304.8a16.88,16.88,0,0,1-.37,4.65"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M401.75,289.78A37.87,37.87,0,0,1,406.24,302"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M411.88,295.88a26.27,26.27,0,0,0-7.13-6.1"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M440.85,246.41s-10.31-1.38-18.49,8.05"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M451.75,275.25a9.7,9.7,0,0,1-5.78-.94"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M464.13,261.42c-1.62,7.83-6,11.8-9.8,13.18"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M415.52,216.94s-17.11,4-26.65-2.39"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M354.14,321.68S363.47,348,385,352.47"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M416.48,358.17c-1.11.84-2.27,1.61-3.45,2.34"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="297.47"
        y1="39.28"
        x2="297.47"
        y2="62.43"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="297.47"
        y1="25.39"
        x2="297.47"
        y2="34.65"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="297.47"
        y1="16.74"
        x2="297.47"
        y2="21.69"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="293.1 59.72 297.47 65.13 301.84 59.72"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="309.04"
        y1="155.19"
        x2="309.04"
        y2="178.34"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="309.04"
        y1="141.31"
        x2="309.04"
        y2="150.56"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="309.04"
        y1="132.65"
        x2="309.04"
        y2="137.61"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="304.67 175.64 309.04 181.05 313.41 175.64"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="331.91"
        y1="85.53"
        x2="331.91"
        y2="108.68"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="331.91"
        y1="71.64"
        x2="331.91"
        y2="80.9"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="331.91"
        y1="62.99"
        x2="331.91"
        y2="67.94"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="327.54 105.97 331.91 111.38 336.28 105.97"
      />
    </g>
    <g id="Hand">
      <path
        fill="white"
        d="M130.44,340.55c-.36,14.53,6.83,13.35,6.83,13.35a30.57,30.57,0,0,1,2.71-4.77c3.77-5.79,9.53-13.91,7.37-18.72a70.29,70.29,0,0,0-4.81-8.3c-3.86-5.91-8.29-11.88-8.29-11.88l19.21-3.18s13.68,5.65,24.47,6.31l.84,0c9.85.19,8.26-7.53,8.26-10,0-1.06-2.66-3-5.78-4.92-4.45-2.73-9.84-5.42-9.84-5.42l2.92-6a17.83,17.83,0,0,0,5.1.27c7.48-.82,6.45-8.17,5.24-11.38-.35-.92-2.07-2.31-4.49-3.91-7.69-5.07-22.4-12.13-22.4-12.13H136.2l-21.23,58S130.8,326,130.44,340.55Z"
      />
      <path
        fill="currentColor"
        fill-opacity="40%"
        d="M130.44,340.55c-.36,14.53,6.83,13.35,6.83,13.35a30.57,30.57,0,0,1,2.71-4.77c3.77-5.79,9.53-13.91,7.37-18.72a70.29,70.29,0,0,0-4.81-8.3c-3.86-5.91-8.29-11.88-8.29-11.88l19.21-3.18s13.68,5.65,24.47,6.31l.84,0c9.85.19,8.26-7.53,8.26-10,0-1.06-2.66-3-5.78-4.92-4.45-2.73-9.84-5.42-9.84-5.42l2.92-6a17.83,17.83,0,0,0,5.1.27c7.48-.82,6.45-8.17,5.24-11.38-.35-.92-2.07-2.31-4.49-3.91-7.69-5.07-22.4-12.13-22.4-12.13H136.2l-21.23,58S130.8,326,130.44,340.55Z"
      />
      <path
        fill="currentColor"
        fill-opacity="40%"
        d="M130.44,340.55c-.36,14.53,6.83,13.35,6.83,13.35a30.57,30.57,0,0,1,2.71-4.77,21.83,21.83,0,0,1-.39-4.23c.09-5,4.18-14.49,4.18-14.49l-4.18-6.61a8.68,8.68,0,0,1,2.95-1.69c-3.86-5.91-8.29-11.88-8.29-11.88l19.21-3.18s13.68,5.65,24.47,6.31l.84,0c-.63-5.41,1.51-12.23,2.48-14.95-4.45-2.73-9.84-5.42-9.84-5.42l2.92-6a17.83,17.83,0,0,0,5.1.27c-1-4.72,0-11.27.75-15.29-7.69-5.07-22.4-12.13-22.4-12.13H136.2l-21.23,58S130.8,326,130.44,340.55Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M136.2,259.82h21.58s25.45,12.21,26.89,16,2.61,13.45-10.34,11.12l-2.92,6S187,300.78,187,303.35s1.69,10.65-9.1,10S153.46,307,153.46,307l-19.2,3.18S144.47,324,147.35,330.4s-8.28,18.67-10.07,23.51c0,0-7.2,1.16-6.84-13.37S115,317.78,115,317.78"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M141.31,327.81s-5.41-3-8.35,2.59"
      />
      <rect
        fill="currentColor"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        x="90.15"
        y="112.56"
        width="127.61"
        height="221.16"
        rx="9.26"
      />
      <path
        fill="var(--illustration-contour)"
        d="M213.8,121.53V325.32a4.61,4.61,0,0,1-4.61,4.61H98.71a4.62,4.62,0,0,1-4.61-4.61V121.53a4.62,4.62,0,0,1,4.61-4.61h24.55a3.88,3.88,0,0,1,3.63,2.52l1,2.8a3.86,3.86,0,0,0,3.63,2.52h44.79a3.85,3.85,0,0,0,3.62-2.52l1.05-2.8a3.87,3.87,0,0,1,3.62-2.52h24.55A4.61,4.61,0,0,1,213.8,121.53Z"
      />
      <path
        fill="currentColor"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M170.13,279.6H137.77a11,11,0,0,1-11-11h0a11,11,0,0,1,11-11h32.36a11,11,0,0,1,11,11h0A11,11,0,0,1,170.13,279.6Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M143.39,119.22a1.91,1.91,0,1,1-1.91-1.91A1.92,1.92,0,0,1,143.39,119.22Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M168.33,119.22a1.2,1.2,0,0,1-2.39,0,1.2,1.2,0,1,1,2.39,0Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M161.54,119.89h-14.3a.67.67,0,0,1-.67-.67h0a.68.68,0,0,1,.67-.68h14.3a.68.68,0,0,1,.68.68h0A.67.67,0,0,1,161.54,119.89Z"
      />
      <rect
        stroke="var(--illustration-contour)"
        fill="white"
        stroke-linecap="round"
        stroke-linejoin="round"
        x="100.01"
        y="136.33"
        width="107.88"
        height="110.11"
        rx="12.49"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="143.07"
        y1="182.92"
        x2="143.07"
        y2="166.37"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="143.07"
        y1="192.85"
        x2="143.07"
        y2="186.23"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="143.07"
        y1="199.04"
        x2="143.07"
        y2="195.5"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="146.19 168.31 143.07 164.44 139.94 168.31"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="164.84"
        y1="176.78"
        x2="164.84"
        y2="160.23"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="164.84"
        y1="186.71"
        x2="164.84"
        y2="180.09"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="164.84"
        y1="192.9"
        x2="164.84"
        y2="189.36"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="167.97 162.17 164.84 158.3 161.71 162.17"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="153.46"
        y1="165.67"
        x2="153.46"
        y2="149.12"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="153.46"
        y1="175.6"
        x2="153.46"
        y2="168.98"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="153.46"
        y1="181.79"
        x2="153.46"
        y2="178.25"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="156.58 151.05 153.46 147.19 150.33 151.05"
      />
      <circle
        fill="currentColor"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        cx="153.95"
        cy="188.9"
        r="17.31"
        transform="translate(-65.1 295.68) rotate(-76.81)"
      />
      <path
        fill="var(--illustration-contour)"
        d="M152.75,179.71v-1.39h2.41v1.39c2.09.43,3.16,2,3.16,4.58v.59h-2.79v-.78c0-1.34-.53-1.85-1.47-1.85s-1.47.51-1.47,1.85.53,2.28,2.51,3.86c2.41,1.93,3.24,3.4,3.24,5.54,0,2.54-1.09,4.18-3.18,4.61v1.36h-2.41v-1.36c-2.12-.43-3.19-2.07-3.19-4.61v-1.28h2.79v1.47c0,1.34.58,1.82,1.52,1.82s1.53-.48,1.53-1.82-.54-2.3-2.52-3.86c-2.41-1.9-3.24-3.4-3.24-5.54C149.64,181.77,150.68,180.16,152.75,179.71Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M140.46,216.85v-.52h.92v.52a1.56,1.56,0,0,1,1.2,1.74v.23h-1.06v-.3c0-.51-.2-.7-.56-.7s-.56.19-.56.7.21.86,1,1.46a2.54,2.54,0,0,1,1.23,2.11,1.57,1.57,0,0,1-1.21,1.75v.51h-.92v-.51a1.57,1.57,0,0,1-1.21-1.75v-.49h1.06v.56c0,.51.23.69.58.69s.58-.18.58-.69-.2-.88-.95-1.46a2.51,2.51,0,0,1-1.23-2.11A1.56,1.56,0,0,1,140.46,216.85Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M144,217.68a1.1,1.1,0,0,0,1.23-.9H146v7.12H144.9v-5.43H144Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M147.28,218.49a1.7,1.7,0,1,1,3.4,0v3.7a1.7,1.7,0,1,1-3.4,0Zm1.12,3.77c0,.51.22.7.58.7s.58-.19.58-.7v-3.84c0-.51-.23-.7-.58-.7s-.58.19-.58.7Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M151.31,218.49a1.7,1.7,0,1,1,3.39,0v3.7a1.7,1.7,0,1,1-3.39,0Zm1.12,3.77c0,.51.22.7.58.7s.57-.19.57-.7v-3.84c0-.51-.22-.7-.57-.7s-.58.19-.58.7Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M155.34,218.49a1.7,1.7,0,1,1,3.39,0v3.7a1.7,1.7,0,1,1-3.39,0Zm1.11,3.77c0,.51.23.7.58.7s.58-.19.58-.7v-3.84c0-.51-.22-.7-.58-.7s-.58.19-.58.7Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M160.53,222.82v1.08h-1.07v-1.08Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M161.26,218.49a1.7,1.7,0,1,1,3.39,0v3.7a1.7,1.7,0,1,1-3.39,0Zm1.11,3.77c0,.51.23.7.58.7s.58-.19.58-.7v-3.84c0-.51-.22-.7-.58-.7s-.58.19-.58.7Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M165.28,218.49a1.7,1.7,0,1,1,3.4,0v3.7a1.7,1.7,0,1,1-3.4,0Zm1.12,3.77c0,.51.23.7.58.7s.58-.19.58-.7v-3.84c0-.51-.22-.7-.58-.7s-.58.19-.58.7Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M134.31,230.48h1.12v7.12h-1.12Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M136.24,230.48H138c1.12,0,1.67.62,1.67,1.76v3.6c0,1.14-.55,1.76-1.67,1.76h-1.77Zm1.12,1v5.08H138c.36,0,.57-.18.57-.69v-3.7c0-.51-.21-.69-.57-.69Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M141.37,236.52v1.08h-1.08v-1.08Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M143.82,231.42c-.36,0-.58.19-.58.7v.76h-1.06v-.69c0-1.14.57-1.79,1.67-1.79s1.67.65,1.67,1.79a3.89,3.89,0,0,1-1.39,2.81c-.66.72-.84,1.05-.84,1.43a.76.76,0,0,0,0,.15h2.12v1h-3.24v-.88a3.08,3.08,0,0,1,1.12-2.26,3,3,0,0,0,1.1-2.23C144.4,231.59,144.18,231.42,143.82,231.42Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M146.9,231.38a1.1,1.1,0,0,0,1.23-.9h.75v7.12h-1.11v-5.43h-.87Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M151.85,237.68a1.6,1.6,0,0,1-1.75-1.79v-.56a1.53,1.53,0,0,1,.69-1.47v0a1.47,1.47,0,0,1-.69-1.43v-.22a1.75,1.75,0,1,1,3.49,0v.22a1.49,1.49,0,0,1-.69,1.43v0a1.53,1.53,0,0,1,.69,1.47v.56A1.6,1.6,0,0,1,151.85,237.68Zm-.64-5.05c0,.56.27.75.64.75s.63-.19.63-.75v-.4c0-.64-.28-.81-.63-.81s-.64.17-.64.81Zm.64,4c.35,0,.62-.17.63-.8v-.66c0-.56-.24-.81-.63-.81s-.64.25-.64.81v.66C151.21,236.49,151.49,236.66,151.85,236.66Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M155,231.38a1.11,1.11,0,0,0,1.23-.9h.75v7.12h-1.12v-5.43H155Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M161.58,235.83c0,1.2-.55,1.85-1.71,1.85s-1.67-.65-1.67-1.79v-.18h1.06V236c0,.51.22.7.58.7s.62-.19.62-.87V234.5h0a1,1,0,0,1-1,.62c-.82,0-1.24-.58-1.24-1.63v-1.3a1.7,1.7,0,1,1,3.4,0Zm-1.12-2.43v-1.28c0-.51-.22-.69-.58-.69s-.58.18-.58.69v1.28c0,.51.23.7.58.7S160.46,233.91,160.46,233.4Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M163.33,235.3V236c0,.51.22.69.58.69s.58-.18.58-.69v-1.57c0-.5-.23-.7-.58-.7s-.58.2-.58.7v.22h-1.06l.21-4.13h2.94v1h-1.95l-.09,1.69h0a1.05,1.05,0,0,1,1-.51c.82,0,1.24.58,1.24,1.62v1.59c0,1.14-.57,1.79-1.67,1.79s-1.67-.65-1.67-1.79v-.59Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M166.28,232.25c0-1.2.55-1.85,1.71-1.85s1.66.65,1.66,1.79v.18H168.6v-.25c0-.51-.23-.7-.58-.7s-.62.19-.62.87v1.29h0a1,1,0,0,1,1-.62c.82,0,1.24.58,1.24,1.63v1.3a1.7,1.7,0,1,1-3.4,0Zm1.12,2.43V236c0,.51.22.69.58.69s.58-.18.58-.69v-1.28c0-.51-.23-.7-.58-.7S167.4,234.17,167.4,234.68Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M173.66,230.48v1L172,237.6H170.9l1.63-6.1h-2.22v-1Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M152.54,225.68h1.3v.39h-.44v2.31H153v-2.31h-.44Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M154,226.33a.64.64,0,1,1,1.28,0v1.4a.64.64,0,1,1-1.28,0Zm.42,1.43c0,.19.08.26.22.26s.22-.07.22-.26V226.3c0-.19-.09-.26-.22-.26s-.22.07-.22.26Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M145,262.74c1.6,0,2.42,1,2.42,2.63v.33h-1.55v-.43c0-.75-.3-1-.82-1s-.82.28-.82,1,.32,1.33,1.4,2.27c1.37,1.21,1.81,2.07,1.81,3.27,0,1.67-.84,2.62-2.45,2.62s-2.45-.95-2.45-2.62v-.65h1.55v.75c0,.75.33,1,.85,1s.85-.27.85-1-.33-1.33-1.4-2.27c-1.37-1.21-1.81-2.07-1.81-3.27C142.57,263.7,143.39,262.74,145,262.74Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M150,267.27h2.25v1.49H150v3.06h2.83v1.49h-4.48V262.86h4.48v1.5H150Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M155.35,265.74h0v7.57h-1.48V262.86h2.06l1.66,6.26h0v-6.26h1.46v10.45h-1.68Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M160.23,262.86h2.6c1.64,0,2.45.91,2.45,2.59v5.28c0,1.67-.81,2.58-2.45,2.58h-2.6Zm1.64,1.5v7.46h.93c.52,0,.83-.27.83-1v-5.44c0-.75-.31-1-.83-1Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M80.44,241.86s7.19,2.22,9.71,8.47"
      />
      <path
        fill="white"
        d="M47.83,440.05c.81-13.9,1.33-28.1,2-39.53,1.44-23.71,2.52-39.61,4.68-55.8s1.79-40.64,2.87-49.18,4.3-10.35,6.84-20.5,4.82-30.34,9-33.18,16.91-8.18,16.91-8.18v57.23c7.55,11.61,16.55,14.49,16.55,14.49,5.75-16.66,5-23.27,10.79-29.39s9.9-14.11,18.94-10.77c4.3,1.59,6.6,3.22,4.44,17.81s-6.47,19.17-6.47,19.17c-.72,5,.36,19.48,2.16,26.89s-5,27.12-8.28,39.71-20.5,38.11-20.5,38.11a220.35,220.35,0,0,0,.72,28.8c.13,1.36.29,2.81.49,4.32l1.14,39.18-62.3-1.17Z"
      />
      <path
        fill="currentColor"
        fill-opacity="40%"
        d="M139.59,269.54a3.55,3.55,0,0,1,0-2.6,9.18,9.18,0,0,0-3.16-1.7c-9-3.34-13.19,4.66-18.94,10.77a17.59,17.59,0,0,0-1.45,1.74c5.21-2,12-1.93,14.79,1.88C134.28,284.4,141.32,275.77,139.59,269.54Z"
      />
      <path
        fill="currentColor"
        fill-opacity="40%"
        d="M100.55,426l-2.84,10.67s4-3.93,10-13.46c-.29-9.3.06-16.29.06-16.29s17.27-25.52,20.51-38.1,10.08-32.3,8.28-39.71-2.88-21.92-2.16-26.9a15.3,15.3,0,0,0,2.09-3.41,5.73,5.73,0,0,0-2.71-1.6,59.49,59.49,0,0,0-3.5,25.33c1.12,14.39-1.35,15.59-6.11,34.29s-12.68,39.89-16.4,43.69S102.45,421.43,100.55,426Z"
      />
      <path
        fill="currentColor"
        fill-opacity="40%"
        d="M93.74,300s-5.67.88-7.95,2.23c0,0,11.07,2.89,11.79,6s3.09,6.28,7.3,2.52c0,0,.22-2.35.66-5.77-2.68-1.2-9.47-4.93-15.39-14V233.68s-6.29,2.64-11.48,5.18c3.84.51,7.78,1.69,9.32,4.3a11.63,11.63,0,0,1-6.48,7.17c-4.09,1.84,0,32.72,0,32.72s-6.71,1.65-8.86,7.86c0,0,11,1.13,15.58,4.63A70.36,70.36,0,0,1,93.74,300Z"
      />
      <path
        fill="currentColor"
        fill-opacity="40%"
        d="M49.86,400.52c-.57,9.25-1,20.33-1.58,31.6C49.7,410.7,54,374,54,374s3.84,13.59,4.13,12.14c.86-4.25-1.8-28.3-1.56-36.87.11-3.88-.17-13.84-.52-23.55-.31,6.67-.75,13.37-1.52,19.05C52.38,360.91,51.3,376.81,49.86,400.52Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M47.83,480.7V440.05c.81-13.9,1.33-28.1,2-39.53,1.44-23.71,2.52-39.61,4.68-55.8s1.79-40.64,2.87-49.18,4.3-10.35,6.84-20.5,4.82-30.34,9-33.18,16.91-8.18,16.91-8.18v57.23c7.55,11.61,16.55,14.49,16.55,14.49,5.75-16.66,5-23.27,10.79-29.39s9.9-14.11,18.94-10.77c4.3,1.59,6.6,3.22,4.44,17.81s-6.47,19.17-6.47,19.17c-.72,5,.36,19.48,2.16,26.89s-5,27.12-8.28,39.71-20.5,38.11-20.5,38.11a220.35,220.35,0,0,0,.72,28.8c.13,1.36.29,2.81.49,4.32,0,0,.58,25.3,3.33,40.65"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M106.7,305.4s-1.8,8-5.4,9.82-8.77,0-8.77,0"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M90.15,290.91s-3.6-8-15.11-1.06"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M90.15,258.6s-.72-4.24-6.66-5.32"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M107.78,406.93c-6.84,8.66-5.54,16.93-8.17,21.25S97.34,448,97.34,448"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M58.13,323.8v30.11a149.31,149.31,0,0,0,2.52,25.71"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M134.4,302.22s0-5.14-1.44-6.68"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M130.44,286.06s-3.19-10.73-12.54-6.05"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M101.62,302.75c-7,.15-14-2.93-18.13.81"
      />
      <path
        fill="var(--illustration-contour)"
        d="M44.95,426.27h2.1a2,2,0,0,1,2,2V467a2,2,0,0,1-2,2h-2.1a0,0,0,0,1,0,0V426.27A0,0,0,0,1,44.95,426.27Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M45,431.79s33-8.62,64.63,1.21a2.41,2.41,0,0,1,1.69,2.31v20.24a1.22,1.22,0,0,1-1.43,1.2c-6.79-1.23-36.67-5.95-63.26.27Z"
      />
      <path
        fill="currentColor"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M46.38,431.42v-4.57a2.72,2.72,0,0,0-2.72-2.72H40a2.56,2.56,0,0,0-2.24,1.33l-3.25,6v34.87l3.21,3.92a2.56,2.56,0,0,0,2,.93h4a2.72,2.72,0,0,0,2.72-2.72Z"
      />
      <path
        fill="currentColor"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M44,429.17l7.53-1.4a1.45,1.45,0,0,1,1.72,1.42v4.35l-2.49,1.85v14.12h2.49v7.37a1.81,1.81,0,0,1-1.43,1.76l-7.15,1.48a1.73,1.73,0,0,1-2.08-1.7V430.87A1.73,1.73,0,0,1,44,429.17Z"
      />
      <path
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M44.32,458.19l6.14-1.4a1,1,0,0,0,.78-1V451.2"
      />
      <polyline
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        points="48.74 449.29 48.74 434.17 51.2 432.3 51.2 429.47"
      />
      <line
        stroke="var(--illustration-contour)"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="38.77"
        y1="426.89"
        x2="38.77"
        y2="467.28"
      />
      <path
        fill="white"
        d="M68.56,442.66h0a1.11,1.11,0,0,1-1.1-1.11v-2.41a1.1,1.1,0,0,1,1.1-1.1h0a1.11,1.11,0,0,1,1.11,1.1v2.41A1.11,1.11,0,0,1,68.56,442.66Z"
      />
      <path
        fill="white"
        d="M85.56,443.1h0a1.11,1.11,0,0,1-1.11-1.1v-2.41a1.11,1.11,0,0,1,1.11-1.1h0a1.1,1.1,0,0,1,1.1,1.1V442A1.1,1.1,0,0,1,85.56,443.1Z"
      />
      <path
        fill="white"
        d="M100.68,445h0a1.11,1.11,0,0,1-1.11-1.11v-2.41a1.11,1.11,0,0,1,1.11-1.1h0a1.1,1.1,0,0,1,1.1,1.1v2.41A1.11,1.11,0,0,1,100.68,445Z"
      />
    </g>
  </svg>
</template>

<style>
:root {
  --illustration-contour: #0f172a;
}

.dark {
  --illustration-contour: #475569;
}
</style>
