<template>
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500">
    <g>
      <path
        fill="currentColor"
        fill-opacity="20%"
        d="M331.94,22.1c-39.85.69-70.59,33.28-104.71,53.9C175.7,107.14,109.52,113.46,66,155.07,36.21,183.56,21.47,226.5,25.66,267.51S52.35,347,84.92,372.23c20.77,16.12,45.15,27,70,35.47,25.21,8.61,51.45,15,78.09,15.31,36.92.47,73.19-10.7,106.59-26.44,23.22-11,45.61-24.34,63.88-42.39,17.26-17.05,30.49-38,39.56-60.49,22.4-55.63,18.83-119.37-.59-176.11C426.42,70.65,387.14,21.15,331.94,22.1Z"
      />
    </g>
    <g>
      <path
        fill-opacity="10%"
        d="M356.66,251.65c-.28-8.16-.81-16.86-1.69-25.86-1.18-18-3.44-34.76-7.52-45.31h0c-22.46-4.6-139.25,3.92-139.34,3.93l-.73-3.25c-.09-.43-.19-.86-.29-1.29l-.51-2.26-.15-.52c-.76-3.05-1.61-6.08-2.61-9.07h0s-88.54-7.9-117.91-.23h0c0,.26,0,.51,0,.77l.09,7.25c.09,14-.24,27.17.49,41.63v.1h0a2.56,2.56,0,0,0-.43,0,6.2,6.2,0,0,0-5,4.11h0a6.17,6.17,0,0,0-.32,2.62l.42,4.5c0,.07,0,.14,0,.22,1.08,11.5,2.14,22.89,2.14,22.89h0s.55,0,1.47.1l4.87.34C79.25,253.3,68,267,68.44,278.71s-10.83,33.88-8,46.59c2.21,9.93,13.31,25.88,18.74,38-.13.75-.27,1.55-.43,2.41L54.5,414.37c1.62-2.38,16.21,61.19,84.14,47.73L187,383.39h0a93.4,93.4,0,0,0,14.71-13.83,23.68,23.68,0,0,0,4.55-6.3h0a33.81,33.81,0,0,0,3.91-8.88c.22-.82.44-1.64.68-2.47,3.94-10,9.21-23.74,13.06-33.23,50.86-1.5,104.33-4.61,131.18-10.93h.05C357.7,290.1,357.75,270.29,356.66,251.65ZM95.27,296.12a76.66,76.66,0,0,1-.75-8.85q.89,4.68,1.92,9.34C96.05,296.44,95.66,296.27,95.27,296.12Z"
      />
      <path
        fill="white"
        d="M49,411.27s21.75,64.29,89.68,50.83c0,0,15.07-70.76,27.7-81.18,8.15-6.72,26.14-18.76,30-27.48,3.92-8.88,19.29-50.12,22.28-55.1S186.2,236,186.2,236L111.65,274.5,70.17,348.19S62.21,395.76,49,411.27Z"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M69.44,353.85s-5.32,39.75-22.89,60.52"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M138.64,462.1s15-68.47,27.7-81.18c0,0,28.72-16.71,33.9-36.36,3.58-13.62,10.1-29.31,13.22-36.43"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M144.69,310.29c-1.11-.69,17.37,14.13,18.09,34.27"
      />
      <path
        fill="currentColor"
        d="M345.21,297.92C285.34,312,92.54,310.14,92.54,310.14c-1.63-3.42-3.59-12-5.48-22-3.79-20-7.33-45.59-7.33-45.59l-6.38-.44s-1.06-11.39-2.15-22.89,5.37-11.49,5.37-11.49l-.46-39.81L76,158c29.37-7.67,117.91.23,117.91.23l2.76,9.6,1.54,6.8,139.35-3.93C350.35,203.69,345.21,297.92,345.21,297.92Z"
      />
      <rect
        fill="var(--illustration-contour)"
        x="223.81"
        y="203.97"
        width="105.03"
        height="32.2"
        rx="6.29"
        transform="translate(-10.23 13.5) rotate(-2.75)"
      />
      <polygon
        fill="var(--illustration-contour)"
        points="329.64 245.68 329.93 248.76 224.69 252.84 224.6 250.07 329.64 245.68"
      />
      <polygon
        fill="var(--illustration-contour)"
        points="330.16 256.41 330.25 259.18 225.02 263.07 224.93 260.3 330.16 256.41"
      />
      <path
        class="cls-7"
        d="M337.55,170.65s15.54,73.64,7.66,127.27c0,0-98.2,11.52-131.82,9.66L212,289.66l2.81-.44S201.24,186.93,198.2,174.58C198.2,174.58,315.14,166,337.55,170.65Z"
      />
      <path
        fill="white"
        fill-opacity="80%"
        d="M208.77,289.39,210,302.94s-99,4.33-107.69,1.19.46-8.19,23.48-10S208.77,289.39,208.77,289.39Z"
      />
      <ellipse
        fill="var(--illustration-contour)"
        cx="149.68"
        cy="223.75"
        rx="9.05"
        ry="8.18"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M79.73,242.52a350.84,350.84,0,0,0,12.8,67.62c42-.35,84-.83,125.93-2.17s85.12-4.17,126.75-10.05c0,0,7.46-65.8-7.66-127.27-16.69-3.47-138.89,3.91-138.89,3.91"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M208.77,289.39,210,302.94l-94.18,2.17c-3.89.33-9.94.84-13.51-1-1.63-.84-2.8-2.88-2-4.54.69-1.5,2.52-2,4.13-2.36,11.53-2.37,23.29-3.37,35-4.24q37.67-2.78,75.44-3.77c-.52,0-6.67-50.67-7.35-55.34-2.66-18.31-5.2-36.69-8.49-54.89a146.73,146.73,0,0,0-5.08-20.8c-38.81-2.78-79.34-5.35-117.91-.24.35,17.19-.28,32.53.59,49.74"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M91.09,305a8.24,8.24,0,0,1-.4-2"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-dasharray="3.97 3.97"
        d="M91.15,299.17c1.29-3.7,5.48-8,17.66-9.38,21.51-2.39,68.5-6.7,95.86-7.72"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M206.66,282l2-.06-.27-2"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-dasharray="3.93 3.93"
        d="M207.85,276.06c-5-35.7-10.46-71.13-16.75-106.62"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M190.75,167.5c-.12-.65-.23-1.31-.35-2l-2-.15"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-dasharray="4.09 4.09"
        d="M184.33,165.1c-17.62-1.18-69.71-4.27-104.19-1.87"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M78.09,163.38l-2,.17"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M73.35,242.08l-2.58-27.62a6.2,6.2,0,0,1,5.81-6.77c15.92-.92,57.09-3.08,72.89-1.81,7.59.61,15.82,3,19.52,10.29,3,6,3.51,15-1.62,19.87-7.8,7.47-22.58,6.89-32.64,7.25C116,244,73.37,242.24,73.35,242.08Z"
      />
      <path
        fill="var(--illustration-contour)"
        d="M171.25,225.2c0-.77,3.94,15.86-10.46,21.35s-81.06,3.76-81.06,3.76v-7.79s47.16,1,55,.77S170.4,245.69,171.25,225.2Z"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M157.4,318.12S169.18,325,173.59,338"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-dasharray="4 4"
        d="M71.09,211.85s57.78-2.72,72.49-2.23,21.9,3.72,23.37,12.94-7.45,15-14.55,16-79.05,1.4-79.05,1.4"
      />
      <path
        fill="white"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M110.79,323.49s-8.94-31.07-25.42-37.19c0,0-3.29-21.21,3.77-22.37S116,249.7,110.79,233.4s-20.71,8.18-31.06,9.12-21.66,14.62-21.19,26.37-10.83,33.88-8,46.59,20.24,35.31,21.65,47.08"
      />
      <path
        fill="white"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M89.13,237.73s12.39,2.26,19.14-5.61-1-8.37-1-8.37Z"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M67,255.28a13.63,13.63,0,0,1,6.37,2.09"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M66.61,259.79a11.82,11.82,0,0,1,6.19,2.45"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M100.07,301.41s53.1-2.74,67-2"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M154.19,295.42s13.36-1,21.68,0"
      />
      <line
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="188.02"
        y1="299.32"
        x2="209.63"
        y2="299.32"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-dasharray="4 4"
        d="M201.09,180.64s94.9-8.47,133-3.63c0,0,10.29,18.91,8.05,115.89A1213.11,1213.11,0,0,1,213,302.5"
      />
    </g>
    <g>
      <g fill-opacity="10%">
        <path
          d="M458,314.82c-2.69-11.47-6.64-15.78-10.6-19.09,0,0-.09-6.52-5.17-10.21a35.58,35.58,0,0,0-4.37-3.23c-4.77-3.57-10-7.62-10-7.62-2.47-7.15-5.81-15.8-9.42-24.31l.18,1.93c-3.7-9-7.33-18-8.35-21l0-.06c1.05,2.88,2.17,5.66,3.36,8.23L400,191.51c-.32-1.93-.65-3.83-1-5.66-.64-5.39-2.42-15.62-6.67-16.33a2.61,2.61,0,0,0-.48-.05s-5-2.17-5,6.73c-6,2-8.27,6.44-8.85,11.79a16.18,16.18,0,0,0-.1,5.09c.1,2,.33,4,.63,5.95l-19.13-25.18-1.86-2.45-23.7-31.19L302,98.25a14.92,14.92,0,0,0-20.9-2.86l-39.51,30-15.44,11.72c-11.84,6.17-8.75,18.34-5.47,22.66l68.95,90.76a7.74,7.74,0,0,0-1.07-.05s-2-4.65-6.85-3.91a5.59,5.59,0,0,0-2.9,1,5.11,5.11,0,0,0-1.93,1.77,13.6,13.6,0,0,0-1.45,2.07c-2.49,4.25-.2,14.63,3.39,27.32.23.82.45,1.61.67,2.35l0,.12c.3,1,.6,2.1.92,3.17,3.51,12.17,10.07,36.07,11.28,47.78.09,1.14.14,2.25.17,3.33.08,13.49-5.92,36.09-3.34,45.42.34,1.22.68,2.4,1,3.55,1.92,6.24,3.63,12.14,6.64,18,3.66,7.14,8,14,12.1,20.92q11.25,19.08,23.27,37.69c3.68,5.7,8,10.89,12.19,16.27,1.91,2.45,6.1,6.15,6.82,9.3,0,0,37.07,3.07,57.82-10.12L406.44,447l13.91-80c4-6,16.2-17.81,18.7-21.54,1.26-1.86,2.5-3.69,3.7-5.49,9.15-8.54,13.74-15.21,15.74-22.87C458.49,317.13,458.35,316.26,458,314.82Zm-129.8,15.65v0Zm1.48,1.82-.18-.35a43.32,43.32,0,0,0,11.89,3.47C337.07,334.91,332.19,334.22,329.66,332.29Zm15.61,3.58h0c.65.06,1.31.1,2,.14A14.62,14.62,0,0,1,345.27,335.87Zm-16-4.39-2.49-4.94c-3.47-10-11.3-33.38-11.33-33.48h0a135,135,0,0,0-6.11-19.77,11.79,11.79,0,0,0,11.06-1.73l18.95-14.4c2,1.18,4,2.24,5.93,3.18L363,274.21l-14.07,57.87ZM401.1,198.21h0c1.47,6.91,3.81,16.85,6.88,26.48A173.57,173.57,0,0,1,401.1,198.21Z"
        />
        <path d="M320.66,476.25l.11.51S320.74,476.57,320.66,476.25Z" />
      </g>
      <path
        fill="white"
        d="M386.47,200.34c1.12-.72,17-9.36,24.88,16.55s17.27,70,17.27,70l-18.88,3.7s-17.5-9.31-33.18-32.63c0,0-22-5.7-31.55-19.24-8.7-12.3,18.06-57.47,18.06-57.47Z"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M379.66,232.59c4.68,7,13.54,21.77,25.49,26.42"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M366.42,240.13c2.21.88,7.63,4,10.14,4.11,0,0,11,21.85,29.26,28.94"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M356.3,248.18c6.77,5.66,14.83,9.53,20.26,9.79,0,0,11,21.84,29.26,28.93"
      />
      <rect
        fill="var(--illustration-contour)"
        x="268.7"
        y="96.22"
        width="98.85"
        height="161.15"
        rx="10.92"
        transform="translate(-42.14 228.45) rotate(-37.22)"
      />
      <path
        fill="currentColor"
        d="M395.08,215.85,340.13,257.6a14.89,14.89,0,0,1-20.9-2.86L239.8,150.18a14.91,14.91,0,0,1,2.85-20.89l15.44-11.73,39.51-30a14.92,14.92,0,0,1,20.9,2.86l31.88,42,23.69,31.19L397.94,195A14.91,14.91,0,0,1,395.08,215.85Z"
      />
      <path
        fill-opacity="10%"
        d="M350.38,132.35l-92.29-14.79,39.51-30a14.92,14.92,0,0,1,20.9,2.86Z"
      />
      <path
        fill-opacity="10%"
        d="M395.08,215.85,340.13,257.6a14.89,14.89,0,0,1-20.9-2.86L239.8,150.18a14.78,14.78,0,0,1-2.67-5.73L375.93,166l22,29A14.91,14.91,0,0,1,395.08,215.85Z"
      />
      <rect
        fill="white"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        x="298.63"
        y="98.95"
        width="21.02"
        height="28.26"
        rx="4.08"
        transform="translate(-5.43 210.03) rotate(-37.22)"
      />
      <line
        fill="white"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="300.8"
        y1="101.67"
        x2="317.55"
        y2="124.43"
      />
      <line
        fill="white"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        x1="300.8"
        y1="119.48"
        x2="317.55"
        y2="106.78"
      />
      <polygon
        fill="var(--illustration-contour)"
        points="333.25 250.78 330.39 252.89 295.51 206.97 298.34 204.82 333.25 250.78"
      />
      <polygon
        fill="var(--illustration-contour)"
        points="338.08 247.2 335.23 249.32 300.31 203.33 303.12 201.19 338.08 247.2"
      />
      <polygon
        fill="var(--illustration-contour)"
        points="342.92 243.64 340.06 245.74 305.09 199.7 307.92 197.56 342.92 243.64"
      />
      <polygon
        fill="var(--illustration-contour)"
        points="347.75 240.06 344.9 242.16 310.39 196.72 313.2 194.58 347.75 240.06"
      />
      <polygon
        fill="var(--illustration-contour)"
        points="352.97 236.21 350.12 238.31 315.55 192.8 318.38 190.66 352.97 236.21"
      />
      <polygon
        fill="var(--illustration-contour)"
        points="352.97 182.66 350.12 184.76 315.55 139.25 318.38 137.11 352.97 182.66"
      />
      <rect
        fill="var(--illustration-contour)"
        x="321.41"
        y="88.07"
        width="5.55"
        height="161.04"
        transform="translate(-35.94 230.44) rotate(-37.22)"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M395.08,215.85,340.13,257.6a14.89,14.89,0,0,1-20.9-2.86L239.8,150.18a14.91,14.91,0,0,1,2.85-20.89l15.44-11.73,39.51-30a14.92,14.92,0,0,1,20.9,2.86l31.88,42,23.69,31.19L397.94,195A14.91,14.91,0,0,1,395.08,215.85Z"
      />
      <path
        fill="white"
        d="M350.57,486.62s64,22.76,83.31-26.8c0,0-30.27-36.79-13.74-66.9,10.4-18.95,39.15-60.78,39.15-60.78s12-11.41,15.75-22.87c0,0-2.06-12.76-11.12-21.4,0,0-.09-6.52-5.17-10.2s-14.39-10.86-14.39-10.86S429,230.07,426.76,223.4s-6.83-19.19-9.12-33.05-4.4-28.78-9.25-28.74c0,0-5-2.17-5,6.73,0,0-10.49,5.4-8.95,16.87s3,17.37,2.57,23.83,1.65,28.82,6.83,35.83c0,0,1.73,31.41,4.32,39.59.08.25.16.48.24.68,2.73,6.91-24.54,33.63-39.3,40.78,0,0-1.54,2.88-7.27,2.1s-15.43-1.16-17.1-5.41S332,285.2,332,285.2s-9-42.55-26.91-42.55c0,0-2.73-6.5-9.75-3s-2.42,22.92.68,33.51,11,38.44,12.26,51.25-6,38.47-3.19,48.57,5.75,16.91,14.46,33.43S347.51,472.21,350.57,486.62Z"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M350.57,486.62s-13.68-51.53-33.81-84.85-8.33-45.11-8.33-72.87-22.59-74.95-16.5-85.36,14.42-2.08,17.89,7.63-1.39,15.27-11.1,16"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M305.06,242.65s11.68-.71,18.86,18,18.68,66.1,30.66,80.47"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M345.13,323.6c.06.15,16.72,9.44,42.59,1.3"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M307.28,297.48a11.7,11.7,0,0,1,10.93-4.15"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M309.5,303.36a9.3,9.3,0,0,1,9.4-3.06"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M403.38,168.34C386.38,174,399,199.68,397,209s3.25,27.31,7.32,37c0,11.4,4.07,39.07,4.07,39.07-2.51,8.8-23.47,34.75-43.85,42.88"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M415.86,181.26s-1.26-19.65-7.47-19.65-5.83,13.25-3.24,23.07S417.75,193.05,415.86,181.26Z"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M415.86,181.26s5.29,30.9,14.24,50.31"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M440.66,303.92c4.33-7,6.25-13.65,7.85-22.09-.4-5.3-6.48-22.59-13.58-39.33"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M444.91,268.44c7,5.28,19.26,9.34,17.84,18.46a76.43,76.43,0,0,1-9.42,25.87"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M435.44,462.1s-24.48-34-19.58-57.47c3.61-17.28,26.8-47.47,43.43-72.49,9.16-8.54,13.74-15.21,15.75-22.87-2.94-14.45-7.7-18.67-12.29-22.37"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M442,274.05a20.68,20.68,0,0,0-16.32,2.22"
      />
      <path
        fill="none"
        stroke="var(--illustration-contour)"
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M440.63,279.87a15.33,15.33,0,0,0-9.69,1.7"
      />
    </g>
  </svg>
</template>

<style>
:root {
  --illustration-contour: #0f172a;
}

.dark {
  --illustration-contour: #475569;
}
</style>
