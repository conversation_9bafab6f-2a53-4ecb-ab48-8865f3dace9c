<script setup lang="ts">
import type { Language } from '../app/types/auth'
import { generateProfileItemId, populateLanguageForm } from '../app/utils/profile-forms'

interface Props {
  open: boolean
  language?: Language | null
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: Language): void
}

const props = withDefaults(defineProps<Props>(), {
  language: null,
})

const emit = defineEmits<Emits>()

// Form state
const form = reactive<Omit<Language, 'id'> & { id?: string }>({
  name: '',
  mastery: '',
  level: 0,
  icon: '',
})

const errors = reactive<Record<string, string>>({})
const isSubmitting = ref(false)
const submitError = ref<string>('')

// Mastery options
const masteryOptions = [
  'Beginner',
  'Elementary',
  'Intermediate',
  'Advanced',
  'Fluent',
  'Native',
]

// Computed properties
const editing = computed(() => !!props.language)

const isFormValid = computed(() => {
  return form.name.trim()
    && form.mastery.trim()
    && form.level >= 0
    && form.level <= 100
    && Object.keys(errors).length === 0
})

// Watch for language changes to populate form
watch(() => props.language, (language) => {
  const formData = populateLanguageForm(language)
  Object.assign(form, formData)

  // Clear errors
  Object.keys(errors).forEach(key => delete errors[key])
  submitError.value = ''
}, { immediate: true })

// Reset form
function resetForm() {
  const emptyForm = populateLanguageForm(null)
  Object.assign(form, emptyForm)

  // Clear errors
  Object.keys(errors).forEach(key => delete errors[key])
  submitError.value = ''
}

// Validation
function validateForm() {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  // Required field validation
  if (!form.name.trim()) {
    errors.name = 'Language name is required'
  }

  if (!form.mastery.trim()) {
    errors.mastery = 'Mastery level is required'
  }

  // Level validation
  if (form.level < 0 || form.level > 100) {
    errors.level = 'Level must be between 0 and 100'
  }

  return Object.keys(errors).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true
  submitError.value = ''

  try {
    const languageData: Language = {
      id: form.id || generateProfileItemId(),
      name: form.name.trim(),
      mastery: form.mastery.trim(),
      level: Number(form.level),
      icon: form.icon?.trim() || undefined,
    }

    emit('submit', languageData)
  }
  catch (error: any) {
    submitError.value = error.message || 'Failed to save language'
  }
  finally {
    isSubmitting.value = false
  }
}

// Watch for open state to reset form when modal opens
watch(() => props.open, (open) => {
  if (open && !props.language) {
    resetForm()
  }
})
</script>

<template>
  <BaseModal
    :open="open"
    size="md"
    @close="$emit('close')"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <BaseHeading
          as="h3"
          size="lg"
          weight="semibold"
          class="text-muted-900 dark:text-white"
        >
          {{ editing ? 'Edit' : 'Add' }} Language
        </BaseHeading>
        <BaseButton
          size="sm"
          variant="ghost"
          @click="$emit('close')"
        >
          <Icon name="lucide:x" class="size-4" />
        </BaseButton>
      </div>
    </template>

    <form class="space-y-6" @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 gap-4">
        <!-- Language Name -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.name"
          :disabled="isSubmitting"
          required
        >
          <BaseLabel>Language</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.name"
            placeholder="e.g., English, Spanish, Mandarin"
            :aria-invalid="errors.name ? 'true' : undefined"
            required
          />
        </BaseField>

        <!-- Mastery Level -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.mastery"
          :disabled="isSubmitting"
          required
        >
          <BaseLabel>Mastery Level</BaseLabel>
          <BaseListbox
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.mastery"
            :items="masteryOptions"
            placeholder="Select your mastery level"
            :aria-invalid="errors.mastery ? 'true' : undefined"
            required
          />
        </BaseField>

        <!-- Proficiency Level (0-100) -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.level"
          :disabled="isSubmitting"
          required
        >
          <BaseLabel>Proficiency Level ({{ form.level }}%)</BaseLabel>
          <div class="space-y-3">
            <input
              :ref="inputRef"
              v-bind="inputAttrs"
              v-model="form.level"
              type="range"
              min="0"
              max="100"
              step="5"
              class="w-full h-2 bg-muted-200 dark:bg-muted-700 rounded-lg appearance-none cursor-pointer slider"
              :aria-invalid="errors.level ? 'true' : undefined"
            >
            <div class="flex justify-between text-xs text-muted-500">
              <span>0%</span>
              <span>25%</span>
              <span>50%</span>
              <span>75%</span>
              <span>100%</span>
            </div>
          </div>
        </BaseField>

        <!-- Icon URL -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.icon"
          :disabled="isSubmitting"
        >
          <BaseLabel>Flag/Icon URL (Optional)</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.icon"
            type="url"
            placeholder="https://example.com/flag.png"
            :aria-invalid="errors.icon ? 'true' : undefined"
          />
        </BaseField>
      </div>

      <!-- Error Message -->
      <div v-if="submitError" class="p-3 bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg">
        <div class="flex items-start gap-3">
          <Icon name="lucide:alert-circle" class="size-4 text-danger-500 mt-0.5 flex-shrink-0" />
          <div>
            <BaseHeading as="h4" size="xs" class="text-danger-700 dark:text-danger-400 mb-1">
              Error saving language
            </BaseHeading>
            <BaseParagraph size="sm" class="text-danger-600 dark:text-danger-300">
              {{ submitError }}
            </BaseParagraph>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-end gap-3 pt-4 border-t border-muted-200 dark:border-muted-800">
        <BaseButton
          variant="ghost"
          :disabled="isSubmitting"
          @click="$emit('close')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          type="submit"
          variant="primary"
          :disabled="!isFormValid || isSubmitting"
          :loading="isSubmitting"
        >
          {{ editing ? 'Update' : 'Add' }} Language
        </BaseButton>
      </div>
    </form>
  </BaseModal>
</template>

<style scoped>
/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .slider::-webkit-slider-thumb {
  background: #60a5fa;
  border-color: #1f2937;
}

.dark .slider::-moz-range-thumb {
  background: #60a5fa;
  border-color: #1f2937;
}
</style>
