<script setup lang="ts">
import type { UserListProps } from '../app/types/ui'

const props = withDefaults(
  defineProps<UserListProps>(),
  {
    users: () => [
      {
        id: 0,
        picture: '/img/avatars/16.svg',
        name: '<PERSON>',
        position: 'Business Analyst',
        progress: 18,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        role: 'Business Analyst',
      },
      {
        id: 1,
        picture: '/img/avatars/10.svg',
        name: '<PERSON>',
        position: 'Project Manager',
        progress: 22,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        role: 'Project Manager',
      },
      {
        id: 2,
        picture: '/img/avatars/6.svg',
        name: '<PERSON>',
        position: 'Product Manager',
        progress: -12,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        role: 'Product Manager',
      },
      {
        id: 3,
        picture: '/img/avatars/12.svg',
        name: '<PERSON>',
        position: 'Product Manager',
        progress: 32,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        role: 'Product Manager',
      },
      {
        id: 4,
        picture: '/img/avatars/11.svg',
        name: '<PERSON>',
        position: 'Mobile Developer',
        progress: -4,
        firstN<PERSON>: '<PERSON>',
        lastName: 'Zanuk',
        role: 'Mobile Developer',
      },
      {
        id: 5,
        picture: '/img/avatars/5.svg',
        name: 'Clarissa Miller',
        position: 'UI/UX Designer',
        progress: 32,
        firstName: 'Clarissa',
        lastName: 'Miller',
        role: 'UI/UX Designer',
      },
    ],
    rounded: 'sm',
    showProgress: true,
  },
)
</script>

<template>
  <div class="space-y-6">
    <div
      v-for="user in props.users"
      :key="user.id"
      class="flex items-center gap-2"
    >
      <BaseAvatar
        :src="user.picture || user.image"
        size="sm"
        :rounded="props.rounded"
      />
      <div>
        <BaseHeading
          as="h3"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-muted-100"
        >
          <span>{{ user.name || `${user.firstName} ${user.lastName}` }}</span>
        </BaseHeading>
        <BaseText size="xs" class="text-muted-600 dark:text-muted-400">
          <span>{{ user.position || user.role }}</span>
        </BaseText>
      </div>
      <div class="ms-auto flex items-center justify-end gap-4">
        <BaseParagraph
          v-if="props.showProgress && user.progress !== undefined"
          size="sm"
          weight="semibold"
          :class="user.progress > 0 ? 'text-success-500' : 'text-destructive-500'"
        >
          <span>{{ user.progress }}%</span>
        </BaseParagraph>
        <BaseButton
          to="#"
          variant="muted"
          size="icon-sm"
          :rounded="props.rounded"
        >
          <Icon name="lucide:chevron-right" class="size-4" />
        </BaseButton>
      </div>
    </div>
  </div>
</template>
