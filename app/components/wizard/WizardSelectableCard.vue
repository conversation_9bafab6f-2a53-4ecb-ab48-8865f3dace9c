<script setup lang="ts">
import { SELECTION_CARD_CLASSES } from '~/utils/wizardCardPattern'

export interface WizardSelectableCardProps {
  /** Whether the card is selected */
  selected?: boolean
  /** Whether the card should show hover effects */
  hoverable?: boolean
  /** Whether the card is disabled */
  disabled?: boolean
  /** Optional custom class for the card wrapper */
  class?: string
}

const props = withDefaults(defineProps<WizardSelectableCardProps>(), {
  selected: false,
  hoverable: true,
  disabled: false,
  class: '',
})

const emit = defineEmits<{
  select: []
}>()

const cardClasses = computed(() => [
  SELECTION_CARD_CLASSES.base,
  {
    [SELECTION_CARD_CLASSES.selected]: props.selected,
    [SELECTION_CARD_CLASSES.disabled]: props.disabled,
    'cursor-pointer': !props.disabled,
    'pointer-events-none': props.disabled,
  },
  props.class,
])

function handleClick() {
  if (!props.disabled) {
    emit('select')
  }
}
</script>

<template>
  <div
    :class="cardClasses"
    @click="handleClick"
  >
    <!-- Image/Icon slot -->
    <div v-if="$slots.image" class="mb-4 flex justify-center">
      <slot name="image" />
    </div>

    <!-- Icon slot (for smaller icons) -->
    <div v-if="$slots.icon" class="mb-3 flex items-center">
      <div
        class="flex items-center justify-center w-8 h-8 rounded-lg mr-3"
        :class="[
          selected
            ? 'bg-primary-500 text-white'
            : 'bg-muted-100 dark:bg-muted-800 text-muted-600 dark:text-muted-400',
        ]"
      >
        <slot name="icon" />
      </div>
      <!-- Title inline with icon -->
      <div class="flex-1">
        <slot name="title" />
      </div>
    </div>

    <!-- Content section -->
    <div class="flex flex-col" :class="{ 'my-4': !$slots.icon }">
      <!-- Title (when not using icon layout) -->
      <div v-if="$slots.title && !$slots.icon" class="mb-2">
        <slot name="title" />
      </div>

      <!-- Description -->
      <div v-if="$slots.description" class="mb-4">
        <slot name="description" />
      </div>

      <!-- Custom content slot -->
      <slot />
    </div>

    <!-- Footer/Button section -->
    <div v-if="$slots.footer" class="mt-auto">
      <slot name="footer" />
    </div>

    <!-- Selection indicator -->
    <div
      v-if="selected && !$slots.footer"
      class="absolute top-2 right-2 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center"
    >
      <Icon name="lucide:check" class="h-3 w-3 text-white" />
    </div>
  </div>
</template>
