<script setup lang="ts">
import type { StarterSwitcherProps } from '../app/types/ui'

const props = withDefaults(
  defineProps<StarterSwitcherProps>(),
  {
    starters: () => [
      {
        id: 'sidebar',
        name: 'Sidebar',
        description: 'Double sidebar navigation',
        url: '/starters/sidebar',
        image: '/img/illustrations/switcher/layout-sidebar-default.svg',
      },
      {
        id: 'collapse',
        name: 'Collapse',
        description: 'Collapsible sidebar navigation',
        url: '/starters/collapse',
        image: '/img/illustrations/switcher/layout-collapse-default.svg',
      },
      {
        id: 'sidenav',
        name: 'Sidenav',
        description: 'Single sidebar navigation',
        url: '/starters/sidenav',
        image: '/img/illustrations/switcher/layout-collapse-default.svg',
      },
      {
        id: 'topnav',
        name: 'Topnav navigation',
        description: 'Navbar dropdowns and links',
        url: '/starters/topnav',
        image: '/img/illustrations/switcher/layout-topnav-default.svg',
      },
      {
        id: 'topnav-slim',
        name: 'Topnav slim navigation',
        description: 'Top navigation with tabs',
        url: '/starters/topnav-slim',
        image: '/img/illustrations/switcher/layout-topnav-default.svg',
      },
    ],
    showDescriptions: true,
    columns: 3,
  },
)
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 py-20">
    <div class="max-w-3xl mx-auto">
      <div class="bg-muted-200/60 dark:bg-muted-800 rounded-xl p-4 grid grid-cols-2 md:grid-cols-3 gap-3">
        <NuxtLink v-for="starter in props.starters" :key="starter.id" :to="starter.url" class="relative block bg-white dark:bg-muted-950 border border-muted-300 dark:border-muted-800 p-4 rounded-xl cursor-pointer" exact-active-class="border-primary-500! [&>svg]:block! [&>div>img]:opacity:100!">
          <Icon name="solar:check-circle-bold-duotone" class="absolute top-3 end-3 size-6 text-primary-500 dark:text-primary-400 hidden" />
          <div class="flex flex-col items-center justify-center">
            <img
              :src="starter.image"
              alt="Illustration"
              class="w-28 mb-2 opacity-60"
            >
            <BaseHeading as="h4" size="sm" class="text-center text-muted-900 dark:text-muted-100">
              {{ starter.name }}
            </BaseHeading>
            <BaseParagraph v-if="props.showDescriptions" size="xs" class="text-center text-muted-600 dark:text-muted-400">
              {{ starter.description }}
            </BaseParagraph>
          </div>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
