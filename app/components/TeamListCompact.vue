<script setup lang="ts">
import type { TeamListCompactProps } from '../app/types/ui'

const props = withDefaults(
  defineProps<TeamListCompactProps>(),
  {
    members: () => [
      {
        id: 0,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        image: '/img/avatars/3.svg',
        text: 'EC',
        role: 'UI/UX designer',
      },
      {
        id: 2,
        firstName: 'Tara',
        lastName: '<PERSON><PERSON>',
        image: '/img/avatars/4.svg',
        text: 'TS',
        role: 'Software architect',
      },
      {
        id: 3,
        firstName: '<PERSON>',
        lastName: 'Liversk<PERSON>',
        image: undefined,
        text: 'NL',
        role: 'UI/UX designer',
      },
      {
        id: 4,
        firstName: '<PERSON>any',
        lastName: '<PERSON>',
        image: '/img/avatars/25.svg',
        text: 'MW',
        role: 'Software architect',
      },
      {
        id: 5,
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        image: '/img/avatars/14.svg',
        text: 'J<PERSON>',
        role: 'Product manager',
      },
    ],
    actions: false,
    avatarSize: 'xs',
  },
)
</script>

<template>
  <div class="mb-2 space-y-5">
    <div
      v-for="member in props.members"
      :key="member.id"
      class="flex items-center gap-3"
    >
      <BaseAvatar
        :src="member.image"
        :text="member.text"
        :size="props.avatarSize"
        class="bg-primary-100 dark:bg-primary-500/20 text-primary-500 shrink-0"
      />
      <div>
        <BaseHeading
          as="h4"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ member.firstName }} {{ member.lastName.slice(0, 1) }}.</span>
        </BaseHeading>
        <BaseParagraph size="xs">
          <span class="text-muted-600 dark:text-muted-400">
            {{ member.role }}
          </span>
        </BaseParagraph>
      </div>
      <div v-if="props.actions" class="ms-auto flex items-center">
        <BaseButton
          rounded="lg"
          variant="muted"
          size="icon-md"
          class="scale-75"
        >
          <Icon name="solar:phone-rounded-linear" class="size-6" />
        </BaseButton>
        <BaseButton
          variant="muted"
          rounded="lg"
          size="icon-md"
          class="scale-75"
        >
          <Icon name="solar:videocamera-record-linear" class="size-6" />
        </BaseButton>
      </div>
    </div>
  </div>
</template>
