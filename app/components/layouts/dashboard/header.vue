<script setup lang="ts">
withDefaults(defineProps<{
  avatarSrc?: string
  displayName?: string
  greeting?: string
  subtitle?: string
}>(), {
  avatarSrc: '/img/avatars/10.svg',
  displayName: 'there',
  greeting: 'Welcome back',
  subtitle: 'Happy to see you again on your dashboard.',
})
</script>

<template>
  <!-- Header -->
  <div class="mb-8 flex flex-col justify-between md:flex-row md:items-center">
    <div
      class="lg:landscape:max-w-full flex max-w-[425px] flex-col items-center gap-4 text-center md:flex-row md:text-start lg:max-w-full"
    >
      <BaseAvatar :src="avatarSrc" size="lg" />
      <div>
        <BaseHeading
          as="h2"
          size="xl"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>{{ greeting }}, {{ displayName }}</span>
        </BaseHeading>
        <BaseParagraph>
          <span class="text-muted-600 dark:text-muted-400">
            {{ subtitle }}
          </span>
        </BaseParagraph>
      </div>
    </div>
    <div
      class="mt-4 flex items-center justify-center gap-2 md:mt-0 md:justify-start"
    >
      <BaseButton>
        <span>View Reports</span>
      </BaseButton>
      <BaseButton variant="primary">
        <span>Manage Store</span>
      </BaseButton>
    </div>
  </div>
</template>
