<script setup lang="ts">
import type { DataViewConfig, DataViewItem } from '~~/components/data-view/types'

interface Props {
  /** Enable feature flag for DataView vs legacy rendering */
  useDataView?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  useDataView: false
})

// Enhanced data fetching with proper validation and error handling
const route = useRoute()
const router = useRouter()

// Page state management with validation
const page = computed({
  get: () => {
    const pageParam = route.query.page as string
    const pageNum = Number.parseInt(pageParam ?? '1', 10)
    return isNaN(pageNum) || pageNum < 1 ? 1 : pageNum
  },
  set: (value) => {
    router.push({
      query: {
        ...route.query,
        page: value > 1 ? value : undefined, // Remove page=1 from URL for cleaner URLs
      },
    })
  },
})

// Search and pagination state with debouncing
const filter = ref('')
const perPage = ref(10)

// Debounced search to match original behavior
const debouncedFilter = refDebounced(filter, 300)

// Reset page when filter or perPage changes (matches original)
watch([debouncedFilter, perPage], () => {
  if (page.value !== 1) {
    router.push({
      query: {
        ...route.query,
        page: undefined, // Reset to page 1
      },
    })
  }
})

// Query parameters for API (exact match with original)
const query = computed(() => ({
  filter: debouncedFilter.value,
  perPage: perPage.value,
  page: page.value,
}))

// Data fetching - exact same API call as original
const { data, pending, error, refresh } = await useFetch(
  '/api/company/members/',
  {
    query,
    // Add validation to ensure API response structure
    transform: (response: any) => {
      // Validate API response structure
      if (!response || typeof response !== 'object') {
        console.warn('[CardGridDataViewAdapter] Invalid API response structure:', response)
        return { data: [], total: 0 }
      }
      
      // Ensure data array exists
      if (!Array.isArray(response.data)) {
        console.warn('[CardGridDataViewAdapter] API response missing data array:', response)
        return { ...response, data: [] }
      }
      
      return response
    },
  },
)

// Enhanced DataView configuration with responsive grid updates
const dataViewConfig = computed<DataViewConfig>(() => ({
  title: 'Team Members',
  viewModes: ['grid'],
  defaultViewMode: 'grid',
  searchable: true,
  searchPlaceholder: 'Filter users...',
  filterable: false,
  showRefresh: true,
  pagination: {
    pageSize: perPage.value,
    pageSizeOptions: [10, 25, 50, 100],
    showPageSizeSelector: true,
    showSummary: true,
    showQuickNav: true,
    maxPageButtons: 7,
  },
  grid: {
    columns: 2, // lg:grid-cols-2 equivalent
    gap: 'md',
    aspectRatio: 'auto',
  },
}))

// Transform and validate data for DataView
const transformedData = computed<DataViewItem[]>(() => {
  if (!data.value?.data || !Array.isArray(data.value.data)) return []
  
  return data.value.data.map((item: any, index: number) => {
    // Validate required fields for member cards
    const transformedItem: DataViewItem = {
      id: item.id || `member-${index}`,
      // Preserve all original fields for exact compatibility
      picture: item.picture || '',
      badge: item.badge || null,
      initials: item.initials || (item.username?.[0] || 'U'),
      username: item.username || 'Unknown User',
      position: item.position || 'No Position',
      socialProfiles: Array.isArray(item.socialProfiles) ? item.socialProfiles : [],
      completed: typeof item.completed === 'number' ? item.completed : 0,
      // Include any additional fields from the API
      ...item,
    }
    
    return transformedItem
  })
})

// Enhanced pagination with proper error handling
const paginationInfo = computed(() => {
  let totalItems = 0 // No hardcoded fallback for this page
  
  // Try multiple ways to get total from API response
  if (data.value?.total && typeof data.value.total === 'number') {
    totalItems = data.value.total
  } else if (data.value?.meta?.total && typeof data.value.meta.total === 'number') {
    totalItems = data.value.meta.total
  } else if (data.value?.pagination?.total && typeof data.value.pagination.total === 'number') {
    totalItems = data.value.pagination.total
  }
  
  return {
    currentPage: page.value,
    pageSize: perPage.value,
    totalItems,
    totalPages: Math.max(1, Math.ceil(totalItems / perPage.value)),
  }
})

// Event handlers with validation and error handling
const handleSearch = (searchQuery: string) => {
  // Trim and validate search query
  const trimmedQuery = typeof searchQuery === 'string' ? searchQuery.trim() : ''
  filter.value = trimmedQuery
  
  // Reset to page 1 when searching (matches original behavior)
  if (page.value !== 1) {
    page.value = 1
  }
}

const handlePageChange = (newPage: number) => {
  // Validate page number
  const validPage = Math.max(1, Math.min(newPage, paginationInfo.value.totalPages))
  if (validPage !== page.value) {
    page.value = validPage
  }
}

const handlePageSizeChange = (newSize: number) => {
  // Validate page size
  const validSizes = [10, 25, 50, 100]
  const validSize = validSizes.includes(newSize) ? newSize : 10
  
  if (validSize !== perPage.value) {
    perPage.value = validSize
    // Reset to page 1 when changing page size (matches original behavior)
    if (page.value !== 1) {
      page.value = 1
    }
  }
}

const handleRefresh = async () => {
  try {
    await refresh()
  } catch (error) {
    console.error('[CardGridDataViewAdapter] Refresh failed:', error)
  }
}

// Action handlers
const handleViewProfile = (item: DataViewItem) => {
  console.log('View profile:', item)
  // TODO: Add navigation logic here
}

// Data flow validation for development debugging
const dataFlowValidation = computed(() => ({
  // API endpoint validation
  endpoint: '/api/company/members/',
  // Query parameters match original format
  queryParams: {
    filter: debouncedFilter.value,
    perPage: perPage.value,
    page: page.value,
  },
  // Response structure validation
  apiResponse: {
    hasData: !!data.value?.data,
    dataCount: Array.isArray(data.value?.data) ? data.value.data.length : 0,
    hasTotal: typeof data.value?.total === 'number',
    totalValue: data.value?.total,
  },
  // State validation
  componentState: {
    isPending: pending.value,
    hasError: !!error.value,
    errorMessage: error.value?.message,
    transformedCount: transformedData.value.length,
  },
  // Pagination validation
  paginationState: {
    currentPage: page.value,
    pageSize: perPage.value,
    totalItems: paginationInfo.value.totalItems,
    totalPages: paginationInfo.value.totalPages,
    isValidPage: page.value >= 1 && page.value <= paginationInfo.value.totalPages,
  },
}))

// Development debugging (removed in production builds)
if (process.env.NODE_ENV === 'development') {
  watch([data, pending, error, page, perPage, filter], () => {
    console.log('[CardGridDataViewAdapter] Data flow validation:', dataFlowValidation.value)
  }, { immediate: true })
}
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20 dark:[--color-input-default-bg:var(--color-muted-950)]">
    <!-- Legacy Implementation -->
    <TairoContentWrapper v-if="!useDataView">
      <template #left>
        <TairoInput
          v-model="filter"
          icon="lucide:search"
          placeholder="Filter users..."
        />
      </template>
      <template #right>
        <BaseSelect
          v-model="perPage"
          placeholder="Items per page"
        >
          <BaseSelectItem :value="10">
            10 per page
          </BaseSelectItem>
          <BaseSelectItem :value="25">
            25 per page
          </BaseSelectItem>
          <BaseSelectItem :value="50">
            50 per page
          </BaseSelectItem>
          <BaseSelectItem :value="100">
            100 per page
          </BaseSelectItem>
        </BaseSelect>
      </template>
      <div>
        <div v-if="!pending && data?.data.length === 0">
          <BasePlaceholderPage
            title="No matching results"
            subtitle="Looks like we couldn't find any matching results for your search terms. Try other search terms."
          >
            <template #image>
              <img
                class="block dark:hidden"
                src="/img/illustrations/placeholders/flat/placeholder-search-6.svg"
                alt="Placeholder image"
              >
              <img
                class="hidden dark:block"
                src="/img/illustrations/placeholders/flat/placeholder-search-6-dark.svg"
                alt="Placeholder image"
              >
            </template>
          </BasePlaceholderPage>
        </div>
        <div v-else>
          <div class="grid w-full gap-4 lg:grid-cols-2">
            <TransitionGroup
              enter-active-class="transform-gpu"
              enter-from-class="opacity-0 -translate-x-full"
              enter-to-class="opacity-100 translate-x-0"
              leave-active-class="absolute transform-gpu"
              leave-from-class="opacity-100 translate-x-0"
              leave-to-class="opacity-0 -translate-x-full"
            >
              <div 
                v-for="item in data?.data" 
                :key="item.id" 
                class="bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-800 rounded-md shadow-sm"
              >
                <div class="border-muted-200 dark:border-muted-800 flex flex-col items-center border-b p-6 sm:flex-row">
                  <div class="flex flex-col items-center gap-3 sm:flex-row">
                    <BaseAvatar
                      :src="item.picture"
                      :badge-src="item.badge"
                      :text="item.initials"
                      size="md"
                      class="bg-muted-500/20 text-muted-500"
                    />
                    <div class="text-center leading-none sm:text-start">
                      <h4 class="text-muted-800 dark:text-muted-100 font-sans text-base font-medium">
                        {{ item.username }}
                      </h4>
                      <p class="text-muted-600 dark:text-muted-400 font-sans text-sm">
                        {{ item.position }}
                      </p>
                    </div>
                  </div>
                  <div class="mt-4 flex items-center gap-3 sm:ms-auto sm:mt-0">
                    <NuxtLink
                      v-for="link in item.socialProfiles"
                      :key="link.name"
                      :to="link.url"
                      target="_blank"
                      rel="noopener noreferrer"
                      class="border-muted-200 dark:border-muted-700 hover:border-primary-500 dark:hover:border-primary-500 dark:bg-muted-800 text-muted-400 hover:text-primary-500 shadow-muted-300/30 dark:shadow-muted-700/30 flex size-8 items-center justify-center rounded-full border bg-white shadow-lg transition-all duration-300"
                    >
                      <Icon :name="link.icon" class="size-3" />
                    </NuxtLink>
                  </div>
                </div>
                <div class="flex flex-col items-center justify-between px-6 py-4 sm:flex-row">
                  <div class="w-full grow space-y-1 sm:w-auto sm:max-w-[260px]">
                    <div class="flex items-center justify-between">
                      <h4 class="text-muted-700 dark:text-muted-100 font-sans text-sm font-medium">
                        Progress
                      </h4>
                      <div>
                        <span class="text-muted-400 font-sans text-sm">
                          {{ item.completed }}%
                        </span>
                      </div>
                    </div>
                    <BaseProgress
                      size="xs"
                      variant="primary"
                      :model-value="item.completed"
                    />
                  </div>
                  <div class="mt-4 w-full sm:mt-0 sm:w-auto">
                    <BaseButton rounded="md" size="sm" class="w-full sm:w-auto" @click="handleViewProfile(item)">
                      View profile
                    </BaseButton>
                  </div>
                </div>
              </div>
            </TransitionGroup>
          </div>
          <div class="mt-6">
            <BasePagination
              v-model:page="page"
              :total="data?.total ?? 0"
              :items-per-page="perPage"
              rounded="lg"
              class="w-full"
            />
          </div>
        </div>
      </div>
    </TairoContentWrapper>

    <!-- Enhanced DataView Implementation -->
    <TairoContentWrapper v-else>
      <template #left>
        <!-- Search is handled by DataView toolbar -->
      </template>
      <template #right>
        <!-- Page size selector is handled by DataView pagination -->
      </template>
      <div class="pt-6">
        <DataView
          :data="transformedData"
          :config="dataViewConfig"
          :loading="pending"
          :current-page="paginationInfo.currentPage"
          :page-size="paginationInfo.pageSize"
          :total-items="paginationInfo.totalItems"
          :total-pages="paginationInfo.totalPages"
          @search="handleSearch"
          @page-changed="handlePageChange"
          @page-size-changed="handlePageSizeChange"
          @refresh="handleRefresh"
        >
          <!-- Grid Item Slot - Exact Card Recreation -->
          <template #grid-item="{ item, index }">
            <div 
              :key="item.id"
              class="bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-800 rounded-md shadow-sm transform-gpu transition-all duration-200"
            >
              <div class="border-muted-200 dark:border-muted-800 flex flex-col items-center border-b p-6 sm:flex-row">
                <div class="flex flex-col items-center gap-3 sm:flex-row">
                  <BaseAvatar
                    :src="item.picture"
                    :badge-src="item.badge"
                    :text="item.initials"
                    size="md"
                    class="bg-muted-500/20 text-muted-500"
                  />
                  <div class="text-center leading-none sm:text-start">
                    <h4 class="text-muted-800 dark:text-muted-100 font-sans text-base font-medium">
                      {{ item.username }}
                    </h4>
                    <p class="text-muted-600 dark:text-muted-400 font-sans text-sm">
                      {{ item.position }}
                    </p>
                  </div>
                </div>
                <div class="mt-4 flex items-center gap-3 sm:ms-auto sm:mt-0">
                  <NuxtLink
                    v-for="link in item.socialProfiles"
                    :key="link.name"
                    :to="link.url"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="border-muted-200 dark:border-muted-700 hover:border-primary-500 dark:hover:border-primary-500 dark:bg-muted-800 text-muted-400 hover:text-primary-500 shadow-muted-300/30 dark:shadow-muted-700/30 flex size-8 items-center justify-center rounded-full border bg-white shadow-lg transition-all duration-300"
                  >
                    <Icon :name="link.icon" class="size-3" />
                  </NuxtLink>
                </div>
              </div>
              <div class="flex flex-col items-center justify-between px-6 py-4 sm:flex-row">
                <div class="w-full grow space-y-1 sm:w-auto sm:max-w-[260px]">
                  <div class="flex items-center justify-between">
                    <h4 class="text-muted-700 dark:text-muted-100 font-sans text-sm font-medium">
                      Progress
                    </h4>
                    <div>
                      <span class="text-muted-400 font-sans text-sm">
                        {{ item.completed }}%
                      </span>
                    </div>
                  </div>
                  <BaseProgress
                    size="xs"
                    variant="primary"
                    :model-value="item.completed"
                  />
                </div>
                <div class="mt-4 w-full sm:mt-0 sm:w-auto">
                  <BaseButton rounded="md" size="sm" class="w-full sm:w-auto" @click="handleViewProfile(item)">
                    View profile
                  </BaseButton>
                </div>
              </div>
            </div>
          </template>

          <!-- Empty State Slot -->
          <template #empty>
            <BasePlaceholderPage
              title="No matching results"
              subtitle="Looks like we couldn't find any matching results for your search terms. Try other search terms."
            >
              <template #image>
                <img
                  class="block dark:hidden"
                  src="/img/illustrations/placeholders/flat/placeholder-search-6.svg"
                  alt="Placeholder image"
                >
                <img
                  class="hidden dark:block"
                  src="/img/illustrations/placeholders/flat/placeholder-search-6-dark.svg"
                  alt="Placeholder image"
                >
              </template>
            </BasePlaceholderPage>
          </template>

          <!-- Grid Container Slot - Add responsive grid and transitions -->
          <template #grid-container="{ items }">
            <div class="grid w-full gap-4 lg:grid-cols-2">
              <TransitionGroup
                enter-active-class="transform-gpu"
                enter-from-class="opacity-0 -translate-x-full"
                enter-to-class="opacity-100 translate-x-0"
                leave-active-class="absolute transform-gpu"
                leave-from-class="opacity-100 translate-x-0"
                leave-to-class="opacity-0 -translate-x-full"
              >
                <slot name="grid-items" :items="items" />
              </TransitionGroup>
            </div>
          </template>
        </DataView>
      </div>
    </TairoContentWrapper>
  </div>
</template>

<style scoped>
/* Ensure smooth transitions match the original */
.transform-gpu {
  transform: translateZ(0);
}

/* Maintain grid gap consistency */
.grid.gap-4 {
  gap: 1rem;
}

/* Ensure responsive behavior matches original */
@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>
