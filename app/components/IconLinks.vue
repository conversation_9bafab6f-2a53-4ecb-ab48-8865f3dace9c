<script setup lang="ts">
import type { IconLinksProps } from '../app/types/ui'

const props = withDefaults(
  defineProps<IconLinksProps>(),
  {
    links: () => [
      {
        id: 1,
        name: 'Activity',
        url: '#',
        title: 'Activity',
        icon: 'solar:heart-pulse-2-linear',
      },
      {
        id: 2,
        name: 'Search',
        url: '#',
        title: 'Search',
        icon: 'solar:minimalistic-magnifer-linear',
      },
      {
        id: 3,
        name: 'Files',
        url: '#',
        title: 'Files',
        icon: 'solar:document-linear',
      },
      {
        id: 4,
        name: 'Home',
        url: '#',
        title: 'Home',
        icon: 'solar:home-smile-angle-linear',
      },
    ],
    iconSize: 'md',
    orientation: 'horizontal',
  },
)
</script>

<template>
  <div class="flex w-full items-center justify-between gap-3">
    <NuxtLink
      v-for="link in props.links"
      :key="link.name"
      :to="link.url"
      class="text-muted-400 hover:bg-primary-500 hover:shadow-primary-500/30 dark:hover:shadow-primary-800/30 flex size-9 items-center justify-center rounded-full transition-all duration-300 hover:text-white hover:shadow-xl"
    >
      <Icon :name="link.icon" class="size-5" />
    </NuxtLink>
  </div>
</template>
