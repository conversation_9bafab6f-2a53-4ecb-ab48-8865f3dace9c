<script setup lang="ts">
import type { Experience } from '../app/types/auth'
import { generateProfileItemId, populateExperienceForm } from '../app/utils/profile-forms'

interface Props {
  open: boolean
  experience?: Experience | null
}

interface Emits {
  (e: 'close'): void
  (e: 'submit', data: Experience): void
}

const props = withDefaults(defineProps<Props>(), {
  experience: null,
})

const emit = defineEmits<Emits>()

// Form state
const form = reactive<Omit<Experience, 'id'> & { id?: string }>({
  company: '',
  position: '',
  period: '',
  description: '',
  startDate: '',
  endDate: '',
  isCurrent: false,
  logo: '',
})

const errors = reactive<Record<string, string>>({})
const isSubmitting = ref(false)
const submitError = ref<string>('')

// Computed properties
const editing = computed(() => !!props.experience)

const isFormValid = computed(() => {
  return form.company.trim()
    && form.position.trim()
    && form.period.trim()
    && Object.keys(errors).length === 0
})

// Watch for experience changes to populate form
watch(() => props.experience, (experience) => {
  const formData = populateExperienceForm(experience)
  Object.assign(form, formData)

  // Clear errors
  Object.keys(errors).forEach(key => delete errors[key])
  submitError.value = ''
}, { immediate: true })

// Reset form
function resetForm() {
  const emptyForm = populateExperienceForm(null)
  Object.assign(form, emptyForm)

  // Clear errors
  Object.keys(errors).forEach(key => delete errors[key])
  submitError.value = ''
}

// Validation
function validateForm() {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key])

  // Required field validation
  if (!form.company.trim()) {
    errors.company = 'Company name is required'
  }

  if (!form.position.trim()) {
    errors.position = 'Position is required'
  }

  if (!form.period.trim()) {
    errors.period = 'Period is required'
  }

  // Date validation - if both start and end dates are provided, start should be before end
  if (form.startDate && form.endDate && form.startDate > form.endDate) {
    errors.endDate = 'End date should be after start date'
  }

  return Object.keys(errors).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true
  submitError.value = ''

  try {
    const experienceData: Experience = {
      id: form.id || generateProfileItemId(),
      company: form.company.trim(),
      position: form.position.trim(),
      period: form.period.trim(),
      description: form.description?.trim() || undefined,
      startDate: form.startDate?.trim() || undefined,
      endDate: form.endDate?.trim() || undefined,
      isCurrent: form.isCurrent,
      logo: form.logo?.trim() || undefined,
    }

    emit('submit', experienceData)
  }
  catch (error: any) {
    submitError.value = error.message || 'Failed to save experience'
  }
  finally {
    isSubmitting.value = false
  }
}

// Watch for open state to reset form when modal opens
watch(() => props.open, (open) => {
  if (open && !props.experience) {
    resetForm()
  }
})
</script>

<template>
  <BaseModal
    :open="open"
    size="lg"
    @close="$emit('close')"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <BaseHeading
          as="h3"
          size="lg"
          weight="semibold"
          class="text-muted-900 dark:text-white"
        >
          {{ editing ? 'Edit' : 'Add' }} Experience
        </BaseHeading>
        <BaseButton
          size="sm"
          variant="ghost"
          @click="$emit('close')"
        >
          <Icon name="lucide:x" class="size-4" />
        </BaseButton>
      </div>
    </template>

    <form class="space-y-6" @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 gap-4">
        <!-- Company -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.company"
          :disabled="isSubmitting"
          required
        >
          <BaseLabel>Company Name</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.company"
            placeholder="e.g., Google, Microsoft, etc."
            :aria-invalid="errors.company ? 'true' : undefined"
            required
          />
          <!-- TODO: Future integration with CRM companies -->
        </BaseField>

        <!-- Position -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.position"
          :disabled="isSubmitting"
          required
        >
          <BaseLabel>Position</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.position"
            placeholder="e.g., Senior Software Engineer"
            :aria-invalid="errors.position ? 'true' : undefined"
            required
          />
        </BaseField>

        <!-- Period -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.period"
          :disabled="isSubmitting"
          required
        >
          <BaseLabel>Period</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.period"
            placeholder="e.g., Jan 2020 - Present, 2018 - 2022"
            :aria-invalid="errors.period ? 'true' : undefined"
            required
          />
        </BaseField>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <!-- Start Date -->
          <BaseField
            v-slot="{ inputAttrs, inputRef }"
            :error="errors.startDate"
            :disabled="isSubmitting"
          >
            <BaseLabel>Start Date (Optional)</BaseLabel>
            <TairoInput
              :ref="inputRef"
              v-bind="inputAttrs"
              v-model="form.startDate"
              type="date"
              :aria-invalid="errors.startDate ? 'true' : undefined"
            />
          </BaseField>

          <!-- End Date -->
          <BaseField
            v-slot="{ inputAttrs, inputRef }"
            :error="errors.endDate"
            :disabled="isSubmitting || form.isCurrent"
          >
            <BaseLabel>End Date (Optional)</BaseLabel>
            <TairoInput
              :ref="inputRef"
              v-bind="inputAttrs"
              v-model="form.endDate"
              type="date"
              :disabled="form.isCurrent"
              :aria-invalid="errors.endDate ? 'true' : undefined"
            />
          </BaseField>
        </div>

        <!-- Current Position Toggle -->
        <div class="flex items-center gap-3">
          <BaseCheckbox
            v-model="form.isCurrent"
            :disabled="isSubmitting"
            color="primary"
          />
          <div>
            <BaseLabel class="text-sm font-medium text-muted-700 dark:text-muted-300">
              This is my current position
            </BaseLabel>
            <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
              Check this if you are currently working in this position
            </BaseParagraph>
          </div>
        </div>

        <!-- Company Logo URL -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.logo"
          :disabled="isSubmitting"
        >
          <BaseLabel>Company Logo URL (Optional)</BaseLabel>
          <TairoInput
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.logo"
            type="url"
            placeholder="https://example.com/logo.png"
            :aria-invalid="errors.logo ? 'true' : undefined"
          />
        </BaseField>

        <!-- Description -->
        <BaseField
          v-slot="{ inputAttrs, inputRef }"
          :error="errors.description"
          :disabled="isSubmitting"
        >
          <BaseLabel>Description (Optional)</BaseLabel>
          <BaseTextarea
            :ref="inputRef"
            v-bind="inputAttrs"
            v-model="form.description"
            placeholder="Describe your role, responsibilities, and achievements..."
            rows="4"
            :aria-invalid="errors.description ? 'true' : undefined"
          />
        </BaseField>
      </div>

      <!-- Error Message -->
      <div v-if="submitError" class="p-3 bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg">
        <div class="flex items-start gap-3">
          <Icon name="lucide:alert-circle" class="size-4 text-danger-500 mt-0.5 flex-shrink-0" />
          <div>
            <BaseHeading as="h4" size="xs" class="text-danger-700 dark:text-danger-400 mb-1">
              Error saving experience
            </BaseHeading>
            <BaseParagraph size="sm" class="text-danger-600 dark:text-danger-300">
              {{ submitError }}
            </BaseParagraph>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-end gap-3 pt-4 border-t border-muted-200 dark:border-muted-800">
        <BaseButton
          variant="ghost"
          :disabled="isSubmitting"
          @click="$emit('close')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          type="submit"
          variant="primary"
          :disabled="!isFormValid || isSubmitting"
          :loading="isSubmitting"
        >
          {{ editing ? 'Update' : 'Add' }} Experience
        </BaseButton>
      </div>
    </form>
  </BaseModal>
</template>
