<script setup lang="ts">
import type { ChartProps } from '../../app/types/ui'
import type { AddonApexchartsProps } from '~/components/AddonApexcharts.vue'

const props = withDefaults(
  defineProps<ChartProps>(),
  {
    options: () => ({
      chart: {
        animations: {
          enabled: false,
        },
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: false,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'straight',
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.75,
        },
      },
      colors: ['var(--color-chart-base)'],
      title: {
        text: '',
        align: 'left',
      },
      labels: [
        '13 Nov 2017',
        '14 Nov 2017',
        '15 Nov 2017',
        '16 Nov 2017',
        '17 Nov 2017',
        '20 Nov 2017',
        '21 Nov 2017',
        '22 Nov 2017',
        '23 Nov 2017',
        '24 Nov 2017',
        '27 Nov 2017',
        '28 Nov 2017',
        '29 Nov 2017',
        '30 Nov 2017',
        '01 Dec 2017',
        '04 Dec 2017',
        '05 Dec 2017',
        '06 Dec 2017',
        '07 Dec 2017',
        '08 Dec 2017',
      ],
      xaxis: {
        type: 'datetime',
      },
      yaxis: {
        opposite: true,
      },
      legend: {
        horizontalAlign: 'left',
      },
    }),
  },
)

const demoArea = reactive(useDemoArea())

function useDemoArea(): AddonApexchartsProps {
  const type = 'area'
  const height = 280

  const series = shallowRef([
    {
      name: 'Balance',
      data: [
        8107.85,
        8128.0,
        8122.9,
        8165.5,
        8340.7,
        8423.7,
        8423.5,
        8514.3,
        8481.85,
        8487.7,
        8506.9,
        8626.2,
        8668.95,
        8602.3,
        8607.55,
        8512.9,
        8496.25,
        8600.65,
        8881.1,
        9340.85,
      ],
    },
  ])

  return {
    type,
    height,
    options: props.options,
    series,
  }
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoArea" />
</template>
