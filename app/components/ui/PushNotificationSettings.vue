<script setup lang="ts">
const { usePushNotifications } = await import('~/composables/usePushNotifications')

// Initialize push notifications
const pushNotifications = usePushNotifications({
  autoInitialize: true,
})

const isTestingSendingTest = ref(false)
const isDev = process.env.NODE_ENV === 'development'

// Get current permission status
const permissionStatus = computed(() => {
  return pushNotifications.checkPermission()
})

/**
 * Get status text based on current state
 */
function getStatusText() {
  if (pushNotifications.isEnabled.value) {
    return 'Active - You\'ll receive push notifications'
  }

  if (permissionStatus.value === 'denied') {
    return 'Blocked - Permission denied in browser'
  }

  if (permissionStatus.value === 'default') {
    return 'Not enabled - Click to enable push notifications'
  }

  return 'Disabled'
}

/**
 * Toggle push notifications on/off
 */
async function togglePushNotifications() {
  try {
    if (pushNotifications.isEnabled.value) {
      await pushNotifications.unsubscribe()

      // Show success toast
      const { globalToast } = useToast()
      globalToast.success('Push notifications disabled')
    }
    else {
      const success = await pushNotifications.subscribe()

      if (success) {
        const { globalToast } = useToast()
        globalToast.success('Push notifications enabled! You\'ll receive notifications even when the app is closed.')
      }
    }
  }
  catch (error: any) {
    console.error('Failed to toggle push notifications:', error)

    const { globalToast } = useToast()
    globalToast.error('Failed to update push notification settings')
  }
}

/**
 * Send a test push notification
 */
async function sendTestNotification() {
  try {
    isTestingSendingTest.value = true

    await $fetch('/api/notifications/test-push', {
      method: 'POST',
    })

    const { globalToast } = useToast()
    globalToast.info('Test notification sent! You should receive it shortly.')
  }
  catch (error: any) {
    console.error('Failed to send test notification:', error)

    const { globalToast } = useToast()
    globalToast.error('Failed to send test notification')
  }
  finally {
    isTestingSendingTest.value = false
  }
}
</script>

<template>
  <div class="push-notification-settings">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Push Notifications
      </h3>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        Receive instant notifications for important updates even when the app is closed.
      </p>
    </div>

    <!-- Support check -->
    <div v-if="!pushNotifications.isSupported.value" class="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
      <div class="flex items-center">
        <Icon name="heroicons:exclamation-triangle" class="h-5 w-5 text-yellow-500 mr-2" />
        <span class="text-sm text-yellow-700 dark:text-yellow-300">
          Push notifications are not supported in this browser
        </span>
      </div>
    </div>

    <!-- Current status -->
    <div v-if="pushNotifications.isSupported.value" class="mb-6">
      <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div class="flex items-center">
          <div
            class="h-3 w-3 rounded-full mr-3"
            :class="{
              'bg-green-500': pushNotifications.isEnabled.value,
              'bg-red-500': !pushNotifications.isEnabled.value && permissionStatus === 'denied',
              'bg-yellow-500': !pushNotifications.isEnabled.value && permissionStatus === 'default',
            }"
          />
          <div>
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              Push Notifications
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {{ getStatusText() }}
            </p>
          </div>
        </div>

        <!-- Toggle switch -->
        <button
          v-if="permissionStatus !== 'denied'"
          :disabled="pushNotifications.isLoading.value"
          class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          :class="{
            'bg-blue-600': pushNotifications.isEnabled.value,
            'bg-gray-200 dark:bg-gray-600': !pushNotifications.isEnabled.value,
          }"
          @click="togglePushNotifications"
        >
          <span
            class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
            :class="{
              'translate-x-6': pushNotifications.isEnabled.value,
              'translate-x-1': !pushNotifications.isEnabled.value,
            }"
          />
        </button>
      </div>
    </div>

    <!-- Error display -->
    <div v-if="pushNotifications.error.value" class="mb-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
      <div class="flex items-center">
        <Icon name="heroicons:exclamation-circle" class="h-5 w-5 text-red-500 mr-2" />
        <span class="text-sm text-red-700 dark:text-red-300">
          {{ pushNotifications.error.value }}
        </span>
      </div>
    </div>

    <!-- Permission denied help -->
    <div v-if="permissionStatus === 'denied'" class="mb-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
      <div class="flex items-start">
        <Icon name="heroicons:information-circle" class="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
        <div class="text-sm text-blue-700 dark:text-blue-300">
          <p class="font-medium mb-2">
            Push notifications are blocked
          </p>
          <p class="mb-2">
            To enable push notifications:
          </p>
          <ol class="list-decimal list-inside space-y-1 ml-4">
            <li>Click the lock icon in your browser's address bar</li>
            <li>Change notifications setting to "Allow"</li>
            <li>Refresh this page</li>
          </ol>
        </div>
      </div>
    </div>

    <!-- Test notification button -->
    <div v-if="pushNotifications.isEnabled.value" class="mt-4">
      <button
        :disabled="isTestingSendingTest"
        class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        @click="sendTestNotification"
      >
        <Icon
          v-if="isTestingSendingTest"
          name="heroicons:arrow-path"
          class="animate-spin h-4 w-4 mr-2"
        />
        <Icon v-else name="heroicons:bell" class="h-4 w-4 mr-2" />
        {{ isTestingSendingTest ? 'Sending...' : 'Send Test Notification' }}
      </button>
    </div>

    <!-- Token info (dev mode) -->
    <div v-if="isDev && pushNotifications.token.value" class="mt-6 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
      <p class="text-gray-500 dark:text-gray-400 mb-1">
        FCM Token (dev):
      </p>
      <p class="text-gray-700 dark:text-gray-300 break-all">
        {{ pushNotifications.token.value }}
      </p>
    </div>
  </div>
</template>

<style scoped>
.push-notification-settings {
  /* Component styles already handled by Tailwind classes */
}
</style>
