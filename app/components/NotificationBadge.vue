<script setup lang="ts">
interface Props {
  /**
   * Show as a small dot instead of number
   */
  dot?: boolean
  /**
   * Maximum number to display before showing "+"
   */
  max?: number
  /**
   * Custom unread count (overrides real-time count)
   */
  count?: number
  /**
   * Size variant
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Color variant
   */
  variant?: 'primary' | 'warning' | 'danger'
  /**
   * Whether to show count of zero
   */
  showZero?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  dot: false,
  max: 99,
  size: 'md',
  variant: 'primary',
  showZero: false,
})

// Use real-time notifications if no custom count provided
const { unreadCount, hasNewNotifications } = props.count !== undefined
  ? { unreadCount: ref(props.count), hasNewNotifications: computed(() => props.count > 0) }
  : useRealTimeNotifications()

// Computed display value
const displayValue = computed(() => {
  const count = props.count ?? unreadCount.value

  if (props.dot)
    return ''
  if (count === 0 && !props.showZero)
    return ''
  if (count > props.max)
    return `${props.max}+`

  return count.toString()
})

// Should show badge
const shouldShow = computed(() => {
  const count = props.count ?? unreadCount.value
  return count > 0 || (count === 0 && props.showZero)
})

// Size classes
const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return props.dot
        ? 'w-2 h-2 top-0 right-0'
        : 'px-1 py-0.5 text-xs min-w-[16px] h-4 top-0 right-0 transform translate-x-1/2 -translate-y-1/2'
    case 'lg':
      return props.dot
        ? 'w-3 h-3 top-0 right-0'
        : 'px-2 py-1 text-sm min-w-[20px] h-5 top-0 right-0 transform translate-x-1/2 -translate-y-1/2'
    default: // md
      return props.dot
        ? 'w-2.5 h-2.5 top-0 right-0'
        : 'px-1.5 py-0.5 text-xs min-w-[18px] h-4 top-0 right-0 transform translate-x-1/2 -translate-y-1/2'
  }
})

// Color classes
const colorClasses = computed(() => {
  switch (props.variant) {
    case 'warning':
      return 'bg-warning-500 text-white'
    case 'danger':
      return 'bg-danger-500 text-white'
    default: // primary
      return 'bg-primary-500 text-white'
  }
})
</script>

<template>
  <div class="relative inline-block">
    <!-- Content (button, icon, etc) -->
    <slot />

    <!-- Badge -->
    <Transition
      enter-active-class="transition-all duration-200"
      enter-from-class="scale-0 opacity-0"
      enter-to-class="scale-100 opacity-100"
      leave-active-class="transition-all duration-200"
      leave-from-class="scale-100 opacity-100"
      leave-to-class="scale-0 opacity-0"
    >
      <span
        v-if="shouldShow"
        class="absolute flex items-center justify-center rounded-full font-semibold leading-none"
        :class="[sizeClasses, colorClasses]"
      >
        {{ displayValue }}
      </span>
    </Transition>
  </div>
</template>

<style scoped>
/* Additional styles if needed */
</style>
