<script setup lang="ts">
import type { ToolStreamData } from '~/types/websocket'
import { computed, inject, ref } from 'vue'

interface Props {
  messageId: string
  toolId: string
  toolName?: string
  // Optional override data - if not provided, will use streaming data
  streamingData?: ToolStreamData
}

const props = defineProps<Props>()

// Inject streaming composable (should be provided by parent component)
const toolStreaming = inject('toolStreaming') as any

// Get real-time tool data from streaming or use provided override
const toolData = computed((): ToolStreamData | null => {
  if (props.streamingData) {
    return props.streamingData
  }

  if (toolStreaming?.getToolData) {
    return toolStreaming.getToolData(props.messageId, props.toolId)
  }

  return {
    toolId: props.toolId,
    toolName: props.toolName || props.toolId,
    status: 'queued',
    progress: 0,
  }
})

// Compute display properties from tool data
const status = computed(() => toolData.value?.status || 'queued')
const progress = computed(() => toolData.value?.progress || 0)
const result = computed(() => toolData.value?.result)
const error = computed(() => toolData.value?.error)
const duration = computed(() => toolData.value?.duration)
const statusMessage = computed(() => toolData.value?.statusMessage)
const toolName = computed(() => toolData.value?.toolName || props.toolName || props.toolId)

// Status styling
const statusColor = computed(() => {
  switch (status.value) {
    case 'starting':
    case 'queued':
      return 'text-blue-600 dark:text-blue-400'
    case 'running':
      return 'text-yellow-600 dark:text-yellow-400'
    case 'success':
      return 'text-green-600 dark:text-green-400'
    case 'error':
    case 'cancelled':
      return 'text-red-600 dark:text-red-400'
    default:
      return 'text-muted-600 dark:text-muted-400'
  }
})

const statusIcon = computed(() => {
  switch (status.value) {
    case 'starting':
    case 'queued':
      return 'lucide:play-circle'
    case 'running':
      return 'lucide:loader'
    case 'success':
      return 'lucide:check-circle'
    case 'error':
    case 'cancelled':
      return 'lucide:x-circle'
    default:
      return 'lucide:circle'
  }
})

const isComplete = computed(() => status.value === 'success' || status.value === 'error')
const isRunning = computed(() => status.value === 'running' || status.value === 'starting')

// Expand/collapse result details
const showDetails = ref(false)

// Format result data for display
function formatResultData(data: any): string {
  if (!data)
    return ''

  if (typeof data === 'string')
    return data

  try {
    return JSON.stringify(data, null, 2)
  }
  catch {
    return String(data)
  }
}

// Copy result to clipboard
async function copyResult() {
  if (!result.value)
    return

  try {
    await navigator.clipboard.writeText(formatResultData(result.value))
    // Could add a toast notification here
  }
  catch {
    // Fallback for older browsers
    const textarea = document.createElement('textarea')
    textarea.value = formatResultData(result.value)
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
  }
}
</script>

<template>
  <div class="bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded-lg p-4 space-y-3">
    <!-- Tool Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <Icon
          :name="statusIcon"
          class="h-5 w-5" :class="[
            statusColor,
            isRunning ? 'animate-spin' : '',
          ]"
        />
        <BaseText size="sm" weight="medium" class="text-muted-900 dark:text-white">
          {{ toolName }}
        </BaseText>
        <span
          class="inline-flex items-center px-2 py-1 rounded text-xs font-medium" :class="[
            status === 'success'
              ? 'bg-green-100 text-green-800 dark:bg-green-500/20 dark:text-green-300'
              : status === 'error' || status === 'cancelled'
                ? 'bg-red-100 text-red-800 dark:bg-red-500/20 dark:text-red-300'
                : status === 'running'
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-500/20 dark:text-yellow-300'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-500/20 dark:text-blue-300',
          ]"
        >
          {{ status }}
        </span>
      </div>

      <div class="flex items-center space-x-2">
        <BaseText v-if="duration && duration > 0" size="xs" class="text-muted-500">
          {{ duration }}ms
        </BaseText>
        <button
          v-if="result"
          class="p-1 rounded hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors"
          title="Copy result"
          @click="copyResult"
        >
          <Icon name="lucide:copy" class="h-4 w-4 text-muted-500" />
        </button>
        <button
          v-if="result || error"
          class="p-1 rounded hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors"
          :title="showDetails ? 'Hide details' : 'Show details'"
          @click="showDetails = !showDetails"
        >
          <Icon
            :name="showDetails ? 'lucide:chevron-up' : 'lucide:chevron-down'"
            class="h-4 w-4 text-muted-500"
          />
        </button>
      </div>
    </div>

    <!-- Progress Bar -->
    <div v-if="!isComplete || isRunning" class="w-full bg-muted-200 dark:bg-muted-700 rounded-full h-2">
      <div
        class="h-2 rounded-full transition-all duration-300" :class="[
          status === 'error' || status === 'cancelled'
            ? 'bg-red-500'
            : status === 'success'
              ? 'bg-green-500'
              : 'bg-blue-500',
        ]"
        :style="{ width: `${progress}%` }"
      />
    </div>

    <!-- Status Message -->
    <div v-if="statusMessage" class="text-sm text-muted-600 dark:text-muted-400">
      {{ statusMessage }}
    </div>

    <!-- Error Message -->
    <div v-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded p-3">
      <div class="flex items-start space-x-2">
        <Icon name="lucide:alert-circle" class="h-4 w-4 text-red-500 mt-0.5" />
        <BaseText size="sm" class="text-red-800 dark:text-red-200">
          {{ error.message || String(error) }}
        </BaseText>
      </div>
    </div>

    <!-- Tool Output Summary -->
    <div v-if="result && !showDetails" class="bg-muted-50 dark:bg-muted-800 rounded p-3">
      <BaseText size="sm" class="text-muted-700 dark:text-muted-300">
        <!-- Format different result types -->
        <template v-if="typeof result === 'string'">
          {{ result.slice(0, 150) }}{{ result.length > 150 ? '...' : '' }}
        </template>
        <template v-else-if="result?.results?.length">
          Found {{ result.results.length }} result(s)
        </template>
        <template v-else-if="result?.query">
          Query: {{ result.query }}
        </template>
        <template v-else>
          Tool completed successfully
        </template>
      </BaseText>
    </div>

    <!-- Detailed Result View -->
    <div v-if="showDetails && (result || error)" class="space-y-3">
      <div class="border-t border-muted-200 dark:border-muted-700 pt-3">
        <BaseText size="sm" weight="medium" class="text-muted-700 dark:text-muted-300 mb-2">
          {{ error ? 'Error Details:' : 'Result Details:' }}
        </BaseText>

        <!-- JSON Result Display -->
        <div class="bg-muted-900 dark:bg-black rounded p-3 overflow-x-auto">
          <pre class="text-sm text-green-400 font-mono">{{ formatResultData(error || result) }}</pre>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-2 mt-3">
          <BaseButton
            size="sm"
            variant="outline"
            class="flex items-center space-x-1"
            @click="copyResult"
          >
            <Icon name="lucide:copy" class="h-4 w-4" />
            <span>Copy</span>
          </BaseButton>
          <BaseButton
            v-if="result?.url"
            size="sm"
            variant="outline"
            class="flex items-center space-x-1"
            @click="window.open(result.url, '_blank')"
          >
            <Icon name="lucide:external-link" class="h-4 w-4" />
            <span>Open</span>
          </BaseButton>
        </div>
      </div>
    </div>

    <!-- Streaming Info (Debug) -->
    <div v-if="showDetails && toolData" class="border-t border-muted-200 dark:border-muted-700 pt-3">
      <BaseText size="sm" weight="medium" class="text-muted-700 dark:text-muted-300 mb-2">
        Streaming Info:
      </BaseText>
      <div class="text-xs text-muted-600 dark:text-muted-400 space-y-1">
        <div>Status: {{ status }}</div>
        <div>Progress: {{ progress }}%</div>
        <div v-if="toolData.startTime">
          Started: {{ new Date(toolData.startTime).toLocaleTimeString() }}
        </div>
        <div v-if="toolData.endTime">
          Ended: {{ new Date(toolData.endTime).toLocaleTimeString() }}
        </div>
        <div v-if="duration">
          Duration: {{ duration }}ms
        </div>
      </div>
    </div>
  </div>
</template>
