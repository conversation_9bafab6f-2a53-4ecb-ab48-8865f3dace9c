<script setup lang="ts">
import { useMultiAgentSettings } from '~/composables/useMultiAgentSettings'

interface Props {
  isMultiAgent?: boolean
  agentCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  isMultiAgent: false,
  agentCount: 0,
})

const {
  routingMode,
  enableAgentNamingRouting,
  showAgentBubbles,
  toggleRoutingMode,
  setAgentNamingRouting,
  setShowAgentBubbles,
} = useMultiAgentSettings()

const showControls = computed(() => props.isMultiAgent && props.agentCount > 1)

const routingModeIcon = computed(() => {
  return routingMode.value === 'smart' ? 'lucide:brain' : 'lucide:repeat'
})

const routingModeTooltip = computed(() => {
  return routingMode.value === 'smart'
    ? 'Smart routing: AI selects the best agent for each message'
    : 'Round-robin: Agents take turns responding'
})
</script>

<template>
  <div v-if="showControls" class="flex items-center gap-2">
    <!-- Routing Mode Toggle -->
    <BaseButtonIcon
      size="sm"
      color="default"
      variant="pastel"
      :name="routingModeIcon"
      :data-tooltip="routingModeTooltip"
      class="transition-all hover:scale-105"
      @click="toggleRoutingMode"
    />

    <!-- Agent Naming Toggle -->
    <div class="flex items-center gap-1">
      <BaseSwitch
        v-model="enableAgentNamingRouting"
        size="sm"
        color="primary"
      />
      <span class="text-xs text-muted-600 dark:text-muted-400">
        @mentions
      </span>
    </div>

    <!-- Show Agent Bubbles Toggle -->
    <div class="flex items-center gap-1">
      <BaseSwitch
        v-model="showAgentBubbles"
        size="sm"
        color="primary"
      />
      <Icon
        name="lucide:messages-square"
        class="size-4 text-muted-600 dark:text-muted-400"
      />
    </div>

    <!-- Agent Count Badge -->
    <BaseBadge size="xs" color="muted" variant="outline">
      <Icon name="lucide:users" class="size-3 mr-1" />
      {{ agentCount }} agents
    </BaseBadge>
  </div>
</template>
