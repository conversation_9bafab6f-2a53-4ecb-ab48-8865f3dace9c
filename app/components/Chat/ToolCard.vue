<script setup lang="ts">
import type { ToolStreamChunk } from '~/server/utils/tools/index'

interface Props {
  toolId: string
  toolName: string
  chunks: ToolStreamChunk[]
  isComplete: boolean
  status: 'started' | 'processing' | 'completed' | 'failed' | 'error'
}

const props = withDefaults(defineProps<Props>(), {
  chunks: () => [],
  isComplete: false,
  status: 'started',
})

// Compute current progress
const progress = computed(() => {
  if (props.isComplete)
    return 100
  if (props.status === 'started')
    return 10
  if (props.status === 'processing')
    return 60
  return 0
})

// Get the latest result data
const latestResult = computed(() => {
  const completedChunk = props.chunks.find(chunk => chunk.type === 'tool_complete')
  if (completedChunk)
    return completedChunk.data

  const progressChunk = props.chunks.find(chunk => chunk.type === 'tool_progress' && chunk.data)
  if (progressChunk)
    return progressChunk.data

  return null
})

// Get latest error if any
const error = computed(() => {
  const errorChunk = props.chunks.find(chunk => chunk.type === 'tool_error')
  return errorChunk?.content
})

// Status styling
const statusColor = computed(() => {
  switch (props.status) {
    case 'started':
      return 'text-blue-600 dark:text-blue-400'
    case 'processing':
      return 'text-yellow-600 dark:text-yellow-400'
    case 'completed':
      return 'text-green-600 dark:text-green-400'
    case 'failed':
    case 'error':
      return 'text-red-600 dark:text-red-400'
    default:
      return 'text-muted-600 dark:text-muted-400'
  }
})

const statusIcon = computed(() => {
  switch (props.status) {
    case 'started':
      return 'lucide:play-circle'
    case 'processing':
      return 'lucide:loader'
    case 'completed':
      return 'lucide:check-circle'
    case 'failed':
    case 'error':
      return 'lucide:x-circle'
    default:
      return 'lucide:circle'
  }
})

// Expand/collapse result details
const showDetails = ref(false)

// Format result data for display
function formatResultData(data: any): string {
  if (!data)
    return ''

  if (typeof data === 'string')
    return data

  try {
    return JSON.stringify(data, null, 2)
  }
  catch {
    return String(data)
  }
}

// Copy result to clipboard
async function copyResult() {
  if (!latestResult.value)
    return

  try {
    await navigator.clipboard.writeText(formatResultData(latestResult.value))
    // Could add a toast notification here
  }
  catch {
    // Fallback for older browsers
    const textarea = document.createElement('textarea')
    textarea.value = formatResultData(latestResult.value)
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
  }
}

// Get execution time from latest chunk
const executionTime = computed(() => {
  const latestChunk = [...props.chunks].reverse()[0]
  return latestChunk?.metadata?.executionTime || 0
})
</script>

<template>
  <div class="bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-700 rounded-lg p-4 space-y-3">
    <!-- Tool Header -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <Icon
          :name="statusIcon"
          class="h-5 w-5" :class="[
            statusColor,
            status === 'processing' ? 'animate-spin' : '',
          ]"
        />
        <BaseText size="sm" weight="medium" class="text-muted-900 dark:text-white">
          {{ toolName }}
        </BaseText>
        <span
          class="inline-flex items-center px-2 py-1 rounded text-xs font-medium" :class="[
            status === 'completed'
              ? 'bg-green-100 text-green-800 dark:bg-green-500/20 dark:text-green-300'
              : status === 'error' || status === 'failed'
                ? 'bg-red-100 text-red-800 dark:bg-red-500/20 dark:text-red-300'
                : status === 'processing'
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-500/20 dark:text-yellow-300'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-500/20 dark:text-blue-300',
          ]"
        >
          {{ status }}
        </span>
      </div>

      <div class="flex items-center space-x-2">
        <BaseText v-if="executionTime > 0" size="xs" class="text-muted-500">
          {{ executionTime }}ms
        </BaseText>
        <button
          v-if="latestResult"
          class="p-1 rounded hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors"
          title="Copy result"
          @click="copyResult"
        >
          <Icon name="lucide:copy" class="h-4 w-4 text-muted-500" />
        </button>
        <button
          v-if="latestResult || error"
          class="p-1 rounded hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors"
          :title="showDetails ? 'Hide details' : 'Show details'"
          @click="showDetails = !showDetails"
        >
          <Icon
            :name="showDetails ? 'lucide:chevron-up' : 'lucide:chevron-down'"
            class="h-4 w-4 text-muted-500"
          />
        </button>
      </div>
    </div>

    <!-- Progress Bar -->
    <div v-if="!isComplete || status === 'processing'" class="w-full bg-muted-200 dark:bg-muted-700 rounded-full h-2">
      <div
        class="h-2 rounded-full transition-all duration-300" :class="[
          status === 'error' || status === 'failed'
            ? 'bg-red-500'
            : status === 'completed'
              ? 'bg-green-500'
              : 'bg-blue-500',
        ]"
        :style="{ width: `${progress}%` }"
      />
    </div>

    <!-- Error Message -->
    <div v-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded p-3">
      <div class="flex items-start space-x-2">
        <Icon name="lucide:alert-circle" class="h-4 w-4 text-red-500 mt-0.5" />
        <BaseText size="sm" class="text-red-800 dark:text-red-200">
          {{ error }}
        </BaseText>
      </div>
    </div>

    <!-- Tool Output Summary -->
    <div v-if="latestResult && !showDetails" class="bg-muted-50 dark:bg-muted-800 rounded p-3">
      <BaseText size="sm" class="text-muted-700 dark:text-muted-300">
        <!-- Format different result types -->
        <template v-if="typeof latestResult === 'string'">
          {{ latestResult.slice(0, 150) }}{{ latestResult.length > 150 ? '...' : '' }}
        </template>
        <template v-else-if="latestResult?.results?.length">
          Found {{ latestResult.results.length }} result(s)
        </template>
        <template v-else-if="latestResult?.query">
          Query: {{ latestResult.query }}
        </template>
        <template v-else>
          Tool completed successfully
        </template>
      </BaseText>
    </div>

    <!-- Detailed Result View -->
    <div v-if="showDetails && (latestResult || error)" class="space-y-3">
      <div class="border-t border-muted-200 dark:border-muted-700 pt-3">
        <BaseText size="sm" weight="medium" class="text-muted-700 dark:text-muted-300 mb-2">
          Result Details:
        </BaseText>

        <!-- JSON Result Display -->
        <div class="bg-muted-900 dark:bg-black rounded p-3 overflow-x-auto">
          <pre class="text-sm text-green-400 font-mono">{{ formatResultData(latestResult) }}</pre>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-2 mt-3">
          <BaseButton
            size="sm"
            variant="outline"
            class="flex items-center space-x-1"
            @click="copyResult"
          >
            <Icon name="lucide:copy" class="h-4 w-4" />
            <span>Copy</span>
          </BaseButton>
          <BaseButton
            v-if="latestResult?.url"
            size="sm"
            variant="outline"
            class="flex items-center space-x-1"
            @click="window.open(latestResult.url, '_blank')"
          >
            <Icon name="lucide:external-link" class="h-4 w-4" />
            <span>Open</span>
          </BaseButton>
        </div>
      </div>
    </div>

    <!-- Execution Timeline (if multiple chunks) -->
    <div v-if="chunks.length > 1 && showDetails" class="border-t border-muted-200 dark:border-muted-700 pt-3">
      <BaseText size="sm" weight="medium" class="text-muted-700 dark:text-muted-300 mb-2">
        Execution Timeline:
      </BaseText>
      <div class="space-y-2">
        <div
          v-for="(chunk, index) in chunks"
          :key="index"
          class="flex items-center space-x-2 text-xs"
        >
          <Icon
            :name="chunk.type === 'tool_start' ? 'lucide:play'
              : chunk.type === 'tool_complete' ? 'lucide:check'
                : chunk.type === 'tool_error' ? 'lucide:x'
                  : 'lucide:activity'"
            class="h-3 w-3 text-muted-500"
          />
          <BaseText size="xs" class="text-muted-600 dark:text-muted-400">
            {{ chunk.content }}
          </BaseText>
          <BaseText v-if="chunk.metadata?.executionTime" size="xs" class="text-muted-500">
            ({{ chunk.metadata.executionTime }}ms)
          </BaseText>
        </div>
      </div>
    </div>
  </div>
</template>
