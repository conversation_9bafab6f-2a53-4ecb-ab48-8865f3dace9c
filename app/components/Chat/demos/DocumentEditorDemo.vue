<script setup lang="ts">
import type { DocumentEditorData } from '~/composables/useBookWritingViews'

interface EventLogEntry {
  id: string
  type: 'save' | 'content-change' | 'ai-suggestion'
  message: string
  timestamp: Date
  data?: any
}

// Demo state
const documentKey = ref(0)
const eventLog = ref<EventLogEntry[]>([])

const currentDocType = ref('chapter')
const currentSample = ref('fantasy-chapter')

// Document type options
const docTypeOptions = [
  { label: 'Chapter', value: 'chapter' },
  { label: 'Scene', value: 'scene' },
  { label: 'Notes', value: 'notes' },
  { label: 'Outline', value: 'outline' },
]

// Sample content options
const sampleOptions = [
  { label: 'Fantasy Chapter', value: 'fantasy-chapter' },
  { label: 'Mystery Scene', value: 'mystery-scene' },
  { label: 'Character Notes', value: 'character-notes' },
  { label: 'Plot Outline', value: 'plot-outline' },
  { label: 'Empty Document', value: 'empty' },
]

// Current document data
const currentDocument = ref<DocumentEditorData>({
  documentId: 'demo-doc-1',
  title: 'The Dragon\'s Awakening',
  content: `# The Dragon's Awakening

The morning mist clung to the valley like a shroud, obscuring the ancient stone towers that had stood sentinel for a thousand years. **Lyra** stepped carefully along the narrow path, her leather boots finding purchase on the slick stones.

She had been traveling for three days now, following the *cryptic map* her grandfather had left her. The parchment, yellowed with age, bore strange markings and symbols that seemed to shift in the candlelight.

## The Discovery

As she rounded the bend, Lyra gasped. Before her lay a vast cavern entrance, carved with intricate runes that pulsed with a faint blue light. The air itself seemed to hum with ancient magic.

"This must be it," she whispered, her voice echoing off the stone walls.

**Key Elements to Remember:**
- The runes are magical and respond to touch
- Lyra carries her grandfather's compass
- The dragon has been sleeping for centuries
- Time is running out - the eclipse is tomorrow

*What happens next will change everything...*`,
  type: 'chapter',
  chapterNumber: 1,
  lastModified: new Date(),
  wordCount: 156,
  characterCount: 889,
  readingTime: 37,
  collaborators: [
    {
      id: 'demo-user-1',
      name: 'Alice Writer',
      avatar: 'https://i.pravatar.cc/40?img=1',
      isOnline: true,
    },
    {
      id: 'demo-user-2',
      name: 'Bob Editor',
      avatar: 'https://i.pravatar.cc/40?img=2',
      isOnline: false,
    },
  ],
  projectId: 'demo-project',
})

// Sample documents
const sampleDocuments = {
  'fantasy-chapter': {
    documentId: 'demo-chapter-1',
    title: 'The Dragon\'s Awakening',
    content: `# The Dragon's Awakening

The morning mist clung to the valley like a shroud, obscuring the ancient stone towers that had stood sentinel for a thousand years. **Lyra** stepped carefully along the narrow path, her leather boots finding purchase on the slick stones.

She had been traveling for three days now, following the *cryptic map* her grandfather had left her. The parchment, yellowed with age, bore strange markings and symbols that seemed to shift in the candlelight.

## The Discovery

As she rounded the bend, Lyra gasped. Before her lay a vast cavern entrance, carved with intricate runes that pulsed with a faint blue light. The air itself seemed to hum with ancient magic.

"This must be it," she whispered, her voice echoing off the stone walls.

**Key Elements to Remember:**
- The runes are magical and respond to touch
- Lyra carries her grandfather's compass
- The dragon has been sleeping for centuries
- Time is running out - the eclipse is tomorrow

*What happens next will change everything...*`,
    type: 'chapter' as const,
    chapterNumber: 1,
  },

  'mystery-scene': {
    documentId: 'demo-scene-1',
    title: 'The Locked Room',
    content: `## Scene: The Locked Room

Detective Sarah Chen examined the door one more time. No scratches on the lock, no signs of forced entry. The victim, *Professor Williams*, lay exactly as the housekeeper had found him—slumped over his mahogany desk.

**Observations:**
- Door locked from inside
- Key still in victim's pocket
- Windows sealed shut (painted over years ago)
- No other exits

"Impossible," she muttered, but the evidence was undeniable. Someone had committed the perfect locked-room murder.

**Timeline:**
- 10:30 PM - Professor heard working in study
- 11:45 PM - Housekeeper knocked, no answer
- 12:15 AM - Door forced open, body discovered

The only question was: **How?**`,
    type: 'scene' as const,
    sceneId: 'scene-locked-room',
  },

  'character-notes': {
    documentId: 'demo-notes-1',
    title: 'Character Development - Lyra Stormwind',
    content: `# Character Notes: Lyra Stormwind

## Basic Information
- **Age:** 19
- **Occupation:** Apprentice mage / treasure hunter
- **Hometown:** Millbrook (small farming village)

## Personality Traits
- **Curious** to a fault—sometimes gets into trouble
- **Stubborn** when she believes in something
- **Loyal** to friends and family
- **Impatient** with authority figures

## Background
Raised by her grandfather after her parents disappeared on a quest when she was 8. He taught her basic magic and filled her head with stories of ancient treasures and forgotten kingdoms.

## Goals & Motivations
- Find out what happened to her parents
- Prove herself as a capable mage
- Uncover the secrets of the Dragon's Crown

## Character Arc
**Beginning:** Naive village girl with big dreams
**Middle:** Faces harsh realities of adventure
**End:** Becomes confident, capable hero

## Important Relationships
- **Grandfather Aldric:** Mentor, father figure
- **Kael:** Mysterious companion, possible love interest
- **The Dragon:** Ancient being, complex ally/enemy

## Notes for Future Scenes
- Show her struggling with more advanced magic
- Develop relationship with Kael slowly
- Reveal parents' fate gradually`,
    type: 'notes' as const,
  },

  'plot-outline': {
    documentId: 'demo-outline-1',
    title: 'The Dragon Crown Saga - Plot Outline',
    content: `# The Dragon Crown Saga - Plot Outline

## Act I: The Call to Adventure
### Chapter 1: The Inheritance
- Lyra receives grandfather's mysterious map
- Village is attacked by shadow creatures
- She must flee and begin her quest

### Chapter 2: The First Clue
- Arrives at the ancient library
- Meets Kael, mysterious scholar
- Discovers the legend of the Dragon Crown

### Chapter 3: Into the Wilderness
- Journey through the Whispering Woods
- First major magical challenge
- Kael reveals he's not entirely human

## Act II: Trials and Tribulations
### Chapter 4: The Underground City
- Discover lost dwarven civilization
- Learn about the Crown's true power
- Face the Shadow Lord's minions

### Chapter 5: The Dragon's Lair
- Confront the ancient dragon
- Dragon is not evil, but guardian
- Must prove worth to gain its help

### Chapter 6: Betrayal
- Kael's true identity revealed
- He steals the Crown for the Shadow Lord
- Lyra must escape and regroup

## Act III: The Final Battle
### Chapter 7: Allies and Enemies
- Gather allies from previous chapters
- Learn the Shadow Lord is her father
- Prepare for final confrontation

### Chapter 8: The Crown's Power
- Epic battle at the Shadow Citadel
- Use Dragon Crown to restore balance
- Father is redeemed, family reunited

### Chapter 9: New Beginnings
- Lyra becomes the new Guardian
- Kael stays to help rebuild
- Hint at future adventures

## Themes
- **Growing up:** From naive to mature
- **Family:** What makes a family
- **Power:** Responsibility that comes with it
- **Trust:** Learning who to trust`,
    type: 'outline' as const,
  },

  'empty': {
    documentId: 'demo-empty-1',
    title: 'New Document',
    content: '',
    type: 'chapter' as const,
  },
}

// Load sample document
function loadSampleDocument() {
  const sample = sampleDocuments[currentSample.value as keyof typeof sampleDocuments]
  if (sample) {
    currentDocument.value = {
      ...currentDocument.value,
      ...sample,
      type: currentDocType.value as any,
      lastModified: new Date(),
      wordCount: sample.content.split(/\s+/).length,
      characterCount: sample.content.length,
      readingTime: Math.ceil(sample.content.split(/\s+/).length / 200 * 60), // Assuming 200 WPM reading speed
      collaborators: currentDocument.value.collaborators,
    }
    documentKey.value++ // Force re-render

    addEvent('content-change', `Loaded sample: ${sample.title}`, { type: currentDocType.value })
  }
}

// Create new document
function createNewDocument() {
  currentDocument.value = {
    documentId: `demo-new-${Date.now()}`,
    title: `New ${currentDocType.value.charAt(0).toUpperCase() + currentDocType.value.slice(1)}`,
    content: '',
    type: currentDocType.value as any,
    lastModified: new Date(),
    wordCount: 0,
    characterCount: 0,
    readingTime: 0,
    collaborators: currentDocument.value.collaborators,
    projectId: 'demo-project',
  }
  documentKey.value++

  addEvent('content-change', `Created new ${currentDocType.value}`, { type: currentDocType.value })
}

// Event handlers
function handleSave(data: any) {
  currentDocument.value = { ...currentDocument.value, ...data }
  addEvent('save', 'Document saved successfully', {
    wordCount: data.wordCount,
    characterCount: data.characterCount,
  })
}

function handleContentChange(data: any) {
  currentDocument.value.wordCount = data.wordCount
  currentDocument.value.characterCount = data.characterCount
  currentDocument.value.readingTime = data.readingTime
  currentDocument.value.lastModified = new Date()

  addEvent('content-change', 'Content updated', {
    wordCount: data.wordCount,
    characterCount: data.characterCount,
    readingTime: data.readingTime,
  })
}

function handleAISuggestion(data: any) {
  addEvent('ai-suggestion', 'AI suggestion requested', {
    content: `${data.content?.substring(0, 100)}...`,
    type: data.type,
  })
}

// Utility function to add events to log
function addEvent(type: EventLogEntry['type'], message: string, data?: any) {
  eventLog.value.unshift({
    id: crypto.randomUUID(),
    type,
    message,
    timestamp: new Date(),
    data,
  })

  // Keep only last 20 events
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

// Initialize with default sample
onMounted(() => {
  loadSampleDocument()
})
</script>

<template>
  <div class="space-y-6 p-6">
    <!-- Demo Header -->
    <div class="text-center">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        DocumentEditor Demo
      </h1>
      <p class="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
        Interactive demonstration of the DocumentEditorView component with various document types and features.
      </p>
    </div>

    <!-- Demo Controls -->
    <BaseCard class="p-4">
      <div class="flex flex-wrap gap-4 items-center">
        <div class="flex flex-col">
          <BaseText size="sm" class="font-medium mb-1">
            Document Type:
          </BaseText>
          <BaseListbox
            v-model="currentDocType"
            :items="docTypeOptions"
            placeholder="Select document type"
            class="w-40"
          />
        </div>

        <div class="flex flex-col">
          <BaseText size="sm" class="font-medium mb-1">
            Sample Content:
          </BaseText>
          <BaseListbox
            v-model="currentSample"
            :items="sampleOptions"
            placeholder="Select sample"
            class="w-48"
          />
        </div>

        <div class="flex gap-2 items-end">
          <BaseButton
            color="primary"
            size="sm"
            @click="loadSampleDocument"
          >
            Load Sample
          </BaseButton>

          <BaseButton
            color="default"
            size="sm"
            @click="createNewDocument"
          >
            Create New
          </BaseButton>
        </div>
      </div>
    </BaseCard>

    <!-- Document Editor -->
    <BaseCard class="p-0 overflow-hidden">
      <DocumentEditorView
        :key="documentKey"
        v-bind="currentDocument"
        class="border-0"
        @save="handleSave"
        @content-change="handleContentChange"
        @ai-suggestion="handleAISuggestion"
      />
    </BaseCard>

    <!-- Event Log -->
    <BaseCard class="p-4">
      <BaseHeading as="h3" size="lg" class="mb-4">
        Event Log
      </BaseHeading>
      <div class="space-y-2 max-h-64 overflow-y-auto">
        <div
          v-for="event in eventLog"
          :key="event.id"
          class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center justify-between mb-1">
            <BaseText
              size="sm"
              class="font-medium"
              :class="{
                'text-green-600 dark:text-green-400': event.type === 'save',
                'text-blue-600 dark:text-blue-400': event.type === 'content-change',
                'text-purple-600 dark:text-purple-400': event.type === 'ai-suggestion',
              }"
            >
              {{ event.type }}
            </BaseText>
            <BaseText size="xs" class="text-gray-500 dark:text-gray-400">
              {{ event.timestamp.toLocaleTimeString() }}
            </BaseText>
          </div>
          <BaseText size="sm" class="text-gray-700 dark:text-gray-300">
            {{ event.message }}
          </BaseText>
          <div v-if="event.data" class="mt-2">
            <BaseText size="xs" class="text-gray-500 dark:text-gray-400 font-mono">
              {{ JSON.stringify(event.data, null, 2) }}
            </BaseText>
          </div>
        </div>
        <div v-if="eventLog.length === 0" class="text-center py-4">
          <BaseText class="text-gray-500 dark:text-gray-400">
            No events yet. Interact with the editor to see events.
          </BaseText>
        </div>
      </div>
    </BaseCard>

    <!-- Document Stats -->
    <BaseCard class="p-4">
      <BaseHeading as="h3" size="lg" class="mb-4">
        Document Statistics
      </BaseHeading>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="text-center">
          <BaseText size="2xl" class="font-bold text-primary-500">
            {{ currentDocument.wordCount || 0 }}
          </BaseText>
          <BaseText size="sm" class="text-gray-500 dark:text-gray-400">
            Words
          </BaseText>
        </div>
        <div class="text-center">
          <BaseText size="2xl" class="font-bold text-primary-500">
            {{ currentDocument.characterCount || 0 }}
          </BaseText>
          <BaseText size="sm" class="text-gray-500 dark:text-gray-400">
            Characters
          </BaseText>
        </div>
        <div class="text-center">
          <BaseText size="2xl" class="font-bold text-primary-500">
            {{ Math.ceil((currentDocument.readingTime || 0) / 60) }}
          </BaseText>
          <BaseText size="sm" class="text-gray-500 dark:text-gray-400">
            Min Read
          </BaseText>
        </div>
        <div class="text-center">
          <BaseText size="2xl" class="font-bold text-primary-500">
            {{ currentDocument.collaborators?.length || 0 }}
          </BaseText>
          <BaseText size="sm" class="text-gray-500 dark:text-gray-400">
            Collaborators
          </BaseText>
        </div>
      </div>
    </BaseCard>
  </div>
</template>
