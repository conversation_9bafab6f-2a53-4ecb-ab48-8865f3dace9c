<script setup lang="ts">
interface GraphState {
  currentAgent?: string
  nextAgent?: string
  turnCount?: number
  visitedNodes?: string[]
  activeTools?: string[]
  isActive?: boolean
  lastUpdate?: Date
}

interface Props {
  isOpen: boolean
  state: GraphState
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  state: () => ({}),
})

const emit = defineEmits<{
  close: []
  toggle: []
}>()

// Format the last update time
const lastUpdateTime = computed(() => {
  if (!props.state.lastUpdate)
    return 'Never'
  const now = new Date()
  const diff = now.getTime() - props.state.lastUpdate.getTime()

  if (diff < 1000)
    return 'Just now'
  if (diff < 60000)
    return `${Math.floor(diff / 1000)}s ago`
  if (diff < 3600000)
    return `${Math.floor(diff / 60000)}m ago`
  return props.state.lastUpdate.toLocaleTimeString()
})

// Activity indicator
const isActiveStreaming = computed(() => props.state.isActive)

// Agent status indicators
const agentStatus = computed(() => {
  if (!props.state.currentAgent && !props.state.nextAgent) {
    return { status: 'idle', message: 'Waiting for conversation to start' }
  }
  if (props.state.isActive) {
    return { status: 'active', message: `${props.state.currentAgent || 'System'} is thinking...` }
  }
  if (props.state.nextAgent) {
    return { status: 'waiting', message: `Next: ${props.state.nextAgent}` }
  }
  return { status: 'idle', message: 'Conversation paused' }
})
</script>

<template>
  <Transition
    enter-active-class="transition-transform duration-200 ease-out"
    leave-active-class="transition-transform duration-200 ease-in"
    enter-from-class="translate-x-full"
    enter-to-class="translate-x-0"
    leave-from-class="translate-x-0"
    leave-to-class="translate-x-full"
  >
    <div
      v-if="isOpen"
      class="fixed top-0 right-0 h-full w-80 bg-white dark:bg-muted-900 border-l border-muted-200 dark:border-muted-700 shadow-lg z-50"
    >
      <!-- Panel Header -->
      <div class="flex items-center justify-between p-4 border-b border-muted-200 dark:border-muted-700">
        <div class="flex items-center gap-2">
          <Icon
            name="lucide:git-graph"
            class="size-5 text-primary-500"
          />
          <h3 class="font-semibold text-muted-900 dark:text-muted-100">
            Graph State
          </h3>
          <div
            v-if="isActiveStreaming"
            class="flex gap-1"
          >
            <div class="size-1.5 rounded-full bg-primary-500 animate-pulse" />
            <div class="size-1.5 rounded-full bg-primary-400 animate-pulse" style="animation-delay: 0.2s" />
            <div class="size-1.5 rounded-full bg-primary-300 animate-pulse" style="animation-delay: 0.4s" />
          </div>
        </div>

        <button
          class="p-1 rounded-md hover:bg-muted-100 dark:hover:bg-muted-800 transition-colors"
          @click="emit('close')"
        >
          <Icon name="lucide:x" class="size-4" />
        </button>
      </div>

      <!-- Panel Content -->
      <div class="flex-1 overflow-y-auto p-4 space-y-6">
        <!-- Agent Status -->
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-muted-600 dark:text-muted-400 uppercase tracking-wider">
            Agent Status
          </h4>

          <div class="space-y-2">
            <div class="flex items-center gap-3 p-3 rounded-lg bg-muted-50 dark:bg-muted-800/50">
              <div
                class="size-3 rounded-full"
                :class="{
                  'bg-green-500 animate-pulse': agentStatus.status === 'active',
                  'bg-yellow-500': agentStatus.status === 'waiting',
                  'bg-muted-400': agentStatus.status === 'idle',
                }"
              />
              <div class="flex-1 min-w-0">
                <div class="text-sm text-muted-900 dark:text-muted-100">
                  {{ agentStatus.message }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Current Agent -->
        <div v-if="state.currentAgent" class="space-y-3">
          <h4 class="text-sm font-medium text-muted-600 dark:text-muted-400 uppercase tracking-wider">
            Current Agent
          </h4>

          <div class="p-3 rounded-lg border border-primary-200 dark:border-primary-800 bg-primary-50 dark:bg-primary-900/20">
            <div class="flex items-center gap-2">
              <Icon name="lucide:user-check" class="size-4 text-primary-600 dark:text-primary-400" />
              <span class="font-medium text-primary-700 dark:text-primary-300">
                {{ state.currentAgent }}
              </span>
            </div>
          </div>
        </div>

        <!-- Next Agent -->
        <div v-if="state.nextAgent" class="space-y-3">
          <h4 class="text-sm font-medium text-muted-600 dark:text-muted-400 uppercase tracking-wider">
            Next Agent
          </h4>

          <div class="p-3 rounded-lg border border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800/50">
            <div class="flex items-center gap-2">
              <Icon name="lucide:user-clock" class="size-4 text-muted-600 dark:text-muted-400" />
              <span class="text-muted-700 dark:text-muted-300">
                {{ state.nextAgent }}
              </span>
            </div>
          </div>
        </div>

        <!-- Turn Counter -->
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-muted-600 dark:text-muted-400 uppercase tracking-wider">
            Conversation Progress
          </h4>

          <div class="grid grid-cols-2 gap-3">
            <div class="p-3 rounded-lg bg-info-50 dark:bg-info-900/20 border border-info-200 dark:border-info-800">
              <div class="text-2xl font-bold text-info-700 dark:text-info-300">
                {{ state.turnCount || 0 }}
              </div>
              <div class="text-xs text-info-600 dark:text-info-400">
                Turns
              </div>
            </div>

            <div class="p-3 rounded-lg bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
              <div class="text-2xl font-bold text-muted-700 dark:text-muted-300">
                {{ state.visitedNodes?.length || 0 }}
              </div>
              <div class="text-xs text-muted-600 dark:text-muted-400">
                Nodes
              </div>
            </div>
          </div>
        </div>

        <!-- Visited Nodes -->
        <div v-if="state.visitedNodes && state.visitedNodes.length > 0" class="space-y-3">
          <h4 class="text-sm font-medium text-muted-600 dark:text-muted-400 uppercase tracking-wider">
            Visited Nodes
          </h4>

          <div class="flex flex-wrap gap-2">
            <BaseBadge
              v-for="node in state.visitedNodes"
              :key="node"
              size="xs"
              color="muted"
              variant="outline"
            >
              {{ node }}
            </BaseBadge>
          </div>
        </div>

        <!-- Active Tools -->
        <div v-if="state.activeTools && state.activeTools.length > 0" class="space-y-3">
          <h4 class="text-sm font-medium text-muted-600 dark:text-muted-400 uppercase tracking-wider">
            Active Tools
          </h4>

          <div class="space-y-2">
            <div
              v-for="tool in state.activeTools"
              :key="tool"
              class="flex items-center gap-2 p-2 rounded-md bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800"
            >
              <Icon name="lucide:wrench" class="size-3 text-warning-600 dark:text-warning-400" />
              <span class="text-sm text-warning-700 dark:text-warning-300">
                {{ tool }}
              </span>
            </div>
          </div>
        </div>

        <!-- Last Update -->
        <div class="pt-4 border-t border-muted-200 dark:border-muted-700">
          <div class="flex items-center justify-between text-xs text-muted-500 dark:text-muted-400">
            <span>Last Update</span>
            <span>{{ lastUpdateTime }}</span>
          </div>
        </div>
      </div>

      <!-- Panel Footer (optional debug info) -->
      <div class="p-4 border-t border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800/30">
        <button
          class="text-xs text-muted-500 dark:text-muted-400 hover:text-muted-700 dark:hover:text-muted-200 transition-colors"
          @click="emit('toggle')"
        >
          Click outside to close panel
        </button>
      </div>
    </div>
  </Transition>

  <!-- Overlay -->
  <Transition
    enter-active-class="transition-opacity duration-200"
    leave-active-class="transition-opacity duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="isOpen"
      class="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
      @click="emit('close')"
    />
  </Transition>
</template>
