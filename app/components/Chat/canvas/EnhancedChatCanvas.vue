<script setup lang="ts">
import type { ChatMessage } from '~/types/chat'
import GraphStatePanel from '../GraphStatePanel.vue'
import EnhancedMessageBubble from '../views/EnhancedMessageBubble.vue'
import MarkdownView from '../views/MarkdownView.vue'
import { getViewComponent } from './registry'

interface GraphState {
  currentAgent?: string
  nextAgent?: string
  turnCount?: number
  visitedNodes?: string[]
  activeTools?: string[]
  isActive?: boolean
  lastUpdate?: Date
}

interface Props {
  message: ChatMessage
  showFallbackAsPlainText?: boolean
  showStatePanel?: boolean
  graphState?: GraphState
  useEnhancedBubble?: boolean
  animate?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showFallbackAsPlainText: true,
  showStatePanel: false,
  graphState: () => ({}),
  useEnhancedBubble: true,
  animate: true,
  compact: false,
})

const emit = defineEmits<{
  'toggle-state-panel': []
  'close-state-panel': []
  'copy': [content: string]
  'retry': [messageId: string]
  'delete': [messageId: string]
  'react': [messageId: string, emoji: string]
}>()

// Helpers to detect view and normalize object content
function detectViewFromContent(content: any): { type?: string, props?: Record<string, any>, text?: string } {
  if (content == null)
    return {}
  if (typeof content === 'string')
    return { text: content }

  if (Array.isArray(content)) {
    const textParts = content.filter((p: any) => p?.type === 'text' && typeof p.text === 'string')
    if (textParts.length)
      return { text: textParts.map((p: any) => p.text).join('\n') }
    return { type: undefined, props: { value: content } }
  }

  if (typeof content === 'object') {
    if (typeof content.text === 'string')
      return { text: content.text }
    if (typeof content.markdown === 'string')
      return { type: 'markdown', props: { content: content.markdown } }
    if (content.code && typeof content.code === 'string') {
      return { type: 'code', props: { code: content.code, language: content.language || 'text', filename: content.filename || '' } }
    }
    if (content.table && (Array.isArray(content.table?.rows) || Array.isArray(content.table?.data))) {
      return { type: 'table', props: { rows: content.table.rows || content.table.data, columns: content.table.columns || [] } }
    }
    if (content.chart || content.series || content.options) {
      return { type: 'chart', props: { series: content.series || [], options: content.options || {}, title: content.title } }
    }
    return { type: undefined, props: { value: content } }
  }
  return {}
}

const resolvedView = computed(() => {
  const metadata = props.message.metadata as any
  const explicit = metadata?.view
  if (explicit?.type)
    return { type: explicit.type as string, props: explicit.props || {} }
  return detectViewFromContent(props.message.content)
})

const viewType = computed(() => resolvedView.value.type)

const viewProps = computed(() => resolvedView.value.props || {})

// Get the component to render based on view type
const viewComponent = computed(() => {
  if (!viewType.value)
    return null
  return getViewComponent(viewType.value)
})

// Determine rendering strategy
const shouldUseEnhancedBubble = computed(() => {
  return props.useEnhancedBubble && !viewComponent.value
})

const shouldRenderCustomView = computed(() => {
  return viewComponent.value && viewType.value
})

const shouldRenderFallback = computed(() => {
  return !shouldUseEnhancedBubble.value && !shouldRenderCustomView.value && props.showFallbackAsPlainText
})

// Content type detection
const isMarkdownContent = computed(() => {
  const value = resolvedView.value.text
  if (!value)
    return false
  const markdownPatterns = [
    /#{1,6}\s+.+/,
    /\*\*.*\*\*/,
    /\*.*\*/,
    /```[\s\S]*```/,
    /`.*`/,
    /\[.*\]\(.*\)/,
    /^\s*[-*+]\s+/,
    /^\s*\d+\.\s+/,
    /^\s*>\s+/,
  ]
  return markdownPatterns.some(pattern => pattern.test(value))
})

// State panel handlers
function handleToggleStatePanel() {
  emit('toggle-state-panel')
}

function handleCloseStatePanel() {
  emit('close-state-panel')
}

// Message action handlers
function handleCopy(content: string) {
  emit('copy', content)
}

function handleRetry(messageId: string) {
  emit('retry', messageId)
}

function handleDelete(messageId: string) {
  emit('delete', messageId)
}

function handleReact(messageId: string, emoji: string) {
  emit('react', messageId, emoji)
}

// Check if graph state has meaningful data
const hasGraphStateData = computed(() => {
  const state = props.graphState
  return Boolean(
    state.currentAgent
    || state.nextAgent
    || (state.turnCount && state.turnCount > 0)
    || (state.visitedNodes && state.visitedNodes.length > 0)
    || (state.activeTools && state.activeTools.length > 0),
  )
})
</script>

<template>
  <div class="enhanced-chat-canvas relative">
    <!-- Enhanced Message Bubble (default for most messages) -->
    <EnhancedMessageBubble
      v-if="shouldUseEnhancedBubble"
      :message="message"
      :animate="animate"
      :compact="compact"
      @copy="handleCopy"
      @retry="handleRetry"
      @delete="handleDelete"
      @react="handleReact"
    >
      <template #content>
        <!-- If custom view is resolved but we're using bubble, render a light wrapper -->
        <component
          :is="viewComponent"
          v-if="viewComponent && viewType"
          v-bind="viewProps"
        />

        <!-- Render markdown content if detected -->
        <MarkdownView
          v-else-if="isMarkdownContent && resolvedView.text"
          :content="resolvedView.text"
        />

        <!-- Fallback to plain text or pretty JSON -->
        <div v-else-if="resolvedView.text" class="whitespace-pre-wrap break-words leading-relaxed">
          {{ resolvedView.text }}
        </div>
        <div v-else-if="typeof message.content === 'object'" class="whitespace-pre rounded-md bg-muted-50 dark:bg-muted-800 border border-muted-200 dark:border-muted-700 p-3 text-xs">
          <pre>{{ JSON.stringify(message.content, null, 2) }}</pre>
        </div>

        <!-- Empty state -->
        <div v-else class="text-muted-400 text-sm italic">
          No content to display
        </div>
      </template>
    </EnhancedMessageBubble>

    <!-- Custom view component for specialized content -->
    <div
      v-else-if="shouldRenderCustomView"
      class="custom-view-container"
    >
      <component
        :is="viewComponent"
        v-bind="viewProps"
        :message="message"
        @copy="handleCopy"
        @retry="handleRetry"
        @delete="handleDelete"
        @react="handleReact"
      >
        <!-- For 'doc' view, pass the message content as slot content -->
        <template v-if="viewType === 'doc'">
          {{ message.content }}
        </template>

        <!-- For 'inbox' view, pass message data as props if not provided -->
        <template v-else-if="viewType === 'inbox'">
          <!-- Slot content rendered by InboxMessage internally -->
        </template>

        <!-- Default slot content for other view types -->
        <template v-else>
          {{ message.content }}
        </template>
      </component>
    </div>

    <!-- Legacy fallback to plain text or pretty JSON -->
    <div
      v-else-if="shouldRenderFallback && message.content !== undefined && message.content !== null"
      class="fallback-content p-4 rounded-lg bg-muted-50 dark:bg-muted-800 border border-muted-200 dark:border-muted-700"
    >
      <div v-if="typeof message.content === 'string'" class="whitespace-pre-wrap break-words text-muted-900 dark:text-muted-100">
        {{ message.content }}
      </div>
      <pre v-else class="text-xs">{{ JSON.stringify(message.content, null, 2) }}</pre>
    </div>

    <!-- Empty state -->
    <div
      v-else-if="!message.content && !shouldRenderCustomView"
      class="empty-state p-4 text-center text-muted-400 text-sm italic"
    >
      No content to display
    </div>

    <!-- Graph State Panel Toggle (floating button) -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 scale-90 translate-y-4"
      enter-to-class="opacity-100 scale-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 scale-100 translate-y-0"
      leave-to-class="opacity-0 scale-90 translate-y-4"
    >
      <button
        v-if="hasGraphStateData"
        class="fixed bottom-6 right-6 p-3 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 z-30 group transform hover:scale-105"
        title="Toggle Graph State Panel"
        @click="handleToggleStatePanel"
      >
        <Icon name="lucide:git-graph" class="size-5" />
        <div
          v-if="graphState.isActive"
          class="absolute -top-1 -right-1 size-3 bg-green-400 rounded-full animate-pulse border-2 border-white"
        />

        <!-- Ripple effect on hover -->
        <div class="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-20 group-hover:animate-ping" />
      </button>
    </Transition>

    <!-- Graph State Panel -->
    <GraphStatePanel
      :is-open="showStatePanel"
      :state="graphState"
      @close="handleCloseStatePanel"
      @toggle="handleToggleStatePanel"
    />
  </div>
</template>

<style scoped>
/* Enhanced canvas styling */
.enhanced-chat-canvas {
  @apply relative;
}

.custom-view-container {
  @apply relative;

  /* Add subtle animation for custom views */
  animation: fadeInUp 0.4s ease-out;
}

.fallback-content {
  /* Subtle animation for fallback content */
  animation: fadeIn 0.3s ease-out;
}

.empty-state {
  animation: fadeIn 0.2s ease-out;
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Enhanced shadows and gradients */
.bg-gradient-to-br {
  background-size: 120% 120%;
  transition: background-size 0.3s ease;
}

.bg-gradient-to-br:hover {
  background-size: 130% 130%;
}

/* Floating button enhancements */
button[title='Toggle Graph State Panel'] {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .enhanced-chat-canvas {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  button[title='Toggle Graph State Panel'] {
    bottom: 1rem;
    right: 1rem;
    padding: 0.625rem;
  }

  button[title='Toggle Graph State Panel'] .size-5 {
    width: 1rem;
    height: 1rem;
  }
}

/* Dark mode specific enhancements */
@media (prefers-color-scheme: dark) {
  .fallback-content {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

/* Print styles */
@media print {
  .enhanced-chat-canvas button,
  .enhanced-chat-canvas .floating-button {
    @apply hidden;
  }
}
</style>
