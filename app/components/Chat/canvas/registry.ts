import type { Component } from 'vue'
import { createLazyComponent, lazyBlockConfigs } from '~/composables/useLazyComponent'
import DocMessage from '../../content/DocMessage.vue'
import InboxMessage from '../../InboxMessage.vue'
import EnhancedMessageBubble from '../views/EnhancedMessageBubble.vue'
import MarkdownView from '../views/MarkdownView.vue'
// Import enhanced registry for full feature set
import { ENHANCED_VIEW_REGISTRY, getViewComponent as getEnhancedViewComponent } from './registry.enhanced'
import ToolCard from './ToolCard.vue'

// Lazy-loaded heavy components with skeleton states
const LazyCodeBlock = createLazyComponent(
  () => import('../blocks/CodeBlock.vue'),
  lazyBlockConfigs.code,
)

const LazyChartBlock = createLazyComponent(
  () => import('../blocks/ChartBlock.vue'),
  lazyBlockConfigs.chart,
)

const LazyTableBlock = createLazyComponent(
  () => import('../blocks/TableBlock.vue'),
  lazyBlockConfigs.table,
)

/**
 * Chat Canvas View Registry
 *
 * Maps view types to Vue components for dynamic rendering in ChatCanvas.
 * Each entry represents a different way to render message content.
 */
export interface ViewRegistry {
  [viewType: string]: Component
}

/**
 * Available view types for message rendering
 */
export type ViewType = 'doc' | 'inbox' | 'tool_card' | 'code' | 'chart' | 'table' | 'enhanced_message' | 'markdown' | string

/**
 * Default view registry mapping types to components
 * Heavy components (code, chart, table) are lazy-loaded with skeleton states
 */
export const DEFAULT_VIEW_REGISTRY: ViewRegistry = {
  doc: DocMessage,
  inbox: InboxMessage,
  tool_card: ToolCard,
  enhanced_message: EnhancedMessageBubble,
  markdown: MarkdownView,
  code: LazyCodeBlock,
  chart: LazyChartBlock,
  table: LazyTableBlock,
}

/**
 * Get a component from the registry by view type
 * @param viewType - The type of view to render
 * @param registry - Optional custom registry (defaults to enhanced registry for full features)
 * @returns Component or null if not found
 */
export function getViewComponent(
  viewType: string,
  registry: ViewRegistry = ENHANCED_VIEW_REGISTRY,
): Component | null {
  return registry[viewType] || getEnhancedViewComponent(viewType) || null
}

/**
 * Check if a view type is registered
 * @param viewType - The type to check
 * @param registry - Optional custom registry (defaults to DEFAULT_VIEW_REGISTRY)
 * @returns True if the view type is registered
 */
export function isViewRegistered(
  viewType: string,
  registry: ViewRegistry = DEFAULT_VIEW_REGISTRY,
): boolean {
  return viewType in registry
}

/**
 * Register a new view type with its component
 * @param viewType - The type to register
 * @param component - The Vue component to associate with the type
 * @param registry - Optional custom registry (defaults to DEFAULT_VIEW_REGISTRY)
 */
export function registerView(
  viewType: string,
  component: Component,
  registry: ViewRegistry = DEFAULT_VIEW_REGISTRY,
): void {
  registry[viewType] = component
}

/**
 * Get all registered view types
 * @param registry - Optional custom registry (defaults to DEFAULT_VIEW_REGISTRY)
 * @returns Array of registered view types
 */
export function getRegisteredViewTypes(
  registry: ViewRegistry = DEFAULT_VIEW_REGISTRY,
): string[] {
  return Object.keys(registry)
}
