<script setup lang="ts">
import type { ChatMessage } from '~/types/chat'
import GraphStatePanel from '../GraphStatePanel.vue'
import { getViewComponent } from './registry'

interface GraphState {
  currentAgent?: string
  nextAgent?: string
  turnCount?: number
  visitedNodes?: string[]
  activeTools?: string[]
  isActive?: boolean
  lastUpdate?: Date
}

interface Props {
  message: ChatMessage
  showFallbackAsPlainText?: boolean
  showStatePanel?: boolean
  graphState?: GraphState
}

const props = withDefaults(defineProps<Props>(), {
  showFallbackAsPlainText: true,
  showStatePanel: false,
  graphState: () => ({}),
})

const emit = defineEmits<{
  'toggle-state-panel': []
  'close-state-panel': []
}>()

// Helpers to detect view from content objects
function detectViewFromContent(content: any): { type?: string, props?: Record<string, any>, text?: string } {
  if (content == null)
    return {}
  if (typeof content === 'string')
    return { text: content }

  // OpenAI-style content parts array
  if (Array.isArray(content)) {
    const textParts = content.filter((p: any) => p?.type === 'text' && typeof p.text === 'string')
    if (textParts.length)
      return { text: textParts.map((p: any) => p.text).join('\n') }
    return { type: undefined, props: { value: content } }
  }

  // Object heuristics
  if (typeof content === 'object') {
    if (typeof content.text === 'string')
      return { text: content.text }
    if (typeof content.markdown === 'string')
      return { type: 'markdown', props: { content: content.markdown } }
    if (content.code && typeof content.code === 'string') {
      return { type: 'code', props: { code: content.code, language: content.language || 'text', filename: content.filename || '' } }
    }
    if (content.table && (Array.isArray(content.table?.rows) || Array.isArray(content.table?.data))) {
      return { type: 'table', props: { rows: content.table.rows || content.table.data, columns: content.table.columns || [] } }
    }
    if (content.chart || content.series || content.options) {
      return { type: 'chart', props: { series: content.series || [], options: content.options || {}, title: content.title } }
    }
    // Unknown object → pretty JSON fallback
    return { type: undefined, props: { value: content } }
  }
  return {}
}

// Extract view metadata from message with auto-detection fallback
const resolvedView = computed(() => {
  const metadata = props.message.metadata as any
  const explicit = metadata?.view
  if (explicit?.type)
    return { type: explicit.type as string, props: explicit.props || {} }
  return detectViewFromContent(props.message.content)
})

const viewType = computed(() => resolvedView.value.type)

const viewProps = computed(() => resolvedView.value.props || {})

// Get the component to render based on view type
const viewComponent = computed(() => {
  if (!viewType.value)
    return null
  return getViewComponent(viewType.value)
})

// Determine if we should render fallback content
const shouldRenderFallback = computed(() => {
  return !viewComponent.value && props.showFallbackAsPlainText
})

// State panel handlers
function handleToggleStatePanel() {
  emit('toggle-state-panel')
}

function handleCloseStatePanel() {
  emit('close-state-panel')
}

// Check if graph state has meaningful data to show toggle button
const hasGraphStateData = computed(() => {
  const state = props.graphState
  return Boolean(
    state.currentAgent
    || state.nextAgent
    || (state.turnCount && state.turnCount > 0)
    || (state.visitedNodes && state.visitedNodes.length > 0)
    || (state.activeTools && state.activeTools.length > 0),
  )
})
</script>

<template>
  <div class="chat-canvas relative">
    <!-- Dynamic view component -->
    <component
      :is="viewComponent"
      v-if="viewComponent"
      v-bind="viewProps"
    >
      <!-- For 'doc' view, pass the message content as slot content -->
      <template v-if="viewType === 'doc'">
        {{ message.content }}
      </template>

      <!-- For 'inbox' view, pass message data as props if not provided -->
      <template v-else-if="viewType === 'inbox'">
        <!-- Slot content rendered by InboxMessage internally -->
      </template>

      <!-- Default slot content for other view types -->
      <template v-else>
        {{ message.content }}
      </template>
    </component>

    <!-- Fallback to plain text or pretty JSON if no view is registered or provided -->
    <div
      v-else-if="shouldRenderFallback && message.content !== undefined && message.content !== null"
      class="whitespace-pre-wrap break-words"
    >
      <template v-if="typeof message.content === 'string'">
        {{ message.content }}
      </template>
      <template v-else>
        <pre class="text-xs">{{ JSON.stringify(message.content, null, 2) }}</pre>
      </template>
    </div>

    <!-- Empty state -->
    <div
      v-else-if="!message.content && !viewComponent"
      class="text-muted-400 text-sm italic"
    >
      No content to display
    </div>

    <!-- Graph State Panel Toggle (floating button) -->
    <button
      v-if="hasGraphStateData"
      class="fixed bottom-6 right-6 p-3 rounded-full bg-primary-600 hover:bg-primary-700 text-white shadow-lg transition-colors z-30 group"
      title="Toggle Graph State Panel"
      @click="handleToggleStatePanel"
    >
      <Icon name="lucide:git-graph" class="size-5" />
      <div
        v-if="graphState.isActive"
        class="absolute -top-1 -right-1 size-3 bg-green-500 rounded-full animate-pulse"
      />
    </button>

    <!-- Graph State Panel -->
    <GraphStatePanel
      :is-open="showStatePanel"
      :state="graphState"
      @close="handleCloseStatePanel"
      @toggle="handleToggleStatePanel"
    />
  </div>
</template>
