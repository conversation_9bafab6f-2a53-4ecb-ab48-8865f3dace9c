import type { Component } from 'vue'
import BaseChart from '~/components/base/BaseChart.vue'
import BaseFormModal from '~/components/base/BaseFormModal.vue'
// Import enhanced base components
import BaseTable from '~/components/base/BaseTable.vue'
import BaseAnalyticsChart from '~/components/base/charts/BaseAnalyticsChart.vue'
import BaseKpiChart from '~/components/base/charts/BaseKpiChart.vue'
import { createLazyComponent, lazyBlockConfigs } from '~/composables/useLazyComponent'
import DocMessage from '../../content/DocMessage.vue'
import InboxMessage from '../../InboxMessage.vue'
import ToolCard from './ToolCard.vue'

// Define lazy loading configurations for enhanced components
export const enhancedLazyConfigs = {
  form_modal: {
    skeletonType: 'generic' as const,
    skeletonHeight: 300,
    skeletonDelay: 100,
  },
  data_table: {
    skeletonType: 'table' as const,
    skeletonHeight: 400,
    skeletonDelay: 100,
  },
  analytics_chart: {
    skeletonType: 'chart' as const,
    skeletonHeight: 350,
    skeletonDelay: 150,
  },
  kpi_chart: {
    skeletonType: 'chart' as const,
    skeletonHeight: 400,
    skeletonDelay: 150,
  },
  // Book writing view configurations
  suggestion_bubble: {
    skeletonType: 'generic' as const,
    skeletonHeight: 200,
    skeletonDelay: 100,
  },
  image_gallery: {
    skeletonType: 'generic' as const,
    skeletonHeight: 400,
    skeletonDelay: 150,
  },
  chapter_outline: {
    skeletonType: 'generic' as const,
    skeletonHeight: 500,
    skeletonDelay: 100,
  },
  writing_stats: {
    skeletonType: 'chart' as const,
    skeletonHeight: 600,
    skeletonDelay: 150,
  },
  document_editor: {
    skeletonType: 'generic' as const,
    skeletonHeight: 400,
    skeletonDelay: 100,
  },
}

// Original lazy-loaded heavy components with skeleton states
const LazyCodeBlock = createLazyComponent(
  () => import('../blocks/CodeBlock.vue'),
  lazyBlockConfigs.code,
)

const LazyChartBlock = createLazyComponent(
  () => import('../blocks/ChartBlock.vue'),
  lazyBlockConfigs.chart,
)

const LazyTableBlock = createLazyComponent(
  () => import('../blocks/TableBlock.vue'),
  lazyBlockConfigs.table,
)

// Enhanced lazy-loaded components with our new reusable base components
const LazyDataTable = createLazyComponent(
  () => BaseTable,
  enhancedLazyConfigs.data_table,
)

const LazyFormModal = createLazyComponent(
  () => BaseFormModal,
  enhancedLazyConfigs.form_modal,
)

const LazyAnalyticsChart = createLazyComponent(
  () => BaseAnalyticsChart,
  enhancedLazyConfigs.analytics_chart,
)

const LazyKpiChart = createLazyComponent(
  () => BaseKpiChart,
  enhancedLazyConfigs.kpi_chart,
)

// Book writing lazy-loaded components
const LazySuggestionBubbleView = createLazyComponent(
  () => import('./views/SuggestionBubbleView.vue'),
  enhancedLazyConfigs.suggestion_bubble,
)

const LazyImageGalleryView = createLazyComponent(
  () => import('./views/ImageGalleryView.vue'),
  enhancedLazyConfigs.image_gallery,
)

const LazyChapterOutlineView = createLazyComponent(
  () => import('./views/ChapterOutlineView.vue'),
  enhancedLazyConfigs.chapter_outline,
)

const LazyWritingStatsView = createLazyComponent(
  () => import('./views/WritingStatsView.vue'),
  enhancedLazyConfigs.writing_stats,
)

const LazyDocumentEditorView = createLazyComponent(
  () => import('./views/DocumentEditorView.vue'),
  enhancedLazyConfigs.document_editor,
)

/**
 * Chat Canvas View Registry
 *
 * Maps view types to Vue components for dynamic rendering in ChatCanvas.
 * Each entry represents a different way to render message content.
 */
export interface ViewRegistry {
  [viewType: string]: Component
}

/**
 * Available view types for message rendering
 * Extended with new enhanced component types and book writing views
 */
export type ViewType =
  | 'doc'
  | 'inbox'
  | 'tool_card'
  | 'code'
  | 'chart'
  | 'table'
  | 'data_table'
  | 'form_modal'
  | 'analytics_chart'
  | 'kpi_chart'
  | 'suggestion_bubble'
  | 'image_gallery'
  | 'chapter_outline'
  | 'writing_stats'
  | 'document_editor'
  | string

/**
 * Enhanced view registry mapping types to components
 * Includes both original, enhanced, and book writing components
 */
export const ENHANCED_VIEW_REGISTRY: ViewRegistry = {
  // Original components
  doc: DocMessage,
  inbox: InboxMessage,
  tool_card: ToolCard,
  code: LazyCodeBlock,
  chart: LazyChartBlock,
  table: LazyTableBlock,

  // Enhanced reusable components
  data_table: LazyDataTable,
  form_modal: LazyFormModal,
  analytics_chart: LazyAnalyticsChart,
  kpi_chart: LazyKpiChart,
  base_chart: BaseChart,

  // Book writing view components
  suggestion_bubble: LazySuggestionBubbleView,
  image_gallery: LazyImageGalleryView,
  chapter_outline: LazyChapterOutlineView,
  writing_stats: LazyWritingStatsView,
  document_editor: LazyDocumentEditorView,
}

/**
 * Get a component from the registry by view type
 * @param viewType - The type of view to render
 * @param registry - Optional custom registry (defaults to ENHANCED_VIEW_REGISTRY)
 * @returns Component or null if not found
 */
export function getViewComponent(
  viewType: string,
  registry: ViewRegistry = ENHANCED_VIEW_REGISTRY,
): Component | null {
  return registry[viewType] || null
}

/**
 * Check if a view type is registered
 * @param viewType - The type to check
 * @param registry - Optional custom registry (defaults to ENHANCED_VIEW_REGISTRY)
 * @returns True if the view type is registered
 */
export function isViewRegistered(
  viewType: string,
  registry: ViewRegistry = ENHANCED_VIEW_REGISTRY,
): boolean {
  return viewType in registry
}

/**
 * Register a new view type with its component
 * @param viewType - The type to register
 * @param component - The Vue component to associate with the type
 * @param registry - Optional custom registry (defaults to ENHANCED_VIEW_REGISTRY)
 */
export function registerView(
  viewType: string,
  component: Component,
  registry: ViewRegistry = ENHANCED_VIEW_REGISTRY,
): void {
  registry[viewType] = component
}

/**
 * Get all registered view types
 * @param registry - Optional custom registry (defaults to ENHANCED_VIEW_REGISTRY)
 * @returns Array of registered view types
 */
export function getRegisteredViewTypes(
  registry: ViewRegistry = ENHANCED_VIEW_REGISTRY,
): string[] {
  return Object.keys(registry)
}

/**
 * Enhanced component bundle map for preloading
 */
export const enhancedComponentBundleMap = {
  DataTable: {
    name: 'DataTable',
    estimatedSize: 40, // Medium - includes sorting, pagination, etc.
    dependencies: ['BaseTable', 'FlexTableWrapper'],
    loadPriority: 'medium',
  },
  FormModal: {
    name: 'FormModal',
    estimatedSize: 35, // Medium - includes validation, field management
    dependencies: ['BaseFormModal'],
    loadPriority: 'medium',
  },
  AnalyticsChart: {
    name: 'AnalyticsChart',
    estimatedSize: 50, // Medium-large - includes chart logic, data processing
    dependencies: ['BaseChart', 'AddonApexcharts'],
    loadPriority: 'low',
  },
  KpiChart: {
    name: 'KpiChart',
    estimatedSize: 45, // Medium - includes KPI visualization logic
    dependencies: ['BaseChart', 'AddonApexcharts'],
    loadPriority: 'low',
  },
  // Book writing components
  SuggestionBubbleView: {
    name: 'SuggestionBubbleView',
    estimatedSize: 25, // Small-medium - suggestion management interface
    dependencies: ['BaseButton', 'BaseTag', 'BaseModal'],
    loadPriority: 'high',
  },
  ImageGalleryView: {
    name: 'ImageGalleryView',
    estimatedSize: 35, // Medium - image gallery with filters and modals
    dependencies: ['BaseButton', 'BaseListbox', 'BaseModal', 'BaseTag'],
    loadPriority: 'medium',
  },
  ChapterOutlineView: {
    name: 'ChapterOutlineView',
    estimatedSize: 30, // Medium - chapter management with collapsible sections
    dependencies: ['BaseButton', 'BaseInput', 'BaseListbox', 'BaseTag'],
    loadPriority: 'high',
  },
  WritingStatsView: {
    name: 'WritingStatsView',
    estimatedSize: 40, // Medium - stats dashboard with charts placeholders
    dependencies: ['BaseButton', 'BaseInput', 'BaseDropdown'],
    loadPriority: 'medium',
  },
  DocumentEditorView: {
    name: 'DocumentEditorView',
    estimatedSize: 45, // Medium - inline document editor with AI assistant
    dependencies: ['BaseButton', 'BaseModal', 'BaseTextarea', 'BaseListbox'],
    loadPriority: 'high',
  },
}

/**
 * Preload enhanced components based on priority
 */
export function preloadEnhancedComponents(priorities: Array<'high' | 'medium' | 'low'> = ['high', 'medium']) {
  const componentsToPreload = Object.entries(enhancedComponentBundleMap)
    .filter(([_, info]) => priorities.includes(info.loadPriority))
    .map(([name]) => name)

  // Preload in idle time
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      componentsToPreload.forEach(async (componentName) => {
        try {
          await getEnhancedComponentLoader(componentName)()
        }
        catch (error) {
          console.warn(`Failed to preload ${componentName}:`, error)
        }
      })
    })
  }
}

/**
 * Get the appropriate enhanced component loader function
 */
function getEnhancedComponentLoader(componentName: string): () => Promise<Component> {
  switch (componentName) {
    case 'DataTable':
      return () => import('~/components/base/BaseTable.vue')
    case 'FormModal':
      return () => import('~/components/base/BaseFormModal.vue')
    case 'AnalyticsChart':
      return () => import('~/components/base/charts/BaseAnalyticsChart.vue')
    case 'KpiChart':
      return () => import('~/components/base/charts/BaseKpiChart.vue')
    // Book writing components
    case 'SuggestionBubbleView':
      return () => import('./views/SuggestionBubbleView.vue')
    case 'ImageGalleryView':
      return () => import('./views/ImageGalleryView.vue')
    case 'ChapterOutlineView':
      return () => import('./views/ChapterOutlineView.vue')
    case 'WritingStatsView':
      return () => import('./views/WritingStatsView.vue')
    case 'DocumentEditorView':
      return () => import('./views/DocumentEditorView.vue')
    default:
      throw new Error(`Unknown component: ${componentName}`)
  }
}
