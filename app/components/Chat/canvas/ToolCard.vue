<script setup lang="ts">
interface ToolCardProps {
  title: string
  status: 'success' | 'error' | 'warning' | 'info' | 'pending'
  summary?: string
  link?: string
  data?: Record<string, any> | string
  icon?: string
}

const props = withDefaults(defineProps<ToolCardProps>(), {
  summary: '',
  link: '',
  data: undefined,
  icon: 'lucide:wrench',
})

// Status-specific styling
const statusConfig = computed(() => {
  const configs = {
    success: {
      color: 'success',
      icon: 'lucide:check-circle',
      bgClass: 'bg-success-50 dark:bg-success-900/20',
      borderClass: 'border-success-200 dark:border-success-800',
    },
    error: {
      color: 'danger',
      icon: 'lucide:x-circle',
      bgClass: 'bg-danger-50 dark:bg-danger-900/20',
      borderClass: 'border-danger-200 dark:border-danger-800',
    },
    warning: {
      color: 'warning',
      icon: 'lucide:alert-triangle',
      bgClass: 'bg-warning-50 dark:bg-warning-900/20',
      borderClass: 'border-warning-200 dark:border-warning-800',
    },
    info: {
      color: 'info',
      icon: 'lucide:info',
      bgClass: 'bg-info-50 dark:bg-info-900/20',
      borderClass: 'border-info-200 dark:border-info-800',
    },
    pending: {
      color: 'muted',
      icon: 'lucide:clock',
      bgClass: 'bg-muted-50 dark:bg-muted-900/20',
      borderClass: 'border-muted-200 dark:border-muted-700',
    },
  }
  return configs[props.status] || configs.info
})

// Format data for display
const formattedData = computed(() => {
  if (!props.data)
    return ''
  if (typeof props.data === 'string')
    return props.data
  try {
    return JSON.stringify(props.data, null, 2)
  }
  catch {
    return String(props.data)
  }
})

// Truncate data for preview
const dataPreview = computed(() => {
  if (!formattedData.value)
    return ''
  const maxLength = 300
  return formattedData.value.length > maxLength
    ? `${formattedData.value.substring(0, maxLength)}...`
    : formattedData.value
})

const showFullData = ref(false)
</script>

<template>
  <div
    class="tool-card rounded-lg border p-4 space-y-3 transition-colors shadow-xl shadow-muted-300/30 dark:shadow-muted-900/40 hover:shadow-xl hover:shadow-muted-300/50 dark:hover:shadow-muted-900/60 transition-shadow"
    :class="[statusConfig.bgClass, statusConfig.borderClass]"
  >
    <!-- Header -->
    <div class="flex items-center gap-3">
      <Icon
        :name="statusConfig.icon"
        class="size-5 shrink-0"
        :class="`text-${statusConfig.color}-500`"
      />
      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-muted-900 dark:text-muted-100 truncate">
          {{ title }}
        </h3>
        <BaseBadge
          :color="statusConfig.color"
          size="xs"
          class="mt-1"
          data-test="status-badge"
        >
          {{ status }}
        </BaseBadge>
      </div>

      <!-- Link if provided -->
      <div v-if="link" class="shrink-0">
        <BaseButton
          :href="link"
          target="_blank"
          size="xs"
          variant="pastel"
          :color="statusConfig.color"
          data-test="link-button"
        >
          <Icon name="lucide:external-link" class="size-3" />
          View
        </BaseButton>
      </div>
    </div>

    <!-- Summary -->
    <div v-if="summary" class="text-sm text-muted-700 dark:text-muted-300" data-test="summary">
      {{ summary }}
    </div>

    <!-- Data Preview -->
    <div v-if="formattedData" class="space-y-2" data-test="data-preview">
      <div class="flex items-center justify-between">
        <span class="text-xs font-medium text-muted-600 dark:text-muted-400 uppercase tracking-wider">
          Data
        </span>
        <button
          v-if="formattedData.length > 300"
          class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
          data-test="expand-data"
          @click="showFullData = !showFullData"
        >
          {{ showFullData ? 'Show Less' : 'Show More' }}
        </button>
      </div>

      <div
        class="bg-muted-100 dark:bg-muted-800 rounded-md p-3 font-mono text-xs overflow-auto max-h-40"
        :class="{ 'max-h-none': showFullData }"
      >
        <pre class="whitespace-pre-wrap break-words text-muted-800 dark:text-muted-200">{{ showFullData ? formattedData : dataPreview }}</pre>
      </div>
    </div>

    <!-- Footer info if data exists -->
    <div v-if="formattedData" class="flex items-center justify-between pt-2 border-t border-muted-200 dark:border-muted-700">
      <span class="text-xs text-muted-500 dark:text-muted-400">
        Tool execution result
      </span>
      <BaseButton
        size="xs"
        variant="pastel"
        color="muted"
        class="gap-1"
        data-test="copy-button"
        @click="navigator.clipboard?.writeText(formattedData)"
      >
        <Icon name="lucide:copy" class="size-3" />
        Copy
      </BaseButton>
    </div>
  </div>
</template>

<style scoped>
/* Custom styles if needed */
</style>
