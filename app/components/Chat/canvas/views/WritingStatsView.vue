<script setup lang="ts">
// Writing Stats View - Placeholder implementation
interface Props {
  /** Writing statistics */
  stats?: {
    wordCount?: number
    pageCount?: number
    charactersCount?: number
    readingTime?: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  stats: () => ({
    wordCount: 0,
    pageCount: 0,
    charactersCount: 0,
    readingTime: 0
  })
})
</script>

<template>
  <div class="writing-stats-view p-4">
    <h3 class="text-lg font-medium text-muted-800 dark:text-muted-100 mb-4">
      Writing Statistics
    </h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="text-center p-4 bg-muted-50 dark:bg-muted-800 rounded-lg">
        <div class="text-2xl font-bold text-primary-500">{{ props.stats.wordCount }}</div>
        <div class="text-sm text-muted-600 dark:text-muted-400">Words</div>
      </div>
      <div class="text-center p-4 bg-muted-50 dark:bg-muted-800 rounded-lg">
        <div class="text-2xl font-bold text-primary-500">{{ props.stats.pageCount }}</div>
        <div class="text-sm text-muted-600 dark:text-muted-400">Pages</div>
      </div>
      <div class="text-center p-4 bg-muted-50 dark:bg-muted-800 rounded-lg">
        <div class="text-2xl font-bold text-primary-500">{{ props.stats.charactersCount }}</div>
        <div class="text-sm text-muted-600 dark:text-muted-400">Characters</div>
      </div>
      <div class="text-center p-4 bg-muted-50 dark:bg-muted-800 rounded-lg">
        <div class="text-2xl font-bold text-primary-500">{{ props.stats.readingTime }}</div>
        <div class="text-sm text-muted-600 dark:text-muted-400">Min Read</div>
      </div>
    </div>
  </div>
</template>
