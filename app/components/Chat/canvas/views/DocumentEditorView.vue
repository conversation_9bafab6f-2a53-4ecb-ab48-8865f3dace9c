<script setup lang="ts">
// Document Editor View - Placeholder implementation
interface Props {
  /** Document content */
  content?: string
  /** Document title */
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  title: 'Untitled Document'
})
</script>

<template>
  <div class="document-editor-view p-4">
    <div class="mb-4">
      <h2 class="text-xl font-semibold text-muted-800 dark:text-muted-100">
        {{ props.title }}
      </h2>
    </div>
    <div class="bg-white dark:bg-muted-900 border border-muted-200 dark:border-muted-800 rounded-lg p-4">
      <div class="prose prose-slate dark:prose-invert max-w-none">
        <div v-if="props.content" v-html="props.content" />
        <div v-else class="text-muted-500 italic">
          No content available
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.document-editor-view {
  min-height: 400px;
}
</style>
