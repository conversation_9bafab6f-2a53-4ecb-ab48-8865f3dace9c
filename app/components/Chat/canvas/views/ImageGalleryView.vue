<script setup lang="ts">
// Image Gallery View - Placeholder implementation
interface Props {
  /** Gallery images */
  images?: Array<{ id: string, url: string, caption?: string }>
}

const props = withDefaults(defineProps<Props>(), {
  images: () => []
})
</script>

<template>
  <div class="image-gallery-view p-4">
    <h3 class="text-lg font-medium text-muted-800 dark:text-muted-100 mb-4">
      Image Gallery
    </h3>
    <div v-if="props.images.length > 0" class="grid grid-cols-2 md:grid-cols-3 gap-4">
      <div 
        v-for="image in props.images" 
        :key="image.id"
        class="aspect-square rounded-lg overflow-hidden bg-muted-100 dark:bg-muted-800"
      >
        <img 
          :src="image.url" 
          :alt="image.caption || 'Gallery image'"
          class="w-full h-full object-cover"
        >
      </div>
    </div>
    <div v-else class="text-muted-500 italic">
      No images available
    </div>
  </div>
</template>
