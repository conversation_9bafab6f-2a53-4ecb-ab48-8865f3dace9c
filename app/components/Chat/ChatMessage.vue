<script setup lang="ts">
import type { ChatMessage } from '~/types/chat'
import ChatCanvas from './canvas/ChatCanvas.vue'
import EnhancedChatCanvas from './canvas/EnhancedChatCanvas.vue'

interface GraphState {
  currentAgent?: string
  nextAgent?: string
  turnCount?: number
  visitedNodes?: string[]
  activeTools?: string[]
  isActive?: boolean
  lastUpdate?: Date
}

interface Props {
  message: ChatMessage
  showAvatar?: boolean
  isLast?: boolean
  graphState?: GraphState
  showStatePanel?: boolean
  useEnhancedRendering?: boolean
  animate?: boolean
  compact?: boolean
  // Extended props for agent status handling
  agentAvatar?: string
  agentStatus?: 'ready' | 'error' | 'loading' | 'disabled'
  provider?: string
  model?: string
  showErrorBadge?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showAvatar: true,
  isLast: false,
  graphState: () => ({}),
  showStatePanel: false,
  useEnhancedRendering: false,
  animate: true,
  compact: false,
})

const emits = defineEmits<{
  'retry': [messageId: string]
  'delete': [messageId: string]
  'copy': [content: string]
  'toggle-state-panel': []
  'close-state-panel': []
  'react': [messageId: string, emoji: string]
}>()

const isUser = computed(() => props.message.role === 'user')
const isAssistant = computed(() => props.message.role === 'assistant')
const isStreaming = computed(() => props.message.isStreaming)

// Get agent color/avatar
const agentColor = computed(() => {
  if (!isAssistant.value)
    return 'primary'

  // Use agent-specific color or default based on agent ID
  const colors = ['primary', 'success', 'info', 'warning', 'danger', 'indigo', 'purple']
  const agentId = props.message.agentId || 'default'
  return colors[agentId.length % colors.length]
})

const agentInitials = computed(() => {
  if (!isAssistant.value)
    return ''

  const name = props.message.agentName || 'AI'
  return name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase()
})

// Format timestamp
const formattedTime = computed(() => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  }).format(props.message.createdAt)
})

// Tool call rendering
const hasToolCalls = computed(() =>
  props.message.toolCalls && props.message.toolCalls.length > 0,
)

// Provider/Model badge logic
const providerBadge = computed(() => {
  const provider = props.message.metadata?.provider
  if (!provider)
    return null

  // Provider display names and colors
  const providerConfig: Record<string, { name: string, color: string, icon?: string }> = {
    openai: { name: 'OpenAI', color: 'success', icon: 'simple-icons:openai' },
    gemini: { name: 'Gemini', color: 'info', icon: 'simple-icons:google' },
    anthropic: { name: 'Anthropic', color: 'warning', icon: 'simple-icons:anthropic' },
    grok: { name: 'Grok', color: 'primary' },
    ollama: { name: 'Ollama', color: 'muted' },
  }

  return providerConfig[provider.toLowerCase()] || { name: provider, color: 'muted' }
})

const modelBadge = computed(() => {
  const model = props.message.metadata?.model
  if (!model)
    return null

  // Simplify model names for display
  const modelName = model
    .replace(/^(gpt-|gemini-|claude-)/i, '')
    .replace(/(-\d+k|-\d+b|-latest|-preview)$/i, '')

  return {
    name: modelName,
    fullName: model,
  }
})

const hasBadges = computed(() => providerBadge.value || modelBadge.value)

// Agent status and error handling
const hasError = computed(() => {
  return props.message.metadata?.error || props.showErrorBadge || props.agentStatus === 'error'
})

const statusBadge = computed(() => {
  if (props.agentStatus === 'error' || props.message.metadata?.error) {
    return {
      color: 'danger',
      text: 'Error',
      icon: 'lucide:alert-circle',
    }
  }
  if (props.agentStatus === 'loading' || isStreaming.value) {
    return {
      color: 'info',
      text: 'Processing',
      icon: 'lucide:loader-2',
      animate: true,
    }
  }
  if (props.agentStatus === 'disabled') {
    return {
      color: 'muted',
      text: 'Disabled',
      icon: 'lucide:pause-circle',
    }
  }
  return null
})

// Enhanced avatar logic to show custom avatar if provided
const displayAvatar = computed(() => {
  if (isUser.value)
    return null
  return props.agentAvatar || props.message.metadata?.avatar || null
})

const avatarComponent = computed(() => {
  if (isUser.value) {
    return {
      icon: 'lucide:user',
      bgColor: 'bg-primary-500',
      textColor: 'text-white',
    }
  }

  if (displayAvatar.value && displayAvatar.value.startsWith('http')) {
    // URL avatar
    return {
      type: 'image',
      src: displayAvatar.value,
      bgColor: `bg-${agentColor.value}-500`,
    }
  }

  if (displayAvatar.value && /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u.test(displayAvatar.value)) {
    // Emoji avatar
    return {
      type: 'emoji',
      emoji: displayAvatar.value,
      bgColor: `bg-${agentColor.value}-100`,
    }
  }

  // Default to initials
  return {
    type: 'initials',
    initials: agentInitials.value,
    bgColor: `bg-${agentColor.value}-500`,
    textColor: 'text-white',
  }
})
</script>

<template>
  <div
    class="group/message relative mb-4 flex gap-3"
    :class="[
      isUser ? 'flex-row-reverse' : 'flex-row',
      isLast ? 'mb-6' : 'mb-4',
    ]"
  >
    <!-- Avatar -->
    <div
      v-if="showAvatar"
      class="flex shrink-0 relative"
    >
      <!-- User Avatar -->
      <div
        v-if="isUser"
        class="flex size-8 items-center justify-center rounded-full bg-primary-500"
      >
        <Icon name="lucide:user" class="size-4 text-white" />
      </div>

      <!-- Agent Avatar -->
      <div
        v-else
        class="relative"
      >
        <!-- Image Avatar -->
        <div
          v-if="avatarComponent.type === 'image'"
          class="flex size-8 items-center justify-center rounded-full overflow-hidden"
          :class="avatarComponent.bgColor"
        >
          <img
            :src="avatarComponent.src"
            :alt="message.agentName || 'Agent'"
            class="size-full object-cover"
            @error="() => { /* fallback to initials */ }"
          >
        </div>

        <!-- Emoji Avatar -->
        <div
          v-else-if="avatarComponent.type === 'emoji'"
          class="flex size-8 items-center justify-center rounded-full"
          :class="avatarComponent.bgColor"
        >
          <span class="text-sm">{{ avatarComponent.emoji }}</span>
        </div>

        <!-- Default Initials Avatar -->
        <div
          v-else
          class="flex size-8 items-center justify-center rounded-full"
          :class="[avatarComponent.bgColor, avatarComponent.textColor]"
        >
          <span class="text-xs font-semibold">
            {{ avatarComponent.initials }}
          </span>
        </div>

        <!-- Status Indicator -->
        <div
          v-if="statusBadge && !isUser"
          class="absolute -bottom-0.5 -right-0.5 flex size-3 items-center justify-center rounded-full"
          :class="[
            statusBadge.color === 'danger' ? 'bg-red-500' : '',
            statusBadge.color === 'info' ? 'bg-blue-500' : '',
            statusBadge.color === 'muted' ? 'bg-gray-400' : '',
          ]"
        >
          <Icon
            :name="statusBadge.icon"
            class="size-2 text-white" :class="[
              statusBadge.animate ? 'animate-spin' : '',
            ]"
          />
        </div>
      </div>
    </div>

    <!-- Message Content -->
    <div
      class="max-w-[80%] flex-1"
      :class="isUser ? 'flex justify-end' : 'flex justify-start'"
    >
      <div
        class="relative rounded-2xl px-4 py-3 shadow-sm"
        :class="[
          isUser
            ? 'bg-primary-500 text-white'
            : 'bg-muted-100 dark:bg-muted-800 border border-muted-200 dark:border-muted-700',
        ]"
      >
        <!-- Agent name and badges for assistant messages -->
        <div
          v-if="isAssistant && (message.agentName || hasBadges)"
          class="mb-2 flex flex-wrap items-center gap-1.5"
        >
          <!-- Agent name -->
          <div
            v-if="message.agentName"
            class="text-xs font-semibold opacity-70"
          >
            {{ message.agentName }}
          </div>

          <!-- Provider badge -->
          <BaseBadge
            v-if="providerBadge"
            size="xs"
            :color="providerBadge.color"
            class="text-xs"
          >
            <Icon
              v-if="providerBadge.icon"
              :name="providerBadge.icon"
              class="size-3 mr-1"
            />
            {{ providerBadge.name }}
          </BaseBadge>

          <!-- Model badge -->
          <BaseBadge
            v-if="modelBadge"
            size="xs"
            color="muted"
            variant="outline"
            class="text-xs"
            :title="modelBadge.fullName"
          >
            {{ modelBadge.name }}
          </BaseBadge>

          <!-- Status badge -->
          <BaseBadge
            v-if="statusBadge"
            size="xs"
            :color="statusBadge.color"
            class="text-xs"
          >
            <Icon
              :name="statusBadge.icon"
              class="size-3 mr-1" :class="[
                statusBadge.animate ? 'animate-spin' : '',
              ]"
            />
            {{ statusBadge.text }}
          </BaseBadge>
        </div>

        <!-- Message content via ChatCanvas -->
        <div
          class="prose prose-sm max-w-none"
          :class="[
            isUser
              ? 'prose-invert text-white'
              : 'text-muted-900 dark:text-muted-100',
          ]"
        >
          <!-- Use Enhanced ChatCanvas when enabled -->
          <EnhancedChatCanvas
            v-if="useEnhancedRendering"
            :message="message"
            :use-enhanced-bubble="false"
            :animate="animate"
            :compact="compact"
            :show-fallback-as-plain-text="true"
            :graph-state="graphState"
            :show-state-panel="showStatePanel"
            @toggle-state-panel="$emit('toggle-state-panel')"
            @close-state-panel="$emit('close-state-panel')"
            @copy="$emit('copy', $event)"
            @retry="$emit('retry', $event)"
            @delete="$emit('delete', $event)"
            @react="$emit('react', $event)"
          />

          <!-- Use regular ChatCanvas as fallback -->
          <ChatCanvas
            v-else
            :message="message"
            :show-fallback-as-plain-text="true"
            :graph-state="graphState"
            :show-state-panel="showStatePanel"
            @toggle-state-panel="$emit('toggle-state-panel')"
            @close-state-panel="$emit('close-state-panel')"
          />

          <!-- Streaming indicator -->
          <div
            v-if="isStreaming"
            class="mt-1 flex items-center gap-1"
          >
            <div class="flex gap-1">
              <div class="size-1 rounded-full bg-current opacity-60 animate-pulse" />
              <div class="size-1 rounded-full bg-current opacity-40 animate-pulse" style="animation-delay: 0.2s" />
              <div class="size-1 rounded-full bg-current opacity-20 animate-pulse" style="animation-delay: 0.4s" />
            </div>
          </div>
        </div>

        <!-- Tool calls -->
        <div
          v-if="hasToolCalls"
          class="mt-3 space-y-2"
        >
          <div
            v-for="tool in message.toolCalls"
            :key="tool.id"
            class="rounded-lg border p-2"
            :class="[
              isUser
                ? 'border-white/20 bg-white/10'
                : 'border-muted-300 dark:border-muted-600 bg-muted-50 dark:bg-muted-900',
            ]"
          >
            <div class="flex items-center gap-2">
              <Icon name="lucide:wrench" class="size-3" />
              <span class="text-xs font-medium">{{ tool.name }}</span>
            </div>
            <div
              v-if="tool.result"
              class="mt-1 text-xs opacity-75"
            >
              Result: {{ typeof tool.result === 'string' ? tool.result : JSON.stringify(tool.result, null, 2).substring(0, 100) }}
            </div>
          </div>
        </div>

        <!-- Attachments -->
        <div
          v-if="message.attachments && message.attachments.length > 0"
          class="mt-3 flex flex-wrap gap-2"
        >
          <div
            v-for="attachment in message.attachments"
            :key="attachment.url"
            class="flex items-center gap-2 rounded-lg border px-2 py-1"
            :class="[
              isUser
                ? 'border-white/20 bg-white/10'
                : 'border-muted-300 dark:border-muted-600 bg-muted-50 dark:bg-muted-900',
            ]"
          >
            <Icon
              :name="attachment.type === 'image' ? 'lucide:image' : 'lucide:file'"
              class="size-3"
            />
            <span class="text-xs">{{ attachment.name || 'Attachment' }}</span>
          </div>
        </div>

        <!-- Message actions -->
        <div
          class="absolute -bottom-8 left-0 flex items-center gap-1 opacity-0 transition-opacity group-hover/message:opacity-100"
          :class="isUser ? '-left-8' : 'left-0'"
        >
          <BaseTooltip content="Copy message">
            <button
              type="button"
              class="flex size-6 items-center justify-center rounded-md bg-muted-100 hover:bg-muted-200 dark:bg-muted-800 dark:hover:bg-muted-700 transition-colors"
              @click="$emit('copy', typeof message.content === 'string' ? message.content : JSON.stringify(message.content, null, 2))"
            >
              <Icon name="lucide:copy" class="size-3" />
            </button>
          </BaseTooltip>

          <BaseTooltip
            v-if="isAssistant"
            content="Retry message"
          >
            <button
              type="button"
              class="flex size-6 items-center justify-center rounded-md bg-muted-100 hover:bg-muted-200 dark:bg-muted-800 dark:hover:bg-muted-700 transition-colors"
              @click="$emit('retry', message.id)"
            >
              <Icon name="lucide:refresh-cw" class="size-3" />
            </button>
          </BaseTooltip>

          <BaseTooltip content="Delete message">
            <button
              type="button"
              class="flex size-6 items-center justify-center rounded-md bg-red-100 hover:bg-red-200 dark:bg-red-900/50 dark:hover:bg-red-900 transition-colors"
              @click="$emit('delete', message.id)"
            >
              <Icon name="lucide:trash-2" class="size-3 text-red-600 dark:text-red-400" />
            </button>
          </BaseTooltip>
        </div>

        <!-- Timestamp -->
        <div
          class="mt-2 text-xs opacity-50"
        >
          {{ formattedTime }}
          <span v-if="message.metadata?.tokens" class="ml-1">
            ({{ message.metadata.tokens }} tokens)
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
