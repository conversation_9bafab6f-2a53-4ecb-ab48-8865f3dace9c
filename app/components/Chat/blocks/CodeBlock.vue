<script setup lang="ts">
interface Props {
  code: string
  language?: string
  filename?: string
  showLineNumbers?: boolean
  highlightLines?: number[]
  maxHeight?: string
  copyable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  language: 'text',
  filename: '',
  showLineNumbers: true,
  highlightLines: () => [],
  maxHeight: '400px',
  copyable: true,
})

const { copy, copied, isSupported } = useClipboard({ source: toRef(props, 'code') })

// Language display names and icons
const languageConfig: Record<string, { name: string, icon?: string, color?: string }> = {
  javascript: { name: 'JavaScript', icon: 'logos:javascript', color: 'yellow' },
  typescript: { name: 'TypeScript', icon: 'logos:typescript-icon', color: 'blue' },
  vue: { name: 'Vue', icon: 'logos:vue', color: 'green' },
  python: { name: 'Python', icon: 'logos:python', color: 'blue' },
  bash: { name: 'Bash', icon: 'logos:bash-icon', color: 'muted' },
  shell: { name: 'Shell', icon: 'lucide:terminal', color: 'muted' },
  json: { name: 'JSON', icon: 'logos:json', color: 'yellow' },
  yaml: { name: 'YAML', icon: 'lucide:file-text', color: 'purple' },
  html: { name: 'HTML', icon: 'logos:html-5', color: 'orange' },
  css: { name: 'CSS', icon: 'logos:css-3', color: 'blue' },
  sql: { name: 'SQL', icon: 'lucide:database', color: 'info' },
  go: { name: 'Go', icon: 'logos:go', color: 'blue' },
  rust: { name: 'Rust', icon: 'logos:rust', color: 'orange' },
  java: { name: 'Java', icon: 'logos:java', color: 'red' },
  php: { name: 'PHP', icon: 'logos:php', color: 'purple' },
  ruby: { name: 'Ruby', icon: 'logos:ruby', color: 'red' },
  swift: { name: 'Swift', icon: 'logos:swift', color: 'orange' },
  kotlin: { name: 'Kotlin', icon: 'logos:kotlin-icon', color: 'purple' },
  dart: { name: 'Dart', icon: 'logos:dart', color: 'blue' },
  text: { name: 'Plain Text', icon: 'lucide:file-text', color: 'muted' },
}

const languageInfo = computed(() => {
  const info = languageConfig[props.language.toLowerCase()]
  return info || { name: props.language.charAt(0).toUpperCase() + props.language.slice(1), icon: 'lucide:code', color: 'muted' }
})

// Count lines for display
const lineCount = computed(() => {
  return props.code.split('\n').length
})

// Format code for display (preserve original whitespace)
const formattedCode = computed(() => {
  return props.code.replace(/\t/g, '  ') // Convert tabs to spaces for consistent display
})

// Copy functionality
async function handleCopy() {
  if (isSupported.value) {
    await copy(props.code)
  }
}

// Handle line highlighting
function getLineClass(lineNumber: number) {
  if (props.highlightLines.includes(lineNumber)) {
    return 'bg-warning-100 dark:bg-warning-900/20'
  }
  return ''
}

// Determine if code is likely long/complex enough to warrant scrolling
const isLongCode = computed(() => {
  return lineCount.value > 20 || props.code.length > 1000
})

// Component size indicator
const sizeIndicator = computed(() => {
  if (lineCount.value < 10)
    return { label: 'Small', color: 'success' }
  if (lineCount.value < 50)
    return { label: 'Medium', color: 'warning' }
  return { label: 'Large', color: 'danger' }
})
</script>

<template>
  <div
    class="code-block border border-muted-200 dark:border-muted-700 rounded-lg overflow-hidden bg-muted-50 dark:bg-muted-900"
    data-testid="code-block"
  >
    <!-- Header -->
    <div class="flex items-center justify-between px-4 py-2 border-b border-muted-200 dark:border-muted-700 bg-muted-100 dark:bg-muted-800">
      <div class="flex items-center gap-2">
        <!-- Language icon and name -->
        <div class="flex items-center gap-1.5">
          <Icon
            :name="languageInfo.icon"
            class="size-4"
            :class="`text-${languageInfo.color}-500`"
          />
          <span class="text-sm font-medium text-muted-700 dark:text-muted-300">
            {{ languageInfo.name }}
          </span>
        </div>

        <!-- Filename if provided -->
        <div v-if="filename" class="flex items-center gap-1">
          <span class="text-muted-400">•</span>
          <Icon name="lucide:file" class="size-3 text-muted-500" />
          <span class="text-sm text-muted-600 dark:text-muted-400 font-mono">
            {{ filename }}
          </span>
        </div>

        <!-- Line count and size indicator -->
        <div class="flex items-center gap-2 ml-2">
          <BaseBadge
            size="xs"
            :color="sizeIndicator.color"
            variant="outline"
            class="text-xs"
          >
            {{ sizeIndicator.label }}
          </BaseBadge>
          <span class="text-xs text-muted-500">
            {{ lineCount }} {{ lineCount === 1 ? 'line' : 'lines' }}
          </span>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center gap-1">
        <!-- Copy button -->
        <BaseTooltip v-if="copyable && isSupported" :content="copied ? 'Copied!' : 'Copy code'">
          <button
            class="p-1.5 rounded-md hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
            :class="copied ? 'text-success-600' : 'text-muted-600 dark:text-muted-400'"
            data-testid="copy-button"
            @click="handleCopy"
          >
            <Icon
              :name="copied ? 'lucide:check' : 'lucide:copy'"
              class="size-3.5"
            />
          </button>
        </BaseTooltip>

        <!-- Expand/collapse for long code -->
        <BaseTooltip v-if="isLongCode" content="Toggle full height">
          <button
            class="p-1.5 rounded-md hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-muted-600 dark:text-muted-400"
            data-testid="expand-button"
          >
            <Icon name="lucide:expand" class="size-3.5" />
          </button>
        </BaseTooltip>
      </div>
    </div>

    <!-- Code content -->
    <div
      class="relative overflow-auto font-mono text-sm"
      :style="{ maxHeight }"
      data-testid="code-content"
    >
      <!-- Background for highlighting -->
      <div class="absolute inset-0 pointer-events-none">
        <div
          v-for="(line, index) in formattedCode.split('\n')"
          :key="index"
          class="h-5 leading-5" :class="[
            showLineNumbers ? 'pl-12' : 'pl-4',
            getLineClass(index + 1),
          ]"
        />
      </div>

      <!-- Line numbers (if enabled) -->
      <div
        v-if="showLineNumbers"
        class="absolute left-0 top-0 bottom-0 w-10 bg-muted-100/50 dark:bg-muted-800/50 border-r border-muted-200 dark:border-muted-700 flex flex-col text-muted-500 dark:text-muted-400 text-xs text-right"
        data-testid="line-numbers"
      >
        <div
          v-for="n in lineCount"
          :key="n"
          class="h-5 leading-5 pr-2 flex-shrink-0"
          :class="highlightLines.includes(n) ? 'bg-warning-200 dark:bg-warning-800' : ''"
        >
          {{ n }}
        </div>
      </div>

      <!-- Code text -->
      <pre
        class="relative z-10 p-4 m-0 bg-transparent text-muted-900 dark:text-muted-100 whitespace-pre overflow-visible"
        :class="showLineNumbers ? 'pl-12' : 'pl-4'"
      ><code data-testid="code-text">{{ formattedCode }}</code></pre>
    </div>

    <!-- Footer with additional info -->
    <div
      v-if="props.code.length > 500 || highlightLines.length > 0"
      class="px-4 py-2 border-t border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800"
    >
      <div class="flex items-center justify-between text-xs text-muted-500 dark:text-muted-400">
        <div class="flex items-center gap-3">
          <span>{{ props.code.length.toLocaleString() }} characters</span>
          <span v-if="highlightLines.length > 0">
            {{ highlightLines.length }} {{ highlightLines.length === 1 ? 'line' : 'lines' }} highlighted
          </span>
        </div>
        <div v-if="isLongCode" class="text-muted-400">
          Scroll to view more
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Ensure proper scrolling behavior */
.code-block pre {
  overflow-x: auto;
  overflow-y: visible;
}

/* Custom scrollbar for code content - using hex colors for compatibility */
.code-block ::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.code-block ::-webkit-scrollbar-track {
  background: transparent;
}

.code-block ::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 4px;
}

.code-block ::-webkit-scrollbar-thumb:hover {
  background: #d1d5db;
}

/* Dark mode scrollbar */
.dark .code-block ::-webkit-scrollbar-thumb {
  background: #374151;
}

.dark .code-block ::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* Preserve whitespace and prevent text wrapping */
.code-block code {
  white-space: pre;
  word-break: normal;
  word-wrap: normal;
}
</style>
