<script setup lang="ts">
interface ChartDataPoint {
  label: string
  value: number
  color?: string
}

interface Props {
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter'
  title?: string
  data: ChartDataPoint[]
  xLabel?: string
  yLabel?: string
  showLegend?: boolean
  height?: number
  colors?: string[]
  animate?: boolean
  interactive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'bar',
  title: '',
  xLabel: '',
  yLabel: '',
  showLegend: true,
  height: 300,
  colors: () => ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'],
  animate: true,
  interactive: true,
})

// Chart type configurations
const chartTypeConfig = {
  line: { name: 'Line Chart', icon: 'lucide:trending-up', description: 'Show trends over time' },
  bar: { name: 'Bar Chart', icon: 'lucide:bar-chart-3', description: 'Compare values across categories' },
  pie: { name: 'Pie Chart', icon: 'lucide:pie-chart', description: 'Show parts of a whole' },
  area: { name: 'Area Chart', icon: 'lucide:area-chart', description: 'Show cumulative data over time' },
  scatter: { name: 'Scatter Plot', icon: 'lucide:scatter-chart', description: 'Show correlation between variables' },
}

const typeInfo = computed(() => chartTypeConfig[props.type])

// Data statistics
const dataStats = computed(() => {
  if (!props.data || props.data.length === 0)
    return null

  const values = props.data.map(d => d.value)
  const total = values.reduce((sum, val) => sum + val, 0)
  const max = Math.max(...values)
  const min = Math.min(...values)
  const avg = total / values.length

  return { total, max, min, avg, count: values.length }
})

// Generate chart placeholder visualization (simple SVG bars for demo)
const chartHeight = computed(() => props.height - 40) // Account for padding

const normalizedData = computed(() => {
  if (!props.data || props.data.length === 0)
    return []

  const max = Math.max(...props.data.map(d => d.value))
  return props.data.map((item, index) => ({
    ...item,
    normalizedValue: (item.value / max) * 0.8, // 80% of chart height
    color: item.color || props.colors[index % props.colors.length],
    x: (index / (props.data.length - 1)) * 100, // Percentage position
  }))
})

// Format number for display
function formatNumber(num: number): string {
  if (num >= 1e9)
    return `${(num / 1e9).toFixed(1)}B`
  if (num >= 1e6)
    return `${(num / 1e6).toFixed(1)}M`
  if (num >= 1e3)
    return `${(num / 1e3).toFixed(1)}K`
  return num.toLocaleString()
}
</script>

<template>
  <div
    class="chart-block border border-muted-200 dark:border-muted-700 rounded-lg overflow-hidden bg-white dark:bg-muted-900"
    data-testid="chart-block"
  >
    <!-- Header -->
    <div class="flex items-center justify-between px-4 py-3 border-b border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800">
      <div class="flex items-center gap-2">
        <Icon
          :name="typeInfo.icon"
          class="size-5 text-primary-600 dark:text-primary-400"
        />
        <div>
          <h3 v-if="title" class="font-medium text-muted-900 dark:text-muted-100">
            {{ title }}
          </h3>
          <div class="flex items-center gap-2 text-sm text-muted-600 dark:text-muted-400">
            <span>{{ typeInfo.name }}</span>
            <span v-if="dataStats" class="text-muted-400">•</span>
            <span v-if="dataStats">{{ dataStats.count }} data points</span>
          </div>
        </div>
      </div>

      <!-- Chart actions -->
      <div class="flex items-center gap-1">
        <BaseTooltip content="This is a chart placeholder - full charting library integration needed">
          <button class="p-1.5 rounded-md hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-muted-500">
            <Icon name="lucide:info" class="size-4" />
          </button>
        </BaseTooltip>
      </div>
    </div>

    <!-- Chart content -->
    <div class="p-4">
      <div
        class="relative rounded-md bg-muted-50 dark:bg-muted-800/50 p-4"
        :style="{ height: `${height}px` }"
        data-testid="chart-content"
      >
        <!-- Chart placeholder - Replace with actual charting library -->
        <div v-if="!data || data.length === 0" class="h-full flex items-center justify-center text-muted-400">
          <div class="text-center">
            <Icon :name="typeInfo.icon" class="size-12 mx-auto mb-2 opacity-50" />
            <p class="text-sm">
              No data to display
            </p>
          </div>
        </div>

        <!-- Simple placeholder visualization for demo -->
        <div v-else class="h-full flex items-end justify-center gap-2">
          <!-- Bar chart placeholder -->
          <div
            v-if="type === 'bar'"
            class="flex items-end gap-2 h-full w-full justify-center"
          >
            <div
              v-for="(item, index) in normalizedData"
              :key="index"
              class="flex-1 max-w-12 rounded-t transition-all duration-300 hover:opacity-80 cursor-pointer group relative"
              :style="{
                height: `${item.normalizedValue * 100}%`,
                backgroundColor: item.color,
                minHeight: '4px',
              }"
              :title="`${item.label}: ${formatNumber(item.value)}`"
            >
              <!-- Hover label -->
              <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity bg-muted-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                {{ formatNumber(item.value) }}
              </div>
            </div>
          </div>

          <!-- Pie chart placeholder -->
          <div
            v-else-if="type === 'pie'"
            class="flex items-center justify-center h-full"
          >
            <div class="relative w-40 h-40">
              <svg viewBox="0 0 100 100" class="w-full h-full transform -rotate-90">
                <circle
                  v-for="(item, index) in normalizedData.slice(0, 6)"
                  :key="index"
                  cx="50"
                  cy="50"
                  r="25"
                  fill="none"
                  :stroke="item.color"
                  stroke-width="20"
                  :stroke-dasharray="`${(item.value / (dataStats?.total || 1)) * 157} 157`"
                  :stroke-dashoffset="index * -15.7"
                  class="opacity-80 hover:opacity-100 transition-opacity"
                />
              </svg>
            </div>
          </div>

          <!-- Line/Area chart placeholder -->
          <div
            v-else-if="type === 'line' || type === 'area'"
            class="h-full w-full flex items-end"
          >
            <svg viewBox="0 0 300 150" class="w-full h-full">
              <polyline
                :points="normalizedData.map((item, i) => `${i * (300 / (normalizedData.length - 1))},${150 - (item.normalizedValue * 150)}`).join(' ')"
                fill="none"
                :stroke="colors[0]"
                stroke-width="2"
                class="drop-shadow-sm"
              />
              <polygon
                v-if="type === 'area'"
                :points="`${normalizedData.map((item, i) => `${i * (300 / (normalizedData.length - 1))},${150 - (item.normalizedValue * 150)}`).join(' ')} 300,150 0,150`"
                :fill="colors[0]"
                fill-opacity="0.3"
              />
              <!-- Data points -->
              <circle
                v-for="(item, index) in normalizedData"
                :key="index"
                :cx="index * (300 / (normalizedData.length - 1))"
                :cy="150 - (item.normalizedValue * 150)"
                r="3"
                :fill="colors[0]"
                class="hover:r-4 transition-all cursor-pointer"
                :title="`${item.label}: ${formatNumber(item.value)}`"
              />
            </svg>
          </div>

          <!-- Scatter plot placeholder -->
          <div
            v-else-if="type === 'scatter'"
            class="h-full w-full flex items-center justify-center"
          >
            <svg viewBox="0 0 200 120" class="w-full h-full">
              <circle
                v-for="(item, index) in normalizedData"
                :key="index"
                :cx="Math.random() * 180 + 10"
                :cy="Math.random() * 100 + 10"
                r="4"
                :fill="item.color"
                class="opacity-70 hover:opacity-100 transition-opacity cursor-pointer"
                :title="`${item.label}: ${formatNumber(item.value)}`"
              />
            </svg>
          </div>
        </div>

        <!-- Axis labels -->
        <div v-if="xLabel || yLabel" class="absolute inset-0 pointer-events-none">
          <div v-if="yLabel" class="absolute left-1 top-1/2 transform -translate-y-1/2 -rotate-90 text-xs text-muted-500">
            {{ yLabel }}
          </div>
          <div v-if="xLabel" class="absolute bottom-1 left-1/2 transform -translate-x-1/2 text-xs text-muted-500">
            {{ xLabel }}
          </div>
        </div>
      </div>

      <!-- Legend -->
      <div v-if="showLegend && data && data.length > 0" class="mt-4 flex flex-wrap gap-3">
        <div
          v-for="(item, index) in normalizedData"
          :key="index"
          class="flex items-center gap-2 text-sm"
        >
          <div
            class="w-3 h-3 rounded-sm"
            :style="{ backgroundColor: item.color }"
          />
          <span class="text-muted-700 dark:text-muted-300">{{ item.label }}</span>
          <span class="text-muted-500">{{ formatNumber(item.value) }}</span>
        </div>
      </div>
    </div>

    <!-- Statistics footer -->
    <div v-if="dataStats" class="px-4 py-3 border-t border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800">
      <div class="flex items-center justify-between text-xs text-muted-500 dark:text-muted-400">
        <div class="flex items-center gap-4">
          <span>Total: {{ formatNumber(dataStats.total) }}</span>
          <span>Avg: {{ formatNumber(dataStats.avg) }}</span>
          <span>Range: {{ formatNumber(dataStats.min) }} - {{ formatNumber(dataStats.max) }}</span>
        </div>
        <div class="text-muted-400">
          Placeholder visualization - charting library needed
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chart-block svg {
  overflow: visible;
}

/* Smooth transitions for interactive elements */
.chart-block circle,
.chart-block polygon,
.chart-block polyline {
  transition: all 0.2s ease;
}

.chart-block [data-testid='chart-content'] {
  /* Ensure proper aspect ratio */
  min-height: 200px;
}
</style>
