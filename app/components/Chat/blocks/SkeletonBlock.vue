<script setup lang="ts">
interface Props {
  type?: 'code' | 'chart' | 'table' | 'tool_card' | 'doc' | 'generic'
  height?: number
  width?: string
  showHeader?: boolean
  showFooter?: boolean
  lines?: number
  animate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'generic',
  height: 200,
  width: '100%',
  showHeader: true,
  showFooter: false,
  lines: 3,
  animate: true,
})

// Generate different skeleton patterns based on type
const skeletonConfig = computed(() => {
  switch (props.type) {
    case 'code':
      return {
        headerHeight: 40,
        contentPadding: 16,
        showLineNumbers: true,
        codeLines: props.lines || 8,
      }
    case 'chart':
      return {
        headerHeight: 48,
        contentPadding: 16,
        showLegend: true,
        chartHeight: props.height - 96, // Account for header and legend
      }
    case 'table':
      return {
        headerHeight: 56,
        contentPadding: 0,
        showTableHeader: true,
        tableRows: props.lines || 5,
        columns: 4,
      }
    case 'tool_card':
      return {
        headerHeight: 44,
        contentPadding: 16,
        showStatus: true,
        showData: true,
      }
    case 'doc':
      return {
        headerHeight: 40,
        contentPadding: 24,
        paragraphs: props.lines || 4,
        showTitle: true,
      }
    default:
      return {
        headerHeight: 40,
        contentPadding: 16,
        showGeneric: true,
      }
  }
})

// Calculate content height
const contentHeight = computed(() => {
  let base = props.height
  if (props.showHeader)
    base -= skeletonConfig.value.headerHeight
  if (props.showFooter)
    base -= 40
  return Math.max(base, 100)
})

// Generate random widths for skeleton lines (for more natural look)
function generateLineWidths(count: number) {
  const widths = []
  for (let i = 0; i < count; i++) {
    // Generate widths between 60% and 95% for variety
    const width = Math.floor(Math.random() * 35) + 60
    widths.push(`${width}%`)
  }
  return widths
}

const codeLineWidths = computed(() => generateLineWidths(skeletonConfig.value.codeLines || 8))
const paragraphWidths = computed(() => generateLineWidths(skeletonConfig.value.paragraphs || 4))

// Animation delay for staggered effect
function getAnimationDelay(index: number) {
  return props.animate ? `${index * 100}ms` : '0ms'
}
</script>

<template>
  <div
    class="skeleton-block border border-muted-200 dark:border-muted-700 rounded-lg overflow-hidden bg-white dark:bg-muted-900"
    :style="{ width, height: `${height}px` }"
    data-testid="skeleton-block"
  >
    <!-- Header skeleton -->
    <div
      v-if="showHeader"
      class="flex items-center justify-between px-4 py-3 border-b border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800"
      :style="{ height: `${skeletonConfig.headerHeight}px` }"
    >
      <div class="flex items-center gap-2">
        <!-- Icon placeholder -->
        <div
          class="skeleton-shimmer w-5 h-5 rounded"
          :style="{ animationDelay: getAnimationDelay(0) }"
        />

        <!-- Title placeholder -->
        <div
          class="skeleton-shimmer h-4 rounded"
          :class="type === 'tool_card' ? 'w-32' : 'w-24'"
          :style="{ animationDelay: getAnimationDelay(1) }"
        />

        <!-- Status badge for tool_card -->
        <div
          v-if="type === 'tool_card' && skeletonConfig.showStatus"
          class="skeleton-shimmer w-16 h-5 rounded-full"
          :style="{ animationDelay: getAnimationDelay(2) }"
        />

        <!-- Subtitle for charts and tables -->
        <template v-if="type === 'chart' || type === 'table'">
          <span class="text-muted-400">•</span>
          <div
            class="skeleton-shimmer h-3 w-20 rounded"
            :style="{ animationDelay: getAnimationDelay(2) }"
          />
        </template>
      </div>

      <!-- Header actions -->
      <div class="flex items-center gap-1">
        <div
          class="skeleton-shimmer w-6 h-6 rounded"
          :style="{ animationDelay: getAnimationDelay(3) }"
        />
        <div
          v-if="type === 'table'"
          class="skeleton-shimmer w-32 h-6 rounded"
          :style="{ animationDelay: getAnimationDelay(4) }"
        />
      </div>
    </div>

    <!-- Content skeleton -->
    <div
      class="relative"
      :style="{
        height: `${contentHeight}px`,
        padding: `${skeletonConfig.contentPadding}px`,
      }"
    >
      <!-- Code skeleton -->
      <div v-if="type === 'code'" class="h-full">
        <!-- Line numbers -->
        <div class="absolute left-0 top-0 w-10 h-full bg-muted-100/50 dark:bg-muted-800/50 border-r border-muted-200 dark:border-muted-700 p-2">
          <div
            v-for="i in skeletonConfig.codeLines"
            :key="`line-${i}`"
            class="skeleton-shimmer h-3 w-4 mb-2 rounded"
            :style="{ animationDelay: getAnimationDelay(i) }"
          />
        </div>

        <!-- Code lines -->
        <div class="ml-12 pt-2">
          <div
            v-for="(width, i) in codeLineWidths"
            :key="`code-${i}`"
            class="skeleton-shimmer h-3 mb-2 rounded"
            :style="{
              width,
              animationDelay: getAnimationDelay(i + 5),
              marginLeft: i % 3 === 1 ? '16px' : i % 4 === 2 ? '32px' : '0px', // Indent variety
            }"
          />
        </div>
      </div>

      <!-- Chart skeleton -->
      <div v-else-if="type === 'chart'" class="h-full flex flex-col">
        <!-- Chart area -->
        <div class="flex-1 bg-muted-50 dark:bg-muted-800/50 rounded-md p-4 mb-4">
          <div class="h-full flex items-end justify-center gap-2">
            <!-- Bar chart skeleton -->
            <div
              v-for="i in 6"
              :key="`bar-${i}`"
              class="skeleton-shimmer flex-1 max-w-12 rounded-t"
              :style="{
                height: `${Math.random() * 70 + 30}%`,
                animationDelay: getAnimationDelay(i + 2),
              }"
            />
          </div>
        </div>

        <!-- Legend -->
        <div v-if="skeletonConfig.showLegend" class="flex flex-wrap gap-3">
          <div
            v-for="i in 4"
            :key="`legend-${i}`"
            class="flex items-center gap-2"
            :style="{ animationDelay: getAnimationDelay(i + 8) }"
          >
            <div class="skeleton-shimmer w-3 h-3 rounded" />
            <div class="skeleton-shimmer h-3 w-16 rounded" />
          </div>
        </div>
      </div>

      <!-- Table skeleton -->
      <div v-else-if="type === 'table'" class="h-full">
        <!-- Table header -->
        <div class="flex border-b border-muted-200 dark:border-muted-700 pb-3 mb-3">
          <div
            v-for="i in skeletonConfig.columns"
            :key="`th-${i}`"
            class="flex-1 px-4"
          >
            <div
              class="skeleton-shimmer h-3 w-20 rounded"
              :style="{ animationDelay: getAnimationDelay(i) }"
            />
          </div>
        </div>

        <!-- Table rows -->
        <div class="space-y-3">
          <div
            v-for="row in skeletonConfig.tableRows"
            :key="`row-${row}`"
            class="flex"
          >
            <div
              v-for="col in skeletonConfig.columns"
              :key="`cell-${row}-${col}`"
              class="flex-1 px-4"
            >
              <div
                class="skeleton-shimmer h-3 rounded"
                :style="{
                  width: `${60 + Math.random() * 30}%`,
                  animationDelay: getAnimationDelay(row * skeletonConfig.columns + col),
                }"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Tool card skeleton -->
      <div v-else-if="type === 'tool_card'" class="h-full space-y-4">
        <!-- Summary -->
        <div class="space-y-2">
          <div
            class="skeleton-shimmer h-4 w-3/4 rounded"
            :style="{ animationDelay: getAnimationDelay(1) }"
          />
          <div
            class="skeleton-shimmer h-4 w-1/2 rounded"
            :style="{ animationDelay: getAnimationDelay(2) }"
          />
        </div>

        <!-- Data preview -->
        <div v-if="skeletonConfig.showData" class="space-y-2">
          <div
            class="skeleton-shimmer h-3 w-16 rounded"
            :style="{ animationDelay: getAnimationDelay(3) }"
          />
          <div
            v-for="i in 3"
            :key="`data-${i}`"
            class="skeleton-shimmer h-3 rounded"
            :style="{
              width: `${50 + Math.random() * 40}%`,
              animationDelay: getAnimationDelay(i + 4),
            }"
          />
        </div>
      </div>

      <!-- Doc skeleton -->
      <div v-else-if="type === 'doc'" class="h-full space-y-4">
        <!-- Title -->
        <div
          v-if="skeletonConfig.showTitle"
          class="skeleton-shimmer h-6 w-2/3 rounded"
          :style="{ animationDelay: getAnimationDelay(1) }"
        />

        <!-- Paragraphs -->
        <div class="space-y-3">
          <div
            v-for="(width, i) in paragraphWidths"
            :key="`para-${i}`"
            class="space-y-1"
          >
            <div
              class="skeleton-shimmer h-3 rounded"
              :style="{
                width,
                animationDelay: getAnimationDelay(i + 2),
              }"
            />
            <div
              v-if="i < paragraphWidths.length - 1"
              class="skeleton-shimmer h-3 w-4/5 rounded"
              :style="{ animationDelay: getAnimationDelay(i + paragraphWidths.length + 2) }"
            />
          </div>
        </div>
      </div>

      <!-- Generic skeleton -->
      <div v-else class="h-full space-y-3">
        <div
          v-for="i in lines"
          :key="`generic-${i}`"
          class="skeleton-shimmer h-4 rounded"
          :style="{
            width: `${60 + Math.random() * 35}%`,
            animationDelay: getAnimationDelay(i),
          }"
        />
      </div>
    </div>

    <!-- Footer skeleton -->
    <div
      v-if="showFooter"
      class="px-4 py-2 border-t border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800"
    >
      <div class="flex items-center justify-between">
        <div
          class="skeleton-shimmer h-3 w-32 rounded"
          :style="{ animationDelay: getAnimationDelay(20) }"
        />
        <div
          class="skeleton-shimmer h-3 w-24 rounded"
          :style="{ animationDelay: getAnimationDelay(21) }"
        />
      </div>
    </div>

    <!-- Loading overlay with subtle animation -->
    <div
      v-if="animate"
      class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent skeleton-shine"
    />
  </div>
</template>

<style scoped>
/* Skeleton shimmer animation */
.skeleton-shimmer {
  background-color: rgb(229 231 235); /* bg-gray-200 */
  position: relative;
  overflow: hidden;
}

.dark .skeleton-shimmer {
  background-color: rgb(68 64 60); /* dark:bg-gray-700 */
}

.skeleton-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 1.5s infinite;
}

.dark .skeleton-shimmer::after {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Loading shine effect */
.skeleton-shine {
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Disable animations when requested */
.skeleton-block:not([animate='true']) .skeleton-shimmer::after,
.skeleton-block:not([animate='true']) .skeleton-shine {
  animation: none;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .skeleton-block {
    min-height: 150px;
  }
}

/* Accessibility - respect prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  .skeleton-shimmer::after,
  .skeleton-shine {
    animation: none;
  }
}
</style>
