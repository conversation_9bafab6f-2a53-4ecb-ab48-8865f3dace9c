<script setup lang="ts">
interface TableColumn {
  key: string
  label: string
  type?: 'text' | 'number' | 'date' | 'boolean' | 'badge' | 'link'
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  format?: (value: any) => string
}

interface TableRow {
  id?: string | number
  [key: string]: any
}

interface Props {
  columns: TableColumn[]
  data: TableRow[]
  title?: string
  searchable?: boolean
  sortable?: boolean
  pagination?: boolean
  pageSize?: number
  maxHeight?: string
  striped?: boolean
  bordered?: boolean
  compact?: boolean
  exportable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  searchable: true,
  sortable: true,
  pagination: true,
  pageSize: 10,
  maxHeight: '500px',
  striped: true,
  bordered: true,
  compact: false,
  exportable: true,
})

// Reactive state
const searchQuery = ref('')
const sortColumn = ref<string>('')
const sortDirection = ref<'asc' | 'desc'>('asc')
const currentPage = ref(1)

// Computed properties
const hasData = computed(() => props.data && props.data.length > 0)

// Search functionality
const filteredData = computed(() => {
  if (!searchQuery.value || !hasData.value) {
    return props.data
  }

  const query = searchQuery.value.toLowerCase()
  return props.data.filter((row) => {
    return props.columns.some((column) => {
      const value = row[column.key]
      if (value == null)
        return false
      return String(value).toLowerCase().includes(query)
    })
  })
})

// Sort functionality
const sortedData = computed(() => {
  if (!sortColumn.value || !hasData.value) {
    return filteredData.value
  }

  const column = props.columns.find(col => col.key === sortColumn.value)
  if (!column?.sortable) {
    return filteredData.value
  }

  return [...filteredData.value].sort((a, b) => {
    const aValue = a[sortColumn.value]
    const bValue = b[sortColumn.value]

    // Handle null/undefined values
    if (aValue == null && bValue == null)
      return 0
    if (aValue == null)
      return 1
    if (bValue == null)
      return -1

    let comparison = 0

    // Type-specific sorting
    switch (column.type) {
      case 'number':
        comparison = Number(aValue) - Number(bValue)
        break
      case 'date':
        comparison = new Date(aValue).getTime() - new Date(bValue).getTime()
        break
      case 'boolean':
        comparison = Boolean(aValue) === Boolean(bValue) ? 0 : aValue ? -1 : 1
        break
      default:
        comparison = String(aValue).localeCompare(String(bValue))
    }

    return sortDirection.value === 'desc' ? -comparison : comparison
  })
})

// Pagination
const totalPages = computed(() => {
  if (!props.pagination)
    return 1
  return Math.ceil(sortedData.value.length / props.pageSize)
})

const paginatedData = computed(() => {
  if (!props.pagination)
    return sortedData.value

  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return sortedData.value.slice(start, end)
})

// Table actions
function handleSort(column: TableColumn) {
  if (!column.sortable)
    return

  if (sortColumn.value === column.key) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  }
  else {
    sortColumn.value = column.key
    sortDirection.value = 'asc'
  }
}

function handleSearch(query: string) {
  searchQuery.value = query
  currentPage.value = 1 // Reset to first page when searching
}

function goToPage(page: number) {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

// Format cell values
function formatCellValue(row: TableRow, column: TableColumn) {
  const value = row[column.key]

  if (value == null)
    return '-'

  if (column.format) {
    return column.format(value)
  }

  switch (column.type) {
    case 'number':
      return Number(value).toLocaleString()
    case 'date':
      return new Date(value).toLocaleDateString()
    case 'boolean':
      return value ? 'Yes' : 'No'
    default:
      return String(value)
  }
}

// Get column alignment class
function getColumnAlignClass(column: TableColumn) {
  switch (column.align || 'left') {
    case 'center': return 'text-center'
    case 'right': return 'text-right'
    default: return 'text-left'
  }
}

// Export data (placeholder)
function handleExport() {
  // Placeholder for CSV/Excel export functionality
  console.log('Export functionality would be implemented here')
}

// Statistics
const tableStats = computed(() => ({
  totalRows: props.data.length,
  filteredRows: filteredData.value.length,
  columns: props.columns.length,
  currentPageRows: paginatedData.value.length,
}))
</script>

<template>
  <div
    class="table-block border border-muted-200 dark:border-muted-700 rounded-lg overflow-hidden bg-white dark:bg-muted-900"
    data-testid="table-block"
  >
    <!-- Header with title and controls -->
    <div class="px-4 py-3 border-b border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800">
      <div class="flex items-center justify-between gap-4">
        <div class="flex items-center gap-2">
          <Icon name="lucide:table" class="size-5 text-primary-600 dark:text-primary-400" />
          <div>
            <h3 v-if="title" class="font-medium text-muted-900 dark:text-muted-100">
              {{ title }}
            </h3>
            <div class="flex items-center gap-2 text-sm text-muted-600 dark:text-muted-400">
              <span>{{ tableStats.columns }} columns</span>
              <span class="text-muted-400">•</span>
              <span>{{ tableStats.totalRows.toLocaleString() }} rows</span>
              <span v-if="searchQuery && tableStats.filteredRows !== tableStats.totalRows" class="text-muted-400">
                ({{ tableStats.filteredRows.toLocaleString() }} filtered)
              </span>
            </div>
          </div>
        </div>

        <!-- Controls -->
        <div class="flex items-center gap-2">
          <!-- Search -->
          <div v-if="searchable" class="relative">
            <Icon name="lucide:search" class="absolute left-2 top-1/2 transform -translate-y-1/2 size-3 text-muted-400" />
            <input
              :value="searchQuery"
              placeholder="Search table..."
              class="pl-7 pr-3 py-1 text-sm border border-muted-300 dark:border-muted-600 rounded bg-white dark:bg-muted-800 text-muted-900 dark:text-muted-100 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 w-48"
              data-testid="search-input"
              @input="handleSearch(($event.target as HTMLInputElement).value)"
            >
          </div>

          <!-- Export button -->
          <BaseTooltip v-if="exportable" content="Export table data (feature placeholder)">
            <button
              class="p-1.5 rounded-md hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors text-muted-600 dark:text-muted-400"
              data-testid="export-button"
              @click="handleExport"
            >
              <Icon name="lucide:download" class="size-4" />
            </button>
          </BaseTooltip>
        </div>
      </div>
    </div>

    <!-- Table container with horizontal scroll -->
    <div
      class="overflow-auto"
      :style="{ maxHeight }"
      data-testid="table-container"
    >
      <table
        class="w-full min-w-full"
        :class="{
          'table-auto': !compact,
          'table-fixed': compact,
        }"
      >
        <!-- Table header -->
        <thead class="bg-muted-100 dark:bg-muted-800 sticky top-0 z-10">
          <tr :class="bordered ? 'border-b border-muted-200 dark:border-muted-700' : ''">
            <th
              v-for="column in columns"
              :key="column.key"
              :style="column.width ? { width: column.width } : {}"
              class="px-4 py-3 text-xs font-medium text-muted-700 dark:text-muted-300 uppercase tracking-wider"
              :class="[
                getColumnAlignClass(column),
                column.sortable && hasData ? 'cursor-pointer hover:bg-muted-200 dark:hover:bg-muted-700 select-none' : '',
              ]"
              data-testid="table-header"
              @click="column.sortable && hasData ? handleSort(column) : undefined"
            >
              <div class="flex items-center gap-1">
                <span>{{ column.label }}</span>
                <div
                  v-if="column.sortable && hasData"
                  class="flex flex-col"
                >
                  <Icon
                    name="lucide:chevron-up"
                    class="size-3"
                    :class="{
                      'text-primary-600': sortColumn === column.key && sortDirection === 'asc',
                      'text-muted-400': sortColumn !== column.key || sortDirection !== 'asc',
                    }"
                  />
                  <Icon
                    name="lucide:chevron-down"
                    class="size-3 -mt-1"
                    :class="{
                      'text-primary-600': sortColumn === column.key && sortDirection === 'desc',
                      'text-muted-400': sortColumn !== column.key || sortDirection !== 'desc',
                    }"
                  />
                </div>
              </div>
            </th>
          </tr>
        </thead>

        <!-- Table body -->
        <tbody v-if="hasData && paginatedData.length > 0">
          <tr
            v-for="(row, rowIndex) in paginatedData"
            :key="row.id || rowIndex"
            class="hover:bg-muted-50 dark:hover:bg-muted-800/50 transition-colors"
            :class="{
              'bg-muted-25 dark:bg-muted-900/25': striped && rowIndex % 2 === 1,
              'border-b border-muted-100 dark:border-muted-800': bordered,
            }"
            data-testid="table-row"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              class="px-4 py-3 text-sm text-muted-900 dark:text-muted-100"
              :class="[
                getColumnAlignClass(column),
                compact ? 'py-2' : 'py-3',
              ]"
              data-testid="table-cell"
            >
              <!-- Special cell types -->
              <template v-if="column.type === 'badge'">
                <BaseBadge
                  size="xs"
                  :color="row[`${column.key}_color`] || 'muted'"
                  :variant="row[`${column.key}_variant`] || 'solid'"
                >
                  {{ formatCellValue(row, column) }}
                </BaseBadge>
              </template>

              <template v-else-if="column.type === 'link'">
                <a
                  :href="row[`${column.key}_href`] || '#'"
                  class="text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {{ formatCellValue(row, column) }}
                </a>
              </template>

              <template v-else-if="column.type === 'boolean'">
                <Icon
                  :name="row[column.key] ? 'lucide:check' : 'lucide:x'"
                  class="size-4"
                  :class="row[column.key] ? 'text-success-600' : 'text-danger-600'"
                />
              </template>

              <!-- Default text content -->
              <template v-else>
                <span class="break-words">
                  {{ formatCellValue(row, column) }}
                </span>
              </template>
            </td>
          </tr>
        </tbody>

        <!-- Empty state -->
        <tbody v-else>
          <tr>
            <td :colspan="columns.length" class="px-4 py-8 text-center text-muted-500">
              <div class="flex flex-col items-center gap-2">
                <Icon name="lucide:table" class="size-8 opacity-50" />
                <p>{{ searchQuery ? 'No matching results found' : 'No data available' }}</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Footer with pagination -->
    <div
      v-if="pagination && totalPages > 1"
      class="px-4 py-3 border-t border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800"
    >
      <div class="flex items-center justify-between">
        <div class="text-sm text-muted-600 dark:text-muted-400">
          Showing {{ ((currentPage - 1) * pageSize) + 1 }} to {{ Math.min(currentPage * pageSize, tableStats.filteredRows) }} of {{ tableStats.filteredRows.toLocaleString() }} results
        </div>

        <!-- Pagination controls -->
        <div class="flex items-center gap-2">
          <button
            :disabled="currentPage === 1"
            class="p-1 rounded hover:bg-muted-200 dark:hover:bg-muted-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            data-testid="prev-page"
            @click="goToPage(currentPage - 1)"
          >
            <Icon name="lucide:chevron-left" class="size-4" />
          </button>

          <!-- Page numbers -->
          <div class="flex items-center gap-1">
            <button
              v-for="page in Math.min(5, totalPages)"
              :key="page"
              class="px-2 py-1 rounded text-sm transition-colors"
              :class="page === currentPage
                ? 'bg-primary-600 text-white'
                : 'hover:bg-muted-200 dark:hover:bg-muted-700 text-muted-700 dark:text-muted-300'"
              data-testid="page-number"
              @click="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>

          <button
            :disabled="currentPage === totalPages"
            class="p-1 rounded hover:bg-muted-200 dark:hover:bg-muted-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            data-testid="next-page"
            @click="goToPage(currentPage + 1)"
          >
            <Icon name="lucide:chevron-right" class="size-4" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Custom scrollbar for table container */
.table-block [data-testid='table-container']::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-block [data-testid='table-container']::-webkit-scrollbar-track {
  background: transparent;
}

.table-block [data-testid='table-container']::-webkit-scrollbar-thumb {
  background-color: #e5e7eb; /* light: gray-200 approximation */
  border-radius: 0.25rem;
}

/* Dark mode override */
.dark .table-block [data-testid='table-container']::-webkit-scrollbar-thumb {
  background-color: #374151; /* dark: gray-700 approximation */
}

.table-block [data-testid='table-container']::-webkit-scrollbar-thumb:hover {
  background-color: #cbd5e1; /* light hover: gray-300 approximation */
}

.dark .table-block [data-testid='table-container']::-webkit-scrollbar-thumb:hover {
  background-color: #4b5563; /* dark hover: gray-600 approximation */
}

/* Ensure table header stays fixed during scroll */
.table-block thead th {
  position: sticky;
  top: 0;
  background: inherit;
  z-index: 10;
}

/* Prevent text selection on sortable headers */
.table-block th.cursor-pointer {
  user-select: none;
}
</style>
