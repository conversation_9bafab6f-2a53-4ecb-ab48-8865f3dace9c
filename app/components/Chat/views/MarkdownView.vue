<script setup lang="ts">
import hljs from 'highlight.js'
import { marked } from 'marked'
import { markedHighlight } from 'marked-highlight'

interface Props {
  content: string
  enableSyntaxHighlighting?: boolean
  enableTables?: boolean
  enableLists?: boolean
  enableLinks?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  enableSyntaxHighlighting: true,
  enableTables: true,
  enableLists: true,
  enableLinks: true,
})

// Configure marked with highlighting
marked.use(markedHighlight({
  langPrefix: 'hljs language-',
  highlight(code, lang) {
    if (!props.enableSyntaxHighlighting)
      return code

    const language = hljs.getLanguage(lang) ? lang : 'plaintext'
    return hljs.highlight(code, { language }).value
  },
}))

// Configure marked options
marked.setOptions({
  breaks: true,
  gfm: true, // GitHub Flavored Markdown
})

// Render markdown content
const renderedContent = computed(() => {
  try {
    return marked.parse(props.content)
  }
  catch (error) {
    console.error('Markdown parsing error:', error)
    return `<p>Error rendering markdown content</p>`
  }
})

// Copy code block functionality
function copyCode(event: Event) {
  const target = event.target as HTMLElement
  const codeBlock = target.closest('.code-block-container')?.querySelector('code')
  if (codeBlock) {
    navigator.clipboard?.writeText(codeBlock.textContent || '')

    // Visual feedback
    const button = target.closest('button')
    if (button) {
      const originalContent = button.innerHTML
      button.innerHTML = '<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
      setTimeout(() => {
        button.innerHTML = originalContent
      }, 1500)
    }
  }
}

// Handle link clicks
function handleLinkClick(event: Event) {
  if (!props.enableLinks) {
    event.preventDefault()
    return
  }

  const target = event.target as HTMLAnchorElement
  if (target.href && !target.href.startsWith('#')) {
    // External links open in new tab
    target.target = '_blank'
    target.rel = 'noopener noreferrer'
  }
}

// Inject copy buttons into code blocks on mount
onMounted(() => {
  nextTick(() => {
    const codeBlocks = document.querySelectorAll('pre code')
    codeBlocks.forEach((block) => {
      const pre = block.parentElement
      if (pre && !pre.querySelector('.copy-button')) {
        const container = document.createElement('div')
        container.className = 'code-block-container relative'

        // Wrap the pre element
        pre.parentNode?.insertBefore(container, pre)
        container.appendChild(pre)

        // Add copy button
        const copyButton = document.createElement('button')
        copyButton.className = 'copy-button absolute top-2 right-2 p-1.5 rounded-md bg-muted-800/80 hover:bg-muted-800 text-white/80 hover:text-white transition-colors'
        copyButton.innerHTML = '<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>'
        copyButton.addEventListener('click', copyCode)
        container.appendChild(copyButton)
      }
    })

    // Add link click handlers
    const links = document.querySelectorAll('a')
    links.forEach((link) => {
      link.addEventListener('click', handleLinkClick)
    })
  })
})
</script>

<template>
  <div
    class="markdown-content prose prose-sm max-w-none dark:prose-invert"
    v-html="renderedContent"
  />
</template>

<style>
/* Enhanced markdown styling - simplified for Tailwind 4.x compatibility */
.markdown-content {
  /* Code blocks */
  pre {
    position: relative;
    background-color: #111827;
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
  }

  pre code {
    font-size: 0.875rem;
    font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
    color: #f3f4f6;
  }

  /* Inline code */
  :not(pre) > code {
    background-color: #f3f4f6;
    color: #111827;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  }

  /* Dark mode inline code */
  .dark :not(pre) > code {
    background-color: #374151;
    color: #f9fafb;
  }

  /* Tables */
  table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  th {
    background-color: #f9fafb;
    padding: 0.5rem 1rem;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid #e5e7eb;
  }

  td {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  /* Dark mode tables */
  .dark table {
    border-color: #4b5563;
  }

  .dark th {
    background-color: #374151;
    border-bottom-color: #4b5563;
  }

  .dark td {
    border-bottom-color: #4b5563;
  }

  /* Lists */
  ul,
  ol {
    margin: 0.5rem 0;
  }

  li {
    line-height: 1.625;
    margin: 0.25rem 0;
  }

  /* Blockquotes */
  blockquote {
    border-left: 4px solid #8b5cf6;
    background-color: #f5f3ff;
    padding: 0.5rem 0 0.5rem 1rem;
    margin: 1rem 0;
    font-style: italic;
  }

  .dark blockquote {
    background-color: rgba(139, 92, 246, 0.1);
  }

  /* Links */
  a {
    color: #2563eb;
    text-decoration: underline;
    transition: color 0.2s;
  }

  a:hover {
    color: #1d4ed8;
  }

  .dark a {
    color: #60a5fa;
  }

  .dark a:hover {
    color: #93c5fd;
  }

  /* Headings */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 600;
    color: #111827;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .dark h1,
  .dark h2,
  .dark h3,
  .dark h4,
  .dark h5,
  .dark h6 {
    color: #f9fafb;
  }

  h1 {
    font-size: 1.5rem;
  }
  h2 {
    font-size: 1.25rem;
  }
  h3 {
    font-size: 1.125rem;
  }
  h4 {
    font-size: 1rem;
  }

  /* Paragraphs */
  p {
    line-height: 1.625;
    margin-bottom: 1rem;
  }

  /* Horizontal rules */
  hr {
    border-color: #e5e7eb;
    margin: 1.5rem 0;
  }

  .dark hr {
    border-color: #4b5563;
  }
}

/* Code block container for copy button positioning */
.code-block-container {
  position: relative;
}

.copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.375rem;
  border-radius: 0.375rem;
  background-color: rgba(31, 41, 55, 0.8);
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.2s;
  opacity: 0;
}

.copy-button:hover {
  background-color: rgba(31, 41, 55, 1);
  color: white;
}

.code-block-container:hover .copy-button {
  opacity: 1;
}

/* Syntax highlighting themes */
.hljs {
  background: transparent;
}

/* Simplified syntax highlighting */
.hljs-keyword,
.hljs-selector-tag,
.hljs-title,
.hljs-section,
.hljs-type,
.hljs-name {
  color: #c084fc;
}

.hljs-string,
.hljs-attr,
.hljs-symbol,
.hljs-bullet,
.hljs-link {
  color: #4ade80;
}

.hljs-number,
.hljs-regexp {
  color: #60a5fa;
}

.hljs-comment,
.hljs-quote,
.hljs-meta {
  color: #9ca3af;
}

.hljs-deletion {
  color: #f87171;
}

.hljs-emphasis {
  font-style: italic;
}
</style>
