// Export all chat view components
export { default as ChapterOutlineView } from './ChapterOutlineView.vue'
export { default as DocumentEditorView } from './DocumentEditorView.vue'
export { default as ImageGalleryView } from './ImageGalleryView.vue'
export { default as SuggestionBubbleView } from './SuggestionBubbleView.vue'
export { default as WritingStatsView } from './WritingStatsView.vue'

// Re-export interfaces from components for external use
// Note: These interfaces are defined within the component script setup blocks
// and should be imported directly from the component files when needed

// Type definitions for external use
export interface Suggestion {
  id: string
  type: 'plot' | 'character' | 'setting' | 'dialogue' | 'pacing' | 'theme' | 'structure'
  priority: 'low' | 'medium' | 'high'
  status: 'pending' | 'applied' | 'dismissed' | 'under-review'
  description: string
  before?: string
  after?: string
  reason?: string
  confidence?: number
  impact?: 'minor' | 'moderate' | 'major'
  chapterNumber?: number
  createdAt?: Date | string
  appliedAt?: Date | string
}

export interface BookImage {
  id: string
  title?: string
  prompt?: string
  url?: string
  type: 'cover' | 'chapter' | 'scene' | 'character' | 'illustration' | 'diagram'
  chapterNumber?: number
  status: 'pending' | 'generating' | 'completed' | 'error'
  createdAt?: Date | string
  metadata?: {
    width?: number
    height?: number
    format?: string
    size?: number
  }
}

export interface Scene {
  id: string
  title?: string
  summary?: string
  wordCount?: number
  status: 'draft' | 'in-progress' | 'completed' | 'needs-revision'
  notes?: string
  createdAt?: Date | string
}

export interface Chapter {
  id: string
  number?: number
  title?: string
  summary?: string
  wordCount?: number
  targetWordCount?: number
  status: 'draft' | 'in-progress' | 'completed' | 'needs-revision'
  scenes?: Scene[]
  notes?: string
  deadline?: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export interface WritingStats {
  totalWords: number
  wordsChange: number
  dailyAverage: number
  sessionsCount: number
  totalTime: number // in minutes
  streak: number
  bestStreak: number
  todayWords?: number
  weeklyWords?: number
  monthlyWords?: number
}

export interface Activity {
  id: string
  type: 'writing' | 'editing' | 'planning' | 'research'
  description: string
  wordCount?: number
  timestamp: Date | string
}
