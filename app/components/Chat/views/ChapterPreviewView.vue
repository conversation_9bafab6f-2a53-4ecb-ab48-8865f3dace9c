<script setup lang="ts">
interface ChapterDraft {
  chapterNumber: number
  title: string
  content?: string
  summary?: string
  wordCount?: number
  targetWordCount?: number
  status?: 'draft' | 'in_progress' | 'review' | 'complete'
  updatedAt?: Date | string
  notes?: string
}

interface Props {
  chapter?: ChapterDraft
  projectId?: string
  canEdit?: boolean
  canPolish?: boolean
  initialShowFull?: boolean
  readingProgress?: number
}

const props = withDefaults(defineProps<Props>(), {
  canEdit: true,
  canPolish: true,
  initialShowFull: false,
  readingProgress: 0,
})

const emit = defineEmits<{
  'edit-chapter': [chapterNumber: number]
  'polish-chapter': [chapterNumber: number]
  'export-chapter': [chapterNumber: number]
  'copy-content': [content: string]
  'generate-summary': [chapterNumber: number]
}>()

// State
const showFullContent = ref(props.initialShowFull)

// Computed properties
const formattedContent = computed(() => {
  if (!props.chapter?.content)
    return ''

  // Basic formatting for better readability
  return props.chapter.content
    .replace(/\n\n/g, '</p><p class="mb-4">')
    .replace(/\n/g, '<br>')
    .replace(/^/, '<p class="mb-4">')
    .replace(/$/, '</p>')
})

const openingExcerpt = computed(() => {
  if (!props.chapter?.content)
    return ''
  const words = props.chapter.content.trim().split(/\s+/)
  return words.slice(0, 50).join(' ')
})

const keySections = computed(() => {
  if (!props.chapter?.content)
    return []

  // Extract key sections by looking for paragraph breaks
  const paragraphs = props.chapter.content.split('\n\n').filter(p => p.trim().length > 50)

  return paragraphs.slice(1, 5).map((paragraph, index) => ({
    title: `Section ${index + 1}`,
    excerpt: paragraph.trim().substring(0, 150),
  }))
})

const paragraphCount = computed(() => {
  if (!props.chapter?.content)
    return 0
  return props.chapter.content.split('\n\n').filter(p => p.trim().length > 0).length
})

const readabilityScore = computed(() => {
  if (!props.chapter?.content)
    return 'N/A'

  // Simple readability estimation (mock implementation)
  const words = props.chapter.content.trim().split(/\s+/).length
  const sentences = props.chapter.content.split(/[.!?]+/).filter(s => s.trim().length > 0).length

  if (sentences === 0)
    return 'N/A'

  const avgWordsPerSentence = words / sentences

  if (avgWordsPerSentence < 15)
    return 'Easy'
  if (avgWordsPerSentence < 25)
    return 'Good'
  if (avgWordsPerSentence < 35)
    return 'Hard'
  return 'Complex'
})

// Helper functions
function getStatusColor(status?: string) {
  switch (status) {
    case 'draft': return 'bg-gray-400'
    case 'in_progress': return 'bg-blue-400'
    case 'review': return 'bg-yellow-400'
    case 'complete': return 'bg-green-400'
    default: return 'bg-gray-400'
  }
}

function getStatusText(status?: string) {
  switch (status) {
    case 'draft': return 'Draft'
    case 'in_progress': return 'In Progress'
    case 'review': return 'Under Review'
    case 'complete': return 'Complete'
    default: return 'Unknown'
  }
}

function formatDate(date?: Date | string) {
  if (!date)
    return 'Unknown'
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Actions
function toggleFullContent() {
  showFullContent.value = !showFullContent.value
}

function editChapter() {
  if (props.chapter?.chapterNumber) {
    emit('edit-chapter', props.chapter.chapterNumber)
  }
}

function polishChapter() {
  if (props.chapter?.chapterNumber) {
    emit('polish-chapter', props.chapter.chapterNumber)
  }
}

function exportChapter() {
  if (props.chapter?.chapterNumber) {
    emit('export-chapter', props.chapter.chapterNumber)
  }
}

function copyToClipboard() {
  if (props.chapter?.content) {
    emit('copy-content', props.chapter.content)

    // Also copy to clipboard
    navigator.clipboard.writeText(props.chapter.content).then(() => {
      // Show success feedback
      console.log('Chapter content copied to clipboard')
    })
  }
}

function generateSummary() {
  if (props.chapter?.chapterNumber) {
    emit('generate-summary', props.chapter.chapterNumber)
  }
}
</script>

<template>
  <div class="chapter-preview-view">
    <!-- Header -->
    <div class="flex items-center justify-between mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
      <div class="flex items-center space-x-3">
        <div class="p-2 rounded-lg bg-blue-100 dark:bg-blue-800">
          <Icon name="lucide:file-text" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-gray-100">
            Chapter {{ chapter?.chapterNumber }}: {{ chapter?.title }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ chapter?.wordCount?.toLocaleString() || 0 }} words •
            {{ Math.ceil((chapter?.wordCount || 0) / 250) }} min read
          </p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <BaseButton
          v-if="canEdit"
          size="sm"
          color="primary"
          @click="editChapter"
        >
          <Icon name="lucide:edit" class="h-4 w-4 mr-1" />
          Edit
        </BaseButton>
        <BaseButton
          v-if="canPolish"
          size="sm"
          variant="outline"
          @click="polishChapter"
        >
          <Icon name="lucide:sparkles" class="h-4 w-4 mr-1" />
          Polish
        </BaseButton>
        <BaseDropdown>
          <BaseButton size="sm" variant="ghost">
            <Icon name="lucide:more-horizontal" class="h-4 w-4" />
          </BaseButton>

          <template #content>
            <BaseDropdownItem @click="exportChapter">
              <Icon name="lucide:download" class="h-4 w-4 mr-2" />
              Export Chapter
            </BaseDropdownItem>
            <BaseDropdownItem @click="copyToClipboard">
              <Icon name="lucide:copy" class="h-4 w-4 mr-2" />
              Copy Content
            </BaseDropdownItem>
            <BaseDropdownItem @click="generateSummary">
              <Icon name="lucide:file-text" class="h-4 w-4 mr-2" />
              Generate Summary
            </BaseDropdownItem>
          </template>
        </BaseDropdown>
      </div>
    </div>

    <!-- Chapter Status & Metadata -->
    <div class="flex items-center justify-between mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-4">
        <!-- Status Badge -->
        <div class="flex items-center space-x-2">
          <div
            class="w-3 h-3 rounded-full"
            :class="getStatusColor(chapter?.status)"
          />
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ getStatusText(chapter?.status) }}
          </span>
        </div>

        <!-- Last Updated -->
        <div class="text-sm text-gray-500">
          Updated {{ formatDate(chapter?.updatedAt) }}
        </div>

        <!-- Word Count Progress -->
        <div v-if="chapter?.targetWordCount" class="text-sm text-gray-500">
          {{ Math.round(((chapter?.wordCount || 0) / chapter.targetWordCount) * 100) }}% of target
        </div>
      </div>

      <!-- Reading Progress -->
      <div class="flex items-center space-x-2">
        <span class="text-xs text-gray-500">Reading Progress</span>
        <div class="w-20 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div
            class="h-full bg-primary-500 transition-all duration-300"
            :style="`width: ${readingProgress}%`"
          />
        </div>
        <span class="text-xs text-gray-600 dark:text-gray-400">{{ readingProgress }}%</span>
      </div>
    </div>

    <!-- Content Preview -->
    <div class="chapter-content">
      <!-- Full Content Mode -->
      <div v-if="showFullContent" class="prose prose-gray dark:prose-invert max-w-none">
        <div
          v-if="chapter?.content"
          class="whitespace-pre-wrap leading-relaxed text-gray-800 dark:text-gray-200"
          v-html="formattedContent"
        />
        <div v-else class="text-center py-8 text-gray-500">
          <Icon name="lucide:file-text" class="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>No content available</p>
        </div>
      </div>

      <!-- Preview Mode (Default) -->
      <div v-else class="space-y-4">
        <!-- Opening Excerpt -->
        <div v-if="openingExcerpt" class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
            <Icon name="lucide:quote" class="h-4 w-4 mr-2" />
            Opening
          </h4>
          <p class="text-gray-800 dark:text-gray-200 leading-relaxed italic">
            "{{ openingExcerpt }}"
          </p>
        </div>

        <!-- Key Sections Preview -->
        <div v-if="keySections.length" class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div
            v-for="(section, index) in keySections"
            :key="index"
            class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ section.title }}
            </h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              {{ section.excerpt }}...
            </p>
          </div>
        </div>

        <!-- Chapter Summary (if no content) -->
        <div v-if="!chapter?.content && chapter?.summary" class="p-4 bg-amber-50 dark:bg-amber-900/10 rounded-lg border border-amber-200 dark:border-amber-800">
          <h4 class="text-sm font-medium text-amber-800 dark:text-amber-200 mb-2 flex items-center">
            <Icon name="lucide:lightbulb" class="h-4 w-4 mr-2" />
            Chapter Summary
          </h4>
          <p class="text-amber-700 dark:text-amber-300 text-sm leading-relaxed">
            {{ chapter.summary }}
          </p>
        </div>

        <!-- Expand/Collapse Toggle -->
        <div class="text-center">
          <BaseButton
            variant="ghost"
            size="sm"
            @click="toggleFullContent"
          >
            <Icon
              :name="showFullContent ? 'lucide:chevron-up' : 'lucide:chevron-down'"
              class="h-4 w-4 mr-2"
            />
            {{ showFullContent ? 'Show Preview' : 'Show Full Content' }}
          </BaseButton>
        </div>
      </div>
    </div>

    <!-- Chapter Statistics -->
    <div v-if="chapter" class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ chapter.wordCount?.toLocaleString() || 0 }}
        </div>
        <div class="text-xs text-gray-500">
          Words
        </div>
      </div>
      <div class="text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ paragraphCount }}
        </div>
        <div class="text-xs text-gray-500">
          Paragraphs
        </div>
      </div>
      <div class="text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ Math.ceil((chapter.wordCount || 0) / 250) }}
        </div>
        <div class="text-xs text-gray-500">
          Min Read
        </div>
      </div>
      <div class="text-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
        <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ readabilityScore }}
        </div>
        <div class="text-xs text-gray-500">
          Readability
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chapter-content .prose {
  @apply text-gray-800 dark:text-gray-200;
}

.chapter-content .prose p {
  @apply mb-4 leading-relaxed;
}

.chapter-content .prose h1,
.chapter-content .prose h2,
.chapter-content .prose h3 {
  @apply text-gray-900 dark:text-gray-100 font-semibold;
}
</style>
