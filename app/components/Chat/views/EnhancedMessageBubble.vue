<script setup lang="ts">
import type { ChatMessage } from '~/types/chat'
import { useMotion } from '@vueuse/motion'

interface Props {
  message: ChatMessage
  showAvatar?: boolean
  showTimestamp?: boolean
  animate?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showAvatar: true,
  showTimestamp: true,
  animate: true,
  compact: false,
})

const emits = defineEmits<{
  copy: [content: string]
  retry: [messageId: string]
  delete: [messageId: string]
  react: [messageId: string, emoji: string]
}>()

// Reactive refs
const bubbleRef = ref<HTMLElement>()
const isHovered = ref(false)
const showActions = ref(false)

// Computed properties
const isUser = computed(() => props.message.role === 'user')
const isAssistant = computed(() => props.message.role === 'assistant')
const isSystem = computed(() => props.message.role === 'system')

// Agent info
const agentInfo = computed(() => ({
  name: props.message.agentName || 'Assistant',
  initials: (props.message.agentName || 'AI').split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase(),
  color: getAgentColor(props.message.agentId || 'default'),
}))

// Provider info
const providerInfo = computed(() => {
  const provider = props.message.metadata?.provider
  if (!provider)
    return null

  const config: Record<string, { name: string, color: string, icon: string }> = {
    openai: { name: 'OpenAI', color: 'success', icon: 'simple-icons:openai' },
    gemini: { name: 'Gemini', color: 'info', icon: 'simple-icons:google' },
    anthropic: { name: 'Anthropic', color: 'warning', icon: 'simple-icons:anthropic' },
    grok: { name: 'Grok', color: 'primary', icon: 'simple-icons:x' },
  }

  return config[provider.toLowerCase()] || { name: provider, color: 'muted', icon: 'lucide:bot' }
})

// Model info
const modelInfo = computed(() => {
  const model = props.message.metadata?.model
  if (!model)
    return null

  return {
    name: model.replace(/^(gpt-|gemini-|claude-)/i, '').replace(/(-\d+k|-\d+b|-latest|-preview)$/i, ''),
    fullName: model,
  }
})

// Status indicators
const messageStatus = computed(() => {
  if (props.message.isStreaming)
    return { icon: 'lucide:loader-2', class: 'animate-spin text-primary-500', label: 'Generating...' }
  if (props.message.metadata?.finishReason === 'error')
    return { icon: 'lucide:alert-circle', class: 'text-danger-500', label: 'Error' }
  return { icon: 'lucide:check', class: 'text-success-500', label: 'Delivered' }
})

// Timestamp formatting
const timestamp = computed(() => {
  return new Intl.DateTimeFormat('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  }).format(props.message.createdAt)
})

// Animation setup
const { apply: applyMotion } = useMotion(bubbleRef, {
  initial: { opacity: 0, y: 20, scale: 0.95 },
  enter: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: 'spring',
      stiffness: 300,
      damping: 25,
      delay: 100,
    },
  },
  hovered: {
    scale: 1.02,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 30,
    },
  },
})

// Helper functions
function getAgentColor(agentId: string): string {
  const colors = ['primary', 'success', 'info', 'warning', 'danger', 'indigo', 'purple']
  return colors[agentId.length % colors.length]
}

function handleMouseEnter() {
  if (!props.animate)
    return
  isHovered.value = true
  showActions.value = true
  applyMotion('hovered')
}

function handleMouseLeave() {
  isHovered.value = false
  setTimeout(() => {
    if (!isHovered.value)
      showActions.value = false
  }, 300)
}

// Actions
function copyMessage() {
  const value = typeof props.message.content === 'string' ? props.message.content : JSON.stringify(props.message.content, null, 2)
  emits('copy', value)
}

function retryMessage() {
  emits('retry', props.message.id)
}

function deleteMessage() {
  emits('delete', props.message.id)
}

function reactToMessage(emoji: string) {
  emits('react', props.message.id, emoji)
}

// Lifecycle
onMounted(() => {
  if (props.animate) {
    applyMotion('enter')
  }
})
</script>

<template>
  <div
    ref="bubbleRef"
    class="group/message relative mb-4 flex gap-3 transition-all duration-200"
    :class="[
      isUser ? 'flex-row-reverse' : 'flex-row',
      compact ? 'mb-2' : 'mb-4',
    ]"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Avatar Section -->
    <div
      v-if="showAvatar"
      class="relative shrink-0"
    >
      <!-- User Avatar -->
      <div
        v-if="isUser"
        class="relative overflow-hidden"
      >
        <div class="flex size-10 items-center justify-center rounded-full bg-gradient-to-br from-primary-400 to-primary-600 shadow-lg">
          <Icon name="lucide:user" class="size-5 text-white" />
        </div>
      </div>

      <!-- Assistant Avatar -->
      <div
        v-else-if="isAssistant"
        class="relative overflow-hidden"
      >
        <div
          class="flex size-10 items-center justify-center rounded-full shadow-lg relative overflow-hidden"
          :class="`bg-gradient-to-br from-${agentInfo.color}-400 to-${agentInfo.color}-600`"
        >
          <!-- Animated background gradient -->
          <div class="absolute inset-0 bg-gradient-to-br animate-pulse opacity-20" :class="`from-${agentInfo.color}-300 to-${agentInfo.color}-500`" />

          <span class="relative z-10 text-sm font-bold text-white">
            {{ agentInfo.initials }}
          </span>
        </div>

        <!-- Active indicator -->
        <div
          v-if="message.isStreaming"
          class="absolute -bottom-1 -right-1 size-4 rounded-full bg-green-500 border-2 border-white animate-pulse"
        />
      </div>

      <!-- System Avatar -->
      <div
        v-else
        class="relative overflow-hidden"
      >
        <div class="flex size-10 items-center justify-center rounded-full bg-gradient-to-br from-muted-400 to-muted-600 shadow-lg">
          <Icon name="lucide:settings" class="size-5 text-white" />
        </div>
      </div>
    </div>

    <!-- Message Content -->
    <div
      class="relative flex-1 transition-all duration-200"
      :class="[
        isUser ? 'flex justify-end max-w-[80%]' : 'flex justify-start max-w-[85%]',
      ]"
    >
      <!-- Message Bubble -->
      <div
        class="relative overflow-hidden transition-all duration-300"
        :class="[
          compact ? 'rounded-xl px-3 py-2' : 'rounded-2xl px-4 py-3',
          isUser
            ? 'bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/25'
            : 'bg-gradient-to-br from-white to-muted-50 dark:from-muted-800 dark:to-muted-900 border border-muted-200/50 dark:border-muted-700/50 shadow-xl shadow-muted-300/20 dark:shadow-muted-900/30',
        ]"
      >
        <!-- Glassmorphism overlay -->
        <div
          v-if="!isUser"
          class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent backdrop-blur-sm"
        />

        <!-- Content wrapper -->
        <div class="relative z-10">
          <!-- Agent Header (for assistant messages) -->
          <div
            v-if="isAssistant && (agentInfo.name || providerInfo || modelInfo)"
            class="mb-2 flex flex-wrap items-center gap-1.5"
          >
            <!-- Agent name -->
            <div class="text-xs font-semibold text-muted-600 dark:text-muted-300">
              {{ agentInfo.name }}
            </div>

            <!-- Provider badge -->
            <BaseBadge
              v-if="providerInfo"
              size="xs"
              :color="providerInfo.color"
              class="shadow-sm"
            >
              <Icon :name="providerInfo.icon" class="size-3 mr-1" />
              {{ providerInfo.name }}
            </BaseBadge>

            <!-- Model badge -->
            <BaseBadge
              v-if="modelInfo"
              size="xs"
              color="muted"
              variant="outline"
              class="text-xs"
              :title="modelInfo.fullName"
            >
              {{ modelInfo.name }}
            </BaseBadge>
          </div>

          <!-- Message Content -->
          <div
            class="prose prose-sm max-w-none"
            :class="[
              isUser
                ? 'prose-invert text-white'
                : 'text-muted-900 dark:text-muted-100',
            ]"
          >
            <!-- Use slot for flexible content rendering -->
            <slot name="content">
              <div class="whitespace-pre-wrap break-words">
                {{ message.content }}
              </div>
            </slot>

            <!-- Streaming indicator -->
            <div
              v-if="message.isStreaming"
              class="mt-2 flex items-center gap-2"
            >
              <div class="flex gap-1">
                <div class="size-1 rounded-full bg-current opacity-60 animate-pulse" />
                <div class="size-1 rounded-full bg-current opacity-40 animate-pulse" style="animation-delay: 0.2s" />
                <div class="size-1 rounded-full bg-current opacity-20 animate-pulse" style="animation-delay: 0.4s" />
              </div>
              <span class="text-xs opacity-70">Generating response...</span>
            </div>
          </div>

          <!-- Tool Calls -->
          <div
            v-if="message.toolCalls && message.toolCalls.length > 0"
            class="mt-3 space-y-2"
          >
            <div
              v-for="tool in message.toolCalls"
              :key="tool.id"
              class="rounded-lg border p-2 bg-black/5 dark:bg-white/5"
              :class="[
                isUser
                  ? 'border-white/20'
                  : 'border-muted-300/50 dark:border-muted-600/50',
              ]"
            >
              <div class="flex items-center gap-2">
                <Icon name="lucide:wrench" class="size-3" />
                <span class="text-xs font-medium">{{ tool.name }}</span>
              </div>
              <div
                v-if="tool.result"
                class="mt-1 text-xs opacity-75 line-clamp-2"
              >
                {{ typeof tool.result === 'string' ? tool.result : JSON.stringify(tool.result, null, 2).substring(0, 100) }}...
              </div>
            </div>
          </div>

          <!-- Status and Timestamp -->
          <div
            v-if="showTimestamp"
            class="mt-2 flex items-center justify-between text-xs"
            :class="isUser ? 'text-white/70' : 'text-muted-500 dark:text-muted-400'"
          >
            <div class="flex items-center gap-1">
              <Icon :name="messageStatus.icon" :class="`size-3 ${messageStatus.class}`" />
              <span>{{ timestamp }}</span>
              <span v-if="message.metadata?.tokens" class="opacity-60">
                ({{ message.metadata.tokens }} tokens)
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Message Actions -->
      <Transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 scale-90 translate-y-2"
        enter-to-class="opacity-100 scale-100 translate-y-0"
        leave-active-class="transition-all duration-150 ease-in"
        leave-from-class="opacity-100 scale-100 translate-y-0"
        leave-to-class="opacity-0 scale-90 translate-y-2"
      >
        <div
          v-if="showActions"
          class="absolute -bottom-10 flex items-center gap-1 rounded-full bg-white dark:bg-muted-800 border border-muted-200 dark:border-muted-700 shadow-lg px-2 py-1"
          :class="isUser ? 'right-0' : 'left-12'"
        >
          <!-- Copy -->
          <BaseTooltip content="Copy message">
            <button
              type="button"
              class="flex size-7 items-center justify-center rounded-full hover:bg-muted-100 dark:hover:bg-muted-700 transition-colors"
              @click="copyMessage"
            >
              <Icon name="lucide:copy" class="size-3" />
            </button>
          </BaseTooltip>

          <!-- React -->
          <BaseTooltip content="Add reaction">
            <button
              type="button"
              class="flex size-7 items-center justify-center rounded-full hover:bg-muted-100 dark:hover:bg-muted-700 transition-colors"
              @click="reactToMessage('👍')"
            >
              <Icon name="lucide:smile" class="size-3" />
            </button>
          </BaseTooltip>

          <!-- Retry (assistant only) -->
          <BaseTooltip
            v-if="isAssistant"
            content="Retry message"
          >
            <button
              type="button"
              class="flex size-7 items-center justify-center rounded-full hover:bg-muted-100 dark:hover:bg-muted-700 transition-colors"
              @click="retryMessage"
            >
              <Icon name="lucide:refresh-cw" class="size-3" />
            </button>
          </BaseTooltip>

          <!-- Delete -->
          <BaseTooltip content="Delete message">
            <button
              type="button"
              class="flex size-7 items-center justify-center rounded-full hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors text-red-600 dark:text-red-400"
              @click="deleteMessage"
            >
              <Icon name="lucide:trash-2" class="size-3" />
            </button>
          </BaseTooltip>
        </div>
      </Transition>
    </div>
  </div>
</template>

<style scoped>
/* Custom animations and effects */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Glassmorphism backdrop blur support */
@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }
}

/* Enhanced shadows with color variants */
.shadow-primary-500\/25 {
  box-shadow:
    0 10px 25px -3px rgba(139, 92, 246, 0.25),
    0 4px 6px -2px rgba(139, 92, 246, 0.1);
}

.shadow-muted-300\/20 {
  box-shadow:
    0 10px 25px -3px rgba(156, 163, 175, 0.2),
    0 4px 6px -2px rgba(156, 163, 175, 0.1);
}

.dark .shadow-muted-900\/30 {
  box-shadow:
    0 10px 25px -3px rgba(17, 24, 39, 0.3),
    0 4px 6px -2px rgba(17, 24, 39, 0.2);
}
</style>
