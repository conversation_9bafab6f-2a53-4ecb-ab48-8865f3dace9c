<script setup lang="ts">
interface Scene {
  id: string
  title?: string
  summary?: string
  wordCount?: number
  status: 'draft' | 'in-progress' | 'completed' | 'needs-revision'
  notes?: string
  createdAt?: Date | string
}

interface Chapter {
  id: string
  number?: number
  title?: string
  summary?: string
  wordCount?: number
  targetWordCount?: number
  status: 'draft' | 'in-progress' | 'completed' | 'needs-revision'
  scenes?: Scene[]
  notes?: string
  deadline?: Date | string
  createdAt?: Date | string
  updatedAt?: Date | string
}

interface Props {
  chapters: Chapter[]
  title?: string
  projectId?: string
}

const props = withDefaults(defineProps<Props>(), {
  chapters: () => [],
})

const emit = defineEmits<{
  'add-chapter': []
  'edit-chapter': [chapter: Chapter]
  'duplicate-chapter': [chapter: Chapter]
  'delete-chapter': [chapter: Chapter]
  'add-scene': [chapter: Chapter]
  'edit-scene': [chapter: Chapter, scene: Scene]
  'delete-scene': [chapter: Chapter, scene: Scene]
  'reorder-chapters': []
  'export-outline': []
  'generate-summary': []
  'analyze-structure': []
}>()

// State
const searchQuery = ref('')
const statusFilter = ref('')
const showOnlyIncomplete = ref(false)
const expandedChapters = ref(new Set<string>())
const selectedChapter = ref<Chapter | null>(null)

// Computed properties
const totalChapters = computed(() => props.chapters.length)

const completedChapters = computed(() =>
  props.chapters.filter(chapter => chapter.status === 'completed').length,
)

const totalWords = computed(() =>
  props.chapters.reduce((total, chapter) => total + (chapter.wordCount || 0), 0),
)

const progressPercentage = computed(() => {
  if (totalChapters.value === 0)
    return 0
  return (completedChapters.value / totalChapters.value) * 100
})

const filteredChapters = computed(() => {
  let filtered = props.chapters

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(chapter =>
      chapter.title?.toLowerCase().includes(query)
      || chapter.summary?.toLowerCase().includes(query)
      || chapter.notes?.toLowerCase().includes(query)
      || chapter.scenes?.some(scene =>
        scene.title?.toLowerCase().includes(query)
        || scene.summary?.toLowerCase().includes(query),
      ),
    )
  }

  // Status filter
  if (statusFilter.value) {
    filtered = filtered.filter(chapter => chapter.status === statusFilter.value)
  }

  // Incomplete only filter
  if (showOnlyIncomplete.value) {
    filtered = filtered.filter(chapter => chapter.status !== 'completed')
  }

  return filtered
})

const statusFilterOptions = computed(() => [
  { value: '', label: 'All Status' },
  { value: 'draft', label: 'Draft' },
  { value: 'in-progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'needs-revision', label: 'Needs Revision' },
])

// Helper functions
function getStatusColor(status: string) {
  switch (status) {
    case 'completed': return 'success'
    case 'in-progress': return 'info'
    case 'needs-revision': return 'warning'
    case 'draft': return 'default'
    default: return 'default'
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'completed': return 'Complete'
    case 'in-progress': return 'In Progress'
    case 'needs-revision': return 'Revision'
    case 'draft': return 'Draft'
    default: return 'Unknown'
  }
}

function getWordCountColor(chapter: Chapter) {
  if (!chapter.targetWordCount)
    return ''
  const progress = (chapter.wordCount || 0) / chapter.targetWordCount
  if (progress >= 1)
    return 'text-green-600 dark:text-green-400'
  if (progress >= 0.8)
    return 'text-blue-600 dark:text-blue-400'
  if (progress >= 0.5)
    return 'text-yellow-600 dark:text-yellow-400'
  return 'text-red-600 dark:text-red-400'
}

function getChapterProgress(chapter: Chapter) {
  if (!chapter.targetWordCount)
    return 0
  return Math.round(((chapter.wordCount || 0) / chapter.targetWordCount) * 100)
}

function formatDate(date?: Date | string) {
  if (!date)
    return 'Not set'
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  })
}

// Actions
function toggleChapter(chapter: Chapter) {
  if (expandedChapters.value.has(chapter.id)) {
    expandedChapters.value.delete(chapter.id)
  }
  else {
    expandedChapters.value.add(chapter.id)
  }
  selectedChapter.value = chapter
}

function expandAll() {
  props.chapters.forEach((chapter) => {
    expandedChapters.value.add(chapter.id)
  })
}

function addChapter() {
  emit('add-chapter')
}

function editChapter(chapter: Chapter) {
  emit('edit-chapter', chapter)
}

function duplicateChapter(chapter: Chapter) {
  emit('duplicate-chapter', chapter)
}

function deleteChapter(chapter: Chapter) {
  emit('delete-chapter', chapter)
  expandedChapters.value.delete(chapter.id)
  if (selectedChapter.value?.id === chapter.id) {
    selectedChapter.value = null
  }
}

function addScene(chapter: Chapter) {
  emit('add-scene', chapter)
}

function editScene(chapter: Chapter, scene: Scene) {
  emit('edit-scene', chapter, scene)
}

function deleteScene(chapter: Chapter, scene: Scene) {
  emit('delete-scene', chapter, scene)
}

function reorderChapters() {
  emit('reorder-chapters')
}

function exportOutline() {
  emit('export-outline')
}

function generateSummary() {
  emit('generate-summary')
}

function analyzeStructure() {
  emit('analyze-structure')
}
</script>

<template>
  <div class="chapter-outline-view">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
      <div class="flex items-center space-x-3">
        <div class="p-2 rounded-lg bg-blue-100 dark:bg-blue-800">
          <Icon name="lucide:book-open" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-gray-100">
            {{ title || 'Chapter Outline' }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ totalChapters }} chapters • {{ totalWords.toLocaleString() }} words • {{ completedChapters }}/{{ totalChapters }} complete
          </p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <BaseButton
          size="sm"
          variant="outline"
          @click="expandAll"
        >
          <Icon name="lucide:expand" class="h-4 w-4 mr-1" />
          Expand All
        </BaseButton>
        <BaseButton
          size="sm"
          color="primary"
          @click="addChapter"
        >
          <Icon name="lucide:plus" class="h-4 w-4 mr-1" />
          Add Chapter
        </BaseButton>
        <BaseDropdown>
          <BaseButton size="sm" variant="ghost">
            <Icon name="lucide:more-horizontal" class="h-4 w-4" />
          </BaseButton>

          <template #content>
            <BaseDropdownItem @click="exportOutline">
              <Icon name="lucide:download" class="h-4 w-4 mr-2" />
              Export Outline
            </BaseDropdownItem>
            <BaseDropdownItem @click="generateSummary">
              <Icon name="lucide:sparkles" class="h-4 w-4 mr-2" />
              Generate Summary
            </BaseDropdownItem>
            <BaseDropdownItem @click="analyzeStructure">
              <Icon name="lucide:bar-chart-3" class="h-4 w-4 mr-2" />
              Analyze Structure
            </BaseDropdownItem>
            <BaseDropdownDivider />
            <BaseDropdownItem @click="reorderChapters">
              <Icon name="lucide:arrow-up-down" class="h-4 w-4 mr-2" />
              Reorder Chapters
            </BaseDropdownItem>
          </template>
        </BaseDropdown>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="mb-6">
      <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
        <span>Writing Progress</span>
        <span>{{ Math.round(progressPercentage) }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${progressPercentage}%` }"
        />
      </div>
    </div>

    <!-- Filters & Search -->
    <div class="flex items-center space-x-4 mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex-1">
        <BaseInput
          v-model="searchQuery"
          placeholder="Search chapters, scenes, or notes..."
          size="sm"
        >
          <template #leading>
            <Icon name="lucide:search" class="h-4 w-4 text-gray-400" />
          </template>
        </BaseInput>
      </div>

      <BaseListbox
        v-model="statusFilter"
        :items="statusFilterOptions"
        placeholder="All Status"
        size="sm"
      />

      <BaseButton
        size="sm"
        :variant="showOnlyIncomplete ? 'filled' : 'ghost'"
        @click="showOnlyIncomplete = !showOnlyIncomplete"
      >
        <Icon name="lucide:clock" class="h-4 w-4 mr-1" />
        Incomplete Only
      </BaseButton>
    </div>

    <!-- Chapter List -->
    <div class="space-y-3">
      <div
        v-for="(chapter, index) in filteredChapters"
        :key="chapter.id || index"
        class="chapter-item bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden"
        :class="{ 'ring-2 ring-blue-500': selectedChapter?.id === chapter.id }"
      >
        <!-- Chapter Header -->
        <div
          class="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
          @click="toggleChapter(chapter)"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3 flex-1">
              <!-- Expand/Collapse Icon -->
              <Icon
                :name="expandedChapters.has(chapter.id) ? 'lucide:chevron-down' : 'lucide:chevron-right'"
                class="h-4 w-4 text-gray-400 transition-transform"
              />

              <!-- Chapter Number & Title -->
              <div class="flex-1">
                <div class="flex items-center space-x-2">
                  <span class="font-medium text-gray-900 dark:text-gray-100">
                    Chapter {{ chapter.number || index + 1 }}
                  </span>
                  <BaseTag
                    :color="getStatusColor(chapter.status)"
                    size="xs"
                    variant="pastel"
                  >
                    {{ getStatusText(chapter.status) }}
                  </BaseTag>
                </div>
                <h4 class="font-semibold text-lg text-gray-900 dark:text-gray-100 mt-1">
                  {{ chapter.title || 'Untitled Chapter' }}
                </h4>
                <p v-if="chapter.summary" class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                  {{ chapter.summary }}
                </p>
              </div>
            </div>

            <!-- Chapter Stats -->
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <div class="text-right">
                <div>{{ (chapter.wordCount || 0).toLocaleString() }} words</div>
                <div>{{ chapter.scenes?.length || 0 }} scenes</div>
              </div>

              <!-- Actions -->
              <div class="flex space-x-1">
                <BaseButton
                  size="xs"
                  variant="ghost"
                  @click.stop="editChapter(chapter)"
                >
                  <Icon name="lucide:edit-3" class="h-3 w-3" />
                </BaseButton>
                <BaseButton
                  size="xs"
                  variant="ghost"
                  @click.stop="duplicateChapter(chapter)"
                >
                  <Icon name="lucide:copy" class="h-3 w-3" />
                </BaseButton>
                <BaseButton
                  size="xs"
                  variant="ghost"
                  color="danger"
                  @click.stop="deleteChapter(chapter)"
                >
                  <Icon name="lucide:trash-2" class="h-3 w-3" />
                </BaseButton>
              </div>
            </div>
          </div>
        </div>

        <!-- Expanded Chapter Content -->
        <div
          v-if="expandedChapters.has(chapter.id)"
          class="border-t border-gray-200 dark:border-gray-700"
        >
          <!-- Chapter Details -->
          <div class="p-4 bg-gray-50 dark:bg-gray-700/50">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Details -->
              <div>
                <h5 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Details
                </h5>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Target Words:</span>
                    <span>{{ (chapter.targetWordCount || 0).toLocaleString() }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Current Words:</span>
                    <span :class="getWordCountColor(chapter)">
                      {{ (chapter.wordCount || 0).toLocaleString() }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Progress:</span>
                    <span>{{ getChapterProgress(chapter) }}%</span>
                  </div>
                  <div v-if="chapter.deadline" class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Deadline:</span>
                    <span>{{ formatDate(chapter.deadline) }}</span>
                  </div>
                </div>
              </div>

              <!-- Notes -->
              <div v-if="chapter.notes">
                <h5 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Notes
                </h5>
                <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  {{ chapter.notes }}
                </p>
              </div>
            </div>
          </div>

          <!-- Scenes -->
          <div v-if="chapter.scenes && chapter.scenes.length > 0" class="p-4">
            <h5 class="font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center">
              <Icon name="lucide:film" class="h-4 w-4 mr-2" />
              Scenes ({{ chapter.scenes.length }})
              <BaseButton
                size="xs"
                variant="ghost"
                class="ml-auto"
                @click="addScene(chapter)"
              >
                <Icon name="lucide:plus" class="h-3 w-3 mr-1" />
                Add Scene
              </BaseButton>
            </h5>

            <div class="space-y-2">
              <div
                v-for="(scene, sceneIndex) in chapter.scenes"
                :key="scene.id || sceneIndex"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
              >
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Scene {{ sceneIndex + 1 }}
                    </span>
                    <BaseTag
                      v-if="scene.status"
                      :color="getStatusColor(scene.status)"
                      size="xs"
                    >
                      {{ getStatusText(scene.status) }}
                    </BaseTag>
                  </div>
                  <h6 class="font-medium text-gray-900 dark:text-gray-100 mt-1">
                    {{ scene.title || 'Untitled Scene' }}
                  </h6>
                  <p v-if="scene.summary" class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {{ scene.summary }}
                  </p>
                </div>

                <div class="flex items-center space-x-2 text-xs text-gray-500">
                  <span>{{ (scene.wordCount || 0).toLocaleString() }} words</span>
                  <div class="flex space-x-1">
                    <BaseButton
                      size="xs"
                      variant="ghost"
                      @click="editScene(chapter, scene)"
                    >
                      <Icon name="lucide:edit-3" class="h-3 w-3" />
                    </BaseButton>
                    <BaseButton
                      size="xs"
                      variant="ghost"
                      color="danger"
                      @click="deleteScene(chapter, scene)"
                    >
                      <Icon name="lucide:trash-2" class="h-3 w-3" />
                    </BaseButton>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Add Scene Button (if no scenes) -->
          <div v-else class="p-4 border-t border-gray-200 dark:border-gray-700">
            <BaseButton
              size="sm"
              variant="outline"
              class="w-full"
              @click="addScene(chapter)"
            >
              <Icon name="lucide:plus" class="h-4 w-4 mr-2" />
              Add First Scene
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="filteredChapters.length === 0" class="text-center py-12 text-gray-500">
      <Icon name="lucide:book-open" class="h-12 w-12 mx-auto mb-4 opacity-50" />
      <h3 class="text-lg font-medium mb-2">
        No chapters found
      </h3>
      <p class="text-sm mb-4">
        {{ searchQuery ? 'No chapters match your search criteria.' : 'Start building your book outline by adding your first chapter.' }}
      </p>
      <BaseButton
        v-if="!searchQuery"
        variant="outline"
        @click="addChapter"
      >
        <Icon name="lucide:plus" class="h-4 w-4 mr-2" />
        Add First Chapter
      </BaseButton>
    </div>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
