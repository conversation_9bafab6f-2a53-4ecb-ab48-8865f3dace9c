<script setup lang="ts">
interface WritingStats {
  totalWords: number
  wordsChange: number
  dailyAverage: number
  sessionsCount: number
  totalTime: number // in minutes
  streak: number
  bestStreak: number
  todayWords?: number
  weeklyWords?: number
  monthlyWords?: number
}

interface Activity {
  id: string
  type: 'writing' | 'editing' | 'planning' | 'research'
  description: string
  wordCount?: number
  timestamp: Date | string
}

interface Props {
  stats?: WritingStats
  activities?: Activity[]
  title?: string
  dailyGoal?: number
  weeklyGoal?: number
  monthlyGoal?: number
  bestWritingTime?: string
  averageSessionLength?: number
  averageWPM?: number
}

const props = withDefaults(defineProps<Props>(), {
  stats: () => ({
    totalWords: 0,
    wordsChange: 0,
    dailyAverage: 0,
    sessionsCount: 0,
    totalTime: 0,
    streak: 0,
    bestStreak: 0,
  }),
  activities: () => [],
  dailyGoal: 1000,
  weeklyGoal: 7000,
  monthlyGoal: 30000,
  bestWritingTime: '9:00 AM - 11:00 AM',
  averageSessionLength: 45,
  averageWPM: 35,
})

const emit = defineEmits<{
  'refresh-stats': []
  'export-stats': []
  'set-goals': []
  'edit-goals': []
  'view-detailed': []
}>()

// State
const selectedPeriod = ref('week')
const customStartDate = ref('')
const customEndDate = ref('')

// Computed
const currentStats = computed(() => props.stats || {
  totalWords: 0,
  wordsChange: 0,
  dailyAverage: 0,
  sessionsCount: 0,
  totalTime: 0,
  streak: 0,
  bestStreak: 0,
})

const recentActivities = computed(() =>
  (props.activities || []).slice(0, 5),
)

const timePeriods = [
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'year', label: 'This Year' },
  { value: 'custom', label: 'Custom' },
]

// Helper functions
function formatDuration(minutes: number) {
  if (minutes < 60)
    return `${minutes}m`
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  if (remainingMinutes === 0)
    return `${hours}h`
  return `${hours}h ${remainingMinutes}m`
}

function formatRelativeTime(timestamp: Date | string) {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1)
    return 'Just now'
  if (diffInHours < 24)
    return `${diffInHours}h ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7)
    return `${diffInDays}d ago`

  return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
}

function getActivityIcon(type: string) {
  switch (type) {
    case 'writing': return 'lucide:pen'
    case 'editing': return 'lucide:edit-3'
    case 'planning': return 'lucide:map'
    case 'research': return 'lucide:search'
    default: return 'lucide:activity'
  }
}

// Actions
function refreshStats() {
  emit('refresh-stats')
}

function exportStats() {
  emit('export-stats')
}

function setGoals() {
  emit('set-goals')
}

function editGoals() {
  emit('edit-goals')
}

function viewDetailed() {
  emit('view-detailed')
}
</script>

<template>
  <div class="writing-stats-view">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6 p-4 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
      <div class="flex items-center space-x-3">
        <div class="p-2 rounded-lg bg-purple-100 dark:bg-purple-800">
          <Icon name="lucide:trending-up" class="h-5 w-5 text-purple-600 dark:text-purple-400" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-gray-100">
            {{ title || 'Writing Statistics' }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Track your progress and maintain writing momentum
          </p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <BaseButton
          size="sm"
          variant="outline"
          @click="refreshStats"
        >
          <Icon name="lucide:refresh-cw" class="h-4 w-4 mr-1" />
          Refresh
        </BaseButton>
        <BaseDropdown>
          <BaseButton size="sm" variant="ghost">
            <Icon name="lucide:more-horizontal" class="h-4 w-4" />
          </BaseButton>

          <template #content>
            <BaseDropdownItem @click="exportStats">
              <Icon name="lucide:download" class="h-4 w-4 mr-2" />
              Export Stats
            </BaseDropdownItem>
            <BaseDropdownItem @click="setGoals">
              <Icon name="lucide:target" class="h-4 w-4 mr-2" />
              Set Goals
            </BaseDropdownItem>
            <BaseDropdownItem @click="viewDetailed">
              <Icon name="lucide:bar-chart-3" class="h-4 w-4 mr-2" />
              Detailed Analytics
            </BaseDropdownItem>
          </template>
        </BaseDropdown>
      </div>
    </div>

    <!-- Time Period Selector -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex space-x-2">
        <BaseButton
          v-for="period in timePeriods"
          :key="period.value"
          size="sm"
          :variant="selectedPeriod === period.value ? 'filled' : 'ghost'"
          @click="selectedPeriod = period.value"
        >
          {{ period.label }}
        </BaseButton>
      </div>

      <!-- Custom Date Range -->
      <div v-if="selectedPeriod === 'custom'" class="flex items-center space-x-2">
        <BaseInput
          v-model="customStartDate"
          type="date"
          size="sm"
          placeholder="Start date"
        />
        <span class="text-gray-400">to</span>
        <BaseInput
          v-model="customEndDate"
          type="date"
          size="sm"
          placeholder="End date"
        />
      </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <!-- Total Words -->
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Total Words
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ currentStats.totalWords.toLocaleString() }}
            </p>
          </div>
          <div class="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
            <Icon name="lucide:type" class="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <div class="flex items-center mt-2">
          <Icon
            :name="currentStats.wordsChange >= 0 ? 'lucide:trending-up' : 'lucide:trending-down'"
            :class="currentStats.wordsChange >= 0 ? 'text-green-500' : 'text-red-500'"
            class="h-4 w-4 mr-1"
          />
          <span
            :class="currentStats.wordsChange >= 0 ? 'text-green-600' : 'text-red-600'"
            class="text-sm font-medium"
          >
            {{ Math.abs(currentStats.wordsChange).toLocaleString() }}
          </span>
          <span class="text-sm text-gray-500 ml-1">this {{ selectedPeriod === 'today' ? 'session' : selectedPeriod }}</span>
        </div>
      </div>

      <!-- Daily Average -->
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Daily Average
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ currentStats.dailyAverage.toLocaleString() }}
            </p>
          </div>
          <div class="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
            <Icon name="lucide:calendar-days" class="h-5 w-5 text-green-600 dark:text-green-400" />
          </div>
        </div>
        <div class="mt-2">
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              class="bg-green-500 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min((currentStats.dailyAverage / (dailyGoal || 1000)) * 100, 100)}%` }"
            />
          </div>
          <p class="text-xs text-gray-500 mt-1">
            Goal: {{ (dailyGoal || 1000).toLocaleString() }} words/day
          </p>
        </div>
      </div>

      <!-- Writing Sessions -->
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Sessions
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ currentStats.sessionsCount }}
            </p>
          </div>
          <div class="p-2 bg-orange-100 dark:bg-orange-800 rounded-lg">
            <Icon name="lucide:clock" class="h-5 w-5 text-orange-600 dark:text-orange-400" />
          </div>
        </div>
        <p class="text-sm text-gray-500 mt-2">
          {{ formatDuration(currentStats.totalTime) }} total time
        </p>
      </div>

      <!-- Streak -->
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              Writing Streak
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ currentStats.streak }}
            </p>
          </div>
          <div class="p-2 bg-yellow-100 dark:bg-yellow-800 rounded-lg">
            <Icon name="lucide:flame" class="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
        <p class="text-sm text-gray-500 mt-2">
          {{ currentStats.streak === 1 ? 'day' : 'days' }} • Best: {{ currentStats.bestStreak }}
        </p>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- Writing Progress Chart -->
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-semibold text-gray-900 dark:text-gray-100">
            Writing Progress
          </h4>
          <BaseButton size="xs" variant="ghost">
            <Icon name="lucide:maximize-2" class="h-3 w-3" />
          </BaseButton>
        </div>

        <!-- Placeholder for chart - replace with actual chart component -->
        <div class="h-48 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
          <div class="text-center text-gray-500">
            <Icon name="lucide:trending-up" class="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p class="text-sm">
              Daily word count chart
            </p>
            <p class="text-xs">
              (Chart component placeholder)
            </p>
          </div>
        </div>
      </div>

      <!-- Session Duration Chart -->
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-semibold text-gray-900 dark:text-gray-100">
            Session Duration
          </h4>
          <BaseButton size="xs" variant="ghost">
            <Icon name="lucide:maximize-2" class="h-3 w-3" />
          </BaseButton>
        </div>

        <!-- Placeholder for chart -->
        <div class="h-48 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
          <div class="text-center text-gray-500">
            <Icon name="lucide:clock" class="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p class="text-sm">
              Writing time analysis
            </p>
            <p class="text-xs">
              (Chart component placeholder)
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Goals & Targets -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <!-- Current Goals -->
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-semibold text-gray-900 dark:text-gray-100">
            Current Goals
          </h4>
          <BaseButton size="xs" variant="ghost" @click="editGoals">
            <Icon name="lucide:edit-3" class="h-3 w-3 mr-1" />
            Edit
          </BaseButton>
        </div>

        <div class="space-y-3">
          <!-- Daily Goal -->
          <div>
            <div class="flex items-center justify-between text-sm mb-1">
              <span class="text-gray-600 dark:text-gray-400">Daily Word Count</span>
              <span class="font-medium">
                {{ (currentStats.todayWords || 0).toLocaleString() }} / {{ (dailyGoal || 1000).toLocaleString() }}
              </span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${Math.min(((currentStats.todayWords || 0) / (dailyGoal || 1000)) * 100, 100)}%` }"
              />
            </div>
          </div>

          <!-- Weekly Goal -->
          <div>
            <div class="flex items-center justify-between text-sm mb-1">
              <span class="text-gray-600 dark:text-gray-400">Weekly Word Count</span>
              <span class="font-medium">
                {{ (currentStats.weeklyWords || 0).toLocaleString() }} / {{ (weeklyGoal || 7000).toLocaleString() }}
              </span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="bg-green-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${Math.min(((currentStats.weeklyWords || 0) / (weeklyGoal || 7000)) * 100, 100)}%` }"
              />
            </div>
          </div>

          <!-- Monthly Goal -->
          <div>
            <div class="flex items-center justify-between text-sm mb-1">
              <span class="text-gray-600 dark:text-gray-400">Monthly Word Count</span>
              <span class="font-medium">
                {{ (currentStats.monthlyWords || 0).toLocaleString() }} / {{ (monthlyGoal || 30000).toLocaleString() }}
              </span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                class="bg-purple-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${Math.min(((currentStats.monthlyWords || 0) / (monthlyGoal || 30000)) * 100, 100)}%` }"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Recent Activity
        </h4>

        <div class="space-y-3">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="flex items-center space-x-3"
          >
            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
              <Icon
                :name="getActivityIcon(activity.type)"
                class="h-4 w-4 text-gray-600 dark:text-gray-400"
              />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                {{ activity.description }}
              </p>
              <p class="text-xs text-gray-500">
                {{ formatRelativeTime(activity.timestamp) }}
              </p>
            </div>
            <div class="flex-shrink-0 text-xs text-gray-500">
              {{ activity.wordCount ? `+${activity.wordCount}` : '' }}
            </div>
          </div>
        </div>

        <div v-if="recentActivities.length === 0" class="text-center py-6 text-gray-500">
          <Icon name="lucide:activity" class="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p class="text-sm">
            No recent activity
          </p>
        </div>
      </div>
    </div>

    <!-- Productivity Insights -->
    <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <h4 class="font-semibold text-gray-900 dark:text-gray-100 mb-4">
        Productivity Insights
      </h4>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Best Writing Time -->
        <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <Icon name="lucide:sun" class="h-6 w-6 mx-auto mb-2 text-yellow-500" />
          <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
            Most Productive Time
          </p>
          <p class="text-xs text-gray-500 mt-1">
            {{ bestWritingTime }}
          </p>
        </div>

        <!-- Average Session -->
        <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <Icon name="lucide:timer" class="h-6 w-6 mx-auto mb-2 text-blue-500" />
          <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
            Avg Session Length
          </p>
          <p class="text-xs text-gray-500 mt-1">
            {{ formatDuration(averageSessionLength) }}
          </p>
        </div>

        <!-- Words per Minute -->
        <div class="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <Icon name="lucide:gauge" class="h-6 w-6 mx-auto mb-2 text-green-500" />
          <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
            Writing Speed
          </p>
          <p class="text-xs text-gray-500 mt-1">
            {{ averageWPM }} WPM
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
