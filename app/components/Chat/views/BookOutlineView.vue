<script setup lang="ts">
interface ChapterOutline {
  chapterNumber: number
  title: string
  summary: string
  keyPoints?: string[]
  themes?: string[]
  notes?: string
  estimatedWordCount?: number
}

interface BookOutline {
  title: string
  totalChapters: number
  genre?: string
  targetAudience?: string
  chapters: ChapterOutline[]
}

interface Props {
  outline?: BookOutline
  projectId?: string
  completedChapterNumbers?: number[]
  inProgressChapterNumbers?: number[]
  chapterProgress?: Record<number, number>
  canOpenInEditor?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  completedChapterNumbers: () => [],
  inProgressChapterNumbers: () => [],
  chapterProgress: () => ({}),
  canOpenInEditor: true,
})

const emit = defineEmits<{
  'write-chapter': [chapterNumber: number]
  'view-chapter': [chapterNumber: number]
  'open-editor': [projectId: string]
  'export-outline': [outline: BookOutline]
}>()

// State for expanded chapters
const expandedChapters = ref<Set<number>>(new Set())

// Computed properties
const estimatedReadTime = computed(() => {
  const wordsPerMinute = 250
  const totalWords = totalEstimatedWords.value
  return Math.ceil(totalWords / wordsPerMinute)
})

const totalEstimatedWords = computed(() => {
  return props.outline?.chapters?.reduce((total, chapter) => {
    return total + (chapter.estimatedWordCount || 2000) // Default 2k words per chapter
  }, 0) || 0
})

const completedChapters = computed(() => {
  return props.completedChapterNumbers.length
})

const overallProgress = computed(() => {
  if (!props.outline?.totalChapters)
    return 0
  return (completedChapters.value / props.outline.totalChapters) * 100
})

// Chapter state helpers
function isChapterCompleted(chapterNumber: number) {
  return props.completedChapterNumbers.includes(chapterNumber)
}

function isChapterInProgress(chapterNumber: number) {
  return props.inProgressChapterNumbers.includes(chapterNumber)
}

function isChapterExpanded(chapterNumber: number) {
  return expandedChapters.value.has(chapterNumber)
}

function getChapterProgress(chapterNumber: number) {
  return props.chapterProgress[chapterNumber] || 0
}

// Styling helpers
function getChapterClasses(chapterNumber: number) {
  if (isChapterCompleted(chapterNumber)) {
    return 'bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-800'
  }
  if (isChapterInProgress(chapterNumber)) {
    return 'bg-blue-50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800'
  }
  return 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750'
}

function getChapterNumberClasses(chapterNumber: number) {
  if (isChapterCompleted(chapterNumber)) {
    return 'bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-300'
  }
  if (isChapterInProgress(chapterNumber)) {
    return 'bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-300'
  }
  return 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
}

// Actions
function toggleChapter(chapterNumber: number) {
  if (expandedChapters.value.has(chapterNumber)) {
    expandedChapters.value.delete(chapterNumber)
  }
  else {
    expandedChapters.value.add(chapterNumber)
  }
}

function writeChapter(chapterNumber: number) {
  emit('write-chapter', chapterNumber)
}

function viewChapter(chapterNumber: number) {
  emit('view-chapter', chapterNumber)
}

function openInEditor() {
  if (props.projectId) {
    emit('open-editor', props.projectId)
  }
}

function exportOutline() {
  if (props.outline) {
    emit('export-outline', props.outline)
  }
}
</script>

<template>
  <div class="book-outline-view">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6 p-4 bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-lg border border-primary-200 dark:border-primary-800">
      <div class="flex items-center space-x-3">
        <div class="p-2 rounded-lg bg-primary-100 dark:bg-primary-800">
          <Icon name="lucide:book-open" class="h-5 w-5 text-primary-600 dark:text-primary-400" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-gray-100">
            {{ outline?.title || 'Book Outline' }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ outline?.totalChapters || 0 }} chapters • {{ estimatedReadTime }} min read
          </p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <BaseButton
          v-if="canOpenInEditor"
          size="sm"
          color="primary"
          @click="openInEditor"
        >
          <Icon name="lucide:edit" class="h-4 w-4 mr-1" />
          Edit
        </BaseButton>
        <BaseButton
          size="sm"
          variant="ghost"
          @click="exportOutline"
        >
          <Icon name="lucide:download" class="h-4 w-4" />
        </BaseButton>
      </div>
    </div>

    <!-- Outline Tree -->
    <div class="space-y-2">
      <div
        v-for="(chapter, index) in outline?.chapters || []"
        :key="chapter.chapterNumber || index"
        class="outline-chapter"
        :class="{
          'is-completed': isChapterCompleted(chapter.chapterNumber),
          'is-in-progress': isChapterInProgress(chapter.chapterNumber),
        }"
      >
        <!-- Chapter Header -->
        <div
          class="flex items-start justify-between p-4 rounded-lg border transition-all cursor-pointer hover:shadow-md"
          :class="getChapterClasses(chapter.chapterNumber)"
          @click="toggleChapter(chapter.chapterNumber)"
        >
          <div class="flex items-start space-x-3 flex-1">
            <!-- Chapter Number & Status -->
            <div class="flex items-center space-x-2">
              <div
                class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium"
                :class="getChapterNumberClasses(chapter.chapterNumber)"
              >
                <Icon
                  v-if="isChapterCompleted(chapter.chapterNumber)"
                  name="lucide:check"
                  class="h-4 w-4"
                />
                <Icon
                  v-else-if="isChapterInProgress(chapter.chapterNumber)"
                  name="lucide:pen-tool"
                  class="h-4 w-4"
                />
                <span v-else>{{ chapter.chapterNumber }}</span>
              </div>

              <!-- Expansion Toggle -->
              <button
                class="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                @click.stop="toggleChapter(chapter.chapterNumber)"
              >
                <Icon
                  :name="isChapterExpanded(chapter.chapterNumber) ? 'lucide:chevron-down' : 'lucide:chevron-right'"
                  class="h-4 w-4 text-gray-500"
                />
              </button>
            </div>

            <!-- Chapter Content -->
            <div class="flex-1 min-w-0">
              <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-1">
                {{ chapter.title }}
              </h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                {{ chapter.summary }}
              </p>

              <!-- Chapter Metadata -->
              <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                <span v-if="chapter.estimatedWordCount">
                  {{ chapter.estimatedWordCount?.toLocaleString() }} words
                </span>
                <span v-if="chapter.themes?.length">
                  {{ chapter.themes.length }} themes
                </span>
                <span v-if="getChapterProgress(chapter.chapterNumber)">
                  {{ getChapterProgress(chapter.chapterNumber) }}% complete
                </span>
              </div>
            </div>
          </div>

          <!-- Chapter Actions -->
          <div class="flex items-center space-x-1 ml-4">
            <BaseButton
              v-if="!isChapterCompleted(chapter.chapterNumber)"
              size="xs"
              variant="ghost"
              @click.stop="writeChapter(chapter.chapterNumber)"
            >
              <Icon name="lucide:pen-tool" class="h-3 w-3" />
            </BaseButton>
            <BaseButton
              v-else
              size="xs"
              variant="ghost"
              @click.stop="viewChapter(chapter.chapterNumber)"
            >
              <Icon name="lucide:eye" class="h-3 w-3" />
            </BaseButton>
          </div>
        </div>

        <!-- Expanded Chapter Details -->
        <div
          v-if="isChapterExpanded(chapter.chapterNumber)"
          class="mt-2 ml-11 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700"
        >
          <!-- Key Points -->
          <div v-if="chapter.keyPoints?.length" class="mb-4">
            <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Key Points
            </h5>
            <ul class="space-y-1">
              <li
                v-for="(point, pointIndex) in chapter.keyPoints"
                :key="pointIndex"
                class="flex items-start space-x-2 text-sm text-gray-600 dark:text-gray-400"
              >
                <Icon name="lucide:dot" class="h-4 w-4 mt-0.5 text-primary-500" />
                <span>{{ point }}</span>
              </li>
            </ul>
          </div>

          <!-- Themes -->
          <div v-if="chapter.themes?.length" class="mb-4">
            <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Themes
            </h5>
            <div class="flex flex-wrap gap-1">
              <BaseTag
                v-for="theme in chapter.themes"
                :key="theme"
                size="xs"
                variant="pastel"
                color="primary"
              >
                {{ theme }}
              </BaseTag>
            </div>
          </div>

          <!-- Notes -->
          <div v-if="chapter.notes" class="mb-4">
            <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notes
            </h5>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ chapter.notes }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Stats -->
    <div v-if="outline" class="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ outline.totalChapters }}
          </div>
          <div class="text-xs text-gray-500">
            Chapters
          </div>
        </div>
        <div>
          <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ totalEstimatedWords.toLocaleString() }}
          </div>
          <div class="text-xs text-gray-500">
            Est. Words
          </div>
        </div>
        <div>
          <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ completedChapters }}
          </div>
          <div class="text-xs text-gray-500">
            Completed
          </div>
        </div>
        <div>
          <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ Math.round(overallProgress) }}%
          </div>
          <div class="text-xs text-gray-500">
            Progress
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.outline-chapter.is-completed {
  @apply opacity-90;
}

.outline-chapter.is-in-progress {
  @apply ring-1 ring-blue-200 dark:ring-blue-800;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
