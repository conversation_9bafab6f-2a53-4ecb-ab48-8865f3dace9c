<script setup lang="ts">
interface BookImage {
  id: string
  title?: string
  prompt?: string
  url?: string
  type: 'cover' | 'chapter' | 'scene' | 'character' | 'illustration' | 'diagram'
  chapterNumber?: number
  status: 'pending' | 'generating' | 'completed' | 'error'
  createdAt?: Date | string
  metadata?: {
    width?: number
    height?: number
    format?: string
    size?: number
  }
}

interface Props {
  images: BookImage[]
  title?: string
  projectId?: string
}

const props = withDefaults(defineProps<Props>(), {
  images: () => [],
})

const emit = defineEmits<{
  'generate-image': [image?: BookImage]
  'generate-new': []
  'download-image': [image: BookImage]
  'download-all': []
  'delete-image': [image: BookImage]
  'export-gallery': []
  'organize-by-chapter': []
}>()

// State
const viewMode = ref<'grid' | 'list'>('grid')
const selectedType = ref<string>('')
const selectedChapter = ref<string>('')
const selectedStatus = ref<string>('')
const showImageModal = ref(false)
const selectedImage = ref<BookImage | null>(null)

// Computed properties
const generateCount = computed(() =>
  props.images.filter(img => img.status === 'completed').length,
)

const filteredImages = computed(() => {
  let filtered = props.images

  if (selectedType.value) {
    filtered = filtered.filter(img => img.type === selectedType.value)
  }

  if (selectedChapter.value) {
    filtered = filtered.filter(img =>
      selectedChapter.value === 'no-chapter'
        ? !img.chapterNumber
        : img.chapterNumber === Number.parseInt(selectedChapter.value),
    )
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(img => img.status === selectedStatus.value)
  }

  return filtered
})

const typeFilterOptions = computed(() => [
  { value: '', label: 'All Types' },
  { value: 'cover', label: 'Cover' },
  { value: 'chapter', label: 'Chapter' },
  { value: 'scene', label: 'Scene' },
  { value: 'character', label: 'Character' },
  { value: 'illustration', label: 'Illustration' },
  { value: 'diagram', label: 'Diagram' },
])

const chapterFilterOptions = computed(() => {
  const chapters = [...new Set(props.images.map(img => img.chapterNumber).filter(Boolean))]
  return [
    { value: '', label: 'All Chapters' },
    { value: 'no-chapter', label: 'No Chapter' },
    ...chapters.sort((a, b) => a! - b!).map(ch => ({
      value: ch!.toString(),
      label: `Chapter ${ch}`,
    })),
  ]
})

const statusFilterOptions = computed(() => [
  { value: '', label: 'All Status' },
  { value: 'completed', label: 'Completed' },
  { value: 'generating', label: 'Generating' },
  { value: 'pending', label: 'Pending' },
  { value: 'error', label: 'Error' },
])

// Helper functions
function getImageTypeLabel(type?: string) {
  switch (type) {
    case 'cover': return 'Book Cover'
    case 'chapter': return 'Chapter Image'
    case 'scene': return 'Scene'
    case 'character': return 'Character'
    case 'illustration': return 'Illustration'
    case 'diagram': return 'Diagram'
    default: return 'Image'
  }
}

function getStatusColor(status?: string) {
  switch (status) {
    case 'completed': return 'success'
    case 'generating': return 'info'
    case 'pending': return 'warning'
    case 'error': return 'danger'
    default: return 'default'
  }
}

function getStatusText(status?: string) {
  switch (status) {
    case 'completed': return 'Complete'
    case 'generating': return 'Generating'
    case 'pending': return 'Pending'
    case 'error': return 'Error'
    default: return 'Unknown'
  }
}

function formatDate(date?: Date | string) {
  if (!date)
    return 'Unknown'
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Actions
function generateImage(image: BookImage) {
  emit('generate-image', image)
}

function generateNewImage() {
  emit('generate-new')
}

function downloadImage(image: BookImage) {
  emit('download-image', image)
}

function downloadAll() {
  emit('download-all')
}

function deleteImage(image: BookImage) {
  emit('delete-image', image)
  if (selectedImage.value?.id === image.id) {
    showImageModal.value = false
    selectedImage.value = null
  }
}

function openImageModal(image: BookImage) {
  selectedImage.value = image
  showImageModal.value = true
}

function exportGallery() {
  emit('export-gallery')
}

function organizeByChapter() {
  emit('organize-by-chapter')
}
</script>

<template>
  <div class="image-gallery-view">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800">
      <div class="flex items-center space-x-3">
        <div class="p-2 rounded-lg bg-green-100 dark:bg-green-800">
          <Icon name="lucide:image" class="h-5 w-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-gray-100">
            {{ title || 'Book Images' }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ images.length }} images • {{ generateCount }} generated
          </p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <BaseButton
          size="sm"
          color="primary"
          @click="generateNewImage"
        >
          <Icon name="lucide:plus" class="h-4 w-4 mr-1" />
          Generate
        </BaseButton>
        <BaseDropdown>
          <BaseButton size="sm" variant="ghost">
            <Icon name="lucide:more-horizontal" class="h-4 w-4" />
          </BaseButton>

          <template #content>
            <BaseDropdownItem @click="downloadAll">
              <Icon name="lucide:download" class="h-4 w-4 mr-2" />
              Download All
            </BaseDropdownItem>
            <BaseDropdownItem @click="exportGallery">
              <Icon name="lucide:archive" class="h-4 w-4 mr-2" />
              Export Gallery
            </BaseDropdownItem>
            <BaseDropdownItem @click="organizeByChapter">
              <Icon name="lucide:folder" class="h-4 w-4 mr-2" />
              Organize by Chapter
            </BaseDropdownItem>
          </template>
        </BaseDropdown>
      </div>
    </div>

    <!-- Filters -->
    <div class="flex items-center justify-between mb-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-4">
        <!-- Type Filter -->
        <BaseListbox
          v-model="selectedType"
          :items="typeFilterOptions"
          placeholder="All Types"
          size="sm"
        />

        <!-- Chapter Filter -->
        <BaseListbox
          v-model="selectedChapter"
          :items="chapterFilterOptions"
          placeholder="All Chapters"
          size="sm"
        />

        <!-- Status Filter -->
        <BaseListbox
          v-model="selectedStatus"
          :items="statusFilterOptions"
          placeholder="All Status"
          size="sm"
        />
      </div>

      <!-- View Options -->
      <div class="flex items-center space-x-2">
        <BaseButton
          size="xs"
          :variant="viewMode === 'grid' ? 'filled' : 'ghost'"
          @click="viewMode = 'grid'"
        >
          <Icon name="lucide:grid-3x3" class="h-3 w-3" />
        </BaseButton>
        <BaseButton
          size="xs"
          :variant="viewMode === 'list' ? 'filled' : 'ghost'"
          @click="viewMode = 'list'"
        >
          <Icon name="lucide:list" class="h-3 w-3" />
        </BaseButton>
      </div>
    </div>

    <!-- Image Grid/List -->
    <div
      v-if="filteredImages.length > 0"
      :class="viewMode === 'grid' ? 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4' : 'space-y-4'"
    >
      <div
        v-for="(image, index) in filteredImages"
        :key="image.id || index"
        class="image-item group relative"
        :class="viewMode === 'list' ? 'flex items-center space-x-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700' : ''"
      >
        <!-- Grid View -->
        <template v-if="viewMode === 'grid'">
          <div class="relative aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <!-- Image -->
            <img
              v-if="image.url"
              :src="image.url"
              :alt="image.title || `Book image ${index + 1}`"
              class="w-full h-full object-cover transition-transform group-hover:scale-105"
              @click="openImageModal(image)"
            >

            <!-- Loading State -->
            <div
              v-else-if="image.status === 'generating'"
              class="w-full h-full flex items-center justify-center"
            >
              <div class="text-center">
                <Icon name="lucide:loader-2" class="h-8 w-8 animate-spin text-gray-400 mx-auto mb-2" />
                <p class="text-xs text-gray-500">
                  Generating...
                </p>
              </div>
            </div>

            <!-- Placeholder -->
            <div
              v-else
              class="w-full h-full flex items-center justify-center cursor-pointer"
              @click="generateImage(image)"
            >
              <div class="text-center">
                <Icon name="lucide:image" class="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p class="text-xs text-gray-500">
                  Click to generate
                </p>
              </div>
            </div>

            <!-- Overlay -->
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-2">
                <BaseButton
                  v-if="image.url"
                  size="xs"
                  variant="filled"
                  color="white"
                  @click.stop="openImageModal(image)"
                >
                  <Icon name="lucide:eye" class="h-3 w-3" />
                </BaseButton>
                <BaseButton
                  v-if="image.url"
                  size="xs"
                  variant="filled"
                  color="white"
                  @click.stop="downloadImage(image)"
                >
                  <Icon name="lucide:download" class="h-3 w-3" />
                </BaseButton>
                <BaseButton
                  size="xs"
                  variant="filled"
                  color="danger"
                  @click.stop="deleteImage(image)"
                >
                  <Icon name="lucide:trash-2" class="h-3 w-3" />
                </BaseButton>
              </div>
            </div>

            <!-- Status Badge -->
            <div
              v-if="image.status"
              class="absolute top-2 left-2"
            >
              <BaseTag
                :color="getStatusColor(image.status)"
                size="xs"
                variant="filled"
              >
                {{ getStatusText(image.status) }}
              </BaseTag>
            </div>

            <!-- Chapter Badge -->
            <div
              v-if="image.chapterNumber"
              class="absolute top-2 right-2"
            >
              <BaseTag
                color="primary"
                size="xs"
                variant="pastel"
              >
                Ch. {{ image.chapterNumber }}
              </BaseTag>
            </div>
          </div>

          <!-- Grid Image Info -->
          <div class="mt-3">
            <h4 class="font-medium text-sm text-gray-900 dark:text-gray-100 line-clamp-2">
              {{ image.title || `Image ${index + 1}` }}
            </h4>
            <p v-if="image.prompt" class="text-xs text-gray-500 mt-1 line-clamp-2">
              {{ image.prompt }}
            </p>
            <div class="flex items-center justify-between mt-2">
              <span class="text-xs text-gray-400">
                {{ getImageTypeLabel(image.type) }}
              </span>
              <span v-if="image.createdAt" class="text-xs text-gray-400">
                {{ formatDate(image.createdAt) }}
              </span>
            </div>
          </div>
        </template>

        <!-- List View -->
        <template v-else>
          <!-- Thumbnail -->
          <div class="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <img
              v-if="image.url"
              :src="image.url"
              :alt="image.title || `Book image ${index + 1}`"
              class="w-full h-full object-cover"
              @click="openImageModal(image)"
            >
            <div
              v-else-if="image.status === 'generating'"
              class="w-full h-full flex items-center justify-center"
            >
              <Icon name="lucide:loader-2" class="h-4 w-4 animate-spin text-gray-400" />
            </div>
            <div
              v-else
              class="w-full h-full flex items-center justify-center cursor-pointer"
              @click="generateImage(image)"
            >
              <Icon name="lucide:image" class="h-4 w-4 text-gray-400" />
            </div>
          </div>

          <!-- List Image Info -->
          <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h4 class="font-medium text-gray-900 dark:text-gray-100">
                  {{ image.title || `Image ${index + 1}` }}
                </h4>
                <p v-if="image.prompt" class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mt-1">
                  {{ image.prompt }}
                </p>
                <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                  <span>{{ getImageTypeLabel(image.type) }}</span>
                  <span v-if="image.chapterNumber">Chapter {{ image.chapterNumber }}</span>
                  <span v-if="image.createdAt">{{ formatDate(image.createdAt) }}</span>
                </div>
              </div>

              <!-- List Actions -->
              <div class="flex items-center space-x-1 ml-4">
                <BaseButton
                  v-if="image.url"
                  size="xs"
                  variant="ghost"
                  @click="openImageModal(image)"
                >
                  <Icon name="lucide:eye" class="h-3 w-3" />
                </BaseButton>
                <BaseButton
                  v-if="image.url"
                  size="xs"
                  variant="ghost"
                  @click="downloadImage(image)"
                >
                  <Icon name="lucide:download" class="h-3 w-3" />
                </BaseButton>
                <BaseButton
                  size="xs"
                  variant="ghost"
                  color="danger"
                  @click="deleteImage(image)"
                >
                  <Icon name="lucide:trash-2" class="h-3 w-3" />
                </BaseButton>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12 text-gray-500">
      <Icon name="lucide:image" class="h-12 w-12 mx-auto mb-4 opacity-50" />
      <h3 class="text-lg font-medium mb-2">
        No images available
      </h3>
      <p class="text-sm mb-4">
        Generate AI images to illustrate your book content.
      </p>
      <BaseButton
        variant="outline"
        @click="generateNewImage"
      >
        <Icon name="lucide:plus" class="h-4 w-4 mr-2" />
        Generate First Image
      </BaseButton>
    </div>

    <!-- Image Modal -->
    <BaseModal v-model="showImageModal" size="xl">
      <template #header>
        <h2 class="text-xl font-semibold">
          {{ selectedImage?.title || 'Book Image' }}
        </h2>
      </template>

      <div v-if="selectedImage" class="space-y-4">
        <!-- Full Size Image -->
        <div class="relative">
          <img
            v-if="selectedImage.url"
            :src="selectedImage.url"
            :alt="selectedImage.title"
            class="w-full max-h-96 object-contain rounded-lg"
          >
        </div>

        <!-- Image Details -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Details
            </h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Type:</span>
                <span>{{ getImageTypeLabel(selectedImage.type) }}</span>
              </div>
              <div v-if="selectedImage.chapterNumber" class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Chapter:</span>
                <span>{{ selectedImage.chapterNumber }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Status:</span>
                <BaseTag :color="getStatusColor(selectedImage.status)" size="xs">
                  {{ getStatusText(selectedImage.status) }}
                </BaseTag>
              </div>
              <div v-if="selectedImage.createdAt" class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">Created:</span>
                <span>{{ formatDate(selectedImage.createdAt) }}</span>
              </div>
            </div>
          </div>

          <div v-if="selectedImage.prompt">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Prompt
            </h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
              {{ selectedImage.prompt }}
            </p>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <BaseButton
            v-if="selectedImage?.url"
            variant="outline"
            @click="downloadImage(selectedImage)"
          >
            <Icon name="lucide:download" class="h-4 w-4 mr-2" />
            Download
          </BaseButton>
          <div class="space-x-2">
            <BaseButton
              v-if="selectedImage"
              variant="outline"
              color="danger"
              @click="deleteImage(selectedImage)"
            >
              <Icon name="lucide:trash-2" class="h-4 w-4 mr-2" />
              Delete
            </BaseButton>
            <BaseButton @click="showImageModal = false">
              Close
            </BaseButton>
          </div>
        </div>
      </template>
    </BaseModal>
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
