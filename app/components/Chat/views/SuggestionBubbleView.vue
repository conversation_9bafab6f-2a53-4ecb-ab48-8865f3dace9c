<script setup lang="ts">
interface Suggestion {
  id: string
  type: 'grammar' | 'style' | 'structure' | 'clarity' | 'flow' | 'content'
  description: string
  before?: string
  after?: string
  reason?: string
  priority: 'low' | 'medium' | 'high'
  confidence?: number
  impact?: 'minor' | 'moderate' | 'significant'
  status: 'pending' | 'applied' | 'dismissed'
  createdAt: Date | string
}

interface Props {
  suggestions: Suggestion[]
  title?: string
  chapterNumber?: number
  projectId?: string
}

const props = withDefaults(defineProps<Props>(), {
  suggestions: () => [],
})

const emit = defineEmits<{
  'apply-suggestion': [suggestion: Suggestion]
  'dismiss-suggestion': [suggestion: Suggestion]
  'undo-suggestion': [suggestion: Suggestion]
  'apply-all': [suggestions: Suggestion[]]
  'dismiss-all': [suggestions: Suggestion[]]
  'generate-more': []
  'export-suggestions': [suggestions: Suggestion[]]
}>()

// State
const applyingId = ref<string | null>(null)
const undoingId = ref<string | null>(null)
const isApplyingAll = ref(false)

// Computed properties
const appliedCount = computed(() =>
  props.suggestions.filter(s => s.status === 'applied').length,
)

const pendingCount = computed(() =>
  props.suggestions.filter(s => s.status === 'pending').length,
)

const dismissedCount = computed(() =>
  props.suggestions.filter(s => s.status === 'dismissed').length,
)

const hasUnappliedSuggestions = computed(() =>
  props.suggestions.some(s => s.status === 'pending'),
)

// Helper functions
function getSuggestionClasses(status?: string) {
  switch (status) {
    case 'applied':
      return 'bg-green-50 dark:bg-green-900/10 border-green-200 dark:border-green-800'
    case 'dismissed':
      return 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 opacity-60'
    case 'pending':
    default:
      return 'bg-white dark:bg-gray-800 border-purple-200 dark:border-purple-700 hover:shadow-md'
  }
}

function getTypeIcon(type: string) {
  switch (type) {
    case 'grammar': return 'lucide:spell-check'
    case 'style': return 'lucide:palette'
    case 'structure': return 'lucide:layers'
    case 'clarity': return 'lucide:eye'
    case 'flow': return 'lucide:git-merge'
    case 'content': return 'lucide:file-text'
    default: return 'lucide:lightbulb'
  }
}

function getTypeIconClasses(type: string) {
  switch (type) {
    case 'grammar': return 'bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400'
    case 'style': return 'bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-400'
    case 'structure': return 'bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-400'
    case 'clarity': return 'bg-green-100 dark:bg-green-800 text-green-600 dark:text-green-400'
    case 'flow': return 'bg-orange-100 dark:bg-orange-800 text-orange-600 dark:text-orange-400'
    case 'content': return 'bg-yellow-100 dark:bg-yellow-800 text-yellow-600 dark:text-yellow-400'
    default: return 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400'
  }
}

function getSuggestionTypeLabel(type: string) {
  switch (type) {
    case 'grammar': return 'Grammar'
    case 'style': return 'Style'
    case 'structure': return 'Structure'
    case 'clarity': return 'Clarity'
    case 'flow': return 'Flow'
    case 'content': return 'Content'
    default: return 'Suggestion'
  }
}

function getPriorityColor(priority: string) {
  switch (priority) {
    case 'high': return 'danger'
    case 'medium': return 'warning'
    case 'low': return 'info'
    default: return 'default'
  }
}

function formatDate(date?: Date | string) {
  if (!date)
    return 'Unknown'
  const d = new Date(date)
  return d.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Actions
async function applySuggestion(suggestion: Suggestion) {
  applyingId.value = suggestion.id
  try {
    emit('apply-suggestion', suggestion)
  }
  finally {
    applyingId.value = null
  }
}

function dismissSuggestion(suggestion: Suggestion) {
  emit('dismiss-suggestion', suggestion)
}

async function undoSuggestion(suggestion: Suggestion) {
  undoingId.value = suggestion.id
  try {
    emit('undo-suggestion', suggestion)
  }
  finally {
    undoingId.value = null
  }
}

async function applyAllSuggestions() {
  isApplyingAll.value = true
  try {
    const pendingSuggestions = props.suggestions.filter(s => s.status === 'pending')
    emit('apply-all', pendingSuggestions)
  }
  finally {
    isApplyingAll.value = false
  }
}

function dismissAllSuggestions() {
  const pendingSuggestions = props.suggestions.filter(s => s.status === 'pending')
  emit('dismiss-all', pendingSuggestions)
}

function generateMoreSuggestions() {
  emit('generate-more')
}

function exportSuggestions() {
  emit('export-suggestions', props.suggestions)
}
</script>

<template>
  <div class="suggestion-bubble-view">
    <!-- Header -->
    <div class="flex items-center justify-between mb-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
      <div class="flex items-center space-x-3">
        <div class="p-2 rounded-lg bg-purple-100 dark:bg-purple-800">
          <Icon name="lucide:lightbulb" class="h-5 w-5 text-purple-600 dark:text-purple-400" />
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 dark:text-gray-100">
            {{ title || 'AI Suggestions' }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            {{ suggestions.length }} suggestions • {{ appliedCount }} applied
          </p>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <BaseButton
          v-if="hasUnappliedSuggestions"
          size="sm"
          color="primary"
          :loading="isApplyingAll"
          @click="applyAllSuggestions"
        >
          <Icon name="lucide:check-circle" class="h-4 w-4 mr-1" />
          Apply All
        </BaseButton>
        <BaseDropdown>
          <BaseButton size="sm" variant="ghost">
            <Icon name="lucide:more-horizontal" class="h-4 w-4" />
          </BaseButton>

          <template #content>
            <BaseDropdownItem @click="dismissAllSuggestions">
              <Icon name="lucide:x-circle" class="h-4 w-4 mr-2" />
              Dismiss All
            </BaseDropdownItem>
            <BaseDropdownItem @click="generateMoreSuggestions">
              <Icon name="lucide:refresh-cw" class="h-4 w-4 mr-2" />
              Generate More
            </BaseDropdownItem>
            <BaseDropdownItem @click="exportSuggestions">
              <Icon name="lucide:download" class="h-4 w-4 mr-2" />
              Export Suggestions
            </BaseDropdownItem>
          </template>
        </BaseDropdown>
      </div>
    </div>

    <!-- Suggestions List -->
    <div class="space-y-3">
      <div
        v-for="(suggestion, index) in suggestions"
        :key="suggestion.id || index"
        class="suggestion-item"
        :class="{
          'is-applied': suggestion.status === 'applied',
          'is-dismissed': suggestion.status === 'dismissed',
          'is-pending': suggestion.status === 'pending',
        }"
      >
        <!-- Suggestion Card -->
        <div
          class="p-4 rounded-lg border transition-all"
          :class="getSuggestionClasses(suggestion.status)"
        >
          <!-- Suggestion Header -->
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-start space-x-3 flex-1">
              <!-- Type Icon -->
              <div
                class="p-2 rounded-lg"
                :class="getTypeIconClasses(suggestion.type)"
              >
                <Icon
                  :name="getTypeIcon(suggestion.type)"
                  class="h-4 w-4"
                />
              </div>

              <!-- Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2 mb-1">
                  <h4 class="font-medium text-gray-900 dark:text-gray-100">
                    {{ getSuggestionTypeLabel(suggestion.type) }}
                  </h4>
                  <BaseTag
                    :color="getPriorityColor(suggestion.priority)"
                    size="xs"
                    variant="pastel"
                  >
                    {{ suggestion.priority }}
                  </BaseTag>
                </div>

                <p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  {{ suggestion.description }}
                </p>

                <!-- Before/After Preview -->
                <div v-if="suggestion.before && suggestion.after" class="mt-3 space-y-2">
                  <div class="p-3 bg-red-50 dark:bg-red-900/10 rounded border border-red-200 dark:border-red-800">
                    <div class="text-xs font-medium text-red-700 dark:text-red-300 mb-1">
                      Before:
                    </div>
                    <div class="text-sm text-red-800 dark:text-red-200 font-mono">
                      {{ suggestion.before }}
                    </div>
                  </div>
                  <div class="p-3 bg-green-50 dark:bg-green-900/10 rounded border border-green-200 dark:border-green-800">
                    <div class="text-xs font-medium text-green-700 dark:text-green-300 mb-1">
                      After:
                    </div>
                    <div class="text-sm text-green-800 dark:text-green-200 font-mono">
                      {{ suggestion.after }}
                    </div>
                  </div>
                </div>

                <!-- Suggestion Reason -->
                <div v-if="suggestion.reason" class="mt-2 p-2 bg-blue-50 dark:bg-blue-900/10 rounded border border-blue-200 dark:border-blue-800">
                  <div class="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">
                    Why this helps:
                  </div>
                  <div class="text-xs text-blue-800 dark:text-blue-200">
                    {{ suggestion.reason }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center space-x-1 ml-4">
              <BaseButton
                v-if="suggestion.status === 'pending'"
                size="xs"
                color="success"
                :loading="applyingId === suggestion.id"
                @click="applySuggestion(suggestion)"
              >
                <Icon name="lucide:check" class="h-3 w-3" />
              </BaseButton>
              <BaseButton
                v-if="suggestion.status === 'pending'"
                size="xs"
                variant="ghost"
                color="danger"
                @click="dismissSuggestion(suggestion)"
              >
                <Icon name="lucide:x" class="h-3 w-3" />
              </BaseButton>
              <BaseButton
                v-if="suggestion.status === 'applied'"
                size="xs"
                variant="ghost"
                :loading="undoingId === suggestion.id"
                @click="undoSuggestion(suggestion)"
              >
                <Icon name="lucide:undo" class="h-3 w-3" />
              </BaseButton>
            </div>
          </div>

          <!-- Status Indicator -->
          <div class="flex items-center justify-between text-xs">
            <div class="flex items-center space-x-2 text-gray-500">
              <span>{{ formatDate(suggestion.createdAt) }}</span>
              <span v-if="suggestion.confidence">
                • {{ Math.round(suggestion.confidence * 100) }}% confidence
              </span>
              <span v-if="suggestion.impact">
                • {{ suggestion.impact }} impact
              </span>
            </div>

            <!-- Status Badge -->
            <div class="flex items-center space-x-2">
              <div
                v-if="suggestion.status === 'applied'"
                class="flex items-center space-x-1 text-green-600 dark:text-green-400"
              >
                <Icon name="lucide:check-circle" class="h-3 w-3" />
                <span>Applied</span>
              </div>
              <div
                v-else-if="suggestion.status === 'dismissed'"
                class="flex items-center space-x-1 text-gray-500"
              >
                <Icon name="lucide:x-circle" class="h-3 w-3" />
                <span>Dismissed</span>
              </div>
              <div
                v-else
                class="flex items-center space-x-1 text-blue-600 dark:text-blue-400"
              >
                <Icon name="lucide:clock" class="h-3 w-3" />
                <span>Pending</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="suggestions.length === 0" class="text-center py-12 text-gray-500">
      <Icon name="lucide:lightbulb" class="h-12 w-12 mx-auto mb-4 opacity-50" />
      <h3 class="text-lg font-medium mb-2">
        No suggestions available
      </h3>
      <p class="text-sm mb-4">
        AI suggestions will appear here to help improve your content.
      </p>
      <BaseButton
        variant="outline"
        @click="generateMoreSuggestions"
      >
        <Icon name="lucide:refresh-cw" class="h-4 w-4 mr-2" />
        Generate Suggestions
      </BaseButton>
    </div>

    <!-- Summary Stats -->
    <div v-if="suggestions.length > 0" class="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div>
          <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ suggestions.length }}
          </div>
          <div class="text-xs text-gray-500">
            Total
          </div>
        </div>
        <div>
          <div class="text-lg font-semibold text-green-600 dark:text-green-400">
            {{ appliedCount }}
          </div>
          <div class="text-xs text-gray-500">
            Applied
          </div>
        </div>
        <div>
          <div class="text-lg font-semibold text-blue-600 dark:text-blue-400">
            {{ pendingCount }}
          </div>
          <div class="text-xs text-gray-500">
            Pending
          </div>
        </div>
        <div>
          <div class="text-lg font-semibold text-gray-500">
            {{ dismissedCount }}
          </div>
          <div class="text-xs text-gray-500">
            Dismissed
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.suggestion-item.is-applied {
  @apply opacity-90;
}

.suggestion-item.is-dismissed {
  @apply opacity-60;
}

.suggestion-item.is-pending {
  @apply transform hover:scale-[1.02] transition-transform;
}
</style>
