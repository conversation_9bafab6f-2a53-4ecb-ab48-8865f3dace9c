<script setup lang="ts">
import type { AgentConfig, ChatMode, ToolConfig } from '~/types/chat'
import type { AIProvider } from '~/types/ui'

interface Props {
  mode: ChatMode
  selectedAgents: AgentConfig[]
  availableTools: ToolConfig[]
  availableProviders: AIProvider[]
  maxAgents?: number
  showAgentCap?: boolean
}

interface Emits {
  'update:selected-agents': [agents: AgentConfig[]]
  'update:mode': [mode: ChatMode]
}

const props = defineProps<Props>()
const emits = defineEmits<Emits>()

const isOpen = ref(false)

// Predefined agent templates
const agentTemplates: AgentConfig[] = [
  {
    id: 'analyst',
    name: 'Business Analyst',
    description: 'Analyzes data and provides business insights',
    systemPrompt: 'You are a skilled business analyst. Focus on providing clear, data-driven insights and actionable recommendations.',
    provider: 'openai',
    modelName: 'gpt-4',
    tools: ['search', 'computation'],
    integrationIds: [],
    avatar: '📊',
    color: 'success',
  },
  {
    id: 'writer',
    name: 'Content Writer',
    description: 'Creates high-quality written content',
    systemPrompt: 'You are a professional content writer. Create engaging, well-structured content that matches the requested tone and style.',
    provider: 'openai',
    modelName: 'gpt-4',
    tools: ['search'],
    integrationIds: [],
    avatar: '✍️',
    color: 'primary',
  },
  {
    id: 'researcher',
    name: 'Research Assistant',
    description: 'Conducts thorough research and fact-checking',
    systemPrompt: 'You are a meticulous researcher. Provide comprehensive, well-sourced information and verify facts carefully.',
    provider: 'openai',
    modelName: 'gpt-4',
    tools: ['search', 'api'],
    integrationIds: [],
    avatar: '🔍',
    color: 'info',
  },
  {
    id: 'developer',
    name: 'Code Assistant',
    description: 'Helps with programming and technical tasks',
    systemPrompt: 'You are an expert software developer. Write clean, efficient code and provide clear technical explanations.',
    provider: 'openai',
    modelName: 'gpt-4',
    tools: ['file', 'computation'],
    integrationIds: [],
    avatar: '💻',
    color: 'warning',
  },
]

// Local state
const selectedMode = computed({
  get: () => props.mode,
  set: value => emits('update:mode', value),
})

const selectedAgentIds = ref<string[]>(
  props.selectedAgents.map(agent => agent.id),
)

// Watch for prop changes
watch(() => props.selectedAgents, (newAgents) => {
  selectedAgentIds.value = newAgents.map(agent => agent.id)
}, { deep: true })

const supervisorPrompt = ref('')

// Computed
const isSingleMode = computed(() => selectedMode.value === 'single')
const isMultiMode = computed(() => selectedMode.value === 'multi')

const maxAgentsForMode = computed(() => {
  if (isSingleMode.value)
    return 1
  return props.maxAgents ?? 10 // Default to 10 agents if no limit specified
})

const availableAgents = computed(() => {
  return agentTemplates.filter(agent =>
    props.availableProviders.includes(agent.provider),
  )
})

const canAddMoreAgents = computed(() =>
  selectedAgentIds.value.length < maxAgentsForMode.value,
)

// Methods
function toggleAgent(agentId: string) {
  if (selectedAgentIds.value.includes(agentId)) {
    selectedAgentIds.value = selectedAgentIds.value.filter(id => id !== agentId)
  }
  else if (canAddMoreAgents.value) {
    if (isSingleMode.value) {
      // Replace current agent in single mode
      selectedAgentIds.value = [agentId]
    }
    else {
      // Add agent in multi mode
      selectedAgentIds.value.push(agentId)
    }
  }

  updateSelectedAgents()
}

function updateSelectedAgents() {
  const agents = selectedAgentIds.value
    .map(id => agentTemplates.find(agent => agent.id === id))
    .filter(Boolean) as AgentConfig[]

  emits('update:selected-agents', agents)
}

function createCustomAgent() {
  // TODO: Implement custom agent creation modal
  console.log('Create custom agent modal')
}

function duplicateAgent(agent: AgentConfig) {
  const duplicate: AgentConfig = {
    ...agent,
    id: `${agent.id}_${Date.now()}`,
    name: `${agent.name} (Copy)`,
  }

  agentTemplates.push(duplicate)
  toggleAgent(duplicate.id)
}

function removeAgent(agentId: string) {
  selectedAgentIds.value = selectedAgentIds.value.filter(id => id !== agentId)
  updateSelectedAgents()
}

function getAgentByIcon(agentId: string) {
  return agentTemplates.find(agent => agent.id === agentId)
}

// Handle mode change
watch(selectedMode, (newMode) => {
  if (newMode === 'single' && selectedAgentIds.value.length > 1) {
    // Keep only the first agent when switching to single mode
    selectedAgentIds.value = [selectedAgentIds.value[0]]
    updateSelectedAgents()
  }
})

// Initialize with default agent if none selected
onMounted(() => {
  if (selectedAgentIds.value.length === 0 && availableAgents.value.length > 0) {
    selectedAgentIds.value = [availableAgents.value[0].id]
    updateSelectedAgents()
  }
})
</script>

<template>
  <div class="space-y-4">
    <!-- Mode Selection -->
    <div>
      <BaseText size="sm" weight="medium" class="mb-3">
        Chat Mode
      </BaseText>
      <div class="grid grid-cols-2 gap-3">
        <BaseRadio
          v-model="selectedMode"
          value="single"
          name="chat-mode"
          class="group"
        >
          <div class="flex items-center gap-3 rounded-lg border p-3 transition-colors group-hover:bg-muted-50 dark:group-hover:bg-muted-800/50">
            <Icon name="lucide:user" class="size-5 text-primary-500" />
            <div>
              <div class="text-sm font-medium">
                Single Agent
              </div>
              <div class="text-xs text-muted-500">
                Chat with one AI assistant
              </div>
            </div>
          </div>
        </BaseRadio>

        <BaseRadio
          v-model="selectedMode"
          value="multi"
          name="chat-mode"
          class="group"
        >
          <div class="flex items-center gap-3 rounded-lg border p-3 transition-colors group-hover:bg-muted-50 dark:group-hover:bg-muted-800/50">
            <Icon name="lucide:users" class="size-5 text-success-500" />
            <div>
              <div class="text-sm font-medium">
                Multi-Agent
              </div>
              <div class="text-xs text-muted-500">
                Orchestrated team collaboration
              </div>
            </div>
          </div>
        </BaseRadio>
      </div>
    </div>

    <!-- Agent Selection -->
    <div>
      <div class="flex items-center justify-between mb-3">
        <BaseText size="sm" weight="medium">
          {{ isSingleMode ? 'Select Agent' : 'Select Agents' }}
        </BaseText>
        <div class="flex items-center gap-2">
          <BaseText size="xs" class="text-muted-500">
            {{ selectedAgentIds.length }} / {{ maxAgentsForMode }} selected
          </BaseText>
          <BaseTooltip
            v-if="props.showAgentCap && selectedAgentIds.length >= maxAgentsForMode"
            content="You've reached the maximum number of agents allowed"
          >
            <BaseBadge color="warning" size="xs" variant="outline">
              Limit reached
            </BaseBadge>
          </BaseTooltip>
        </div>
      </div>

      <div class="grid grid-cols-1 gap-2">
        <div
          v-for="agent in availableAgents"
          :key="agent.id"
          class="group relative"
        >
          <label
            class="flex cursor-pointer items-center gap-3 rounded-lg border p-3 transition-colors"
            :class="[
              selectedAgentIds.includes(agent.id)
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-muted-200 dark:border-muted-700 hover:bg-muted-50 dark:hover:bg-muted-800/50',
            ]"
          >
            <input
              type="checkbox"
              :checked="selectedAgentIds.includes(agent.id)"
              :disabled="!canAddMoreAgents && !selectedAgentIds.includes(agent.id)"
              class="hidden"
              @change="toggleAgent(agent.id)"
            >

            <!-- Agent Avatar -->
            <div
              class="flex size-10 shrink-0 items-center justify-center rounded-lg"
              :class="`bg-${agent.color}-500`"
            >
              <span class="text-lg">{{ agent.avatar }}</span>
            </div>

            <!-- Agent Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2">
                <BaseText size="sm" weight="medium">{{ agent.name }}</BaseText>
                <BaseBadge
                  :color="agent.provider === 'openai' ? 'success' : 'primary'"
                  size="xs"
                  variant="pastel"
                >
                  {{ agent.provider }}
                </BaseBadge>
              </div>
              <BaseText size="xs" class="text-muted-500 mt-1">
                {{ agent.description }}
              </BaseText>
              <div class="flex flex-wrap gap-1 mt-2">
                <BaseBadge
                  v-for="tool in agent.tools"
                  :key="tool"
                  size="xs"
                  variant="outline"
                >
                  {{ tool }}
                </BaseBadge>
              </div>
            </div>

            <!-- Selection Indicator -->
            <div
              class="flex size-5 items-center justify-center rounded-full border-2 transition-colors"
              :class="[
                selectedAgentIds.includes(agent.id)
                  ? 'border-primary-500 bg-primary-500'
                  : 'border-muted-300 dark:border-muted-600',
              ]"
            >
              <Icon
                v-if="selectedAgentIds.includes(agent.id)"
                name="lucide:check"
                class="size-3 text-white"
              />
            </div>
          </label>

          <!-- Agent actions -->
          <div class="absolute top-2 right-2 flex gap-1 opacity-0 transition-opacity group-hover:opacity-100">
            <BaseTooltip content="Duplicate agent">
              <button
                type="button"
                class="flex size-6 items-center justify-center rounded-md bg-white hover:bg-muted-100 dark:bg-muted-800 dark:hover:bg-muted-700 border border-muted-200 dark:border-muted-600 transition-colors"
                @click.stop="duplicateAgent(agent)"
              >
                <Icon name="lucide:copy" class="size-3" />
              </button>
            </BaseTooltip>
          </div>
        </div>
      </div>

      <!-- Add custom agent button -->
      <button
        type="button"
        class="flex w-full items-center justify-center gap-2 rounded-lg border-2 border-dashed border-muted-300 dark:border-muted-600 p-4 text-muted-500 hover:border-primary-400 hover:text-primary-500 transition-colors"
        @click="createCustomAgent"
      >
        <Icon name="lucide:plus" class="size-4" />
        <span class="text-sm">Create Custom Agent</span>
      </button>
    </div>

    <!-- Multi-agent supervisor prompt -->
    <div v-if="isMultiMode && selectedAgentIds.length > 1">
      <BaseText size="sm" weight="medium" class="mb-3">
        Supervisor Prompt
      </BaseText>
      <BaseTextarea
        v-model="supervisorPrompt"
        placeholder="Describe how agents should collaborate and hand off tasks..."
        rows="3"
      />
    </div>

    <!-- Selected agents preview -->
    <div v-if="selectedAgentIds.length > 0">
      <BaseText size="sm" weight="medium" class="mb-3">
        {{ isSingleMode ? 'Selected Agent' : 'Agent Team' }}
      </BaseText>
      <div class="flex flex-wrap gap-2">
        <div
          v-for="agentId in selectedAgentIds"
          :key="agentId"
          class="flex items-center gap-2 rounded-lg border border-muted-200 dark:border-muted-700 bg-muted-50 dark:bg-muted-800 px-3 py-2"
        >
          <span class="text-sm">{{ getAgentByIcon(agentId)?.avatar }}</span>
          <BaseText size="sm">
            {{ getAgentByIcon(agentId)?.name }}
          </BaseText>
          <button
            type="button"
            class="flex size-4 items-center justify-center rounded-full hover:bg-muted-200 dark:hover:bg-muted-700 transition-colors"
            @click="removeAgent(agentId)"
          >
            <Icon name="lucide:x" class="size-3" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
