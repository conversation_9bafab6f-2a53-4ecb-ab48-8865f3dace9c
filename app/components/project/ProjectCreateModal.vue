<script setup lang="ts">
import type { CreateProjectInput, Project } from '~/types/project'
import { toTypedSchema } from '@vee-validate/zod'
import { useForm } from 'vee-validate'
import { z } from 'zod'
import { projectStatusSchema } from '~/types/project'

interface Props {
  open: boolean
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'created', project: Project): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { createProject } = useProjects()
const { show } = useToaster()

// Form validation schema - simplified for essential fields
const projectCreateSchema = z.object({
  name: z.string()
    .min(1, 'Project name is required')
    .max(100, 'Project name must be less than 100 characters'),
  description: z.string().optional(),
  status: projectStatusSchema.default('planning'),
  customer: z.object({
    name: z.string()
      .min(1, 'Customer name is required')
      .max(100, 'Customer name must be less than 100 characters'),
    logo: z.string().url().optional().or(z.literal('')),
    text: z.string().optional(), // Industry/description
    location: z.string().optional(),
    website: z.string().url().optional().or(z.literal('')),
    contact_email: z.string().email().optional().or(z.literal('')),
    contact_phone: z.string().optional(),
  }).optional(),
  category: z.string().optional(),
  color: z.string().optional(),
})

type FormData = z.infer<typeof projectCreateSchema>

// Form setup
const validationSchema = toTypedSchema(projectCreateSchema)
const initialValues: FormData = {
  name: '',
  description: '',
  status: 'planning',
  customer: {
    name: '',
    logo: '',
    text: '',
    location: '',
    website: '',
    contact_email: '',
    contact_phone: '',
  },
  category: '',
  color: '#3b82f6',
}

const {
  handleSubmit,
  isSubmitting,
  resetForm,
  errors,
  values,
} = useForm({
  validationSchema,
  initialValues,
})

// Submit handler
const onSubmit = handleSubmit(async (formData) => {
  try {
    // Prepare project data for creation
    const projectData: CreateProjectInput = {
      name: formData.name,
      description: formData.description || undefined,
      status: formData.status,
      category: formData.category || undefined,
      color: formData.color || undefined,
      customer: formData.customer?.name
        ? {
            name: formData.customer.name,
            logo: formData.customer.logo || undefined,
            text: formData.customer.text || undefined,
            location: formData.customer.location || undefined,
            website: formData.customer.website || undefined,
            contact_email: formData.customer.contact_email || undefined,
            contact_phone: formData.customer.contact_phone || undefined,
          }
        : undefined,
    }

    // Create the project
    const newProject = await createProject(projectData, {
      showSuccessToast: true,
      generateEmbeddings: true,
    })

    // Emit success and close modal
    emit('created', newProject)
    emit('close')
    resetForm()
  }
  catch (error: any) {
    console.error('Failed to create project:', error)
    show({
      title: 'Error',
      message: error.message || 'Failed to create project. Please try again.',
      color: 'danger',
      icon: 'lucide:alert-circle',
      closable: true,
    })
  }
})

// Reset form when modal closes
watch(() => props.open, (isOpen) => {
  if (!isOpen) {
    resetForm()
  }
})

// Project status options
const statusOptions = [
  { value: 'planning', label: 'Planning', icon: 'lucide:lightbulb', color: 'text-yellow-600' },
  { value: 'active', label: 'Active', icon: 'lucide:play', color: 'text-blue-600' },
  { value: 'paused', label: 'Paused', icon: 'lucide:pause', color: 'text-orange-600' },
  { value: 'completed', label: 'Completed', icon: 'lucide:check-circle', color: 'text-green-600' },
  { value: 'cancelled', label: 'Cancelled', icon: 'lucide:x-circle', color: 'text-red-600' },
]

// Project colors for visual identification
const colorOptions = [
  '#3b82f6',
  '#ef4444',
  '#10b981',
  '#f59e0b',
  '#8b5cf6',
  '#ec4899',
  '#06b6d4',
  '#84cc16',
]
</script>

<template>
  <BaseModal
    :open="open"
    size="lg"
    @close="emit('close')"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <div class="flex size-12 items-center justify-center rounded-lg bg-primary-100 dark:bg-primary-800/20">
          <Icon
            name="lucide:folder-plus"
            class="size-6 text-primary-600 dark:text-primary-400"
          />
        </div>
        <div>
          <BaseHeading as="h3" size="lg" weight="semibold">
            Create New Project
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-500">
            Add a new project to your workspace
          </BaseParagraph>
        </div>
      </div>
    </template>

    <form class="space-y-6" @submit.prevent="onSubmit">
      <!-- Project Basic Info -->
      <div class="space-y-4">
        <div>
          <BaseLabel for="projectName" class="mb-2">
            Project Name *
          </BaseLabel>
          <TairoInput
            id="projectName"
            v-model="values.name"
            type="text"
            icon="lucide:folder"
            placeholder="Enter project name..."
            :error="!!errors.name"
            :disabled="isSubmitting || loading"
          />
          <BaseParagraph
            v-if="errors.name"
            size="xs"
            class="text-destructive-500 mt-1"
          >
            {{ errors.name }}
          </BaseParagraph>
        </div>

        <div>
          <BaseLabel for="projectDescription" class="mb-2">
            Description
          </BaseLabel>
          <BaseTextarea
            id="projectDescription"
            v-model="values.description"
            placeholder="Describe your project (optional)..."
            rows="3"
            :error="!!errors.description"
            :disabled="isSubmitting || loading"
          />
          <BaseParagraph
            v-if="errors.description"
            size="xs"
            class="text-destructive-500 mt-1"
          >
            {{ errors.description }}
          </BaseParagraph>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <BaseLabel for="projectStatus" class="mb-2">
              Status
            </BaseLabel>
            <BaseSelect
              id="projectStatus"
              v-model="values.status"
              :error="!!errors.status"
              :disabled="isSubmitting || loading"
            >
              <BaseSelectItem
                v-for="status in statusOptions"
                :key="status.value"
                :value="status.value"
              >
                <div class="flex items-center gap-2">
                  <Icon :name="status.icon" class="size-4" :class="status.color" />
                  {{ status.label }}
                </div>
              </BaseSelectItem>
            </BaseSelect>
            <BaseParagraph
              v-if="errors.status"
              size="xs"
              class="text-destructive-500 mt-1"
            >
              {{ errors.status }}
            </BaseParagraph>
          </div>

          <div>
            <BaseLabel for="projectCategory" class="mb-2">
              Category
            </BaseLabel>
            <TairoInput
              id="projectCategory"
              v-model="values.category"
              type="text"
              icon="lucide:tag"
              placeholder="e.g., Web Development"
              :disabled="isSubmitting || loading"
            />
          </div>
        </div>

        <div>
          <BaseLabel class="mb-3">
            Project Color
          </BaseLabel>
          <div class="flex gap-2 flex-wrap">
            <button
              v-for="color in colorOptions"
              :key="color"
              type="button"
              class="size-8 rounded-full border-2 border-transparent focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all"
              :class="{ 'ring-2 ring-primary-500 ring-offset-2': values.color === color }"
              :style="{ backgroundColor: color }"
              :disabled="isSubmitting || loading"
              @click="values.color = color"
            />
          </div>
        </div>
      </div>

      <!-- Customer Info -->
      <div class="border-t pt-6">
        <BaseHeading as="h4" size="sm" weight="medium" class="mb-4">
          Customer Information
        </BaseHeading>
        <div class="space-y-4">
          <div>
            <BaseLabel for="customerName" class="mb-2">
              Customer/Client Name *
            </BaseLabel>
            <TairoInput
              id="customerName"
              v-model="values.customer!.name"
              type="text"
              icon="lucide:building-2"
              placeholder="Enter customer name..."
              :error="!!errors['customer.name']"
              :disabled="isSubmitting || loading"
            />
            <BaseParagraph
              v-if="errors['customer.name']"
              size="xs"
              class="text-destructive-500 mt-1"
            >
              {{ errors['customer.name'] }}
            </BaseParagraph>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <BaseLabel for="customerIndustry" class="mb-2">
                Industry/Type
              </BaseLabel>
              <TairoInput
                id="customerIndustry"
                v-model="values.customer!.text"
                type="text"
                icon="lucide:briefcase"
                placeholder="e.g., Technology, Healthcare"
                :disabled="isSubmitting || loading"
              />
            </div>

            <div>
              <BaseLabel for="customerLocation" class="mb-2">
                Location
              </BaseLabel>
              <TairoInput
                id="customerLocation"
                v-model="values.customer!.location"
                type="text"
                icon="lucide:map-pin"
                placeholder="e.g., New York, USA"
                :disabled="isSubmitting || loading"
              />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <BaseLabel for="customerWebsite" class="mb-2">
                Website
              </BaseLabel>
              <TairoInput
                id="customerWebsite"
                v-model="values.customer!.website"
                type="url"
                icon="lucide:globe"
                placeholder="https://example.com"
                :error="!!errors['customer.website']"
                :disabled="isSubmitting || loading"
              />
              <BaseParagraph
                v-if="errors['customer.website']"
                size="xs"
                class="text-destructive-500 mt-1"
              >
                {{ errors['customer.website'] }}
              </BaseParagraph>
            </div>

            <div>
              <BaseLabel for="customerEmail" class="mb-2">
                Contact Email
              </BaseLabel>
              <TairoInput
                id="customerEmail"
                v-model="values.customer!.contact_email"
                type="email"
                icon="lucide:mail"
                placeholder="<EMAIL>"
                :error="!!errors['customer.contact_email']"
                :disabled="isSubmitting || loading"
              />
              <BaseParagraph
                v-if="errors['customer.contact_email']"
                size="xs"
                class="text-destructive-500 mt-1"
              >
                {{ errors['customer.contact_email'] }}
              </BaseParagraph>
            </div>
          </div>
        </div>
      </div>
    </form>

    <template #footer>
      <div class="flex justify-end gap-3">
        <BaseButton
          variant="outline"
          :disabled="isSubmitting || loading"
          @click="emit('close')"
        >
          Cancel
        </BaseButton>
        <BaseButton
          variant="primary"
          :disabled="isSubmitting || loading"
          :loading="isSubmitting || loading"
          @click="onSubmit"
        >
          <Icon
            v-if="isSubmitting || loading"
            name="lucide:loader-2"
            class="size-4 animate-spin mr-2"
          />
          Create Project
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
