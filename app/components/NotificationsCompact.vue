<script setup lang="ts">
import type { NotificationsCompactProps } from '../app/types/ui'

const props = withDefaults(
  defineProps<NotificationsCompactProps>(),
  {
    categories: () => [
      {
        id: 1,
        name: 'Personal',
        count: 12,
        link: '#',
      },
      {
        id: 2,
        name: 'Business',
        count: 31,
        link: '#',
      },
      {
        id: 3,
        name: 'Family',
        count: 4,
        link: '#',
      },
    ],
    title: 'Notifications',
    showTitle: true,
    baseLinkPrefix: '#',
  },
)
</script>

<template>
  <div>
    <div v-if="props.showTitle" class="mb-4 flex items-center justify-between">
      <BaseHeading
        as="h3"
        size="sm"
        weight="medium"
        lead="tight"
        class="text-muted-900 dark:text-white"
      >
        <span>{{ props.title }}</span>
      </BaseHeading>
    </div>
    <div>
      <ul class="space-y-3">
        <li v-for="category in props.categories" :key="category.id || category.name">
          <NuxtLink :to="category.link || props.baseLinkPrefix" class="group flex items-center justify-between">
            <BaseParagraph size="sm">
              <span
                class="text-muted-500 dark:text-muted-400 group-hover:text-primary-500 transition-colors duration-300"
              >
                {{ category.name }}
              </span>
            </BaseParagraph>
            <div
              class="bg-muted-200 dark:bg-muted-700 text-muted-500 dark:text-muted-200 flex size-7 items-center justify-center rounded-full text-xs"
            >
              <span>{{ category.count }}</span>
            </div>
          </NuxtLink>
        </li>
      </ul>
    </div>
  </div>
</template>
