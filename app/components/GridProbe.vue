<script setup lang="ts">
// Test component to reproduce grid issues in main app
const items = Array.from({ length: 6 }, (_, i) => ({
  id: i + 1,
  name: `App Item ${i + 1}`,
  color: 'bg-primary-500',
}))
</script>

<template>
  <div class="p-4 border border-blue-500 rounded-lg">
    <h2 class="text-lg font-bold mb-4 text-blue-700">
      Grid Test - Main App Component
    </h2>

    <!-- This should create a 3-column grid -->
    <div class="grid grid-cols-3 gap-4 mb-4">
      <div
        v-for="item in items"
        :key="item.id"
        :class="item.color"
        class="p-4 text-white text-center rounded"
      >
        {{ item.name }}
      </div>
    </div>

    <!-- Alternative grid layouts to test -->
    <div class="grid grid-cols-2 gap-2 mb-4">
      <div class="bg-green-500 p-2 text-white text-center rounded">
        2-Col A
      </div>
      <div class="bg-green-600 p-2 text-white text-center rounded">
        2-Col B
      </div>
    </div>

    <!-- Test col-span -->
    <div class="grid grid-cols-4 gap-2">
      <div class="col-span-2 bg-orange-500 p-2 text-white text-center rounded">
        Span 2 cols
      </div>
      <div class="bg-orange-600 p-2 text-white text-center rounded">
        Col 1
      </div>
      <div class="bg-orange-700 p-2 text-white text-center rounded">
        Col 2
      </div>
    </div>
  </div>
</template>
