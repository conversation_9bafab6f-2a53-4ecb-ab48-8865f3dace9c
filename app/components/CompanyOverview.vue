<script setup lang="ts">
import type { CompanyOverviewProps } from '../app/types/ui'

const props = withDefaults(
  defineProps<CompanyOverviewProps>(),
  {
    company: () => ({
      name: 'Slicer Learning',
      logo: '/img/icons/logos/slicer.svg',
      industry: 'Online courses',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed fac ista esse non inportuna.',
      stats: {
        posts: 864,
        projects: 247,
        followers: '19k',
      },
    }),
    avatarSize: 'xl',
    showAddButton: true,
    maxDescriptionWidth: 320,
  },
)
</script>

<template>
  <div class="flex h-full flex-col">
    <div class="relative mx-auto mb-4 size-20">
      <BaseAvatar :size="props.avatarSize" :src="props.company.logo" />
      <div v-if="props.showAddButton" class="absolute bottom-0 end-0">
        <BaseButton size="icon-sm" rounded="full">
          <Icon name="lucide:plus" />
        </BaseButton>
      </div>
    </div>
    <div class="text-center">
      <BaseHeading
        size="md"
        weight="medium"
        class="text-muted-900 dark:text-muted-100"
      >
        <span>{{ props.company.name }}</span>
      </BaseHeading>
      <BaseParagraph size="xs" class="text-muted-600 dark:text-muted-400 mb-2">
        <span>{{ props.company.industry }}</span>
      </BaseParagraph>
      <BaseParagraph
        size="sm"
        class="text-muted-500 dark:text-muted-400 mx-auto"
        :style="{ maxWidth: `${props.maxDescriptionWidth}px` }"
      >
        <span>
          {{ props.company.description }}
        </span>
      </BaseParagraph>
    </div>
    <div
      class="border-muted-200 dark:border-muted-700 mt-auto border-t pt-6 text-center"
    >
      <div
        class="divide-muted-200 dark:divide-muted-700 flex w-full items-center divide-x"
      >
        <!-- Item -->
        <div class="flex-1">
          <div class="flex flex-col px-4 text-center">
            <h4
              class="text-muted-900 dark:text-muted-100 font-sans text-xl font-semibold"
            >
              {{ props.company.stats.posts }}
            </h4>
            <p
              class="font-sans font-semibold text-muted-400 text-[0.65rem] uppercase"
            >
              Posts
            </p>
          </div>
        </div>
        <!-- Item -->
        <div class="flex-1">
          <div class="flex flex-col px-4 text-center">
            <h4
              class="text-muted-900 dark:text-muted-100 font-sans text-xl font-semibold"
            >
              {{ props.company.stats.projects }}
            </h4>
            <p
              class="text-muted-400 font-sans text-[0.65rem] font-semibold uppercase"
            >
              Projects
            </p>
          </div>
        </div>
        <!-- Item -->
        <div class="flex-1">
          <div class="flex flex-col px-4 text-center">
            <h4
              class="text-muted-900 dark:text-muted-100 font-sans text-xl font-semibold"
            >
              {{ props.company.stats.followers }}
            </h4>
            <p
              class="font-sans font-semibold text-muted-400 text-[0.65rem] uppercase"
            >
              Followers
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
