<script setup lang="ts">
interface Props {
  /**
   * Show text label alongside indicator
   */
  showLabel?: boolean
  /**
   * Size variant
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Show last sync time
   */
  showLastSync?: boolean
  /**
   * Custom connection status (overrides real-time status)
   */
  status?: 'connected' | 'loading' | 'error' | 'disconnected'
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: true,
  size: 'md',
  showLastSync: false,
})

// Use real-time notifications status if no custom status provided
const { isConnected, isLoading, error, lastSyncTime } = props.status
  ? {
      isConnected: computed(() => props.status === 'connected'),
      isLoading: computed(() => props.status === 'loading'),
      error: computed(() => props.status === 'error' ? 'Connection error' : null),
      lastSyncTime: ref(null),
    }
  : useRealTimeNotifications()

// Computed status
const connectionStatus = computed(() => {
  if (props.status)
    return props.status
  if (isLoading.value)
    return 'loading'
  if (error.value)
    return 'error'
  if (!isConnected.value)
    return 'disconnected'
  return 'connected'
})

// Status messages
const statusMessages = {
  connected: 'Live updates enabled',
  loading: 'Connecting...',
  error: 'Connection error',
  disconnected: 'Offline',
}

// Status colors
const statusColors = {
  connected: 'bg-green-500',
  loading: 'bg-yellow-500',
  error: 'bg-red-500',
  disconnected: 'bg-gray-500',
}

// Size classes
const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'w-2 h-2'
    case 'lg':
      return 'w-4 h-4'
    default: // md
      return 'w-3 h-3'
  }
})

// Text size classes
const textSizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'text-xs'
    case 'lg':
      return 'text-base'
    default: // md
      return 'text-sm'
  }
})
</script>

<template>
  <div class="flex items-center gap-2">
    <!-- Status indicator dot -->
    <div
      class="rounded-full flex-shrink-0 transition-colors duration-200"
      :class="[sizeClasses, statusColors[connectionStatus]]"
      :title="statusMessages[connectionStatus]"
    >
      <!-- Pulsing animation for loading state -->
      <div
        v-if="connectionStatus === 'loading'"
        class="w-full h-full rounded-full animate-pulse"
        :class="statusColors[connectionStatus]"
      />
    </div>

    <!-- Status text -->
    <div
      v-if="showLabel"
      class="text-muted-600 dark:text-muted-400 flex items-center gap-2"
      :class="textSizeClasses"
    >
      <span>{{ statusMessages[connectionStatus] }}</span>

      <!-- Last sync time -->
      <span
        v-if="showLastSync && lastSyncTime"
        class="text-xs text-muted-500"
      >
        • Last updated {{ lastSyncTime.toLocaleTimeString() }}
      </span>
    </div>
  </div>
</template>

<style scoped>
/* Additional pulse animation */
@keyframes pulse-dot {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-dot {
  animation: pulse-dot 2s infinite;
}
</style>
