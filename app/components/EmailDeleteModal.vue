<script setup lang="ts">
import type { EmailMessage } from '~/types/email'

interface Props {
  /** Whether the modal is open */
  modelValue?: boolean
  /** Email messages to delete (can be single or multiple) */
  emails?: EmailMessage[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', messageIds: string[]): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  emails: () => [],
})

const emit = defineEmits<Emits>()

// Computed properties
const isOpen = computed({
  get: () => {
    console.log('EmailDeleteModal - isOpen getter called, modelValue:', props.modelValue)
    return props.modelValue
  },
  set: (value: boolean) => {
    console.log('EmailDeleteModal - isOpen setter called with:', value)
    emit('update:modelValue', value)
  },
})

const emailCount = computed(() => props.emails.length)

const displayEmails = computed(() => {
  // Show up to 3 email subjects for preview
  return props.emails.slice(0, 3)
})

const hasMoreEmails = computed(() => props.emails.length > 3)

const remainingCount = computed(() => props.emails.length - 3)

// Modal actions
function handleCancel() {
  isOpen.value = false
}

function handleConfirm() {
  const messageIds = props.emails.map(email => email.id)
  emit('confirm', messageIds)
  isOpen.value = false
}

// Extract subject or fallback text for display
function getEmailSubject(email: EmailMessage): string {
  return email.subject || 'No subject'
}

// Extract sender name for display
function getSenderName(email: EmailMessage): string {
  return email.from?.name || email.from?.email || 'Unknown sender'
}
</script>

<template>
  <BaseModal
    v-model="isOpen"
    size="md"
    :close-on-backdrop="false"
    :close-on-escape="false"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
          <Icon
            name="lucide:trash-2"
            class="h-5 w-5 text-red-600 dark:text-red-400"
          />
        </div>
        <div>
          <h3 class="text-lg font-semibold text-muted-900 dark:text-white">
            Delete {{ emailCount === 1 ? 'email' : 'emails' }}
          </h3>
          <p class="text-sm text-muted-500 dark:text-muted-400">
            {{ emailCount === 1 ? 'This action cannot be undone' : `This will delete ${emailCount} emails` }}
          </p>
        </div>
      </div>
    </template>

    <div class="space-y-4">
      <!-- Warning message -->
      <div class="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800/50 dark:bg-red-900/20">
        <div class="flex items-start gap-3">
          <Icon
            name="lucide:alert-triangle"
            class="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0"
          />
          <div>
            <p class="text-sm font-medium text-red-800 dark:text-red-200">
              Are you sure you want to delete {{ emailCount === 1 ? 'this email' : 'these emails' }}?
            </p>
            <p class="mt-1 text-sm text-red-700 dark:text-red-300">
              {{ emailCount === 1
                ? 'This email will be moved to trash and can be recovered from there.'
                : `These ${emailCount} emails will be moved to trash and can be recovered from there.`
              }}
            </p>
          </div>
        </div>
      </div>

      <!-- Email preview list -->
      <div v-if="emailCount > 0" class="space-y-2">
        <h4 class="text-sm font-medium text-muted-700 dark:text-muted-300">
          {{ emailCount === 1 ? 'Email to delete:' : 'Emails to delete:' }}
        </h4>

        <div class="space-y-2 max-h-48 overflow-y-auto">
          <div
            v-for="email in displayEmails"
            :key="email.id"
            class="flex items-start gap-3 rounded-lg border border-muted-200 bg-muted-50 p-3 dark:border-muted-700 dark:bg-muted-800"
          >
            <!-- Sender avatar -->
            <div class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-br from-primary-400 to-primary-600">
              <span class="text-xs font-medium text-white">
                {{ getSenderName(email).charAt(0).toUpperCase() }}
              </span>
            </div>

            <!-- Email details -->
            <div class="min-w-0 flex-1">
              <p class="truncate text-sm font-medium text-muted-900 dark:text-white">
                {{ getEmailSubject(email) }}
              </p>
              <p class="truncate text-xs text-muted-500 dark:text-muted-400">
                From: {{ getSenderName(email) }}
              </p>
            </div>
          </div>

          <!-- Show remaining count if there are more emails -->
          <div
            v-if="hasMoreEmails"
            class="flex items-center justify-center rounded-lg border-2 border-dashed border-muted-300 bg-muted-50/50 p-3 dark:border-muted-600 dark:bg-muted-800/50"
          >
            <p class="text-sm text-muted-600 dark:text-muted-400">
              and {{ remainingCount }} more {{ remainingCount === 1 ? 'email' : 'emails' }}...
            </p>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <BaseButton
          variant="outline"
          size="sm"
          @click="handleCancel"
        >
          <Icon name="lucide:x" class="h-4 w-4 mr-2" />
          Cancel
        </BaseButton>
        <BaseButton
          variant="solid"
          color="danger"
          size="sm"
          @click="handleConfirm"
        >
          <Icon name="lucide:trash-2" class="h-4 w-4 mr-2" />
          Delete {{ emailCount === 1 ? 'email' : `${emailCount} emails` }}
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>
