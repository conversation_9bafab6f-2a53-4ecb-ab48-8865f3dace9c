<script setup lang="ts" generic="T = Record<string, any>">
interface TableColumn<T = Record<string, any>> {
  key: keyof T
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (value: any, item: T, index: number) => any
  headerClass?: string
  cellClass?: string
}

interface TableProps<T> {
  columns: TableColumn<T>[]
  data: T[]
  loading?: boolean
  sortBy?: keyof T
  sortDirection?: 'asc' | 'desc'
  pagination?: {
    page: number
    limit: number
    total: number
  }
  selectable?: boolean
  selectedItems?: T[]
  emptyMessage?: string
  skeletonRows?: number
  condensed?: boolean
  rounded?: 'none' | 'sm' | 'md' | 'lg'
  spaced?: boolean
}

interface TableEmits<T> {
  (e: 'sort', column: keyof T, direction: 'asc' | 'desc'): void
  (e: 'page-change', page: number): void
  (e: 'selection-change', selectedItems: T[]): void
  (e: 'row-click', item: T, index: number): void
}

const props = withDefaults(defineProps<TableProps<T>>(), {
  loading: false,
  sortBy: undefined,
  sortDirection: 'asc',
  pagination: undefined,
  selectable: false,
  selectedItems: () => [],
  emptyMessage: 'No data available',
  skeletonRows: 5,
  condensed: false,
  rounded: 'md',
  spaced: false,
})

const emit = defineEmits<TableEmits<T>>()

// Computed properties
const headerLabels = computed(() => {
  const labels = []
  if (props.selectable) {
    labels.push('')
  }
  return labels.concat(props.columns.map(col => col.label))
})

const sortedData = computed(() => {
  if (!props.sortBy)
    return props.data

  const column = props.columns.find(col => col.key === props.sortBy)
  if (!column?.sortable)
    return props.data

  return [...props.data].sort((a, b) => {
    const aVal = a[props.sortBy as keyof T]
    const bVal = b[props.sortBy as keyof T]

    if (aVal === bVal)
      return 0
    if (aVal == null)
      return 1
    if (bVal == null)
      return -1

    const comparison = aVal < bVal ? -1 : 1
    return props.sortDirection === 'desc' ? -comparison : comparison
  })
})

const internalSelectedItems = ref<T[]>([...(props.selectedItems || [])])

// Watch for external changes to selectedItems
watch(
  () => props.selectedItems,
  (newSelection) => {
    internalSelectedItems.value = [...(newSelection || [])]
  },
  { deep: true },
)

// Methods
function handleSort(column: TableColumn<T>) {
  if (!column.sortable)
    return

  const newDirection
    = props.sortBy === column.key && props.sortDirection === 'asc'
      ? 'desc'
      : 'asc'

  emit('sort', column.key, newDirection)
}

function handleRowSelection(item: T, selected: boolean) {
  if (selected) {
    if (!internalSelectedItems.value.includes(item)) {
      internalSelectedItems.value.push(item)
    }
  }
  else {
    const index = internalSelectedItems.value.indexOf(item)
    if (index > -1) {
      internalSelectedItems.value.splice(index, 1)
    }
  }
  emit('selection-change', internalSelectedItems.value)
}

function handleSelectAll(selected: boolean) {
  if (selected) {
    internalSelectedItems.value = [...sortedData.value]
  }
  else {
    internalSelectedItems.value = []
  }
  emit('selection-change', internalSelectedItems.value)
}

function isRowSelected(item: T) {
  return internalSelectedItems.value.includes(item)
}

function handleRowClick(item: T, index: number) {
  emit('row-click', item, index)
}

function getCellValue(item: T, column: TableColumn<T>, index: number) {
  if (column.render) {
    return column.render(item[column.key], item, index)
  }
  return item[column.key]
}

const allSelected = computed(() => {
  return sortedData.value.length > 0
    && internalSelectedItems.value.length === sortedData.value.length
})

const someSelected = computed(() => {
  return internalSelectedItems.value.length > 0
    && internalSelectedItems.value.length < sortedData.value.length
})
</script>

<template>
  <div class="relative w-full">
    <!-- Loading overlay -->
    <div
      v-if="loading"
      class="absolute inset-0 bg-white/50 dark:bg-muted-900/50 backdrop-blur-sm z-10 flex items-center justify-center"
    >
      <div class="flex items-center gap-3">
        <Icon name="nui-icon:spiner" class="size-6 animate-spin text-primary-500" />
        <span class="text-muted-600 dark:text-muted-400 font-medium">Loading...</span>
      </div>
    </div>

    <!-- Table Header -->
    <FlexTableWrapper :head="headerLabels">
      <!-- Data Rows -->
      <template v-if="!loading && sortedData.length > 0">
        <FlexTableRow
          v-for="(item, index) in sortedData"
          :key="`row-${index}`"
          :rounded="rounded"
          :spaced="spaced"
          :condensed="condensed"
          class="cursor-pointer hover:bg-muted-50 dark:hover:bg-muted-900/50 transition-colors"
          @click="handleRowClick(item, index)"
        >
          <template #start>
            <div class="flex items-center gap-4 w-full">
              <!-- Selection checkbox -->
              <div v-if="selectable" class="flex-shrink-0">
                <BaseCheckbox
                  :model-value="isRowSelected(item)"
                  @update:model-value="(selected) => handleRowSelection(item, selected)"
                />
              </div>

              <!-- First column content -->
              <div class="flex-1 min-w-0">
                <slot
                  name="cell"
                  :item="item"
                  :column="columns[0]"
                  :value="getCellValue(item, columns[0], index)"
                  :index="index"
                >
                  <div
                    v-if="columns[0]"
                    :class="[
                      columns[0].align === 'center' ? 'text-center'
                      : columns[0].align === 'right' ? 'text-right' : 'text-left',
                      columns[0].cellClass,
                    ]"
                  >
                    {{ getCellValue(item, columns[0], index) }}
                  </div>
                </slot>
              </div>
            </div>
          </template>

          <template #end>
            <!-- Additional columns -->
            <div class="flex items-center gap-4">
              <FlexTableCell
                v-for="(column, colIndex) in columns.slice(1)"
                :key="`col-${colIndex}`"
                :label="column.label"
                class="min-w-0"
              >
                <slot
                  name="cell"
                  :item="item"
                  :column="column"
                  :value="getCellValue(item, column, index)"
                  :index="index"
                >
                  <div
                    :class="[
                      column.align === 'center' ? 'text-center'
                      : column.align === 'right' ? 'text-right' : 'text-left',
                      column.cellClass,
                    ]"
                  >
                    {{ getCellValue(item, column, index) }}
                  </div>
                </slot>
              </FlexTableCell>
            </div>
          </template>
        </FlexTableRow>
      </template>

      <!-- Skeleton Loading Rows -->
      <template v-else-if="loading">
        <FlexTableRow
          v-for="i in skeletonRows"
          :key="`skeleton-${i}`"
          :rounded="rounded"
          :spaced="spaced"
          :condensed="condensed"
        >
          <template #start>
            <div class="flex items-center gap-4 w-full">
              <div v-if="selectable" class="flex-shrink-0">
                <div class="h-4 w-4 bg-muted-200 dark:bg-muted-800 rounded animate-pulse" />
              </div>
              <div class="flex-1">
                <div class="h-4 bg-muted-200 dark:bg-muted-800 rounded animate-pulse w-3/4" />
              </div>
            </div>
          </template>

          <template #end>
            <div class="flex items-center gap-4">
              <FlexTableCell
                v-for="(column, colIndex) in columns.slice(1)"
                :key="`skeleton-col-${colIndex}`"
                :label="column.label"
              >
                <div class="h-4 bg-muted-200 dark:bg-muted-800 rounded animate-pulse w-full" />
              </FlexTableCell>
            </div>
          </template>
        </FlexTableRow>
      </template>

      <!-- Empty State -->
      <template v-else>
        <div class="py-12 text-center">
          <div class="flex flex-col items-center justify-center">
            <Icon
              name="lucide:inbox"
              class="size-12 text-muted-400 dark:text-muted-600 mb-4"
            />
            <BaseHeading
              as="h3"
              size="lg"
              weight="medium"
              class="text-muted-800 dark:text-muted-200 mb-2"
            >
              No data available
            </BaseHeading>
            <BaseParagraph
              size="sm"
              class="text-muted-500 dark:text-muted-400 max-w-xs"
            >
              {{ emptyMessage }}
            </BaseParagraph>
          </div>
        </div>
      </template>
    </FlexTableWrapper>

    <!-- Table Header with Sorting (Custom implementation over FlexTableWrapper) -->
    <div v-if="!loading && sortedData.length > 0" class="mb-2 flex items-center border-b border-muted-200 dark:border-muted-800 pb-2">
      <!-- Select All Checkbox -->
      <div v-if="selectable" class="flex items-center gap-2 px-4 shrink-0">
        <BaseCheckbox
          :model-value="allSelected"
          :indeterminate="someSelected"
          @update:model-value="handleSelectAll"
        />
      </div>

      <!-- Column Headers with Sorting -->
      <div
        v-for="(column, index) in columns"
        :key="`header-${index}`"
        class="flex items-center gap-2 px-4 cursor-pointer hover:bg-muted-50 dark:hover:bg-muted-900/50 transition-colors rounded"
        :class="[
          index === 0 ? 'grow' : 'shrink-0',
          column.headerClass,
          column.sortable ? 'cursor-pointer' : 'cursor-default',
        ]"
        @click="handleSort(column)"
      >
        <BaseHeading
          as="h5"
          size="sm"
          weight="semibold"
          lead="none"
          class="text-muted-600 dark:text-muted-400 text-[0.6rem] uppercase"
        >
          {{ column.label }}
        </BaseHeading>

        <!-- Sort Indicator -->
        <div v-if="column.sortable" class="flex flex-col">
          <Icon
            name="lucide:chevron-up"
            class="size-3 transition-colors"
            :class="[
              sortBy === column.key && sortDirection === 'asc'
                ? 'text-primary-500'
                : 'text-muted-300 dark:text-muted-700',
            ]"
          />
          <Icon
            name="lucide:chevron-down"
            class="size-3 -mt-1 transition-colors"
            :class="[
              sortBy === column.key && sortDirection === 'desc'
                ? 'text-primary-500'
                : 'text-muted-300 dark:text-muted-700',
            ]"
          />
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="pagination && pagination.total > pagination.limit" class="flex items-center justify-between mt-4 pt-4 border-t border-muted-200 dark:border-muted-800">
      <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400">
        Showing {{ (pagination.page - 1) * pagination.limit + 1 }} to
        {{ Math.min(pagination.page * pagination.limit, pagination.total) }} of
        {{ pagination.total }} results
      </BaseParagraph>

      <div class="flex items-center gap-2">
        <BaseButton
          size="sm"
          variant="ghost"
          :disabled="pagination.page <= 1"
          @click="emit('page-change', pagination.page - 1)"
        >
          <Icon name="lucide:chevron-left" class="size-4" />
          Previous
        </BaseButton>

        <div class="flex items-center gap-1">
          <template v-for="page in Math.min(Math.ceil(pagination.total / pagination.limit), 7)" :key="page">
            <BaseButton
              v-if="page === pagination.page"
              size="sm"
              variant="primary"
              class="min-w-[2rem]"
            >
              {{ page }}
            </BaseButton>
            <BaseButton
              v-else
              size="sm"
              variant="ghost"
              class="min-w-[2rem]"
              @click="emit('page-change', page)"
            >
              {{ page }}
            </BaseButton>
          </template>
        </div>

        <BaseButton
          size="sm"
          variant="ghost"
          :disabled="pagination.page >= Math.ceil(pagination.total / pagination.limit)"
          @click="emit('page-change', pagination.page + 1)"
        >
          Next
          <Icon name="lucide:chevron-right" class="size-4" />
        </BaseButton>
      </div>
    </div>
  </div>
</template>
