<script setup lang="ts">
import type { ApexOptions } from 'apexcharts'
import { useLazyComponent } from '~/composables/useLazyComponent'

interface ChartTheme {
  primary: string
  secondary: string
  accent: string
  surface: string
  text: string
  textSecondary: string
  border: string
  gridLines: string
}

interface BaseChartProps {
  type: 'line' | 'area' | 'bar' | 'column' | 'pie' | 'donut' | 'radar' | 'scatter' | 'heatmap'
  data: any[]
  categories?: string[]
  height?: number | string
  width?: number | string
  title?: string
  subtitle?: string
  theme?: 'light' | 'dark' | 'auto'
  colors?: string[]
  loading?: boolean
  skeletonHeight?: number
  enableDataLabels?: boolean
  enableLegend?: boolean
  enableTooltip?: boolean
  enableZoom?: boolean
  enableGridLines?: boolean
  stacked?: boolean
  smooth?: boolean
  fill?: 'solid' | 'gradient' | 'pattern'
  borderRadius?: number
  dataLabelPosition?: 'top' | 'center' | 'bottom'
  legendPosition?: 'top' | 'bottom' | 'left' | 'right'
  responsive?: boolean
  animations?: boolean
  customOptions?: Partial<ApexOptions>
}

const props = withDefaults(defineProps<BaseChartProps>(), {
  height: 350,
  width: '100%',
  theme: 'auto',
  colors: undefined,
  loading: false,
  skeletonHeight: 350,
  enableDataLabels: false,
  enableLegend: true,
  enableTooltip: true,
  enableZoom: false,
  enableGridLines: true,
  stacked: false,
  smooth: true,
  fill: 'solid',
  borderRadius: 4,
  dataLabelPosition: 'top',
  legendPosition: 'bottom',
  responsive: true,
  animations: true,
  customOptions: () => ({}),
})

// Emit events
const emit = defineEmits<{
  'data-point-click': [event: any, chartContext: any, config: any]
  'legend-click': [chartContext: any, seriesIndex: number, config: any]
  'marker-click': [event: any, chartContext: any, config: any]
}>()
// Get current color mode
const { $colorMode } = useNuxtApp()
const currentTheme = computed(() => {
  return props.theme === 'auto' ? $colorMode.preference : props.theme
})

// Default color palettes
const defaultColors = {
  light: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'],
  dark: ['#60a5fa', '#34d399', '#fbbf24', '#f87171', '#a78bfa', '#38bdf8', '#a3e635', '#fb923c'],
}

const chartColors = computed(() => {
  if (props.colors)
    return props.colors
  return currentTheme.value === 'dark' ? defaultColors.dark : defaultColors.light
})

// Theme configuration
function getTheme(): ChartTheme {
  if (currentTheme.value === 'dark') {
    return {
      primary: '#60a5fa',
      secondary: '#34d399',
      accent: '#a78bfa',
      surface: '#1f2937',
      text: '#f9fafb',
      textSecondary: '#9ca3af',
      border: '#374151',
      gridLines: '#374151',
    }
  }
  else {
    return {
      primary: '#3b82f6',
      secondary: '#10b981',
      accent: '#8b5cf6',
      surface: '#ffffff',
      text: '#111827',
      textSecondary: '#6b7280',
      border: '#e5e7eb',
      gridLines: '#f3f4f6',
    }
  }
}

// Process data based on chart type
const processedSeries = computed(() => {
  if (!props.data?.length)
    return []

  switch (props.type) {
    case 'pie':
    case 'donut':
      // For pie/donut charts, data should be simple numbers
      return Array.isArray(props.data[0]) ? props.data[0] : props.data

    case 'line':
    case 'area':
    case 'bar':
    case 'column':
    case 'scatter':
      // For other chart types, expect series format
      if (Array.isArray(props.data[0]) && typeof props.data[0][0] === 'object') {
        return props.data
      }
      // Single series data
      return [{
        name: 'Series 1',
        data: props.data,
      }]

    default:
      return props.data
  }
})

// Build ApexCharts options
const chartOptions = computed((): ApexOptions => {
  const theme = getTheme()

  const baseOptions: ApexOptions = {
    chart: {
      type: props.type as any,
      height: props.height,
      width: props.width,
      background: 'transparent',
      foreColor: theme.text,
      fontFamily: 'Inter, ui-sans-serif, system-ui, -apple-system, sans-serif',
      animations: {
        enabled: props.animations,
        easing: 'easeinout',
        speed: 800,
      },
      zoom: {
        enabled: props.enableZoom,
      },
      toolbar: {
        show: props.enableZoom,
        tools: {
          download: true,
          selection: props.enableZoom,
          zoom: props.enableZoom,
          zoomin: props.enableZoom,
          zoomout: props.enableZoom,
          pan: props.enableZoom,
          reset: props.enableZoom,
        },
      },
    },

    colors: chartColors.value,

    title: props.title
      ? {
          text: props.title,
          align: 'left',
          style: {
            fontSize: '18px',
            fontWeight: '600',
            color: theme.text,
          },
        }
      : undefined,

    subtitle: props.subtitle
      ? {
          text: props.subtitle,
          align: 'left',
          style: {
            fontSize: '14px',
            fontWeight: '400',
            color: theme.textSecondary,
          },
        }
      : undefined,

    legend: {
      show: props.enableLegend,
      position: props.legendPosition as any,
      labels: {
        colors: theme.textSecondary,
      },
    },

    dataLabels: {
      enabled: props.enableDataLabels,
      style: {
        colors: [theme.text],
      },
    },

    tooltip: {
      enabled: props.enableTooltip,
      theme: currentTheme.value,
      style: {
        fontSize: '12px',
      },
    },

    grid: {
      show: props.enableGridLines,
      borderColor: theme.gridLines,
      strokeDashArray: 2,
    },

    xaxis: props.categories
      ? {
          categories: props.categories,
          labels: {
            style: {
              colors: theme.textSecondary,
            },
          },
        }
      : undefined,

    yaxis: {
      labels: {
        style: {
          colors: [theme.textSecondary],
        },
      },
    },

    stroke: {
      curve: props.smooth ? 'smooth' : 'straight',
      width: props.type === 'line' ? 3 : 1,
    },

    fill: {
      type: props.fill === 'gradient' ? 'gradient' : 'solid',
      gradient: props.fill === 'gradient'
        ? {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.9,
          }
        : undefined,
    },

    plotOptions: {
      bar: {
        borderRadius: props.borderRadius,
        dataLabels: {
          position: props.dataLabelPosition as any,
        },
        horizontal: props.type === 'bar',
      },
      pie: {
        donut: {
          size: props.type === 'donut' ? '70%' : '0%',
        },
      },
    },

    responsive: props.responsive
      ? [
          {
            breakpoint: 640,
            options: {
              chart: {
                height: Math.min(Number(props.height), 250),
              },
              legend: {
                position: 'bottom',
              },
            },
          },
        ]
      : undefined,
  }

  // Handle stacked charts
  if (props.stacked) {
    baseOptions.chart!.stacked = true
    baseOptions.plotOptions!.bar = {
      ...baseOptions.plotOptions?.bar,
      horizontal: props.type === 'bar',
    }
  }

  // Merge custom options
  return {
    ...baseOptions,
    ...props.customOptions,
  }
})

// Lazy load chart component with skeleton
const {
  component: ChartComponent,
  loading: chartLoading,
  error: chartError,
} = useLazyComponent(
  () => import('~/components/AddonApexcharts.vue'),
  {
    skeleton: {
      component: () => import('~/components/Chat/blocks/SkeletonBlock.vue'),
      props: {
        type: 'chart',
        height: props.skeletonHeight,
      },
    },
  },
)

function handleDataPointSelection(event: any, chartContext: any, config: any) {
  emit('data-point-click', event, chartContext, config)
}

function handleLegendClick(chartContext: any, seriesIndex: number, config: any) {
  emit('legend-click', chartContext, seriesIndex, config)
}

function handleMarkerClick(event: any, chartContext: any, config: any) {
  emit('marker-click', event, chartContext, config)
}
</script>

<template>
  <div class="w-full">
    <!-- Error State -->
    <div v-if="chartError" class="flex items-center justify-center p-8 bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg">
      <div class="text-center">
        <Icon name="lucide:alert-triangle" class="size-12 text-danger-500 mx-auto mb-4" />
        <BaseHeading as="h3" size="lg" weight="medium" class="text-danger-700 dark:text-danger-400 mb-2">
          Failed to load chart
        </BaseHeading>
        <BaseParagraph size="sm" class="text-danger-600 dark:text-danger-300 max-w-sm">
          There was an error loading the chart component. Please try refreshing the page.
        </BaseParagraph>
      </div>
    </div>

    <!-- Loading or Skeleton State -->
    <div v-else-if="loading || chartLoading" class="w-full">
      <SkeletonBlock type="chart" :height="skeletonHeight" />
    </div>

    <!-- Chart Component -->
    <Suspense v-else>
      <ChartComponent
        :type="type"
        :height="height"
        :width="width"
        :series="processedSeries"
        :options="chartOptions"
        @data-point-selection="handleDataPointSelection"
        @legend-click="handleLegendClick"
        @marker-click="handleMarkerClick"
      />

      <!-- Fallback loading state -->
      <template #fallback>
        <SkeletonBlock type="chart" :height="skeletonHeight" />
      </template>
    </Suspense>

    <!-- Empty State -->
    <div v-if="!loading && !chartLoading && !chartError && (!processedSeries || processedSeries.length === 0)" class="flex items-center justify-center p-12 bg-muted-50 dark:bg-muted-900/50 border border-muted-200 dark:border-muted-800 rounded-lg">
      <div class="text-center">
        <Icon name="lucide:bar-chart" class="size-12 text-muted-400 dark:text-muted-600 mx-auto mb-4" />
        <BaseHeading as="h3" size="lg" weight="medium" class="text-muted-800 dark:text-muted-200 mb-2">
          No data available
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-500 dark:text-muted-400 max-w-sm">
          There's no data to display in this chart yet.
        </BaseParagraph>
      </div>
    </div>
  </div>
</template>
