<script setup lang="ts" generic="T = Record<string, any>">
interface FormField {
  key: string
  label: string
  type?: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'range' | 'url' | 'custom'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  min?: number | string
  max?: number | string
  step?: number | string
  options?: Array<{ value: any, label: string }>
  validation?: (value: any) => string | null
  description?: string
  colspan?: 1 | 2 // For grid layout
}

interface FormModalProps<T> {
  open: boolean
  title: string
  data?: T | null
  fields: FormField[]
  loading?: boolean
  size?: 'sm' | 'md' | 'lg' | 'xl'
  submitText?: string
  cancelText?: string
  showCancel?: boolean
  preventCloseOnSubmit?: boolean
  gridColumns?: 1 | 2
}

interface FormModalEmits<T> {
  (e: 'close'): void
  (e: 'submit', data: T): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<FormModalProps<T>>(), {
  data: null,
  loading: false,
  size: 'md',
  submitText: 'Save',
  cancelText: 'Cancel',
  showCancel: true,
  preventCloseOnSubmit: false,
  gridColumns: 1,
})

const emit = defineEmits<FormModalEmits<T>>()

// Form state
const form = reactive<Record<string, any>>({})
const errors = reactive<Record<string, string>>({})
const isSubmitting = ref(false)
const submitError = ref<string>('')
const fieldRefs = ref<Record<string, any>>({})

// Computed properties
const editing = computed(() => !!props.data)

const isFormValid = computed(() => {
  // Check if all required fields have values
  const hasRequiredFields = props.fields.every((field) => {
    if (!field.required)
      return true
    const value = form[field.key]
    return value !== null && value !== undefined && value !== ''
  })

  // Check if there are no validation errors
  const hasNoErrors = Object.keys(errors).length === 0

  return hasRequiredFields && hasNoErrors
})

// Initialize form
function initializeForm() {
  // Clear form
  Object.keys(form).forEach(key => delete form[key])

  // Set form values from props.data or initialize with defaults
  props.fields.forEach((field) => {
    const defaultValue = getDefaultValue(field)
    form[field.key] = props.data?.[field.key] ?? defaultValue
  })

  // Clear errors
  clearErrors()
}

function getDefaultValue(field: FormField) {
  switch (field.type) {
    case 'number':
    case 'range':
      return Number(field.min || 0)
    case 'checkbox':
      return false
    case 'select':
      return field.options?.[0]?.value || ''
    default:
      return ''
  }
}

function clearErrors() {
  Object.keys(errors).forEach(key => delete errors[key])
  submitError.value = ''
}

// Validation
function validateField(field: FormField) {
  const value = form[field.key]

  // Clear existing error
  if (errors[field.key]) {
    delete errors[field.key]
  }

  // Required field validation
  if (field.required && (value === null || value === undefined || value === '')) {
    errors[field.key] = `${field.label} is required`
    return false
  }

  // Custom validation
  if (field.validation && value !== null && value !== undefined && value !== '') {
    const validationResult = field.validation(value)
    if (validationResult) {
      errors[field.key] = validationResult
      return false
    }
  }

  // Type-specific validation
  if (value !== null && value !== undefined && value !== '') {
    switch (field.type) {
      case 'email':
        if (!/^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/.test(value)) {
          errors[field.key] = 'Please enter a valid email address'
          return false
        }
        break
      case 'url':
        try {
          new URL(value)
        }
        catch {
          errors[field.key] = 'Please enter a valid URL'
          return false
        }
        break
      case 'number':
      case 'range':
        const numValue = Number(value)
        if (isNaN(numValue)) {
          errors[field.key] = 'Please enter a valid number'
          return false
        }
        if (field.min !== undefined && numValue < Number(field.min)) {
          errors[field.key] = `Value must be at least ${field.min}`
          return false
        }
        if (field.max !== undefined && numValue > Number(field.max)) {
          errors[field.key] = `Value must be at most ${field.max}`
          return false
        }
        break
    }
  }

  return true
}

function validateForm() {
  clearErrors()
  return props.fields.every(field => validateField(field))
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    // Focus first field with error
    const firstErrorField = props.fields.find(field => errors[field.key])
    if (firstErrorField && fieldRefs.value[firstErrorField.key]) {
      fieldRefs.value[firstErrorField.key].$el?.querySelector('input, textarea, select')?.focus()
    }
    return
  }

  isSubmitting.value = true
  submitError.value = ''

  try {
    // Convert form data to proper types
    const formData = { ...form } as T
    props.fields.forEach((field) => {
      if (field.type === 'number' || field.type === 'range') {
        formData[field.key as keyof T] = Number(form[field.key]) as T[keyof T]
      }
    })

    emit('submit', formData)

    if (!props.preventCloseOnSubmit) {
      emit('close')
    }
  }
  catch (error: any) {
    submitError.value = error.message || 'Failed to save'
  }
  finally {
    isSubmitting.value = false
  }
}

function handleCancel() {
  emit('cancel')
  emit('close')
}

// Watch for data changes to reinitialize form
watch(() => props.data, initializeForm, { immediate: true, deep: true })
watch(() => props.open, (open) => {
  if (open) {
    initializeForm()
    // Focus first field after modal opens
    nextTick(() => {
      const firstField = props.fields[0]
      if (firstField && fieldRefs.value[firstField.key]) {
        fieldRefs.value[firstField.key].$el?.querySelector('input, textarea, select')?.focus()
      }
    })
  }
})

// Set field ref
function setFieldRef(fieldKey: string, ref: any) {
  if (ref) {
    fieldRefs.value[fieldKey] = ref
  }
}
</script>

<template>
  <BaseModal
    :open="open"
    :size="size"
    @close="$emit('close')"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <BaseHeading
          as="h3"
          size="lg"
          weight="semibold"
          class="text-muted-900 dark:text-white"
        >
          {{ title }}
        </BaseHeading>
        <BaseButton
          size="sm"
          variant="ghost"
          @click="$emit('close')"
        >
          <Icon name="lucide:x" class="size-4" />
        </BaseButton>
      </div>
    </template>

    <form class="space-y-6" @submit.prevent="handleSubmit">
      <!-- Dynamic Fields -->
      <div
        :class="[
          gridColumns === 2 ? 'grid grid-cols-1 md:grid-cols-2 gap-4' : 'space-y-4',
        ]"
      >
        <template v-for="field in fields" :key="field.key">
          <div
            :class="[
              gridColumns === 2 && field.colspan === 2 ? 'md:col-span-2' : '',
            ]"
          >
            <!-- Text Input -->
            <BaseField
              v-if="field.type === 'text' || field.type === 'email' || field.type === 'password' || field.type === 'url' || !field.type"
              v-slot="{ inputAttrs, inputRef }"
              :error="errors[field.key]"
              :disabled="isSubmitting || field.disabled"
              :required="field.required"
            >
              <BaseLabel>
                {{ field.label }}
                <span v-if="field.required" class="text-danger-500">*</span>
              </BaseLabel>
              <TairoInput
                :ref="(ref) => setFieldRef(field.key, inputRef(ref))"
                v-bind="inputAttrs"
                v-model="form[field.key]"
                :type="field.type || 'text'"
                :placeholder="field.placeholder"
                :required="field.required"
                :disabled="isSubmitting || field.disabled"
                :aria-invalid="errors[field.key] ? 'true' : undefined"
                @blur="validateField(field)"
              />
              <BaseText v-if="field.description" size="xs" class="text-muted-500 mt-1">
                {{ field.description }}
              </BaseText>
            </BaseField>

            <!-- Number Input -->
            <BaseField
              v-else-if="field.type === 'number'"
              v-slot="{ inputAttrs, inputRef }"
              :error="errors[field.key]"
              :disabled="isSubmitting || field.disabled"
              :required="field.required"
            >
              <BaseLabel>
                {{ field.label }}
                <span v-if="field.required" class="text-danger-500">*</span>
              </BaseLabel>
              <TairoInput
                :ref="(ref) => setFieldRef(field.key, inputRef(ref))"
                v-bind="inputAttrs"
                v-model="form[field.key]"
                type="number"
                :placeholder="field.placeholder"
                :min="field.min"
                :max="field.max"
                :step="field.step"
                :required="field.required"
                :disabled="isSubmitting || field.disabled"
                :aria-invalid="errors[field.key] ? 'true' : undefined"
                @blur="validateField(field)"
              />
              <BaseText v-if="field.description" size="xs" class="text-muted-500 mt-1">
                {{ field.description }}
              </BaseText>
            </BaseField>

            <!-- Range Input -->
            <BaseField
              v-else-if="field.type === 'range'"
              :error="errors[field.key]"
              :disabled="isSubmitting || field.disabled"
            >
              <BaseLabel>
                {{ field.label }} ({{ form[field.key] }}{{ field.max === 100 ? '%' : '' }})
                <span v-if="field.required" class="text-danger-500">*</span>
              </BaseLabel>
              <div class="space-y-3">
                <input
                  v-model="form[field.key]"
                  type="range"
                  :min="field.min || 0"
                  :max="field.max || 100"
                  :step="field.step || 1"
                  :disabled="isSubmitting || field.disabled"
                  class="w-full h-2 bg-muted-200 dark:bg-muted-700 rounded-lg appearance-none cursor-pointer slider"
                  @blur="validateField(field)"
                >
                <div v-if="field.max && Number(field.max) <= 100" class="flex justify-between text-xs text-muted-500">
                  <span>{{ field.min || 0 }}{{ field.max === 100 ? '%' : '' }}</span>
                  <span>{{ Math.floor(Number(field.max) / 4) }}{{ field.max === 100 ? '%' : '' }}</span>
                  <span>{{ Math.floor(Number(field.max) / 2) }}{{ field.max === 100 ? '%' : '' }}</span>
                  <span>{{ Math.floor(Number(field.max) * 3 / 4) }}{{ field.max === 100 ? '%' : '' }}</span>
                  <span>{{ field.max }}{{ field.max === 100 ? '%' : '' }}</span>
                </div>
              </div>
              <BaseText v-if="field.description" size="xs" class="text-muted-500 mt-1">
                {{ field.description }}
              </BaseText>
            </BaseField>

            <!-- Textarea -->
            <BaseField
              v-else-if="field.type === 'textarea'"
              v-slot="{ inputAttrs, inputRef }"
              :error="errors[field.key]"
              :disabled="isSubmitting || field.disabled"
              :required="field.required"
            >
              <BaseLabel>
                {{ field.label }}
                <span v-if="field.required" class="text-danger-500">*</span>
              </BaseLabel>
              <BaseTextarea
                :ref="(ref) => setFieldRef(field.key, inputRef(ref))"
                v-bind="inputAttrs"
                v-model="form[field.key]"
                :placeholder="field.placeholder"
                :required="field.required"
                :disabled="isSubmitting || field.disabled"
                :aria-invalid="errors[field.key] ? 'true' : undefined"
                rows="4"
                @blur="validateField(field)"
              />
              <BaseText v-if="field.description" size="xs" class="text-muted-500 mt-1">
                {{ field.description }}
              </BaseText>
            </BaseField>

            <!-- Select -->
            <BaseField
              v-else-if="field.type === 'select'"
              v-slot="{ inputAttrs, inputRef }"
              :error="errors[field.key]"
              :disabled="isSubmitting || field.disabled"
              :required="field.required"
            >
              <BaseLabel>
                {{ field.label }}
                <span v-if="field.required" class="text-danger-500">*</span>
              </BaseLabel>
              <BaseSelect
                :ref="(ref) => setFieldRef(field.key, inputRef(ref))"
                v-bind="inputAttrs"
                v-model="form[field.key]"
                :required="field.required"
                :disabled="isSubmitting || field.disabled"
                :aria-invalid="errors[field.key] ? 'true' : undefined"
                @blur="validateField(field)"
              >
                <option v-if="!field.required" value="">
                  Select {{ field.label.toLowerCase() }}
                </option>
                <option
                  v-for="option in field.options"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </option>
              </BaseSelect>
              <BaseText v-if="field.description" size="xs" class="text-muted-500 mt-1">
                {{ field.description }}
              </BaseText>
            </BaseField>

            <!-- Checkbox -->
            <BaseField
              v-else-if="field.type === 'checkbox'"
              :error="errors[field.key]"
              :disabled="isSubmitting || field.disabled"
            >
              <div class="flex items-start gap-3">
                <BaseCheckbox
                  :ref="(ref) => setFieldRef(field.key, ref)"
                  v-model="form[field.key]"
                  :disabled="isSubmitting || field.disabled"
                  @update:model-value="validateField(field)"
                />
                <div class="flex-1">
                  <BaseLabel class="cursor-pointer">
                    {{ field.label }}
                    <span v-if="field.required" class="text-danger-500">*</span>
                  </BaseLabel>
                  <BaseText v-if="field.description" size="xs" class="text-muted-500">
                    {{ field.description }}
                  </BaseText>
                </div>
              </div>
            </BaseField>

            <!-- Radio -->
            <BaseField
              v-else-if="field.type === 'radio'"
              :error="errors[field.key]"
              :disabled="isSubmitting || field.disabled"
            >
              <BaseLabel>
                {{ field.label }}
                <span v-if="field.required" class="text-danger-500">*</span>
              </BaseLabel>
              <div class="space-y-3">
                <label
                  v-for="option in field.options"
                  :key="option.value"
                  class="flex items-center gap-3 cursor-pointer"
                >
                  <input
                    v-model="form[field.key]"
                    type="radio"
                    :value="option.value"
                    :disabled="isSubmitting || field.disabled"
                    class="text-primary-500"
                    @change="validateField(field)"
                  >
                  <span class="text-sm font-medium text-muted-700 dark:text-muted-300">
                    {{ option.label }}
                  </span>
                </label>
              </div>
              <BaseText v-if="field.description" size="xs" class="text-muted-500 mt-1">
                {{ field.description }}
              </BaseText>
            </BaseField>

            <!-- Custom Field Slot -->
            <BaseField
              v-else-if="field.type === 'custom'"
              :error="errors[field.key]"
              :disabled="isSubmitting || field.disabled"
              :required="field.required"
            >
              <BaseLabel>
                {{ field.label }}
                <span v-if="field.required" class="text-danger-500">*</span>
              </BaseLabel>
              <slot
                :name="field.key"
                :field="field"
                :value="form[field.key]"
                :error="errors[field.key]"
                :disabled="isSubmitting || field.disabled"
                :update-value="(value: any) => form[field.key] = value"
                :validate="() => validateField(field)"
              />
              <BaseText v-if="field.description" size="xs" class="text-muted-500 mt-1">
                {{ field.description }}
              </BaseText>
            </BaseField>
          </div>
        </template>
      </div>

      <!-- Error Message -->
      <div v-if="submitError" class="p-3 bg-danger-50 dark:bg-danger-900/20 border border-danger-200 dark:border-danger-800 rounded-lg">
        <div class="flex items-start gap-3">
          <Icon name="lucide:alert-circle" class="size-4 text-danger-500 mt-0.5 flex-shrink-0" />
          <div>
            <BaseHeading as="h4" size="xs" class="text-danger-700 dark:text-danger-400 mb-1">
              Error saving
            </BaseHeading>
            <BaseParagraph size="sm" class="text-danger-600 dark:text-danger-300">
              {{ submitError }}
            </BaseParagraph>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-end gap-3 pt-4 border-t border-muted-200 dark:border-muted-800">
        <BaseButton
          v-if="showCancel"
          variant="ghost"
          :disabled="isSubmitting"
          @click="handleCancel"
        >
          {{ cancelText }}
        </BaseButton>
        <BaseButton
          type="submit"
          variant="primary"
          :disabled="!isFormValid"
          :loading="isSubmitting"
        >
          {{ editing ? submitText : submitText }}
        </BaseButton>
      </div>
    </form>
  </BaseModal>
</template>

<style scoped>
/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .slider::-webkit-slider-thumb {
  background: #60a5fa;
  border-color: #1f2937;
}

.dark .slider::-moz-range-thumb {
  background: #60a5fa;
  border-color: #1f2937;
}
</style>
