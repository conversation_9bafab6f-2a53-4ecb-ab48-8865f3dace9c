<script setup lang="ts">
interface KpiData {
  label: string
  current: number
  target: number
  previous?: number
  format?: 'number' | 'percentage' | 'currency' | 'custom'
  customUnit?: string
  color?: string
}

interface KpiChartProps {
  data: KpiData[]
  title?: string
  height?: number
  showTargetLine?: boolean
  showProgress?: boolean
  layout?: 'vertical' | 'horizontal'
  compactMode?: boolean
}

const props = withDefaults(defineProps<KpiChartProps>(), {
  height: 400,
  showTargetLine: true,
  showProgress: true,
  layout: 'horizontal',
  compactMode: false,
})

// Process data for chart
const chartData = computed(() => {
  if (!props.data?.length)
    return []

  return [{
    name: 'Current',
    data: props.data.map(item => item.current),
  }, {
    name: 'Target',
    data: props.data.map(item => item.target),
  }]
})

const categories = computed(() => {
  return props.data?.map(item => item.label) || []
})

// Calculate progress for each KPI
const kpiProgress = computed(() => {
  return props.data?.map((item) => {
    const progress = item.target > 0 ? (item.current / item.target) * 100 : 0
    const change = item.previous ? ((item.current - item.previous) / item.previous) * 100 : null

    return {
      ...item,
      progress: Math.min(progress, 100),
      change,
      status: progress >= 100 ? 'success' : progress >= 75 ? 'warning' : 'danger',
    }
  }) || []
})

// Custom colors based on performance
const chartColors = computed(() => {
  const colors: string[] = []

  props.data?.forEach((item) => {
    if (item.color) {
      colors.push(item.color)
    }
    else {
      const progress = item.target > 0 ? (item.current / item.target) * 100 : 0
      if (progress >= 100) {
        colors.push('#10b981') // Success green
      }
      else if (progress >= 75) {
        colors.push('#f59e0b') // Warning yellow
      }
      else {
        colors.push('#ef4444') // Danger red
      }
    }
  })

  return colors
})

// Format value based on type
function formatValue(value: number, format?: string, customUnit?: string): string {
  switch (format) {
    case 'percentage':
      return `${value.toFixed(1)}%`
    case 'currency':
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(value)
    case 'custom':
      return `${value.toLocaleString()}${customUnit || ''}`
    default:
      return value.toLocaleString()
  }
}

// Chart options for KPI display
const chartOptions = computed(() => ({
  chart: {
    type: props.layout === 'horizontal' ? 'bar' : 'column',
    stacked: false,
  },
  plotOptions: {
    bar: {
      horizontal: props.layout === 'horizontal',
      borderRadius: 4,
      dataLabels: {
        position: 'top',
      },
    },
  },
  dataLabels: {
    enabled: true,
    formatter(val: number, opts: any) {
      const dataIndex = opts.dataPointIndex
      const item = props.data[dataIndex]
      return formatValue(val, item.format, item.customUnit)
    },
    style: {
      fontSize: '12px',
      fontWeight: '500',
    },
  },
  tooltip: {
    y: {
      formatter(val: number, opts: any) {
        const dataIndex = opts.dataPointIndex
        const item = props.data[dataIndex]
        return formatValue(val, item.format, item.customUnit)
      },
    },
  },
  yaxis: {
    labels: {
      formatter: (value: number) => {
        // Use first item's format as default for axis labels
        const firstItem = props.data[0]
        return formatValue(value, firstItem?.format, firstItem?.customUnit)
      },
    },
  },
}))
</script>

<template>
  <div class="w-full">
    <!-- Title -->
    <BaseHeading
      v-if="title"
      as="h3"
      size="lg"
      weight="semibold"
      class="text-muted-900 dark:text-white mb-6"
    >
      {{ title }}
    </BaseHeading>

    <!-- Compact Mode: Progress Bars -->
    <div v-if="compactMode" class="space-y-4">
      <div
        v-for="kpi in kpiProgress"
        :key="kpi.label"
        class="p-4 bg-white dark:bg-muted-800 rounded-lg border border-muted-200 dark:border-muted-700"
      >
        <div class="flex items-center justify-between mb-2">
          <BaseHeading as="h4" size="sm" weight="medium" class="text-muted-700 dark:text-muted-300">
            {{ kpi.label }}
          </BaseHeading>
          <div class="text-right">
            <div class="flex items-center gap-2">
              <BaseText size="lg" weight="semibold" class="text-muted-900 dark:text-white">
                {{ formatValue(kpi.current, kpi.format, kpi.customUnit) }}
              </BaseText>
              <BaseText size="sm" class="text-muted-500">
                / {{ formatValue(kpi.target, kpi.format, kpi.customUnit) }}
              </BaseText>
            </div>
            <div
              v-if="kpi.change !== null"
              class="flex items-center gap-1 text-xs"
              :class="[
                kpi.change >= 0
                  ? 'text-success-600 dark:text-success-400'
                  : 'text-danger-600 dark:text-danger-400',
              ]"
            >
              <Icon
                :name="kpi.change >= 0 ? 'lucide:trending-up' : 'lucide:trending-down'"
                class="size-3"
              />
              {{ Math.abs(kpi.change).toFixed(1) }}%
            </div>
          </div>
        </div>

        <!-- Progress Bar -->
        <div v-if="showProgress" class="w-full bg-muted-200 dark:bg-muted-700 rounded-full h-2">
          <div
            class="h-2 rounded-full transition-all duration-300"
            :class="[
              kpi.status === 'success' ? 'bg-success-500'
              : kpi.status === 'warning' ? 'bg-warning-500' : 'bg-danger-500',
            ]"
            :style="{ width: `${Math.min(kpi.progress, 100)}%` }"
          />
        </div>

        <!-- Progress Text -->
        <div v-if="showProgress" class="flex justify-between items-center mt-1">
          <BaseText size="xs" class="text-muted-500">
            {{ kpi.progress.toFixed(1) }}% of target
          </BaseText>
          <BaseText
            size="xs"
            :class="[
              kpi.status === 'success' ? 'text-success-600 dark:text-success-400'
              : kpi.status === 'warning' ? 'text-warning-600 dark:text-warning-400'
                : 'text-danger-600 dark:text-danger-400',
            ]"
          >
            {{ kpi.status === 'success' ? 'Target achieved'
              : kpi.status === 'warning' ? 'Near target' : 'Below target' }}
          </BaseText>
        </div>
      </div>
    </div>

    <!-- Full Chart Mode -->
    <div v-else>
      <!-- KPI Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div
          v-for="kpi in kpiProgress"
          :key="kpi.label"
          class="p-4 bg-white dark:bg-muted-800 rounded-lg border border-muted-200 dark:border-muted-700"
        >
          <div class="flex items-center justify-between mb-2">
            <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
              {{ kpi.label }}
            </BaseText>
            <div
              class="size-2 rounded-full"
              :class="[
                kpi.status === 'success' ? 'bg-success-500'
                : kpi.status === 'warning' ? 'bg-warning-500' : 'bg-danger-500',
              ]"
            />
          </div>

          <div class="flex items-baseline gap-2 mb-1">
            <BaseHeading as="h3" size="xl" weight="bold" class="text-muted-900 dark:text-white">
              {{ formatValue(kpi.current, kpi.format, kpi.customUnit) }}
            </BaseHeading>
            <BaseText size="sm" class="text-muted-500">
              / {{ formatValue(kpi.target, kpi.format, kpi.customUnit) }}
            </BaseText>
          </div>

          <div class="flex items-center justify-between">
            <BaseText size="xs" class="text-muted-500">
              {{ kpi.progress.toFixed(0) }}% of target
            </BaseText>
            <div
              v-if="kpi.change !== null"
              class="flex items-center gap-1 text-xs"
              :class="[
                kpi.change >= 0
                  ? 'text-success-600 dark:text-success-400'
                  : 'text-danger-600 dark:text-danger-400',
              ]"
            >
              <Icon
                :name="kpi.change >= 0 ? 'lucide:trending-up' : 'lucide:trending-down'"
                class="size-3"
              />
              {{ Math.abs(kpi.change).toFixed(1) }}%
            </div>
          </div>
        </div>
      </div>

      <!-- Chart -->
      <BaseChart
        :type="layout === 'horizontal' ? 'bar' : 'column'"
        :data="chartData"
        :categories="categories"
        :height="height"
        :colors="chartColors"
        :enable-data-labels="true"
        :enable-legend="showTargetLine"
        :enable-tooltip="true"
        :custom-options="chartOptions"
        :responsive="true"
      />
    </div>
  </div>
</template>
