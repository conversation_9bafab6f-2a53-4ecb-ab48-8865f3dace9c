<script setup lang="ts">
interface AnalyticsDataPoint {
  date: string
  value: number
  comparison?: number // For comparison with previous period
}

interface AnalyticsChartProps {
  data: AnalyticsDataPoint[]
  title: string
  subtitle?: string
  metric: string
  height?: number
  showComparison?: boolean
  dateFormat?: 'day' | 'week' | 'month' | 'year'
  loading?: boolean
  fillGradient?: boolean
  showDataPoints?: boolean
}

const props = withDefaults(defineProps<AnalyticsChartProps>(), {
  height: 300,
  showComparison: false,
  dateFormat: 'day',
  loading: false,
  fillGradient: true,
  showDataPoints: false,
})

// Process data for chart
const chartData = computed(() => {
  if (!props.data?.length)
    return []

  const mainSeries = {
    name: props.metric,
    data: props.data.map(item => ({
      x: item.date,
      y: item.value,
    })),
  }

  const series = [mainSeries]

  if (props.showComparison) {
    const comparisonSeries = {
      name: `Previous ${props.dateFormat}`,
      data: props.data
        .filter(item => item.comparison !== undefined)
        .map(item => ({
          x: item.date,
          y: item.comparison,
        })),
    }
    series.push(comparisonSeries)
  }

  return series
})

const categories = computed(() => {
  return props.data?.map(item => item.date) || []
})

// Calculate percentage change
const percentageChange = computed(() => {
  if (!props.data?.length || props.data.length < 2)
    return null

  const current = props.data[props.data.length - 1].value
  const previous = props.data[props.data.length - 2].value

  if (previous === 0)
    return null

  return ((current - previous) / previous) * 100
})

// Custom chart options for analytics
const chartOptions = computed(() => ({
  xaxis: {
    type: 'datetime',
    labels: {
      format: getDateFormat(),
    },
  },
  yaxis: {
    labels: {
      formatter: (value: number) => formatMetricValue(value),
    },
  },
  tooltip: {
    x: {
      format: getTooltipDateFormat(),
    },
    y: {
      formatter: (value: number) => formatMetricValue(value),
    },
  },
  markers: {
    size: props.showDataPoints ? 4 : 0,
  },
}))

function getDateFormat() {
  switch (props.dateFormat) {
    case 'day': return 'dd MMM'
    case 'week': return 'dd MMM'
    case 'month': return 'MMM yyyy'
    case 'year': return 'yyyy'
    default: return 'dd MMM'
  }
}

function getTooltipDateFormat() {
  switch (props.dateFormat) {
    case 'day': return 'dd MMM yyyy'
    case 'week': return 'dd MMM yyyy'
    case 'month': return 'MMM yyyy'
    case 'year': return 'yyyy'
    default: return 'dd MMM yyyy'
  }
}

function formatMetricValue(value: number): string {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  }
  else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  else {
    return value.toString()
  }
}
</script>

<template>
  <div class="w-full">
    <!-- Header with metrics -->
    <div class="mb-6">
      <div class="flex items-start justify-between">
        <div>
          <BaseHeading as="h3" size="lg" weight="semibold" class="text-muted-900 dark:text-white mb-1">
            {{ title }}
          </BaseHeading>
          <BaseParagraph v-if="subtitle" size="sm" class="text-muted-500 dark:text-muted-400">
            {{ subtitle }}
          </BaseParagraph>
        </div>

        <!-- Current value and change indicator -->
        <div v-if="data?.length && !loading" class="text-right">
          <div class="flex items-center gap-2 mb-1">
            <BaseHeading as="h2" size="2xl" weight="bold" class="text-muted-900 dark:text-white">
              {{ formatMetricValue(data[data.length - 1].value) }}
            </BaseHeading>
            <div
              v-if="percentageChange !== null"
              class="flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium"
              :class="[
                percentageChange >= 0
                  ? 'bg-success-50 text-success-700 dark:bg-success-900/20 dark:text-success-400'
                  : 'bg-danger-50 text-danger-700 dark:bg-danger-900/20 dark:text-danger-400',
              ]"
            >
              <Icon
                :name="percentageChange >= 0 ? 'lucide:trending-up' : 'lucide:trending-down'"
                class="size-3"
              />
              {{ Math.abs(percentageChange).toFixed(1) }}%
            </div>
          </div>
          <BaseParagraph size="xs" class="text-muted-500 dark:text-muted-400">
            vs previous {{ dateFormat }}
          </BaseParagraph>
        </div>
      </div>
    </div>

    <!-- Chart -->
    <BaseChart
      type="area"
      :data="chartData"
      :categories="categories"
      :height="height"
      :loading="loading"
      :fill="fillGradient ? 'gradient' : 'solid'"
      :smooth="true"
      :enable-grid-lines="true"
      :enable-data-labels="false"
      :enable-legend="showComparison"
      :enable-tooltip="true"
      :custom-options="chartOptions"
      :responsive="true"
    />
  </div>
</template>
