<script setup lang="ts" generic="T = any">
import type { ValidationRule } from '~/composables/useFormValidation'
import { useFormValidation } from '~/composables/useFormValidation'

interface FormFieldProps<T> {
  modelValue: T
  name: string
  label: string
  type?: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'range' | 'url' | 'file'
  placeholder?: string
  description?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  validation?: ValidationRule
  options?: Array<{ value: any, label: string, disabled?: boolean }>
  multiple?: boolean // For select and file inputs
  accept?: string // For file inputs
  rows?: number // For textarea
  min?: number | string
  max?: number | string
  step?: number | string
  autocomplete?: string
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'flush' | 'outline'
  loading?: boolean
  leftIcon?: string
  rightIcon?: string
  prefix?: string
  suffix?: string
  showValidation?: boolean
  validateOnBlur?: boolean
  validateOnChange?: boolean
}

interface FormFieldEmits<T> {
  (e: 'update:modelValue', value: T): void
  (e: 'blur', event: FocusEvent): void
  (e: 'focus', event: FocusEvent): void
  (e: 'change', value: T): void
}

const props = withDefaults(defineProps<FormFieldProps<T>>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  required: false,
  validation: () => ({}),
  size: 'md',
  variant: 'default',
  loading: false,
  showValidation: true,
  validateOnBlur: true,
  validateOnChange: false,
  rows: 4,
})

const emit = defineEmits<FormFieldEmits<T>>()

// Set up validation if provided
const validationConfig = computed(() => ({
  [props.name]: {
    required: props.required,
    ...props.validation,
  },
}))

const {
  validateField,
  getFieldError,
  isFieldValid,
  isFieldValidated,
  handleBlur,
  handleChange,
} = useFormValidation(validationConfig.value, {
  validateOnBlur: props.validateOnBlur,
  validateOnChange: props.validateOnChange,
})

// Internal value management
const internalValue = computed({
  get: () => props.modelValue,
  set: (value: T) => {
    emit('update:modelValue', value)
    emit('change', value)

    // Trigger validation if enabled
    if (props.validateOnChange) {
      handleChange(props.name, value)
    }
  },
})

// Computed properties
const fieldError = computed(() => {
  if (!props.showValidation)
    return null
  return getFieldError(props.name)
})

const hasError = computed(() => !!fieldError.value)
const isValid = computed(() => props.showValidation && isFieldValid(props.name))
const wasValidated = computed(() => isFieldValidated(props.name))

const fieldId = computed(() => `field-${props.name}`)

// Input classes based on state
const inputClasses = computed(() => {
  const base = 'transition-colors duration-200'
  const sizeClasses = {
    sm: 'text-sm px-3 py-2',
    md: 'text-base px-4 py-2.5',
    lg: 'text-lg px-5 py-3',
  }

  const variantClasses = {
    default: 'border border-muted-300 dark:border-muted-700 rounded-lg',
    flush: 'border-0 border-b border-muted-300 dark:border-muted-700 rounded-none',
    outline: 'border-2 border-muted-300 dark:border-muted-700 rounded-lg',
  }

  const stateClasses = hasError.value
    ? 'border-danger-500 dark:border-danger-400 ring-1 ring-danger-500/20'
    : isValid.value
      ? 'border-success-500 dark:border-success-400 ring-1 ring-success-500/20'
      : 'focus:border-primary-500 dark:focus:border-primary-400 focus:ring-1 focus:ring-primary-500/20'

  return [
    base,
    sizeClasses[props.size],
    variantClasses[props.variant],
    stateClasses,
    props.disabled && 'opacity-50 cursor-not-allowed',
    props.readonly && 'bg-muted-50 dark:bg-muted-800',
  ].filter(Boolean).join(' ')
})

// Event handlers
function handleInputBlur(event: FocusEvent) {
  emit('blur', event)

  if (props.validateOnBlur) {
    handleBlur(props.name, internalValue.value)
  }
}

function handleInputFocus(event: FocusEvent) {
  emit('focus', event)
}

// File input specific handlers
function handleFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (props.multiple) {
    internalValue.value = Array.from(files || []) as T
  }
  else {
    internalValue.value = files?.[0] as T
  }
}

// Range input specific handlers
const rangeValue = computed(() => {
  if (props.type === 'range') {
    return Number(internalValue.value) || 0
  }
  return 0
})

// Generate a unique ID for form field
const generateId = () => `field-${props.name}-${Math.random().toString(36).substr(2, 9)}`
const uniqueId = ref(generateId())
</script>

<template>
  <div class="w-full">
    <!-- Label -->
    <BaseLabel
      v-if="label"
      :for="uniqueId"
      class="mb-2 block"
      :class="[required && 'required-field']"
    >
      {{ label }}
    </BaseLabel>

    <!-- Input Container -->
    <div class="relative">
      <!-- Left Icon -->
      <div v-if="leftIcon" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-500 z-10">
        <Icon :name="leftIcon" class="size-4" />
      </div>

      <!-- Prefix -->
      <div
        v-if="prefix"
        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-600 dark:text-muted-400 text-sm font-medium z-10"
      >
        {{ prefix }}
      </div>

      <!-- Text Input -->
      <TairoInput
        v-if="type === 'text' || type === 'email' || type === 'password' || type === 'url'"
        :id="uniqueId"
        v-model="internalValue"
        :type="type"
        :placeholder="placeholder"
        :disabled="disabled || loading"
        :readonly="readonly"
        :required="required"
        :autocomplete="autocomplete"
        :class="[inputClasses, leftIcon && 'pl-10', prefix && 'pl-12', (rightIcon || suffix) && 'pr-10']"
        :aria-invalid="hasError ? 'true' : undefined"
        :aria-describedby="description ? `${uniqueId}-description` : undefined"
        @blur="handleInputBlur"
        @focus="handleInputFocus"
      />

      <!-- Number Input -->
      <TairoInput
        v-else-if="type === 'number'"
        :id="uniqueId"
        v-model="internalValue"
        type="number"
        :placeholder="placeholder"
        :disabled="disabled || loading"
        :readonly="readonly"
        :required="required"
        :min="min"
        :max="max"
        :step="step"
        :autocomplete="autocomplete"
        :class="[inputClasses, leftIcon && 'pl-10', prefix && 'pl-12', (rightIcon || suffix) && 'pr-10']"
        :aria-invalid="hasError ? 'true' : undefined"
        :aria-describedby="description ? `${uniqueId}-description` : undefined"
        @blur="handleInputBlur"
        @focus="handleInputFocus"
      />

      <!-- Textarea -->
      <BaseTextarea
        v-else-if="type === 'textarea'"
        :id="uniqueId"
        v-model="internalValue"
        :placeholder="placeholder"
        :disabled="disabled || loading"
        :readonly="readonly"
        :required="required"
        :rows="rows"
        :class="inputClasses"
        :aria-invalid="hasError ? 'true' : undefined"
        :aria-describedby="description ? `${uniqueId}-description` : undefined"
        @blur="handleInputBlur"
        @focus="handleInputFocus"
      />

      <!-- Select -->
      <BaseSelect
        v-else-if="type === 'select'"
        :id="uniqueId"
        v-model="internalValue"
        :disabled="disabled || loading"
        :required="required"
        :multiple="multiple"
        :class="inputClasses"
        :aria-invalid="hasError ? 'true' : undefined"
        :aria-describedby="description ? `${uniqueId}-description` : undefined"
        @blur="handleInputBlur"
        @focus="handleInputFocus"
      >
        <option v-if="!required && !multiple" value="">
          Select {{ label?.toLowerCase() || 'option' }}
        </option>
        <option
          v-for="option in options"
          :key="option.value"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </option>
      </BaseSelect>

      <!-- Checkbox -->
      <div v-else-if="type === 'checkbox'" class="flex items-start gap-3">
        <BaseCheckbox
          :id="uniqueId"
          v-model="internalValue"
          :disabled="disabled || loading"
          :required="required"
          :aria-invalid="hasError ? 'true' : undefined"
          :aria-describedby="description ? `${uniqueId}-description` : undefined"
          @blur="handleInputBlur"
          @focus="handleInputFocus"
        />
        <div v-if="!label" class="flex-1 cursor-pointer" @click="() => !disabled && !loading && (internalValue = !internalValue)">
          <slot name="checkbox-label" />
        </div>
      </div>

      <!-- Radio Group -->
      <div v-else-if="type === 'radio'" class="space-y-3">
        <label
          v-for="option in options"
          :key="option.value"
          class="flex items-center gap-3 cursor-pointer"
          :class="[option.disabled && 'opacity-50 cursor-not-allowed']"
        >
          <input
            v-model="internalValue"
            type="radio"
            :value="option.value"
            :disabled="disabled || loading || option.disabled"
            :required="required"
            class="text-primary-500 focus:ring-primary-500"
            :aria-invalid="hasError ? 'true' : undefined"
            @blur="handleInputBlur"
            @focus="handleInputFocus"
          >
          <span class="text-sm font-medium text-muted-700 dark:text-muted-300">
            {{ option.label }}
          </span>
        </label>
      </div>

      <!-- Range -->
      <div v-else-if="type === 'range'" class="space-y-3">
        <input
          :id="uniqueId"
          v-model="internalValue"
          type="range"
          :min="min || 0"
          :max="max || 100"
          :step="step || 1"
          :disabled="disabled || loading"
          :required="required"
          class="w-full h-2 bg-muted-200 dark:bg-muted-700 rounded-lg appearance-none cursor-pointer slider"
          :aria-invalid="hasError ? 'true' : undefined"
          :aria-describedby="description ? `${uniqueId}-description` : undefined"
          @blur="handleInputBlur"
          @focus="handleInputFocus"
        >

        <!-- Range Value Display -->
        <div class="flex justify-between items-center">
          <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
            {{ min || 0 }}{{ suffix }}
          </BaseText>
          <BaseText size="sm" weight="medium" class="text-muted-800 dark:text-muted-200">
            {{ rangeValue }}{{ suffix }}
          </BaseText>
          <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
            {{ max || 100 }}{{ suffix }}
          </BaseText>
        </div>
      </div>

      <!-- File -->
      <input
        v-else-if="type === 'file'"
        :id="uniqueId"
        type="file"
        :disabled="disabled || loading"
        :required="required"
        :multiple="multiple"
        :accept="accept"
        :class="inputClasses"
        :aria-invalid="hasError ? 'true' : undefined"
        :aria-describedby="description ? `${uniqueId}-description` : undefined"
        @change="handleFileChange"
        @blur="handleInputBlur"
        @focus="handleInputFocus"
      >

      <!-- Right Icon -->
      <div v-if="rightIcon" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-500">
        <Icon :name="rightIcon" class="size-4" />
      </div>

      <!-- Suffix -->
      <div
        v-if="suffix && type !== 'range'"
        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-600 dark:text-muted-400 text-sm font-medium"
      >
        {{ suffix }}
      </div>

      <!-- Loading Spinner -->
      <div v-if="loading" class="absolute right-3 top-1/2 transform -translate-y-1/2">
        <Icon name="nui-icon:spiner" class="size-4 animate-spin text-primary-500" />
      </div>

      <!-- Validation Icons -->
      <div v-if="showValidation && wasValidated && !loading" class="absolute right-3 top-1/2 transform -translate-y-1/2">
        <Icon
          v-if="hasError"
          name="lucide:x-circle"
          class="size-4 text-danger-500"
        />
        <Icon
          v-else-if="isValid"
          name="lucide:check-circle"
          class="size-4 text-success-500"
        />
      </div>
    </div>

    <!-- Description -->
    <BaseText
      v-if="description"
      :id="`${uniqueId}-description`"
      size="xs"
      class="text-muted-500 mt-1"
    >
      {{ description }}
    </BaseText>

    <!-- Error Message -->
    <BaseText
      v-if="fieldError"
      size="xs"
      class="text-danger-600 dark:text-danger-400 mt-1 flex items-center gap-1"
    >
      <Icon name="lucide:alert-circle" class="size-3" />
      {{ fieldError }}
    </BaseText>
  </div>
</template>

<style scoped>
/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.dark .slider::-webkit-slider-thumb {
  background: #60a5fa;
  border-color: #1f2937;
}

.dark .slider::-moz-range-thumb {
  background: #60a5fa;
  border-color: #1f2937;
}

/* Required field indicator */
.required-field::after {
  content: '*';
  color: rgb(239 68 68); /* text-danger-500 */
  margin-left: 0.25rem;
}
</style>
