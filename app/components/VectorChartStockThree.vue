<template>
  <svg
    id="stock-chart-3"
    data-vivus
    viewBox="0 0 250 60"
    width="250"
    height="90"
  >
    <path
      fill="none"
      vector-effect="non-scaling-stroke"
      stroke-width="2"
      stroke="currentColor"
      stroke-linejoin="miter"
      stroke-linecap="round"
      stroke-miterlimit="3"
      d="M209.3,32.3c12.6,0.2,25.9,15.3,40.7,5"
    />
    <text
      transform="matrix(1 0 0 1 195 20)"
      stroke="none"
      fill="rgb(14 165 233)"
    >
      +8%
    </text>
    <linearGradient
      id="_lgradient_3"
      x1="0%"
      y1="50%"
      x2="100%"
      y2="50%"
    >
      <stop offset="0" style="stop-color: rgb(14 165 233)" />
      <stop offset="1" style="stop-color: rgb(100 200 245)" />
    </linearGradient>
    <path
      fill="none"
      vector-effect="non-scaling-stroke"
      stroke-width="2"
      stroke="url(#_lgradient_3)"
      stroke-linejoin="miter"
      stroke-linecap="round"
      stroke-miterlimit="3"
      d="M0,57.5c0,0,13-11.3,27.5-7.7s31.7,18.4,45.4,16.4c0,0,43.4-1.7,54.7-39.7c11.3-38,31.5,26.1,45.9,24.7
                                c14.5-1.4,12.4-25,33.2-19"
    />
    <path
      d="M206.6,32.2c0-1.5,1.2-2.7,2.7-2.7c1.5,0,2.7,1.2,2.7,2.7c0,1.5-1.2,2.7-2.7,2.7
                                C207.9,34.9,206.6,33.7,206.6,32.2z"
      fill="rgb(14 165 233)"
    />
  </svg>
</template>
