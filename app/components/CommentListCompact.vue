<script setup lang="ts">
import type { CommentListCompactProps } from '../app/types/ui'

const props = withDefaults(
  defineProps<CommentListCompactProps>(),
  {
    comments: () => [
      {
        id: 0,
        name: '<PERSON>',
        firstName: '<PERSON>',
        lastName: '<PERSON><PERSON>',
        image: '/img/avatars/18.svg',
        text: 'G<PERSON>',
        author: '<PERSON>',
        avatar: '/img/avatars/18.svg',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliter autem vobis placet. Quae duo sunt, unum facit.',
        date: '2 hours ago',
      },
      {
        id: 1,
        name: '<PERSON>',
        firstName: '<PERSON>',
        lastName: '<PERSON><PERSON>',
        image: '/img/avatars/14.svg',
        text: '<PERSON>',
        author: '<PERSON>',
        avatar: '/img/avatars/14.svg',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliter autem vobis placet.',
        date: '4 hours ago',
      },
      {
        id: 2,
        name: '<PERSON><PERSON><PERSON>',
        firstName: '<PERSON><PERSON><PERSON>',
        lastName: '<PERSON>',
        image: '/img/avatars/5.svg',
        text: 'CM',
        author: 'Clarissa M',
        avatar: '/img/avatars/5.svg',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliter autem vobis placet. Quae duo sunt, unum facit.',
        date: '6 hours ago',
      },
      {
        id: 3,
        name: 'Eddy Flayer',
        firstName: 'Eddy',
        lastName: 'Flayer',
        image: '/img/avatars/8.svg',
        text: 'EF',
        author: 'Eddy Flayer',
        avatar: '/img/avatars/8.svg',
        content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliter autem vobis placet. Quae duo sunt, unum facit.',
        date: '1 day ago',
      },
    ],
    showLikes: true,
  },
)
</script>

<template>
  <div class="mb-2 space-y-5">
    <div
      v-for="comment in props.comments"
      :key="comment.id"
      class="flex gap-3"
    >
      <BaseAvatar
        :src="comment.image"
        :text="comment.text"
        size="xs"
        rounded="none"
        mask="blob"
        class="bg-primary-100 dark:bg-primary-500/20 text-primary-500 shrink-0"
      />
      <div>
        <BaseHeading
          as="h4"
          size="sm"
          weight="medium"
          lead="tight"
          class="text-muted-900 dark:text-white"
        >
          <span>
            {{ comment.firstName }} {{ comment.lastName.slice(0, 1) }}.
          </span>
        </BaseHeading>
        <BaseParagraph size="xs">
          <span class="text-muted-600 dark:text-muted-400">
            {{ comment.content }}
          </span>
        </BaseParagraph>
      </div>
    </div>
  </div>
</template>
