/* Email content styling */
.email-content {
  /* Reset some email client styles */
  font-family: inherit !important;
  line-height: inherit !important;
}

.email-content * {
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Typography */
.email-content h1,
.email-content h2,
.email-content h3,
.email-content h4,
.email-content h5,
.email-content h6 {
  margin-top: 1.5rem !important;
  margin-bottom: 0.75rem !important;
  font-weight: 600 !important;
  line-height: 1.25 !important;
}

.email-content h1 {
  font-size: 1.875rem !important;
}
.email-content h2 {
  font-size: 1.5rem !important;
}
.email-content h3 {
  font-size: 1.25rem !important;
}
.email-content h4 {
  font-size: 1.125rem !important;
}
.email-content h5 {
  font-size: 1rem !important;
}
.email-content h6 {
  font-size: 0.875rem !important;
}

.email-content p {
  margin-bottom: 1rem !important;
  line-height: 1.6 !important;
}

/* Links */
.email-content a {
  color: rgb(59 130 246) !important; /* blue-500 */
  text-decoration: underline !important;
}

.email-content a:hover {
  color: rgb(37 99 235) !important; /* blue-600 */
}

/* Dark mode links */
.dark .email-content a {
  color: rgb(96 165 250) !important; /* blue-400 */
}

.dark .email-content a:hover {
  color: rgb(59 130 246) !important; /* blue-500 */
}

/* Lists */
.email-content ul,
.email-content ol {
  margin-bottom: 1rem !important;
  padding-left: 1.5rem !important;
}

.email-content li {
  margin-bottom: 0.25rem !important;
}

/* Images */
.email-content img {
  max-width: 100% !important;
  height: auto !important;
  border-radius: 0.375rem !important; /* rounded-md */
}

/* Tables */
.email-content table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin-bottom: 1rem !important;
}

.email-content th,
.email-content td {
  padding: 0.5rem !important;
  border: 1px solid rgb(229 231 235) !important; /* border-gray-200 */
  text-align: left !important;
}

.email-content th {
  background-color: rgb(249 250 251) !important; /* bg-gray-50 */
  font-weight: 600 !important;
}

/* Dark mode tables */
.dark .email-content th,
.dark .email-content td {
  border-color: rgb(55 65 81) !important; /* border-gray-700 */
}

.dark .email-content th {
  background-color: rgb(31 41 55) !important; /* bg-gray-800 */
}

/* Blockquotes */
.email-content blockquote {
  margin: 1rem 0 !important;
  padding-left: 1rem !important;
  border-left: 4px solid rgb(209 213 219) !important; /* border-gray-300 */
  font-style: italic !important;
  color: rgb(107 114 128) !important; /* text-gray-500 */
}

.dark .email-content blockquote {
  border-left-color: rgb(55 65 81) !important; /* border-gray-700 */
  color: rgb(156 163 175) !important; /* text-gray-400 */
}

/* Code blocks */
.email-content pre {
  background-color: rgb(249 250 251) !important; /* bg-gray-50 */
  padding: 1rem !important;
  border-radius: 0.375rem !important; /* rounded-md */
  overflow-x: auto !important;
  font-family:
    ui-monospace, SFMono-Regular, 'Cascadia Code', 'Roboto Mono', Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace !important;
  font-size: 0.875rem !important;
}

.dark .email-content pre {
  background-color: rgb(31 41 55) !important; /* bg-gray-800 */
}

.email-content code {
  background-color: rgb(249 250 251) !important; /* bg-gray-50 */
  padding: 0.125rem 0.25rem !important;
  border-radius: 0.25rem !important; /* rounded-sm */
  font-family:
    ui-monospace, SFMono-Regular, 'Cascadia Code', 'Roboto Mono', Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace !important;
  font-size: 0.875rem !important;
}

.dark .email-content code {
  background-color: rgb(31 41 55) !important; /* bg-gray-800 */
}

/* Horizontal rules */
.email-content hr {
  margin: 1.5rem 0 !important;
  border: none !important;
  border-top: 1px solid rgb(229 231 235) !important; /* border-gray-200 */
}

.dark .email-content hr {
  border-top-color: rgb(55 65 81) !important; /* border-gray-700 */
}

/* Remove unwanted margins on first/last elements */
.email-content > *:first-child {
  margin-top: 0 !important;
}

.email-content > *:last-child {
  margin-bottom: 0 !important;
}

/* Hide potentially malicious content */
.email-content script,
.email-content object,
.email-content embed,
.email-content iframe,
.email-content form {
  display: none !important;
}

/* Responsive images */
@media (max-width: 640px) {
  .email-content img {
    max-width: 100% !important;
    height: auto !important;
  }

  .email-content table {
    font-size: 0.875rem !important;
  }
}
