/**
 * Development Logger Utility
 *
 * Provides console logging that respects environment settings and
 * follows coding standards for the TurboIS platform.
 *
 * Usage:
 *   import { logger } from '~/utils/logger'
 *   logger.info('Debug info')
 *   logger.warn('Warning message')
 *   logger.error('Error message')
 */

const isDevelopment = import.meta.dev
const isTest = import.meta.env.NODE_ENV === 'test'

export interface Logger {
  info: (...args: any[]) => void
  warn: (...args: any[]) => void
  error: (...args: any[]) => void
  debug: (...args: any[]) => void
}

/**
 * Logger instance that follows ESLint rules and environment awareness
 */
export const logger: Logger = {
  /**
   * Info level logging - only in development
   */
  info: (...args: any[]) => {
    if (isDevelopment) {
      console.warn('[INFO]', ...args) // Using console.warn (ESLint allowed)
    }
  },

  /**
   * Warning level logging - always shown
   */
  warn: (...args: any[]) => {
    console.warn('[WARN]', ...args) // ESLint allowed
  },

  /**
   * Error level logging - always shown
   */
  error: (...args: any[]) => {
    console.error('[ERROR]', ...args) // ESLint allowed
  },

  /**
   * Debug level logging - only in development, suppressed in tests
   */
  debug: (...args: any[]) => {
    if (isDevelopment && !isTest) {
      console.warn('[DEBUG]', ...args) // Using console.warn (ESLint allowed)
    }
  },
}

/**
 * Create a namespaced logger for specific modules
 *
 * @param namespace - Module or component name
 * @returns Logger instance with namespace prefix
 */
export function createLogger(namespace: string): Logger {
  return {
    info: (...args: any[]) => logger.info(`[${namespace}]`, ...args),
    warn: (...args: any[]) => logger.warn(`[${namespace}]`, ...args),
    error: (...args: any[]) => logger.error(`[${namespace}]`, ...args),
    debug: (...args: any[]) => logger.debug(`[${namespace}]`, ...args),
  }
}

/**
 * Default export for convenience
 */
export default logger
