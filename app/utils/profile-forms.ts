import type { Experience, Language, Skill, Tool } from '../types/auth'

/**
 * Generate a new UUID for profile items
 */
export function generateProfileItemId(): string {
  return crypto.randomUUID()
}

/**
 * Create an empty Experience object
 */
export function createEmptyExperience(): Omit<Experience, 'id'> {
  return {
    company: '',
    position: '',
    period: '',
    description: '',
    startDate: '',
    endDate: '',
    isCurrent: false,
    logo: '',
  }
}

/**
 * Create an empty Language object
 */
export function createEmptyLanguage(): Omit<Language, 'id'> {
  return {
    name: '',
    mastery: '',
    level: 0,
    icon: '',
  }
}

/**
 * Create an empty Skill object
 */
export function createEmptySkill(): Omit<Skill, 'id'> {
  return {
    name: '',
    experience: 0,
    level: 0,
    logo: '',
    icon: '',
  }
}

/**
 * Create an empty Tool object
 */
export function createEmptyTool(): Omit<Tool, 'id'> {
  return {
    name: '',
    mastery: '',
    level: 0,
    logo: '',
    category: '',
  }
}

/**
 * Populate form data from existing item or create empty one
 */
export function populateExperienceForm(experience: Experience | null): Omit<Experience, 'id'> & { id?: string } {
  if (experience) {
    return { ...experience }
  }
  return createEmptyExperience()
}

export function populateLanguageForm(language: Language | null): Omit<Language, 'id'> & { id?: string } {
  if (language) {
    return { ...language }
  }
  return createEmptyLanguage()
}

export function populateSkillForm(skill: Skill | null): Omit<Skill, 'id'> & { id?: string } {
  if (skill) {
    return { ...skill }
  }
  return createEmptySkill()
}

export function populateToolForm(tool: Tool | null): Omit<Tool, 'id'> & { id?: string } {
  if (tool) {
    return { ...tool }
  }
  return createEmptyTool()
}
