/**
 * Wizard Card Design Patterns
 * Extracted from app/pages/wizard components for consistent styling across wizards
 */

// Step 1 Selection Card Pattern (from wizard/index.vue)
export const SELECTION_CARD_CLASSES = {
  base: 'border border-transparent hover:border-muted-200 dark:hover:border-muted-800 dark:hover:bg-muted-950 hover:shadow-muted-300/30 dark:hover:shadow-muted-800/30 group rounded-2xl p-5 transition-all duration-300 hover:bg-white hover:shadow-xl',
  selected: 'dark:bg-muted-950 shadow-muted-300/30 dark:shadow-muted-800/30 bg-white border border-muted-200 dark:border-muted-800 shadow-xl',
  disabled: 'opacity-50 cursor-not-allowed',
} as const

// Step 3 Base Card Pattern (from wizard/step-3.vue)
export const BASE_CARD_CLASSES = {
  container: 'p-6',
  rounded: 'lg',
  header: 'tracking-wide text-muted-400 font-sans text-[0.65rem] font-semibold uppercase',
  content: 'space-y-4',
} as const

// Step 6 Tools Card Pattern (from wizard/step-6.vue)
export const TOOLS_CARD_CLASSES = {
  base: 'p-4 group-data-[state=checked]:border-primary-500 group-data-[state=checked]:shadow-muted-300/30 dark:group-data-[state=checked]:shadow-muted-900/30 group-data-[state=checked]:shadow-xl',
  content: 'cursor-pointer flex items-center justify-start gap-3',
  icon: 'size-8',
  title: 'text-muted-800 dark:text-muted-100 text-sm font-medium',
  description: 'text-muted-400 text-xs',
} as const

// Step 7 Summary Card Pattern (from wizard/step-7.vue)
export const SUMMARY_CARD_CLASSES = {
  container: 'group relative p-6',
  rounded: 'lg',
  grid: 'grid grid-cols-12 gap-4',
  editButton: 'absolute end-3 top-3 z-10 hover:border-primary-500 hover:text-primary-500 dark:hover:border-primary-500 dark:hover:text-primary-500 pointer-events-none opacity-0 group-hover:pointer-events-auto group-hover:opacity-100',
  header: 'mb-4 scale-90 uppercase text-muted-500 dark:text-muted-400',
  content: 'text-muted-800 dark:text-muted-100',
} as const

// Preview/Dark Card Pattern
export const PREVIEW_CARD_CLASSES = {
  container: 'p-6 bg-muted-50 dark:bg-muted-900/50 rounded-xl',
  header: 'text-sm font-medium mb-3 text-muted-600 dark:text-muted-400',
  content: 'space-y-3',
} as const

// Grid Layout Patterns
export const GRID_LAYOUTS = {
  // Step 1: 3-column selection grid
  selection: 'grid gap-6 sm:grid-cols-2 lg:grid-cols-3',
  // Step 3: Single column with max width
  singleColumn: 'mx-auto flex w-full max-w-sm flex-col gap-3',
  // Step 6: Tools in 3-column grid
  tools: 'mx-auto grid max-w-4xl gap-4 px-4 sm:grid-cols-3',
  // Step 7: Responsive summary grid
  summary: 'grid grid-cols-1 lg:grid-cols-2 gap-6',
} as const

// Animation/Transition Classes
export const TRANSITIONS = {
  all: 'transition-all duration-300',
  colors: 'transition-colors duration-200',
  shadow: 'transition-shadow duration-200',
} as const
