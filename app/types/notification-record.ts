import { z } from 'zod'

/**
 * Notification Type Enum
 * Defines the different types of notifications that can be created
 */
export const notificationTypeSchema = z.enum([
  // System notifications
  'system.security',
  'system.update',
  'system.maintenance',

  // Workspace notifications
  'workspace.invitation',
  'workspace.member_joined',
  'workspace.member_left',
  'workspace.settings_changed',

  // Profile & User notifications
  'profile.mention',
  'profile.view',
  'profile.connection_request',

  // Content & Collaboration
  'task.assigned',
  'task.completed',
  'task.due_soon',
  'comment.new',
  'comment.reply',

  // File & Document notifications
  'file.shared',
  'file.uploaded',
  'document.edited',

  // Communication
  'message.new',
  'message.reaction',

  // Marketing & External
  'marketing.product_update',
  'marketing.newsletter',
  'marketing.partner_offer',
])

/**
 * Notification Status Enum
 */
export const notificationStatusSchema = z.enum([
  'unread',
  'read',
  'archived',
  'dismissed',
])

/**
 * Notification Priority Enum
 */
export const notificationPrioritySchema = z.enum([
  'low',
  'normal',
  'high',
  'urgent',
])

/**
 * User Reference Schema
 * Minimal user info embedded in notifications
 */
export const notificationUserSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  avatar: z.string().optional(),
  username: z.string().optional(),
})

/**
 * Target Reference Schema
 * Reference to the object/entity the notification is about
 */
export const notificationTargetSchema = z.object({
  type: z.enum(['project', 'task', 'document', 'workspace', 'profile', 'message', 'file']),
  name: z.string().min(1),
  url: z.string().optional(),
  id: z.string().optional(),
})

/**
 * Notification Payload Schema
 * Contains the main content and context for the notification
 */
export const notificationPayloadSchema = z.object({
  // Primary actor (user who triggered the notification)
  user: notificationUserSchema,

  // Target object/entity (what the notification is about)
  target: notificationTargetSchema.optional(),

  // Additional involved users (for multi-user notifications)
  people: z.array(notificationUserSchema).default([]),

  // Custom text content
  text: z.string().optional(),

  // Action that triggered this notification
  action: z.string().optional(),

  // Additional metadata
  metadata: z.record(z.any()).default({}),
})

/**
 * Complete Notification Record Schema
 * Represents a single notification in the Firestore collection
 */
export const notificationRecordSchema = z.object({
  // Unique identifier
  id: z.string().uuid(),

  // Multi-tenant isolation
  workspace_id: z.string().uuid(),
  profile_id: z.string().uuid(), // Target recipient profile
  owner_id: z.string().uuid(), // Creator/system that generated notification

  // Notification classification
  type: notificationTypeSchema,
  status: notificationStatusSchema.default('unread'),
  priority: notificationPrioritySchema.default('normal'),

  // Notification content
  payload: notificationPayloadSchema,

  // Vector embedding for semantic search (optional)
  embedding: z.array(z.number()).optional(),

  // Timestamps
  created_at: z.date(),
  updated_at: z.date(),
  read_at: z.date().nullable().default(null),
  expires_at: z.date().optional(), // TTL for temporary notifications

  // Soft delete support
  deleted_at: z.date().nullable().default(null),
})

/**
 * Notification Creation Input Schema
 * For creating new notifications via API
 */
export const notificationCreateInputSchema = z.object({
  workspace_id: z.string().uuid(),
  profile_id: z.string().uuid(),
  type: notificationTypeSchema,
  priority: notificationPrioritySchema.default('normal'),
  payload: notificationPayloadSchema,
  expires_at: z.date().optional(),
})

/**
 * Notification Update Input Schema
 * For updating existing notifications (mainly status changes)
 */
export const notificationUpdateInputSchema = z.object({
  status: notificationStatusSchema.optional(),
  read_at: z.date().nullable().optional(),
  updated_at: z.date().default(() => new Date()),
})

/**
 * Notification Query Schema
 * For filtering and searching notifications
 */
export const notificationQuerySchema = z.object({
  workspace_id: z.string().uuid().optional(),
  profile_id: z.string().uuid().optional(),
  type: z.array(notificationTypeSchema).optional(),
  status: z.array(notificationStatusSchema).optional(),
  priority: z.array(notificationPrioritySchema).optional(),
  limit: z.number().int().min(1).max(100).default(50),
  offset: z.number().int().min(0).default(0),
  order_by: z.enum(['created_at', 'updated_at', 'priority']).default('created_at'),
  order_direction: z.enum(['asc', 'desc']).default('desc'),
  include_read: z.boolean().default(true),
  include_archived: z.boolean().default(false),
})

// Type exports
export type NotificationType = z.infer<typeof notificationTypeSchema>
export type NotificationStatus = z.infer<typeof notificationStatusSchema>
export type NotificationPriority = z.infer<typeof notificationPrioritySchema>
export type NotificationUser = z.infer<typeof notificationUserSchema>
export type NotificationTarget = z.infer<typeof notificationTargetSchema>
export type NotificationPayload = z.infer<typeof notificationPayloadSchema>
export type NotificationRecord = z.infer<typeof notificationRecordSchema>
export type NotificationCreateInput = z.infer<typeof notificationCreateInputSchema>
export type NotificationUpdateInput = z.infer<typeof notificationUpdateInputSchema>
export type NotificationQuery = z.infer<typeof notificationQuerySchema>

/**
 * Utility function to create a notification matching the UI display format
 * Maps our notification record to the format expected by profile-notifications.vue
 */
export function formatNotificationForDisplay(notification: NotificationRecord) {
  return {
    id: notification.id,
    user: {
      name: notification.payload.user.name,
      src: notification.payload.user.avatar || `/img/avatars/${Math.floor(Math.random() * 25) + 1}.svg`,
      text: notification.payload.user.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase(),
    },
    date: notification.created_at.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }),
    time: notification.created_at.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    }),
    status: notification.status === 'unread' ? 0 : 1,
    target: notification.payload.target
      ? {
          type: notification.payload.target.type,
          name: notification.payload.target.name,
          url: notification.payload.target.url || '#',
          text: notification.payload.action || getDefaultActionText(notification.type),
        }
      : undefined,
    people: notification.payload.people.map(person => ({
      name: person.name,
      src: person.avatar || `/img/avatars/${Math.floor(Math.random() * 25) + 1}.svg`,
      text: person.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase(),
    })),
  }
}

/**
 * Get default action text based on notification type
 */
function getDefaultActionText(type: NotificationType): string {
  const actionMap: Record<NotificationType, string> = {
    'system.security': 'sent a security alert about',
    'system.update': 'announced an update to',
    'system.maintenance': 'scheduled maintenance for',
    'workspace.invitation': 'invited you to',
    'workspace.member_joined': 'joined',
    'workspace.member_left': 'left',
    'workspace.settings_changed': 'updated settings for',
    'profile.mention': 'mentioned you in',
    'profile.view': 'viewed your profile in',
    'profile.connection_request': 'sent a connection request via',
    'task.assigned': 'assigned you to',
    'task.completed': 'completed a task in',
    'task.due_soon': 'reminded you about a due task in',
    'comment.new': 'commented on',
    'comment.reply': 'replied to your comment on',
    'file.shared': 'shared a file with',
    'file.uploaded': 'uploaded a file to',
    'document.edited': 'edited a document in',
    'message.new': 'sent you a message in',
    'message.reaction': 'reacted to your message in',
    'marketing.product_update': 'announced a product update for',
    'marketing.newsletter': 'sent the newsletter for',
    'marketing.partner_offer': 'shared a partner offer for',
  }

  return actionMap[type] || 'updated'
}
