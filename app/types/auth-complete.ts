import type { Component } from 'vue'
import type { SocialProvider } from './auth-components'
import type { LoginFormInput, SignupFormInput } from '~/schemas/auth-validation'

// Base interfaces for common props
interface BaseAuthProps {
  /**
   * Title text
   */
  title?: string
  /**
   * Subtitle text
   */
  subtitle?: string
  /**
   * Whether to show navigation
   * @default true
   */
  showNavigation?: boolean
  /**
   * URL to redirect to after successful action
   */
  redirectTo?: string
  /**
   * Back navigation URL
   * @default '/dashboards'
   */
  backUrl?: string
  /**
   * Back navigation text
   * @default 'Back to Home'
   */
  backText?: string
  /**
   * Custom logo component
   */
  logo?: Component
  /**
   * Logo size class
   * @default 'size-10'
   */
  logoSize?: string
  /**
   * Whether to show theme toggle
   * @default true
   */
  showThemeToggle?: boolean
  /**
   * Input border radius
   * @default 'md'
   */
  inputRounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  /**
   * Submit button border radius
   * @default 'md'
   */
  submitRounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  /**
   * Loading state
   * @default false
   */
  loading?: boolean
  /**
   * Disabled state
   * @default false
   */
  disabled?: boolean
}

interface BaseSocialAuthProps {
  /**
   * Whether to show social login
   * @default true
   */
  showSocialLogin?: boolean
  /**
   * Social providers array
   */
  socialProviders?: SocialProvider[]
  /**
   * Social buttons variant
   * @default 'default'
   */
  socialVariant?: 'default' | 'compact'
  /**
   * Whether to show divider
   * @default true
   */
  showDivider?: boolean
  /**
   * Divider text
   * @default 'OR'
   */
  dividerText?: string
}

// Login component interfaces
export interface AuthLogin1Props extends BaseAuthProps, BaseSocialAuthProps {
  /**
   * Illustration source path
   * @default '/img/illustrations/magician.svg'
   */
  illustrationSrc?: string
  /**
   * Illustration alt text
   * @default 'Authentication illustration'
   */
  illustrationAlt?: string
  /**
   * Which side to place illustration
   * @default 'right'
   */
  illustrationSide?: 'left' | 'right'
  /**
   * Whether to show remember me checkbox
   * @default true
   */
  showRememberMe?: boolean
  /**
   * Forgot password URL
   * @default '/auth/recover'
   */
  forgotPasswordUrl?: string
  /**
   * Signup page URL
   * @default '/auth/signup-1'
   */
  signupUrl?: string
  /**
   * Remember me checkbox label
   * @default 'Trust for 60 days'
   */
  rememberMeLabel?: string
  /**
   * Forgot password link text
   * @default 'Forgot your password?'
   */
  forgotPasswordText?: string
  /**
   * Signup prompt text
   * @default "Don't have an account?"
   */
  signupPrompt?: string
  /**
   * Signup link text
   * @default 'start your 14-day free trial'
   */
  signupText?: string
  /**
   * Submit button text
   * @default 'Sign in'
   */
  submitText?: string
  /**
   * Initial form values
   */
  initialValues?: Partial<LoginFormInput>
}

export interface AuthLogin2Props extends AuthLogin1Props {
  // Inherits all props from AuthLogin1Props
  // Login2 uses same props but different styling
}

export interface AuthLogin3Props extends AuthLogin1Props {
  /**
   * Component variant for custom styling
   * @default 'default'
   */
  variant?: 'default' | 'minimal' | 'custom'
  /**
   * Custom CSS classes when variant is 'custom'
   */
  customClass?: string
}

// Signup component interfaces
export interface AuthSignup1Props extends BaseAuthProps {
  /**
   * Marketing section title
   * @default 'Have an Account?'
   */
  marketingTitle?: string
  /**
   * Marketing section subtitle
   */
  marketingSubtitle?: string
  /**
   * Marketing button text
   * @default 'Login to Account'
   */
  marketingButtonText?: string
  /**
   * Login page URL
   * @default '/auth/login-1'
   */
  loginUrl?: string
  /**
   * Whether to show marketing section
   * @default true
   */
  showMarketingSection?: boolean
  /**
   * Whether to show password strength indicator
   * @default true
   */
  showPasswordStrength?: boolean
  /**
   * Terms and conditions URL
   */
  termsUrl?: string
  /**
   * Submit button text
   * @default 'Create Account'
   */
  submitText?: string
  /**
   * Login prompt text
   * @default 'Have an account?'
   */
  loginPrompt?: string
  /**
   * Login link text
   * @default 'Login here'
   */
  loginText?: string
  /**
   * Gradient starting color
   * @default 'primary-900'
   */
  gradientFrom?: string
  /**
   * Gradient ending color
   * @default 'primary-500'
   */
  gradientTo?: string
  /**
   * Whether to enable hover animations
   * @default true
   */
  enableAnimations?: boolean
  /**
   * Initial form values
   */
  initialValues?: Partial<SignupFormInput>
}

export interface AuthSignup2Props extends BaseAuthProps, BaseSocialAuthProps {
  /**
   * Login page URL
   * @default '/auth/login-2'
   */
  loginUrl?: string
  /**
   * Whether to show password strength indicator
   * @default true
   */
  showPasswordStrength?: boolean
  /**
   * Terms and conditions URL
   */
  termsUrl?: string
  /**
   * Submit button text
   * @default 'Sign Up'
   */
  submitText?: string
  /**
   * Login prompt text
   * @default 'Already have an account?'
   */
  loginPrompt?: string
  /**
   * Login link text
   * @default 'Sign in'
   */
  loginText?: string
  /**
   * Maximum width class for the form
   * @default 'max-w-md'
   */
  maxWidth?: string
  /**
   * Spacing class for padding
   * @default 'px-4'
   */
  spacing?: string
  /**
   * Whether to show background content
   * @default true
   */
  showBackground?: boolean
  /**
   * Background image path
   */
  backgroundImage?: string
  /**
   * Initial form values
   */
  initialValues?: Partial<SignupFormInput>
}

export interface AuthSignup3Props extends BaseAuthProps, BaseSocialAuthProps {
  /**
   * Login page URL
   * @default '/auth/login-3'
   */
  loginUrl?: string
  /**
   * Whether to show password strength indicator
   * @default true
   */
  showPasswordStrength?: boolean
  /**
   * Terms and conditions URL
   */
  termsUrl?: string
  /**
   * Submit button text
   * @default 'Create Account'
   */
  submitText?: string
  /**
   * Login prompt text
   * @default 'Have an account?'
   */
  loginPrompt?: string
  /**
   * Login link text
   * @default 'Login here'
   */
  loginText?: string
  /**
   * Illustration source path
   * @default '/img/illustrations/magician.svg'
   */
  illustrationSrc?: string
  /**
   * Illustration alt text
   * @default 'Authentication illustration'
   */
  illustrationAlt?: string
  /**
   * Which side to place illustration
   * @default 'right'
   */
  illustrationSide?: 'left' | 'right'
  /**
   * Component variant for custom styling
   * @default 'default'
   */
  variant?: 'default' | 'minimal' | 'custom'
  /**
   * Custom CSS classes when variant is 'custom'
   */
  customClass?: string
  /**
   * Initial form values
   */
  initialValues?: Partial<SignupFormInput>
}

// Recover component interfaces
export interface AuthRecoverProps extends BaseAuthProps {
  /**
   * Success message text
   */
  successMessage?: string
  /**
   * Success note text
   */
  successNote?: string
  /**
   * Login page URL
   * @default '/auth/login-1'
   */
  loginUrl?: string
  /**
   * Submit button text
   * @default 'Recover Password'
   */
  submitText?: string
  /**
   * Login prompt text
   * @default 'False alert?'
   */
  loginPrompt?: string
  /**
   * Login link text
   * @default 'Sign in'
   */
  loginText?: string
  /**
   * Maximum width class for the form
   * @default 'max-w-md'
   */
  maxWidth?: string
  /**
   * Spacing class for padding
   * @default 'px-4'
   */
  spacing?: string
  /**
   * Whether to show background content
   * @default true
   */
  showBackground?: boolean
  /**
   * Background image path
   */
  backgroundImage?: string
  /**
   * Success state (controlled externally)
   */
  success?: boolean
  /**
   * Initial form values
   */
  initialValues?: Partial<{ email: string }>
}

// Emit interfaces
export interface AuthLogin1Emits {
  /**
   * Emitted on successful login
   */
  loginSuccess: [result: { user: any, workspaces: any[], currentWorkspaceId?: string }]
  /**
   * Emitted on login error
   */
  loginError: [error: { code: string, message: string }]
  /**
   * Emitted when social login button is clicked
   */
  socialLogin: [provider: string]
  /**
   * Emitted when forgot password link is clicked
   */
  forgotPassword: [email: string]
}

export interface AuthLogin2Emits extends AuthLogin1Emits {
  // Same as AuthLogin1Emits
}

export interface AuthLogin3Emits extends AuthLogin1Emits {
  // Same as AuthLogin1Emits
}

export interface AuthSignup1Emits {
  /**
   * Emitted on successful signup
   */
  signupSuccess: [result: { user: any, workspaceId: string }]
  /**
   * Emitted on signup error
   */
  signupError: [error: { code: string, message: string }]
}

export interface AuthSignup2Emits extends AuthSignup1Emits {
  /**
   * Emitted when social login button is clicked
   */
  socialLogin: [provider: string]
}

export interface AuthSignup3Emits extends AuthSignup2Emits {
  // Same as AuthSignup2Emits
}

export interface AuthRecoverEmits {
  /**
   * Emitted on successful password reset request
   */
  recoverSuccess: [email: string]
  /**
   * Emitted on password recovery error
   */
  recoverError: [error: { code: string, message: string }]
}

// Export all prop types for external usage
export type {
  BaseAuthProps,
  BaseSocialAuthProps,
}
