/**
 * AI-related type definitions for the application
 */

export interface AiPromptSuggestion {
  id: string
  title: string
  prompt: string
  description?: string
  icon?: string // iconify name, e.g., 'solar:sparkles-bold-duotone'
  color?: 'primary' | 'success' | 'info' | 'warning' | 'danger' | 'muted'
  variant?: 'solid' | 'pastel' | 'outline' | 'glass'
}

export interface WidgetsAiPromptSuggestionData {
  title?: string
  subtitle?: string
  items: AiPromptSuggestion[]
  columns?: {
    base?: 1 | 2
    sm?: 1 | 2
    md?: 2 | 3 | 4
    lg?: 3 | 4
    xl?: 4 | 6
  }
  clickable?: boolean
}

export interface ChatMode {
  mode: 'single' | 'multi'
  agents: string[]
}

export interface AIProvider {
  id: string
  name: string
  models: string[]
}
