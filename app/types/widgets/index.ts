/**
 * Widget Configuration Types
 *
 * Centralized TypeScript interfaces for all widget data structures.
 * These types ensure consistent APIs across all widgets.
 */

// ===== Base Types =====

export interface BaseWidgetData {
  title?: string
  subtitle?: string
}

export interface TrendDelta {
  value: number
  percentage?: number
  isIncrease: boolean
  label?: string
  period?: string // e.g., "since last month", "this week"
}

export interface CurrencyAmount {
  value: number
  currency?: string // Default 'USD'
  formatted?: string // Pre-formatted string like "$1,234.56"
}

export interface Icon {
  name: string
  color?: string
  bgColor?: string
  size?: string
}

// ===== Stats Widget Types =====

export interface StatMetric {
  icon: Icon
  title: string
  value: string | number
  trend?: TrendDelta
  subtitle?: string
}

export interface KpiTileData extends BaseWidgetData {
  icon: Icon
  value: string | number
  trend?: TrendDelta
}

export interface KpiRowData extends BaseWidgetData {
  items: StatMetric[]
}

// ===== Chart Widget Types =====

// Re-export common Apex types (assuming ApexCharts is available)
// Note: In actual implementation, import these from 'apexcharts'
export type ApexOptions = any // Would be: import { ApexOptions } from 'apexcharts'
export type ApexAxisChartSeries = any[]
export type ApexNonAxisChartSeries = number[]

export interface ChartConfig {
  options: ApexOptions
  series: ApexAxisChartSeries | ApexNonAxisChartSeries
  height?: number | string
  type?: 'line' | 'area' | 'bar' | 'column' | 'pie' | 'donut' | 'radial' | 'sparkline'
}

export interface ChartWidgetData extends BaseWidgetData {
  chart: ChartConfig
  metrics?: {
    label: string
    value: string | number
  }[]
  actions?: {
    label: string
    href?: string
    onClick?: () => void
  }[]
}

export interface SparklineData {
  chartComponent: string // Name of chart component to render
}

export interface GoalOverviewData extends BaseWidgetData {
  completed: number | string
  inProgress: number | string
}

export interface SalesGrowthData extends BaseWidgetData {
  channel: {
    name: string
    icon: string
    description: string
  }
}

export interface ProfitChartData extends BaseWidgetData {
  // Simple chart widget with just a title
}

export interface SalesRevenueData extends BaseWidgetData {
  revenue: string
  description: string
}

export interface AdditionalStatsData extends BaseWidgetData {
  stats: {
    chartComponent: string
    value: string | number
    label: string
  }[]
}

export interface HistoryChartData extends BaseWidgetData {
  viewReportsLink?: {
    text: string
    href: string
  }
}

export interface OverallProgressData extends BaseWidgetData {
  level: string
  progress: number
  description: string
  viewDetailsLink?: {
    text: string
    href: string
  }
}

export interface GenericChartData extends BaseWidgetData {
  chartComponent: string
  chartProps?: Record<string, any>
}

export interface SimpleChartData {
  title: string
  chartComponent: string
}

// Trading widgets
export interface TradingStockCardData {
  name: string
  logo: string
  label: string
  change: number
  percentage: number
  changeSymbol: string
  isPositive: boolean
}

export interface TradingStockItem {
  name: string
  logo: string
  change: number
  percentage: number
  changeSymbol: string
  isPositive: boolean
}

export interface TradingTrendingStocksData {
  title: string
  viewAllLink: string
  viewAllLabel: string
  stocks: TradingStockItem[]
}

export interface TradingInsightItem {
  label: string
  value: string
}

export interface TradingMarketInsightsData {
  title: string
  subtitle: string
  icon: string
  insights: TradingInsightItem[]
}

export interface TradingPriceDetail {
  label: string
  value: string
}

export interface TradingMarketOrderData {
  title: string
  subtitle: string
  sharesLabel: string
  shares: number
  priceDetails: TradingPriceDetail[]
  totalLabel: string
  totalValue: string
  buttonText: string
}

// Trading chart data
export interface TradingBankData {
  id: number | string
  name: string
  logo: string
  series: any[] // ApexCharts series data
}

export interface TradingStockChartData {
  banks: TradingBankData[]
}

// Stocks widgets
export interface StockCategory {
  name: string
  icon: string
  iconColor: string
  hoverColors: string
  href: string
}

export interface StockCategoryNavigationData {
  categories: StockCategory[]
}

export interface StockAction {
  title: string
  description: string
  icon: string
  href: string
}

export interface StockCompany {
  symbol: string
  name: string
  icon: string
  iconColors: string
}

export interface StockCardData {
  isOpen: boolean
  chartComponent: string
  company: StockCompany
  price: number
  actions: StockAction[]
}

export interface StockItem {
  id: string
  name: string
  company: string
  icon: string
  iconColors: string
  price: number
}

export interface StockTrendingListData {
  title: string
  viewAllLabel: string
  stocks: StockItem[]
}

export interface StockProfitEvolutionData {
  title: string
  viewAllLabel: string
  chartComponent: string
}

// Health widgets
export interface HealthMetric {
  value: string
  label: string
}

export interface HealthSummaryHeaderData {
  title: string
  description: string
  illustration: string
  illustrationAlt: string
  metrics: HealthMetric[]
}

export interface HealthMetricCardData {
  icon: string
  value: string
  unit: string
  title: string
  description: string
}

export interface HealthChartCardData {
  title: string
  tooltip?: string
  description: string
  readMoreLink?: string
  chartComponent: string
  chartClass?: string
}

// Company widgets
export interface CompanyStat {
  icon: string
  value: string | number
  label: string
}

export interface CompanyStatsHeaderData {
  stats: CompanyStat[]
}

export interface CompanyTeamMember {
  id: string
  src: string
  name: string
  role: string
  expertise: string
  rate: number
  status: 'Available' | 'New' | 'Hired'
}

export interface CompanyTeamTableData {
  team: CompanyTeamMember[]
  actionLabel?: string
}

export interface CompanyPendingTicketsData {
  title: string
  viewAllLink?: {
    href: string
    label: string
  }
  contentComponent: string
  contentProps?: Record<string, any>
}

// Enhanced simple chart widget (more flexible than previous versions)
export interface SimpleChartWidgetData {
  title?: string
  chartComponent: string
  chartClass?: string
  chartProps?: Record<string, any>
  // Card styling options
  rounded?: string
  cardClass?: string
  // Title styling options
  titleClass?: string
  titleTag?: string
  titleSize?: string
  titleWeight?: string
  titleLead?: string
  titleHeadingClass?: string
}

// Banking widgets (extension of existing finance types)
export interface CreditCardData {
  type: string
  endingNumber: number
  balance: number
}

export interface BankingCardsOverviewData {
  title: string
  cards: CreditCardData[]
  description: string
  balanceTitle?: string
  banks: BankAccount[]
  currentBalance: number
  cardNumber: string
  income: number
  expense: number
}

export interface BankingTransaction {
  icon: string
  iconClass: string
  title: string
  timestamp: string
  amount: number
  isIncome: boolean
}

export interface BankingSummaryData {
  summaryTitle: string
  summary: {
    income: {
      label: string
      value: number
    }
    expenses: {
      label: string
      value: number
    }
  }
  chartComponent: string
  chartProps?: Record<string, any>
  transactionsTitle: string
  transactions: BankingTransaction[]
  actions?: {
    settings?: string
    create?: string
  }
}

// Shared widgets
export interface InfoCardData {
  title: string
  description: string
  icon: string
  link: string
  linkLabel: string
}
export interface PersonalScoreData extends BaseWidgetData {
  chartComponent: string
  description: string
}

export interface EcommerceMetricData extends BaseWidgetData {
  value: string | number
  icon: string
  iconClass: string
  chartComponent: string
}

// ===== Finance Widget Types =====

export interface TransactionItem {
  id?: string | number
  icon: string
  iconColor?: string
  title: string
  subtitle?: string
  amount: number
  currency?: string
  timestamp?: Date | string
}

export interface BankAccount {
  id: string | number
  name: string
  text?: string // e.g., "checking **** **** 0499"
  media?: string // Logo/icon URL
  balance?: CurrencyAmount
}

export interface MyCardsData extends BaseWidgetData {
  cards: {
    label: string
    balance: CurrencyAmount
    color: string
    actions: {
      title: string
      description: string
      icon: string
      href: string
    }[]
  }[]
  information?: {
    editLink?: {
      text: string
      href: string
    }
    items: {
      label: string
      value: string
      status?: {
        color: string
      }
      icon?: string
      iconClass?: string
      textSize?: string
    }[]
  }
}

export interface QuickTransferData extends BaseWidgetData {
  fromAccounts: BankAccount[]
  toAccounts?: BankAccount[]
  selectedFrom?: BankAccount
  selectedTo?: BankAccount
  currencies?: string[]
  selectedCurrency?: string
  description?: string
  submitButtonText?: string
}

export interface SendMoneyData extends BaseWidgetData {
  toAccounts: BankAccount[]
  selectedTo?: BankAccount
  currencies: {
    value: string
    symbol: string
  }[]
  selectedCurrency?: string
  contacts: {
    src?: string
    text?: string
    class?: string
  }[]
  seeAllLink?: {
    text: string
    href: string
  }
  contactsLink?: {
    text: string
    href: string
  }
  transferNote?: string
  submitButtonText?: string
}

export interface RecentActivityData extends BaseWidgetData {
  transactions: TransactionItem[]
  showAll?: boolean
  maxItems?: number
  actions?: {
    label: string
    href?: string
  }
}

export interface AccountBalanceData extends BaseWidgetData {
  balance: number
  change: number
  changeLabel?: string
  changeIcon?: string
  isIncrease?: boolean
}

// ===== List Widget Types =====

export interface ListItem {
  id?: string | number
  title: string
  subtitle?: string
  description?: string
  icon?: string
  media?: string // Image URL
  badge?: {
    text: string
    color?: string
  }
  timestamp?: Date | string
  href?: string
  onClick?: () => void
}

export interface ActivityListData extends BaseWidgetData {
  items: ListItem[]
  maxItems?: number
  showTimestamp?: boolean
  emptyMessage?: string
}

// ===== Widget Props Interfaces =====

// Base props that all widgets should accept
export interface BaseWidgetProps {
  // Optional class overrides for styling customization
  cardClass?: string
  headerClass?: string
  bodyClass?: string
  footerClass?: string
}

// Specific widget prop interfaces
export interface KpiTileProps extends BaseWidgetProps {
  data?: KpiTileData
}

export interface ChartWidgetProps extends BaseWidgetProps {
  data?: ChartWidgetData
}

export interface MyCardsProps extends BaseWidgetProps {
  data?: MyCardsData
}

export interface QuickTransferProps extends BaseWidgetProps {
  data?: QuickTransferData
}

export interface RecentActivityProps extends BaseWidgetProps {
  data?: RecentActivityData
}

export interface AccountBalanceProps extends BaseWidgetProps {
  data?: AccountBalanceData
}

export interface ActivityListProps extends BaseWidgetProps {
  data?: ActivityListData
}

export interface SparklineProps extends BaseWidgetProps {
  data?: SparklineData
}

// ===== Crypto Widget Types =====

export interface CryptoPriceHeaderData {
  currency: {
    name: string
    symbol: string
  }
  price: {
    value: number
    formatted: string
  }
  change: {
    value: number
    percentage: number
    isPositive: boolean
    formatted: string
  }
  periods: {
    label: string
    value: string
    active?: boolean
  }[]
  onPeriodChange?: (period: string) => void
}

export interface CryptoStatTile {
  title: string
  value: string
  subtitle?: string
  trend?: {
    value: number
    percentage: number
    isPositive: boolean
    formatted: string
  }
}

export interface CryptoStatsTilesData {
  stats: CryptoStatTile[]
}

export interface CryptoChartData {
  title: string
  chartComponent: string
  chartProps?: Record<string, any>
  actionButton?: {
    label: string
    variant?: string
    color?: string
    size?: string
    onClick?: () => void
  }
}

export interface CryptoInfoCardData {
  icon: string
  title: string
  description: string
  actions: {
    label: string
    color: string
    variant: string
  }[]
}

export interface CryptoContentWidgetData {
  title: string
  viewAllLabel?: string
  contentComponent: string
  contentProps?: Record<string, any>
  onViewAll?: () => void
}

// ===== Utils Widget Types =====

export interface DatePickerWidgetData {
  locale?: string
  label?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  rounded?: string
}

export interface NotificationsWidgetData {
  title?: string
  notificationComponent?: string
  notificationProps?: Record<string, any>
  viewAllLink?: {
    href: string
    text?: string
  }
  cardClass?: string
  rounded?: string
}

export interface PromotionalCardData {
  title: string
  description: string
  link?: {
    href: string
    text: string
  }
  icon?: {
    name: string
    size?: string
    class?: string
  }
  gradient?: string
  rounded?: string
  cardClass?: string
  titleSize?: string
  titleClass?: string
  descriptionSize?: string
  descriptionClass?: string
  maxWidth?: string
  linkClass?: string
  iconPosition?: string
  iconSize?: string
}

// ===== Personal Widget Types =====

export interface PersonalMetricData {
  value: string | number
  label: string
  description: string
}

export interface PersonalPromotionalCardData {
  text: string
  link?: {
    href: string
    text: string
  }
  icon?: {
    name: string
    class?: string
  }
  gradient?: string
  textClass?: string
  linkClass?: string
}

export interface PersonalHeaderData {
  avatarSrc: string
  badgeSrc?: string
  greeting: string
  displayName: string
  subtitle: string
  metric?: PersonalMetricData
  promotionalCard?: PersonalPromotionalCardData
}

export interface PersonalContentListData {
  title: string
  titleSize?: string
  viewAllLink?: {
    href: string
    text?: string
  }
  contentComponent: string
  contentProps?: Record<string, any>
  contentClass?: string
  cardClass?: string
  rounded?: string
}

// Interview-specific personal widget types
export interface InterviewWelcomeData {
  displayName: string
  description: string
  emoji: string
  progressLabel: string
  progressStatus: string
  ctaText: string
}

export interface PersonalMetricCardData {
  icon: string
  iconClass: string
  value: string | number
  label: string
}

export interface InterviewCardData {
  name: string
  avatar: string
  timeSlot: string
}

// ===== Creative Widget Types =====

export interface CreativeInfoBadgesData {
  image: string
  badgeSmall: string
  badgeMedium: string
  title: string
  text: string
}

export interface CreativeInfoImageData {
  image: string
  title: string
  text: string
  rounded?: string
}

export interface CreativeTeamListData {
  title: string
  actionLabel?: string
  contentComponent: string
  contentProps?: Record<string, any>
}

export interface CreativeGenericData {
  contentComponent: string
  contentProps?: Record<string, any>
  rounded?: string
  cardClass?: string
  contentRounded?: string
}

// ===== UI Widget Types =====

export interface UICalendarData {
  attributes?: any[]
  titlePosition?: string
  expanded?: boolean
  borderless?: boolean
  transparent?: boolean
  trimWeeks?: boolean
  calendarClass?: string
}

export interface UIIconTextData {
  title: string
  icon: string
  text: string
  indicator?: boolean
}

export interface UIPictureData {
  src: string
  alt: string
  rounded?: string
  height?: number
  width?: number
  loading?: boolean
}

export interface UIInboxMessageData {
  picture: string
  name: string
  title: string
  text: string
  time: string
  rounded?: string
}

// ===== List Widget Types =====

export interface ListTitledData {
  title: string
  actionLabel?: string
  listComponent: string
  listProps?: Record<string, any>
  rounded?: string
  color?: string
}

export interface ListSimpleData {
  listComponent: string
  listProps?: Record<string, any>
  rounded?: string
  color?: string
}

// ===== Writer Widget Types =====

export interface WriterHeaderData {
  illustration: string
  illustrationAlt: string
  greeting: string
  description: string
  ctaIcon: string
  ctaText: string
}

export interface WriterMetricTileData {
  label: string
  value: string | number
}

export interface WriterArticleAuthor {
  avatar: string
  initials: string
  name: string
  role: string
}

export interface WriterArticle {
  id: string
  title: string
  excerpt: string
  image: string
  imageAlt: string
  link: string
  category: string
  author: WriterArticleAuthor
}

export interface WriterFilterOption {
  label: string
  value: string
}

export interface WriterArticlesSectionData {
  title: string
  filters: WriterFilterOption[]
  defaultFilter?: string
  articles: WriterArticle[]
}

// ===== Flights Widget Types =====

export interface FlightLocation {
  time: string
  date: string
  city: string
  airport: string
}

export interface FlightResult {
  logo: string
  company: string
  stops: number
  price: number
  departure: FlightLocation
  arrival: FlightLocation
}

export interface FlightSearchHeaderData {
  icon: string
  route: string
  subtitle: string
  dateRange: {
    start: Date
    end: Date
  }
  illustration: string
  illustrationAlt: string
}

export interface FlightOptionCardData {
  price: string
  label: string
  duration: string
  variant?: 'default' | 'primary'
  isPrimary?: boolean
}

export interface FlightResultCardData {
  results: FlightResult[]
  formatPrice: (price: number) => string
}

export interface FlightTravelerStat {
  label: string
  value: string | number
}

export interface FlightTravelerProfileData {
  avatar: string
  name: string
  subtitle: string
  stats: FlightTravelerStat[]
}

export interface FlightFilterOption {
  value: string
  label: string
}

export interface FlightFilterSection {
  title: string
  options: FlightFilterOption[]
}

export interface FlightFiltersData {
  ctaText: string
  sections: FlightFilterSection[]
}

// ===== Hobbies Widget Types =====

export interface HobbyCategoryData {
  title: string
  icon: string
  image: string
  link: string
  ctaText?: string
}

export interface HobbyActivityItem {
  name: string
  date: string
  icon: string
  image?: string
}

export interface HobbyActivityListData {
  title: string
  subtitle: string
  activities: HobbyActivityItem[]
  getRandomColor?: () => string
}

export interface HobbyLocationItem {
  name: string
  location: string
  level: string
  image: string
  link: string
}

export interface HobbyLocationsListData {
  title: string
  locations: HobbyLocationItem[]
}

// ===== Influencer/Social Media Widget Types =====

export interface InfluencerProfileBadge {
  icon: string
  bgColor: string
  iconColor: string
}

export interface InfluencerProfileStat {
  value: string
  label: string
}

export interface InfluencerProfileHeaderData {
  username: string
  realName: string
  avatar: string
  badgeSrc: string
  verified: boolean
  stats: InfluencerProfileStat[]
  bio: string
  bioLink?: {
    text: string
    href: string
  }
  badges: InfluencerProfileBadge[]
  ctaText: string
}

export interface SocialMediaStat {
  label: string
  action: string
  value: number
  growth: number
  growthText: string
  icon: string
}

export interface SocialMediaStatsData {
  platform: string
  title: string
  description: string
  stats: SocialMediaStat[]
  ctaText: string
}

export interface CompanyCollaborationStat {
  label: string
  value: number
}

export interface CompanyFollower {
  tooltip: string
  src: string
}

export interface CompanyCollaboration {
  name: string
  logo: string
  title: string
  description: string
  tags: string[]
  stats: CompanyCollaborationStat[]
  followers: CompanyFollower[]
}

export interface CompanyCollaborationsData {
  title: string
  description: string
  companies: CompanyCollaboration[]
  ctaText: string
}

// ===== Jobs Widget Types =====

export interface JobSearchOption {
  value: string
  label: string
}

export interface JobSearchData {
  searchPlaceholder?: string
  locationPlaceholder?: string
  typeOptions: JobSearchOption[]
  salaryRangeOptions: JobSearchOption[]
  searchButtonText?: string
  onSearch?: (data: {
    keywords: string
    location: string
    type: string
    salaryRange: string
  }) => void
}

export interface JobAlertData {
  title: string
  description: string
  inputPlaceholder?: string
  buttonText?: string
  onCreateAlert?: (keywords: string) => void
}

export interface JobFilterOption {
  value: string
  label: string
  count: number
}

export interface JobFilterGroup {
  title: string
  options: JobFilterOption[]
}

export interface JobFiltersData {
  filterGroups: JobFilterGroup[]
  selectedFilters?: string[]
  onFiltersChange?: (filters: string[]) => void
}

export interface JobApplicant {
  tooltip: string
  src: string
}

export interface JobListing {
  company: string
  logo: string
  title: string
  description: string
  tags: string[]
  applicants: JobApplicant[]
}

export interface JobListingData {
  title?: string
  subtitle?: string
  jobs: JobListing[]
  onApply?: (job: JobListing) => void
  onViewDetails?: (job: JobListing) => void
  applyButtonText?: string
  detailsButtonText?: string
}

// ===== HR/Human Resources Widget Types =====

export interface HRWelcomeHeaderData {
  avatar: string
  greeting: string
  newRookies: {
    title: string
    description: string
    avatars: string[]
    ctaText: string
  }
  jobFeed: {
    title: string
    description: string
    ctaText: string
  }
}

export interface HRNotificationItem {
  title: string
  subtitle: string
  icon: string
  count: number
  status: string
  statusVariant: string
}

export interface HRNotificationsTableData {
  title: string
  description: string
  learnMoreLink?: string
  items: HRNotificationItem[]
}

export interface HRRookie {
  name: string
  role: string
  avatar: string
  stack: string
}

export interface HRRookiesGridData {
  title: string
  viewAllLink?: string
  rookies: HRRookie[]
  ctaText: string
}

// ===== Soccer Widget Types =====

export interface SoccerTeam {
  name: string
  logo: string
  score?: number
}

export interface SoccerMatch {
  home: SoccerTeam
  away: SoccerTeam
}

export interface SoccerLiveMatchData {
  tournament: string
  group: string
  matchDescription: string
  liveTag?: {
    icon: string
    text: string
  }
  match: SoccerMatch
  ctaText: string
}

export interface SoccerAvailableLeaguesData {
  title: string
  listComponent: string
  listProps?: Record<string, any>
}

export interface SoccerContestPromoData {
  title: string
  description: string
  ctaText: string
  ctaIcon?: string
  illustration: string
  illustrationAlt: string
  bgClass?: string
}

export interface SoccerMatchItem {
  id: number
  status: 'live' | 'scheduled' | 'finished'
  time: string
  stadium: string
  match: SoccerMatch
}

export interface SoccerMatchesTableData {
  title?: string
  matches: SoccerMatchItem[]
  filters: {
    label: string
    value: string
    active?: boolean
  }[]
}

// ===== Settings Widget Types =====

export interface SettingsGridItem {
  title: string
  description: string
  icon: string
  link: string
  titleSize?: string
}

export interface SettingsGridData {
  items: SettingsGridItem[]
  gridClass?: string
}

// ===== Wizard Widget Types =====

export interface WizardAgentInfoCardData {
  icon: string
  iconClass?: string
  label: string
  value?: string
  fallback?: string
  editButton?: {
    to: string
    class?: string
  }
  avatarSrc?: string
  showMultipleIcons?: boolean
  iconConditions?: {
    condition: any
    icon: string
    iconClass: string
  }[]
}

export interface WizardAgentMetricCardData {
  label: string
  value?: string | number
  fallback?: string
  valueSize?: 'sm' | 'md' | 'lg'
  valueClass?: string
  labelClass?: string
  cardClass?: string
  heightClass?: string
  editButton?: {
    to: string
    class?: string
  }
}

export interface WizardAgentBadgeSection {
  label: string
  count?: number
  items: string[]
  badgeClass?: string
}

export interface WizardAgentContentCardData {
  title?: string
  titleType?: 'heading' | 'text'
  titleClass?: string
  content?: string
  contentType?: 'text' | 'code' | 'badges' | 'list'
  contentClass?: string
  textClass?: string
  emptyMessage?: string
  footerText?: string
  footerClass?: string
  cardClass?: string
  badgeSections?: WizardAgentBadgeSection[]
  editButton?: {
    to: string
    class?: string
  }
  listItems?: {
    name: string
    role?: string
    description?: string
    picture?: string
    logo?: string
  }[]
}

// ===== Project Widget Types =====

export interface ProjectOwner {
  name: string
  avatar: string
}

export interface ProjectTeamMember {
  tooltip: string
  src: string
  text?: string
  badge?: string
}

export interface ProjectFeature {
  title: string
  description: string
  icon: string
}

export interface ProjectFile {
  name: string
  icon: string
  size: string
  version: string
}

export interface ProjectOverviewCardData {
  name: string
  category: string
  description: string
  image: string
  owner: ProjectOwner
  team: ProjectTeamMember[]
  features: ProjectFeature[]
  files?: ProjectFile[]
}

export interface ProjectCustomer {
  name: string
  logo: string
  text: string
  progress?: number
}

export interface ProjectListItem {
  name: string
  icon: string
  description: string
}

export interface ProjectInfoCardData {
  title: string
  type: 'customer' | 'list'
  customer?: ProjectCustomer
  items?: ProjectListItem[]
}

export interface ProjectTeamCardData {
  name: string
  avatar: string
  badge?: string
  role: string
  bio: string
}

export interface ProjectTaskAssignee {
  tooltip: string
  src: string
}

export interface ProjectTaskCardData {
  name: string
  description: string
  status: number
  completion: number
  assignee: ProjectTaskAssignee
  filesCount: number
  commentsCount: number
}

// ===== Utility Functions Type =====

export interface WidgetUtils {
  formatPrice: (amount: number, currency?: string) => string
  formatPercentage: (value: number) => string
  formatDate: (date: Date | string) => string
  getTrendIcon: (isIncrease: boolean) => string
  getTrendColor: (isIncrease: boolean) => string
}
