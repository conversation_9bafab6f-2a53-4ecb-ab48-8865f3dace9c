import type { BaseMessage } from '@langchain/core/messages'
import type { AIProvider, IntegrationCredential } from './ui'

/**
 * Chat Mode Types
 */
export type ChatMode = 'single' | 'multi'

export type ConversationStatus = 'active' | 'completed' | 'error' | 'archived'

export type MessageType = 'user' | 'assistant' | 'system' | 'tool' | 'error'

export type MessageRole = 'user' | 'assistant' | 'system'

/**
 * Chat Canvas View Configuration
 * Defines how a message should be rendered in the ChatCanvas
 */
export interface ChatViewConfig {
  type: string // View type (e.g., 'doc', 'inbox', 'tool_card', etc.)
  props?: Record<string, any> // Properties to pass to the view component
}

/**
 * Agent Configuration for LangGraph
 */
export interface AgentConfig {
  id: string
  name: string
  description: string
  systemPrompt: string
  provider: AIProvider
  modelName: string
  tools: string[] // Tool/MCP identifiers
  integrationIds: string[] // Required integration credentials
  maxTokens?: number
  temperature?: number
  avatar?: string
  color?: string
}

/**
 * Tool/MCP Configuration
 */
export interface ToolConfig {
  id: string
  name: string
  description: string
  category: 'search' | 'computation' | 'file' | 'api' | 'database' | 'custom'
  mcpServerId?: string // If this is an MCP tool
  schema?: Record<string, any> // Tool schema definition
}

/**
 * Chat Message Structure
 */
export interface ChatMessage {
  id: string
  conversationId: string
  type: MessageType
  role: MessageRole
  content: any
  agentId?: string // For multi-agent chats
  agentName?: string
  toolCalls?: Array<{
    id: string
    name: string
    arguments: Record<string, any>
    result?: any
  }>
  attachments?: Array<{
    type: 'image' | 'file' | 'url'
    url: string
    name?: string
    size?: number
  }>
  metadata?: {
    tokens?: number
    model?: string
    temperature?: number
    finishReason?: string
    streamId?: string
    // ChatCanvas view configuration
    view?: ChatViewConfig
    // Provider/model information for badges
    provider?: string
    // Additional metadata fields
    [key: string]: any
  }
  isStreaming?: boolean
  parentMessageId?: string
  userId: string
  workspaceId: string
  profileId: string
  createdAt: Date
  updatedAt?: Date
}

/**
 * Conversation Structure
 */
export interface Conversation {
  id: string
  title?: string
  description?: string
  mode: ChatMode
  status: ConversationStatus

  // Multi-agent configuration
  agents?: AgentConfig[]
  supervisorPrompt?: string

  // Single-agent configuration
  agentConfig?: AgentConfig

  // Conversation metadata
  messageCount: number
  lastMessageAt?: Date
  lastMessage?: string

  // Threading and state
  threadId?: string // LangGraph thread ID for persistence
  state?: Record<string, any> // Current conversation state

  // User/workspace context
  userId: string
  workspaceId: string
  profileId: string

  // Audit fields
  createdAt: Date
  updatedAt?: Date
  archivedAt?: Date
}

/**
 * Real-time WebSocket Message Types (Enhanced for Story 4.4)
 */
export interface WebSocketChatMessage {
  type: 'init' | 'start_chat' | 'user_message' | 'agent_message' | 'agent_token' | 'tool_call_started' | 'tool_progress' | 'tool_result' | 'tool_error' | 'chat_complete' | 'error' | 'typing' | 'heartbeat'
  conversationId?: string
  threadId?: string
  sessionId?: string
  message?: ChatMessage
  data?: {
    content?: string
    agentId?: string
    agentName?: string
    toolCalls?: any[]
    metadata?: Record<string, any>
    isComplete?: boolean
    streamId?: string
    // Tool streaming data (Story 4.4)
    toolId?: string
    toolName?: string
    progress?: number
    status?: 'running' | 'success' | 'error'
    result?: any
    toolPermissions?: { [toolId: string]: 'read' | 'write' } // Story 4.2 integration
  }
  error?: {
    code: string
    message: string
    details?: any
    toolId?: string // For tool-specific errors
  }
  timestamp: string
}

/**
 * Chat Session Configuration
 */
export interface ChatSessionConfig {
  mode: ChatMode
  agents: AgentConfig[]
  tools: ToolConfig[]
  supervisorPrompt?: string
  conversationId?: string
  maxTurns?: number
  timeout?: number
  // Optional per-session tool budget overrides (Story 4.6)
  toolBudgets?: Record<string, {
    callsPerMinute?: number
    dailyCalls?: number
    dailyTokens?: number
  }>
  // 4.8: Max parallel tool calls for this session
  maxParallelTools?: number
}

/**
 * LangGraph Workflow State
 */
export interface ChatWorkflowState {
  messages: BaseMessage[]
  currentAgent?: string
  nextAgent?: string
  isComplete: boolean
  metadata: {
    conversationId: string
    threadId: string
    userId: string
    workspaceId: string
    profileId: string
    turnCount: number
  }
}

/**
 * Agent Performance Metrics
 */
export interface AgentMetrics {
  agentId: string
  conversationId: string
  totalTokens: number
  responseTime: number
  toolUsageCount: number
  errorCount: number
  successRate: number
  timestamp: Date
}

/**
 * Chat Notification Configuration
 */
export interface ChatNotificationConfig {
  enabled: boolean
  newMessage: boolean
  agentResponse: boolean
  toolExecution: boolean
  errorAlerts: boolean
  fcmToken?: string
}

/**
 * Streaming Response Structure
 */
export interface StreamingChatResponse {
  conversationId: string
  messageId: string
  agentId?: string
  agentName?: string
  content: string
  isComplete: boolean
  toolCalls?: any[]
  metadata: {
    tokens: number
    model: string
    streamId: string
  }
}

/**
 * Chat History Filter Options
 */
export interface ChatHistoryFilter {
  workspaceId: string
  profileId: string
  mode?: ChatMode
  status?: ConversationStatus
  dateFrom?: Date
  dateTo?: Date
  agentId?: string
  limit?: number
  offset?: number
}

/**
 * Integration Context for Chat
 */
export interface ChatIntegrationContext {
  credentials: IntegrationCredential[]
  availableModels: Array<{
    provider: AIProvider
    modelName: string
    maxTokens: number
    supportsStreaming: boolean
  }>
  tools: ToolConfig[]
  mcpServers: Array<{
    id: string
    name: string
    url: string
    tools: string[]
  }>
}

/**
 * Conversation Analytics
 */
export interface ConversationAnalytics {
  conversationId: string
  totalMessages: number
  totalTokens: number
  averageResponseTime: number
  toolUsageBreakdown: Record<string, number>
  agentPerformance: AgentMetrics[]
  userSatisfactionScore?: number
  duration: number
  cost?: number
}

/**
 * Chat Export Options
 */
export interface ChatExportOptions {
  format: 'json' | 'markdown' | 'pdf' | 'csv'
  includeMetadata: boolean
  includeToolCalls: boolean
  dateRange?: {
    from: Date
    to: Date
  }
  conversations?: string[]
}
