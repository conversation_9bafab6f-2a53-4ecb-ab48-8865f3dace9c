import { z } from 'zod'
import { notificationPreferencesSchema } from './notification'

// Sub-schemas for profile arrays
export const experienceSchema = z.object({
  id: z.string().uuid().optional(),
  company: z.string().min(1),
  position: z.string().min(1),
  logo: z.string().optional(),
  period: z.string().min(1),
  description: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  isCurrent: z.boolean().default(false),
})

export const languageSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  mastery: z.string().min(1),
  level: z.number().min(0).max(100),
  icon: z.string().optional(),
})

export const skillSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  experience: z.number().min(0), // years of experience
  level: z.number().min(0).max(100),
  logo: z.string().optional(),
  icon: z.string().optional(),
})

export const toolSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  mastery: z.string().min(1),
  level: z.number().min(0).max(100),
  logo: z.string().optional(),
  category: z.string().optional(),
})

export const professionalInfoSchema = z.object({
  experience: z.enum(['0-2 years', '2-5 years', '5-10 years', '10+ years']).optional(),
  isFirstJob: z.boolean().optional(),
  isFlexible: z.boolean().optional(),
  worksRemotely: z.boolean().optional(),
})

export const socialLinksSchema = z.object({
  facebook: z.string().optional(),
  twitter: z.string().optional(),
  dribbble: z.string().optional(),
  instagram: z.string().optional(),
  github: z.string().optional(),
  gitlab: z.string().optional(),
  linkedin: z.string().optional(),
  website: z.string().optional(),
})

export const profileSettingsSchema = z.object({
  twoFactorAuth: z.object({
    enabled: z.boolean().default(false),
    phoneNumber: z.string().optional(),
  }).default({ enabled: false }),

  // Enhanced notification preferences with backward compatibility
  notifications: z.union([
    // New comprehensive notification preferences
    notificationPreferencesSchema,
    // Legacy simple notification settings (for backward compatibility)
    z.object({
      enabled: z.boolean().default(true),
      flushLowPriority: z.boolean().default(true),
      marketing: z.boolean().default(false),
      partners: z.boolean().default(false),
    }),
  ]).default({
    channels: { email: true, inApp: true, push: false },
    content: {
      system: true,
      workspace: true,
      mentions: true,
      assignments: true,
      reminders: true,
      comments: true,
      fileActivity: false,
    },
    marketing: { productUpdates: false, marketing: false, partnerOffers: false, newsletter: false },
    priority: {
      flushLowPriority: true,
      quietHours: { enabled: false, startTime: '22:00', endTime: '08:00', timezone: 'UTC' },
      digest: { enabled: false, frequency: 'daily', time: '09:00' },
    },
  }),

  privacy: z.object({
    emailOnProfileView: z.boolean().default(true),
    showOnlineStatus: z.boolean().default(true),
  }).default({ emailOnProfileView: true, showOnlineStatus: true }),
})

// Base schemas
export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  username: z.string().min(3),
  password_hash: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  last_login: z.date().nullable(),
  is_active: z.boolean(),
  deleted_at: z.date().nullable(),
})

export const workspaceRoleSchema = z.enum(['owner', 'admin', 'member', 'guest'])

export const workspaceSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  slug: z.string().min(1),
  description: z.string().nullable().optional(),
  logo_url: z.string().nullable().optional(),
  created_by: z.string().uuid(),
  created_at: z.date(),
  updated_at: z.date(),
  deleted_at: z.date().nullable(),
  // Role is added dynamically when workspace is loaded in context of a user
  role: workspaceRoleSchema.optional(),
})

// Extended profile schema
export const profileSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  display_name: z.string().min(1),

  // Core profile fields
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  role: z.string().optional(),
  location: z.string().optional(),
  bio: z.string().optional(),
  avatar_url: z.string().optional(),

  // Professional information
  professionalInfo: professionalInfoSchema.optional(),

  // Social links
  socialLinks: socialLinksSchema.optional(),

  // Experience and skills arrays
  experiences: z.array(experienceSchema).default([]),
  languages: z.array(languageSchema).default([]),
  skills: z.array(skillSchema).default([]),
  tools: z.array(toolSchema).default([]),

  // Profile settings (new structure)
  profileSettings: profileSettingsSchema.optional(),

  // Legacy settings (for backward compatibility - will be removed in future version)
  /** @deprecated Use profileSettings instead */
  settings: profileSettingsSchema.optional(),

  // Metadata
  created_at: z.date(),
  updated_at: z.date(),
  deleted_at: z.date().nullable(),
}).transform((data) => {
  // Transform legacy settings to profileSettings if needed
  if (data.settings && !data.profileSettings) {
    const legacySettings = data.settings as any
    return {
      ...data,
      profileSettings: {
        twoFactorAuth: {
          enabled: legacySettings.twoFactorEnabled || false,
          phoneNumber: legacySettings.phoneNumber,
        },
        notifications: {
          enabled: legacySettings.notificationsEnabled !== false,
          flushLowPriority: legacySettings.flushLowPriority !== false,
          marketing: legacySettings.marketingEmails || false,
          partners: legacySettings.partnerEmails || false,
        },
        privacy: {
          emailOnProfileView: legacySettings.emailOnProfileView !== false,
          showOnlineStatus: legacySettings.showOnlineStatus !== false,
        },
      },
      settings: undefined, // Remove legacy field after transformation
    }
  }
  return data
})

export const workspaceMemberSchema = z.object({
  workspace_id: z.string().uuid(),
  user_id: z.string().uuid(),
  profile_id: z.string().uuid(),
  role: workspaceRoleSchema,
  created_at: z.date(),
  updated_at: z.date(),
})

export const inviteStatusSchema = z.enum(['pending', 'accepted', 'rejected', 'expired'])

export const workspaceInviteSchema = z.object({
  id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  invited_email: z.string().email(),
  invited_by: z.string().uuid(),
  role: workspaceRoleSchema,
  status: inviteStatusSchema,
  token: z.string(),
  expires_at: z.date(),
  created_at: z.date(),
  updated_at: z.date(),
})

// Input schemas
export const signupSchema = z.object({
  email: z.string().email(),
  username: z.string().min(3),
  password: z.string().min(8),
  confirmPassword: z.string(),
}).refine((data: { password: any, confirmPassword: any }) => data.password === data.confirmPassword, {
  message: 'Passwords don\'t match',
  path: ['confirmPassword'],
})

export const loginSchema = z.object({
  email: z.string().email(),
  password: z.string(),
})

export const createWorkspaceSchema = z.object({
  name: z.string().min(1),
  slug: z.string().min(1),
  description: z.string().nullable().optional(),
  logo_url: z.string().nullable().optional(),
  useExistingProfile: z.boolean().optional(),
  profileId: z.string().uuid().optional(),
  displayName: z.string().min(1).optional(),
}).refine(
  (data: { useExistingProfile: any, profileId: any, displayName: any }) => {
    if (data.useExistingProfile) {
      return !!data.profileId
    }
    return !!data.displayName
  },
  {
    message: 'Either profileId or displayName must be provided',
    path: ['profileId'],
  },
)

export const inviteToWorkspaceSchema = z.object({
  workspaceId: z.string().uuid(),
  email: z.string().email(),
  role: workspaceRoleSchema,
})

// Types
export type User = z.infer<typeof userSchema>
export type Workspace = z.infer<typeof workspaceSchema>
export type Profile = z.infer<typeof profileSchema>
export type WorkspaceRole = z.infer<typeof workspaceRoleSchema>
export type WorkspaceMember = z.infer<typeof workspaceMemberSchema>
export type InviteStatus = z.infer<typeof inviteStatusSchema>
export type WorkspaceInvite = z.infer<typeof workspaceInviteSchema>

// Profile sub-types
export type Experience = z.infer<typeof experienceSchema>
export type Language = z.infer<typeof languageSchema>
export type Skill = z.infer<typeof skillSchema>
export type Tool = z.infer<typeof toolSchema>
export type ProfessionalInfo = z.infer<typeof professionalInfoSchema>
export type SocialLinks = z.infer<typeof socialLinksSchema>
export type ProfileSettings = z.infer<typeof profileSettingsSchema>

// Input types
export type SignupInput = z.infer<typeof signupSchema>
export type LoginInput = z.infer<typeof loginSchema>
export type CreateWorkspaceInput = z.infer<typeof createWorkspaceSchema>
export type InviteToWorkspaceInput = z.infer<typeof inviteToWorkspaceSchema>

// Profile update input type
export type ProfileUpdateInput = Partial<Omit<Profile, 'id' | 'user_id' | 'workspace_id' | 'created_at' | 'updated_at' | 'deleted_at'>>

// Password change schema and type
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
})

export type ChangePasswordInput = z.infer<typeof changePasswordSchema>
