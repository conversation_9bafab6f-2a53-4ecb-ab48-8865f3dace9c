// Planner types (Story 4.9)
// A plan represents a multi-step strategy for tool usage.

export type PlanStatus = 'draft' | 'ready' | 'running' | 'awaiting_approval' | 'completed' | 'failed' | 'cancelled'
export type PlanStepStatus = 'pending' | 'queued' | 'running' | 'awaiting_approval' | 'skipped' | 'success' | 'error'

export interface PlanStep {
  id: string
  toolId: string
  toolName: string
  inputs: Record<string, any>
  dependsOn: string[]
  requiresApproval: boolean
  permission: 'read' | 'write'
  sensitivity: 'low' | 'medium' | 'high'
  status: PlanStepStatus
  correlationId?: string
  result?: any
  error?: { code: string, message: string }
}

export interface PlanApproval {
  stepIds: string[]
  approverId: string
  approvedAt: string
  context?: any
}

export interface ToolPlan {
  id: string
  sessionId: string
  conversationId: string
  userId: string
  workspaceId: string
  profileId?: string
  message: string
  steps: PlanStep[]
  status: PlanStatus
  approvals?: PlanApproval[]
  createdAt: string
  updatedAt?: string
}
