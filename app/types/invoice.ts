/**
 * Invoice-related TypeScript type definitions
 */

// Customer information for invoices
export interface InvoiceCustomer {
  id: string
  name: string
  email: string
  address: {
    street: string
    city: string
    country: string
  }
  avatarUrl?: string
}

// Invoice billing information
export interface InvoiceBilling {
  invoiceNumber: string
  issuedDate: string
  dueDate: string
  company: {
    name: string
    description: string
    logoUrl?: string
    address: {
      street: string
      city: string
      country: string
    }
  }
  billTo: {
    name: string
    address: {
      street: string
      suite?: string
      city: string
      country: string
    }
  }
}

// Payment method information
export interface PaymentMethod {
  type: 'paypal' | 'bank' | 'card'
  name: string
  accountDetails: {
    accountNumber?: string
    routingNumber?: string
    [key: string]: any
  }
  iconName?: string
}

// Invoice line item
export interface InvoiceLineItem {
  id: string
  name: string
  hours: number
  hourlyRate: number
  taxRate: number
  subtotal: number
}

// Invoice amount summary
export interface InvoiceAmount {
  subtotal: number
  discount: number
  taxes: number
  total: number
  currency: string
  taxIncluded: boolean
  dueDate: string
}

// Widget data structures
export interface WidgetsInvoiceCustomerData {
  customer: InvoiceCustomer
  editUrl?: string
}

export interface WidgetsInvoiceAmountData {
  amount: InvoiceAmount
  showPdfAttachment?: boolean
  pdfAttachment?: boolean
}

export interface WidgetsInvoiceActionsData {
  actions: {
    preview?: {
      enabled: boolean
      url?: string
    }
    download?: {
      enabled: boolean
      url?: string
    }
    send?: {
      enabled: boolean
      url?: string
    }
  }
}
