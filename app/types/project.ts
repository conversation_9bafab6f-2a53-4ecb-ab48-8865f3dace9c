import { z } from 'zod'

/**
 * Project Management Domain Types & Schemas
 * Based on analysis of existing project pages and TurboIS UWP architecture
 */

// Base UWP/Firebase fields for all entities
export const baseEntitySchema = z.object({
  id: z.string().uuid(),
  workspace_id: z.string().uuid(),
  owner_id: z.string().uuid(), // User who created the entity
  created_at: z.date(),
  updated_at: z.date(),
  deleted_at: z.date().nullable().default(null),
})

// Embedding support for search functionality
export const embedFieldSchema = z.object({
  embedding: z.array(z.number()).optional(), // Vector embeddings for semantic search
})

// Project status enumeration based on existing data
export const projectStatusSchema = z.enum([
  'planning',
  'active',
  'paused',
  'completed',
  'cancelled',
])

// Task status enumeration (0-5 mapping from current implementation)
export const taskStatusSchema = z.enum([
  'new',
  'in_progress',
  'blocked',
  'on_hold',
  'in_review',
  'done',
])

// Task status number mapping for backward compatibility
export const taskStatusNumberSchema = z.union([
  z.literal(0), // new
  z.literal(1), // in_progress
  z.literal(2), // blocked
  z.literal(3), // on_hold
  z.literal(4), // in_review
  z.literal(5), // done
])

// Priority enumeration
export const prioritySchema = z.enum(['low', 'medium', 'high', 'urgent'])

// Customer/Client schema
export const customerSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  logo: z.string().url().optional(),
  text: z.string().optional(), // Description/industry
  location: z.string().optional(),
  website: z.string().url().optional(),
  contact_email: z.string().email().optional(),
  contact_phone: z.string().optional(),
})

// Team member role schema
export const teamRoleSchema = z.enum(['owner', 'manager', 'developer', 'designer', 'qa', 'consultant'])

// Team member schema (for project teams)
export const teamMemberSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(), // Reference to users collection
  profile_id: z.string().uuid(), // Reference to profiles collection
  role: teamRoleSchema,
  permissions: z.array(z.string()).default([]), // Custom permissions
  joined_at: z.date(),

  // Display fields (denormalized for performance)
  name: z.string().optional(),
  avatar: z.string().url().optional(),
  initials: z.string().optional(),
  bio: z.string().optional(),
})

// File attachment schema
export const fileAttachmentSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  url: z.string().url(),
  size: z.string(), // Human readable size (e.g. "4.7MB")
  size_bytes: z.number().int().min(0),
  mime_type: z.string(),
  version: z.string().default('1.0.0'),
  icon: z.string().url().optional(), // File type icon
  uploaded_by: z.string().uuid(), // User ID
  uploaded_at: z.date(),
  description: z.string().optional(),
})

// Tool/Technology schema
export const toolSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  icon: z.string().url().optional(),
  logo: z.string().url().optional(),
  category: z.string().optional(), // e.g., 'design', 'development', 'communication'
  website: z.string().url().optional(),
})

// Technology stack schema
export const stackSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string().min(1),
  description: z.string().optional(),
  icon: z.string().url().optional(),
  logo: z.string().url().optional(),
  category: z.string().optional(), // e.g., 'frontend', 'backend', 'database', 'mobile'
  version: z.string().optional(),
  website: z.string().url().optional(),
})

// Comment schema
export const commentSchema = z.object({
  id: z.string().uuid(),
  task_id: z.string().uuid(),
  author_id: z.string().uuid(),
  content: z.string().min(1),
  created_at: z.date(),
  updated_at: z.date(),
  edited: z.boolean().default(false),

  // Display fields (denormalized)
  author_name: z.string().optional(),
  author_avatar: z.string().url().optional(),
})

// Checklist item schema
export const checklistItemSchema = z.object({
  id: z.string().uuid(),
  text: z.string().min(1),
  completed: z.boolean().default(false),
  completed_by: z.string().uuid().optional(),
  completed_at: z.date().optional(),
  order: z.number().int().min(0).default(0),
})

// Time tracking schema
export const timeEntrySchema = z.object({
  id: z.string().uuid(),
  task_id: z.string().uuid(),
  user_id: z.string().uuid(),
  description: z.string().optional(),
  hours: z.number().min(0),
  date: z.date(),
  created_at: z.date(),
})

// Task schema (core entity)
export const taskSchema = baseEntitySchema.merge(embedFieldSchema).extend({
  // Core task fields
  name: z.string().min(1),
  description: z.string().optional(),
  status: taskStatusSchema.default('new'),
  status_number: taskStatusNumberSchema.default(0), // For backward compatibility
  priority: prioritySchema.default('medium'),

  // Relationships
  project_id: z.string().uuid(),
  assignee_id: z.string().uuid().optional(),
  reporter_id: z.string().uuid().optional(),

  // Task details
  completion_percentage: z.number().min(0).max(100).default(0),
  estimated_hours: z.number().min(0).optional(),
  actual_hours: z.number().min(0).optional(),
  due_date: z.date().optional(),
  start_date: z.date().optional(),

  // Sub-entities
  attachments: z.array(fileAttachmentSchema).default([]),
  comments: z.array(commentSchema).default([]),
  checklist: z.array(checklistItemSchema).default([]),
  time_entries: z.array(timeEntrySchema).default([]),
  labels: z.array(z.string()).default([]),

  // Display fields (denormalized for performance)
  assignee_name: z.string().optional(),
  assignee_avatar: z.string().url().optional(),
  assignee_initials: z.string().optional(),

  // Board position
  column_id: z.string().optional(),
  board_order: z.number().int().min(0).default(0),
})

// Board column schema
export const boardColumnSchema = z.object({
  id: z.string().uuid(),
  project_id: z.string().uuid(),
  name: z.string().min(1),
  description: z.string().optional(),
  color: z.string().optional(), // Hex color for UI
  order: z.number().int().min(0),
  task_limit: z.number().int().min(0).optional(), // WIP limit

  // Column type/mapping to task status
  maps_to_status: taskStatusSchema.optional(),
  is_done_column: z.boolean().default(false),
})

// Project schema (main entity)
export const projectSchema = baseEntitySchema.merge(embedFieldSchema).extend({
  // Core project fields
  name: z.string().min(1),
  description: z.string().optional(),
  slug: z.string().min(1), // URL-friendly identifier
  status: projectStatusSchema.default('planning'),

  // Project metadata
  category: z.string().optional(), // e.g., 'UI/UX Design', 'Development'
  image: z.string().url().optional(), // Project thumbnail/banner
  color: z.string().optional(), // Brand color for UI

  // Timeline
  start_date: z.date().optional(),
  due_date: z.date().optional(),
  completed_at: z.date().optional(),

  // Progress tracking
  completion_percentage: z.number().min(0).max(100).default(0),

  // Customer/Client
  customer: customerSchema.optional(),

  // Team
  team_members: z.array(teamMemberSchema).default([]),

  // Project assets
  files: z.array(fileAttachmentSchema).default([]),
  tools: z.array(toolSchema).default([]),
  stacks: z.array(stackSchema).default([]),

  // Board configuration
  board_columns: z.array(boardColumnSchema).default([]),

  // Settings
  is_template: z.boolean().default(false),
  is_archived: z.boolean().default(false),
  is_public: z.boolean().default(false),

  // Metadata for UI
  recent: z.boolean().default(false), // For "recently viewed" functionality
})

// Board configuration for a project
export const projectBoardSchema = z.object({
  project_id: z.string().uuid(),
  columns: z.array(boardColumnSchema),
  settings: z.object({
    show_task_ids: z.boolean().default(true),
    show_assignee_avatars: z.boolean().default(true),
    show_task_counts: z.boolean().default(true),
    enable_wip_limits: z.boolean().default(false),
    auto_move_completed: z.boolean().default(false),
  }).default({}),
})

// Input schemas for API operations
export const createProjectInputSchema = projectSchema
  .omit({ id: true, created_at: true, updated_at: true, workspace_id: true, owner_id: true })
  .extend({
    // Allow workspace_id override (for workspace switching)
    workspace_id: z.string().uuid().optional(),
  })

export const updateProjectInputSchema = createProjectInputSchema.partial()

export const createTaskInputSchema = taskSchema
  .omit({ id: true, created_at: true, updated_at: true, workspace_id: true, owner_id: true })
  .extend({
    project_id: z.string().uuid(), // Required for task creation
  })

export const updateTaskInputSchema = createTaskInputSchema.partial()

// Query/filter schemas
export const projectsQuerySchema = z.object({
  // Pagination
  page: z.number().int().min(1).default(1),
  per_page: z.number().int().min(1).max(100).default(25),

  // Filtering
  filter: z.string().optional(), // Search term
  status: projectStatusSchema.optional(),
  customer: z.string().optional(),
  team_member: z.string().uuid().optional(),

  // Sorting
  sort_by: z.enum(['name', 'created_at', 'updated_at', 'due_date', 'completion_percentage']).default('updated_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),

  // Special filters
  recent_only: z.boolean().default(false),
  archived: z.boolean().default(false),

  // Include relations
  include_tasks: z.boolean().default(false),
  include_team: z.boolean().default(true),
  include_files: z.boolean().default(false),
})

export const tasksQuerySchema = z.object({
  // Required
  project_id: z.string().uuid(),

  // Pagination
  page: z.number().int().min(1).default(1),
  per_page: z.number().int().min(1).max(100).default(50),

  // Filtering
  filter: z.string().optional(),
  status: taskStatusSchema.optional(),
  assignee: z.string().uuid().optional(),
  priority: prioritySchema.optional(),

  // Sorting
  sort_by: z.enum(['name', 'created_at', 'updated_at', 'due_date', 'priority', 'board_order']).default('board_order'),
  sort_order: z.enum(['asc', 'desc']).default('asc'),

  // Special options
  include_comments: z.boolean().default(false),
  include_checklist: z.boolean().default(false),
  include_time_entries: z.boolean().default(false),
})

// Export types
export type BaseEntity = z.infer<typeof baseEntitySchema>
export type ProjectStatus = z.infer<typeof projectStatusSchema>
export type TaskStatus = z.infer<typeof taskStatusSchema>
export type TaskStatusNumber = z.infer<typeof taskStatusNumberSchema>
export type Priority = z.infer<typeof prioritySchema>
export type TeamRole = z.infer<typeof teamRoleSchema>

export type Customer = z.infer<typeof customerSchema>
export type TeamMember = z.infer<typeof teamMemberSchema>
export type FileAttachment = z.infer<typeof fileAttachmentSchema>
export type Tool = z.infer<typeof toolSchema>
export type Stack = z.infer<typeof stackSchema>
export type Comment = z.infer<typeof commentSchema>
export type ChecklistItem = z.infer<typeof checklistItemSchema>
export type TimeEntry = z.infer<typeof timeEntrySchema>

export type Task = z.infer<typeof taskSchema>
export type BoardColumn = z.infer<typeof boardColumnSchema>
export type Project = z.infer<typeof projectSchema>
export type ProjectBoard = z.infer<typeof projectBoardSchema>

// Input types
export type CreateProjectInput = z.infer<typeof createProjectInputSchema>
export type UpdateProjectInput = z.infer<typeof updateProjectInputSchema>
export type CreateTaskInput = z.infer<typeof createTaskInputSchema>
export type UpdateTaskInput = z.infer<typeof updateTaskInputSchema>

// Query types
export type ProjectsQuery = z.infer<typeof projectsQuerySchema>
export type TasksQuery = z.infer<typeof tasksQuerySchema>

// Status mapping utilities (for backward compatibility with existing pages)
export const TASK_STATUS_NUMBER_MAP: Record<TaskStatusNumber, TaskStatus> = {
  0: 'new',
  1: 'in_progress',
  2: 'blocked',
  3: 'on_hold',
  4: 'in_review',
  5: 'done',
}

export const TASK_STATUS_TO_NUMBER_MAP: Record<TaskStatus, TaskStatusNumber> = {
  new: 0,
  in_progress: 1,
  blocked: 2,
  on_hold: 3,
  in_review: 4,
  done: 5,
}

// Default board columns
export const DEFAULT_BOARD_COLUMNS: Omit<BoardColumn, 'id' | 'project_id'>[] = [
  { name: 'New', order: 0, maps_to_status: 'new', color: '#6b7280' },
  { name: 'In Progress', order: 1, maps_to_status: 'in_progress', color: '#3b82f6' },
  { name: 'Blocked', order: 2, maps_to_status: 'blocked', color: '#ef4444' },
  { name: 'On Hold', order: 3, maps_to_status: 'on_hold', color: '#f59e0b' },
  { name: 'In Review', order: 4, maps_to_status: 'in_review', color: '#8b5cf6' },
  { name: 'Done', order: 5, maps_to_status: 'done', color: '#10b981', is_done_column: true },
]
