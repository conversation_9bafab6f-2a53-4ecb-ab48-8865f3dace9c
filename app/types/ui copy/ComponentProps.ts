/**
 * Common props interfaces for list-based components
 */
import type {
  CommentItem,
  CryptoItem,
  FollowerItem,
  LeagueItem,
  LinkItem,
  ListItem,
  ProjectItem,
  TicketItem,
  WorkspaceItem,
} from './ListItem'

/**
 * Generic list component props
 */
export interface ListItemProps<T = ListItem> {
  /**
   * List of items to display
   */
  items?: T[]

  /**
   * Whether to show actions/buttons
   * @default true
   */
  showActions?: boolean

  /**
   * Loading state
   * @default false
   */
  loading?: boolean

  /**
   * Empty state message
   * @default "No items found"
   */
  emptyMessage?: string
}

/**
 * Comment list component props
 */
export interface CommentListCompactProps extends ListItemProps<CommentItem> {
  /**
   * List of comments to display
   */
  comments?: CommentItem[]

  /**
   * Whether to show like buttons
   * @default true
   */
  showLikes?: boolean

  /**
   * Maximum number of comments to show
   */
  limit?: number
}

/**
 * Followers list component props
 */
export interface FollowersCompactProps extends ListItemProps<FollowerItem> {
  /**
   * List of followers to display
   */
  followers?: FollowerItem[]

  /**
   * Whether to show follow/unfollow buttons
   * @default true
   */
  showFollowButton?: boolean

  /**
   * Avatar size
   * @default "sm"
   */
  avatarSize?: 'xs' | 'sm' | 'md' | 'lg'
}

/**
 * Links list component props
 */
export interface IconLinksProps extends ListItemProps<LinkItem> {
  /**
   * List of links to display
   */
  links?: LinkItem[]

  /**
   * Icon size
   * @default "md"
   */
  iconSize?: 'sm' | 'md' | 'lg'

  /**
   * Layout orientation
   * @default "vertical"
   */
  orientation?: 'horizontal' | 'vertical'
}

/**
 * Image links component props
 */
export interface ImageLinksProps extends ListItemProps<LinkItem> {
  /**
   * List of image links to display
   */
  links?: LinkItem[]

  /**
   * Image size
   * @default "md"
   */
  imageSize?: 'sm' | 'md' | 'lg'

  /**
   * Border radius
   * @default "md"
   */
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'
}

/**
 * League list component props
 */
export interface LeagueListCompactProps extends ListItemProps<LeagueItem> {
  /**
   * List of leagues to display
   */
  leagues?: LeagueItem[]

  /**
   * Whether to show rank changes
   * @default true
   */
  showChanges?: boolean

  /**
   * Whether to highlight top positions
   * @default true
   */
  highlightTop?: boolean
}

/**
 * Pending tickets component props
 */
export interface PendingTicketsProps extends ListItemProps<TicketItem> {
  /**
   * List of tickets to display
   */
  tickets?: TicketItem[]

  /**
   * Whether to show priority indicators
   * @default true
   */
  showPriority?: boolean

  /**
   * Whether to show status badges
   * @default true
   */
  showStatus?: boolean
}

/**
 * Popular cryptos component props
 */
export interface PopularCryptosProps extends ListItemProps<CryptoItem> {
  /**
   * List of cryptocurrencies to display
   */
  currencies?: CryptoItem[]

  /**
   * Whether to show price changes
   * @default true
   */
  showChanges?: boolean

  /**
   * Currency format
   * @default "USD"
   */
  currency?: string
}

/**
 * Project list component props
 */
export interface ProjectListCompactProps extends ListItemProps<ProjectItem> {
  /**
   * List of projects to display
   */
  projects?: ProjectItem[]

  /**
   * Whether to show progress bars
   * @default true
   */
  showProgress?: boolean

  /**
   * Whether to show team members
   * @default true
   */
  showTeam?: boolean
}

/**
 * Social links component props
 */
export interface SocialLinksProps extends ListItemProps<LinkItem> {
  /**
   * List of social links to display
   */
  links?: LinkItem[]

  /**
   * Button variant
   * @default "ghost"
   */
  variant?: 'solid' | 'outline' | 'ghost'

  /**
   * Button size
   * @default "md"
   */
  size?: 'sm' | 'md' | 'lg'
}

/**
 * Workspace dropdown component props
 */
export interface WorkspaceDropdownProps {
  /**
   * List of workspaces to display
   */
  workspaces?: WorkspaceItem[]

  /**
   * Current workspace ID
   */
  currentWorkspaceId?: string

  /**
   * Whether to show member counts
   * @default true
   */
  showMemberCount?: boolean

  /**
   * Whether to show role badges
   * @default true
   */
  showRole?: boolean

  /**
   * Dropdown position
   * @default "bottom"
   */
  position?: 'top' | 'bottom' | 'left' | 'right'
}

/**
 * Chart component props (generic)
 */
export interface ChartProps {
  /**
   * Chart data series
   */
  series?: any[]

  /**
   * Chart options
   */
  options?: any

  /**
   * Chart height
   * @default 300
   */
  height?: number

  /**
   * Chart width
   * @default "100%"
   */
  width?: string | number

  /**
   * Loading state
   * @default false
   */
  loading?: boolean
}

/**
 * Filter component props (generic)
 */
export interface FilterProps {
  /**
   * Available filter options
   */
  options?: any[]

  /**
   * Selected filter values
   */
  selected?: any[]

  /**
   * Whether multiple selection is allowed
   * @default false
   */
  multiple?: boolean

  /**
   * Filter placeholder text
   */
  placeholder?: string
}

/**
 * Starter switcher component props
 */
export interface StarterSwitcherProps {
  /**
   * List of available starters
   */
  starters?: Array<{
    id: string | number
    name: string
    description?: string
    image?: string
    url?: string
    features?: string[]
  }>

  /**
   * Currently selected starter ID
   */
  currentStarter?: string

  /**
   * Whether to show descriptions
   * @default true
   */
  showDescriptions?: boolean

  /**
   * Grid layout columns
   * @default 2
   */
  columns?: 1 | 2 | 3 | 4
}
