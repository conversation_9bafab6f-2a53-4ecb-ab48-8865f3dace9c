/**
 * Authentication methods supported by integrations
 */
export enum AuthType {
  /** OAuth 2.0 flow (e.g., Google, GitHub) */
  OAUTH = 'oauth',
  /** API key authentication */
  API_KEY = 'api_key',
  /** URL connection (e.g., self-hosted services) */
  URL = 'url',
  /** Built-in service (no additional auth required) */
  BUILT_IN = 'built_in',
}

/**
 * Integration provider categories
 */
export enum IntegrationCategory {
  AI_LLM = 'ai_llm',
  EMAIL = 'email',
  CALENDAR = 'calendar',
  PRODUCTIVITY = 'productivity',
  COMMUNICATION = 'communication',
  STORAGE = 'storage',
  MARKETING = 'marketing',
  DEVELOPMENT = 'development',
}

/**
 * Supported AI LLM providers
 */
export enum AIProvider {
  GEMINI = 'gemini',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  GROK = 'grok',
  OLLAMA = 'ollama',
}

/**
 * Supported Email providers
 */
export enum EmailProvider {
  SMTP = 'smtp',
  GOOGLE_EMAIL = 'google_email',
  MICROSOFT_EMAIL = 'microsoft_email',
  APPLE_EMAIL = 'apple_email',
}

/**
 * Supported Calendar providers
 */
export enum CalendarProvider {
  BUILT_IN = 'built_in',
  GOOGLE_CALENDAR = 'google_calendar',
  MICROSOFT_CALENDAR = 'microsoft_calendar',
  APPLE_CALENDAR = 'apple_calendar',
}

/**
 * All supported providers (union of AI, Email, and Calendar)
 */
export type IntegrationProvider = AIProvider | EmailProvider | CalendarProvider

/**
 * Configuration for AI provider connections
 */
export interface AIProviderConfig {
  /** Provider identifier */
  provider: AIProvider
  /** Available models for this provider */
  models: string[]
  /** Whether this provider supports streaming */
  supportsStreaming: boolean
  /** Rate limit information */
  rateLimit?: {
    requestsPerMinute: number
    tokensPerMinute?: number
  }
  /** Whether this provider requires an organization ID (e.g., OpenAI) */
  requiresOrgId?: boolean
  /** Whether this provider requires a project ID (e.g., OpenAI Enterprise) */
  requiresProjectId?: boolean
}

/**
 * Configuration for Email provider connections
 */
export interface EmailProviderConfig {
  /** Provider identifier */
  provider: EmailProvider
  /** Supported email protocols */
  protocols?: string[]
  /** Default ports for different protocols */
  defaultPorts?: {
    smtp?: number
    imap?: number
    pop3?: number
  }
  /** OAuth scopes required (for OAuth providers) */
  requiredScopes?: string[]
}

/**
 * Configuration for Calendar provider connections
 */
export interface CalendarProviderConfig {
  /** Provider identifier */
  provider: CalendarProvider
  /** Default timezone for calendar operations */
  defaultTimezone?: string
  /** OAuth scopes required (for OAuth providers) */
  requiredScopes?: string[]
  /** Webhook support for real-time updates */
  supportsWebhooks?: boolean
  /** Rate limit information */
  rateLimit?: {
    requestsPerMinute: number
    eventsPerMinute?: number
  }
  /** Supported calendar features */
  features?: {
    createEvents?: boolean
    editEvents?: boolean
    deleteEvents?: boolean
    recurringEvents?: boolean
    attachments?: boolean
    reminders?: boolean
  }
}

/**
 * Integration interface for managing third-party service integrations
 * Used in preferences/integrations page for both connected and available integrations
 */
export interface Integration {
  /** Unique identifier for the integration */
  id: string

  /** Display name of the integration service (e.g., 'Mailchimp', 'OpenAI') */
  name: string

  /** Brief description of what the integration provides */
  description: string

  /** Iconify icon name for the service (e.g., 'logos:openai', 'logos:google') */
  icon: string

  /** Integration category */
  category: IntegrationCategory

  /** Authentication method required */
  authType: AuthType

  /** Whether this integration allows multiple connections per profile */
  allowMultiple?: boolean

  /** Whether this integration is currently connected to the user's account */
  connected?: boolean

  /** Number of active connections (for multi-connection integrations) */
  connectionCount?: number

  /** Array of active connections (for multi-connection integrations) */
  connections?: IntegrationCredential[]

  /** Optional link to integration documentation or setup guide */
  docsUrl?: string

  /** AI provider configuration (if applicable) */
  aiConfig?: AIProviderConfig

  /** Email provider configuration (if applicable) */
  emailConfig?: EmailProviderConfig

  /** Calendar provider configuration (if applicable) */
  calendarConfig?: CalendarProviderConfig

  /** Whether the integration is currently being connected/tested */
  connecting?: boolean

  /** Last connection attempt timestamp */
  lastConnected?: Date

  /** Connection status message */
  status?: 'connected' | 'disconnected' | 'error' | 'testing'

  /** Error message if connection failed */
  errorMessage?: string
}

/**
 * Encrypted credentials stored in database
 */
export interface IntegrationCredential {
  /** Document ID */
  id?: string

  /** Integration identifier */
  integrationId: string

  /** User ID who owns these credentials */
  userId: string

  /** Workspace ID for isolation */
  workspaceId: string

  /** Profile ID */
  profileId: string

  /** User-friendly name for this connection (e.g., 'Sales Email', 'Personal Email') */
  connectionLabel?: string

  /** Authentication type */
  authType: AuthType

  /** Encrypted credential data */
  encryptedData: string

  /** Initialization vector for decryption */
  iv: string

  /** Connection status */
  status: 'connected' | 'disconnected' | 'error'

  /** Last successful connection */
  lastConnected?: Date

  /** Last connection test */
  lastTested?: Date

  /** Error message if connection failed */
  errorMessage?: string

  /** Additional metadata (unencrypted) */
  metadata?: {
    /** Provider-specific info */
    provider?: IntegrationProvider
    /** Available models (for AI providers) */
    models?: string[]
    /** Connection URL (for URL-based integrations) */
    connectionUrl?: string
    /** OAuth scopes granted */
    scopes?: string[]
    /** Email address (for email providers) */
    emailAddress?: string
    /** SMTP server info (for SMTP providers) */
    smtpServer?: string
  }

  /** Standard timestamps */
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
}

/**
 * Raw credential data before encryption
 */
export interface CredentialData {
  /** API key for API_KEY auth type */
  apiKey?: string

  /** Organization ID (e.g., OpenAI: org-...) */
  orgId?: string

  /** Project ID (e.g., OpenAI Enterprise: proj_...) */
  projectId?: string

  /** Connection URL for URL auth type */
  url?: string

  /** OAuth tokens for OAUTH auth type */
  oauth?: {
    accessToken: string
    refreshToken?: string
    expiresAt?: Date
    tokenType?: string
  }

  /** Email-specific credentials */
  email?: {
    /** Email address */
    address: string
    /** SMTP server settings (for SMTP) */
    smtp?: {
      host: string
      port: number
      secure: boolean
      username: string
      password: string
    }
  }

  /** Connection label for multi-connection integrations */
  connectionLabel?: string

  /** Additional provider-specific data */
  extra?: Record<string, any>
}

/**
 * Form data for connection modals
 */
export interface ConnectionFormData {
  /** Integration being connected */
  integration: Integration

  /** Connection label for multi-connection integrations */
  connectionLabel?: string

  /** Form fields based on auth type */
  credentials: {
    /** API key input */
    apiKey?: string
    /** Organization ID (optional, e.g., OpenAI) */
    orgId?: string
    /** Project ID (optional, e.g., OpenAI Enterprise) */
    projectId?: string
    /** URL input */
    url?: string
    /** Email address (for email integrations) */
    emailAddress?: string
    /** SMTP settings (for SMTP integrations) */
    smtpHost?: string
    smtpPort?: number
    smtpSecure?: boolean
    smtpUsername?: string
    smtpPassword?: string
    /** Additional fields */
    [key: string]: any
  }

  /** Whether to test connection before saving */
  testConnection?: boolean
}

/**
 * Props for integration-related components
 */
export interface IntegrationListProps {
  /** List of integrations to display */
  integrations: Integration[]

  /** Whether to show search functionality */
  searchable?: boolean

  /** Placeholder text for search input */
  searchPlaceholder?: string
}

/**
 * Props for integration connection modals
 */
export interface IntegrationModalProps {
  /** Whether modal is open */
  open: boolean

  /** Integration to connect */
  integration?: Integration

  /** Loading state */
  loading?: boolean

  /** Close modal callback */
  onClose: () => void

  /** Submit callback */
  onSubmit: (data: ConnectionFormData) => Promise<void>
}
