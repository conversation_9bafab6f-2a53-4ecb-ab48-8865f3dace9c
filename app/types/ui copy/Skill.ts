/**
 * Skill interface for technology/framework data with usage statistics
 * Used in TrendingSkills.vue and similar components
 */
export interface Skill {
  /**
   * Unique identifier
   */
  id: string | number

  /**
   * Skill/technology name
   * @example "Vue JS"
   */
  name: string

  /**
   * Usage count or popularity metric
   * @example 149
   */
  count: number

  /**
   * Icon name for the skill/technology
   * @example "logos:vue"
   */
  icon: string

  /**
   * Optional description
   */
  description?: string

  /**
   * Optional category
   * @example "Framework"
   */
  category?: string
}

/**
 * Props interface for TrendingSkills component
 */
export interface TrendingSkillsProps {
  /**
   * List of skills to display
   */
  skills?: Skill[]

  /**
   * Title for the skills section
   * @default "Trending Skills"
   */
  title?: string

  /**
   * Whether to show actions/buttons
   * @default true
   */
  showActions?: boolean

  /**
   * Custom action button icon
   * @default "lucide:arrow-right"
   */
  actionIcon?: string

  /**
   * Custom suffix text for count display
   * @default "candidates"
   */
  countSuffix?: string
}
