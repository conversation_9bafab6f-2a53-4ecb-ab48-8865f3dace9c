/**
 * TeamMember interface for team member data with roles and profile info
 * Used in TeamListCompact.vue, UserList.vue and similar components
 */
export interface TeamMember {
  /**
   * Unique identifier
   */
  id: string | number

  /**
   * Member's first name
   * @example "<PERSON>"
   */
  firstName: string

  /**
   * Member's last name
   * @example "Smith"
   */
  lastName: string

  /**
   * Profile image URL
   * @example "/img/avatars/3.svg"
   */
  image?: string

  /**
   * Initials for avatar fallback
   * @example "EC"
   */
  text?: string

  /**
   * Member's role or job title
   * @example "UI/UX designer"
   */
  role: string
}

/**
 * User interface extends TeamMember with additional progress/performance data
 * Used in UserList.vue
 */
export interface User extends TeamMember {
  /**
   * User's full display name (alternative to firstName/lastName)
   * @example "Hermann Mayer"
   */
  name?: string

  /**
   * Job position/title (alternative to role)
   * @example "Business Analyst"
   */
  position?: string

  /**
   * Performance progress percentage
   * @example 18 or -12
   */
  progress?: number

  /**
   * Profile picture URL (alternative to image)
   * @example "/img/avatars/16.svg"
   */
  picture?: string
}

/**
 * Props interface for TeamListCompact component
 */
export interface TeamListCompactProps {
  /**
   * List of team members to display
   */
  members?: TeamMember[]

  /**
   * Whether to show action buttons (call, video)
   * @default false
   */
  actions?: boolean

  /**
   * Avatar size
   * @default "xs"
   */
  avatarSize?: 'xxs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

/**
 * Props interface for UserList component
 */
export interface UserListProps {
  /**
   * List of users to display
   */
  users?: User[]

  /**
   * Avatar border radius
   * @default "sm"
   */
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full'

  /**
   * Whether to show progress indicators
   * @default true
   */
  showProgress?: boolean
}
