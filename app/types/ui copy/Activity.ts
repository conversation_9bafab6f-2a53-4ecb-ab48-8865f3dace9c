/**
 * Activity interface for financial transaction data
 * Used in ActivityTable.vue and similar components
 */
export interface Activity {
  /**
   * Icon name for the transaction type
   * @example "solar:card-transfer-bold-duotone"
   */
  icon: string

  /**
   * Transaction title/name
   * @example "Victoria's Corner"
   */
  title: string

  /**
   * Transaction description
   * @example "Outgoing payment"
   */
  description: string

  /**
   * Transaction amount (negative for outgoing, positive for incoming)
   * @example -938.39
   */
  amount: number

  /**
   * Transaction date
   * @example "Jun 12, 2024"
   */
  date: string

  /**
   * Optional unique identifier
   */
  id?: string | number
}

/**
 * Props interface for ActivityTable component
 */
export interface ActivityTableProps {
  /**
   * List of activities to display
   */
  activities?: Activity[]

  /**
   * Title for the activity section
   * @default "Recent Activity"
   */
  title?: string

  /**
   * Link for "View all" button
   * @default "/layouts"
   */
  viewAllLink?: string

  /**
   * Label for "View all" button
   * @default "View all"
   */
  viewAllLabel?: string

  /**
   * Whether to show the "View all" link
   * @default true
   */
  showViewAll?: boolean
}
