/**
 * NotificationCategory interface for notification category data
 * Used in NotificationsCompact.vue and similar components
 */
export interface NotificationCategory {
  /**
   * Unique identifier
   */
  id?: string | number

  /**
   * Category name
   * @example "Personal"
   */
  name: string

  /**
   * Number of notifications in this category
   * @example 12
   */
  count: number

  /**
   * Link URL for the category
   * @example "/notifications/personal"
   */
  link?: string

  /**
   * Category color/theme
   * @example "primary"
   */
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'muted'

  /**
   * Optional icon for the category
   * @example "lucide:bell"
   */
  icon?: string
}

/**
 * Props interface for NotificationsCompact component
 */
export interface NotificationsCompactProps {
  /**
   * List of notification categories to display
   */
  categories?: NotificationCategory[]

  /**
   * Title for the notifications section
   * @default "Notifications"
   */
  title?: string

  /**
   * Whether to show the title
   * @default true
   */
  showTitle?: boolean

  /**
   * Default link prefix for categories without explicit links
   * @default "#"
   */
  baseLinkPrefix?: string
}
