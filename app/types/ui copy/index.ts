/**
 * UI Component Types Barrel Export
 * Auto-import from @shared/types/ui
 */

// Activity components
export type {
  Activity,
  ActivityTableProps,
} from './Activity'

// Author components
export type {
  Author,
  AuthorsListCompactProps,
} from './Author'

// Company components
export type {
  Company,
  CompanyOverviewProps,
  CompanyStats,
} from './Company'

// Component props interfaces
export type {
  ChartProps,
  CommentListCompactProps,
  FilterProps,
  FollowersCompactProps,
  IconLinksProps,
  ImageLinksProps,
  LeagueListCompactProps,
  ListItemProps,
  PendingTicketsProps,
  PopularCryptosProps,
  ProjectListCompactProps,
  SocialLinksProps,
  StarterSwitcherProps,
  WorkspaceDropdownProps,
} from './ComponentProps'

// Integration components
export type {
  AIProviderConfig,
  ConnectionFormData,
  CredentialData,
  EmailProviderConfig,
  Integration,
  IntegrationCredential,
  IntegrationListProps,
  IntegrationModalProps,
} from './Integration'

export {
  AIProvider,
  AuthType,
  CalendarProvider,
  EmailProvider,
  IntegrationCategory,
} from './Integration'

// List item types
export type {
  CommentItem,
  CryptoItem,
  FollowerItem,
  LeagueItem,
  LinkItem,
  ListItem,
  ProjectItem,
  TicketItem,
  WorkspaceItem,
} from './ListItem'

// Notification components
export type {
  NotificationCategory,
  NotificationsCompactProps,
} from './Notification'

// Skill components
export type {
  Skill,
  TrendingSkillsProps,
} from './Skill'

// Team/User components
export type {
  TeamListCompactProps,
  TeamMember,
  User,
  UserListProps,
} from './TeamMember'
