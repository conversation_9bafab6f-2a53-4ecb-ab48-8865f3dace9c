/**
 * Author interface for user profile data with article statistics
 * Used in AuthorsListCompact.vue and similar components
 */
export interface Author {
  /**
   * Unique identifier
   */
  id: string | number

  /**
   * Author's first name
   * @example "<PERSON>"
   */
  firstName: string

  /**
   * Author's last name
   * @example "<PERSON>"
   */
  lastName: string

  /**
   * Profile image URL
   * @example "/img/avatars/16.svg"
   */
  image?: string

  /**
   * Initials for avatar fallback
   * @example "HM"
   */
  text?: string

  /**
   * Author's role or job title
   * @example "Project manager"
   */
  role: string

  /**
   * Number of articles authored
   * @example 39
   */
  articles: number
}

/**
 * Props interface for AuthorsListCompact component
 */
export interface AuthorsListCompactProps {
  /**
   * List of authors to display
   */
  authors?: Author[]

  /**
   * Whether to show actions/buttons
   * @default true
   */
  showActions?: boolean

  /**
   * Custom action button icon
   * @default "lucide:arrow-right"
   */
  actionIcon?: string

  /**
   * Avatar mask style
   * @default "blob"
   */
  avatarMask?: 'blob' | 'hex' | 'deca' | 'rounded' | 'none'
}
