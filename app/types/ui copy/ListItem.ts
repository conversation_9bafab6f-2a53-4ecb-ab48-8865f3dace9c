/**
 * Generic interfaces for common list-based components
 */

/**
 * Basic list item with id, name, and optional properties
 */
export interface ListItem {
  /**
   * Unique identifier
   */
  id: string | number

  /**
   * Item name/title
   */
  name: string

  /**
   * Optional description
   */
  description?: string

  /**
   * Optional icon
   */
  icon?: string

  /**
   * Optional image URL
   */
  image?: string

  /**
   * Optional link/URL
   */
  link?: string
}

/**
 * Link item with title and URL
 */
export interface LinkItem extends ListItem {
  /**
   * Link URL
   */
  url: string

  /**
   * Link title
   */
  title?: string

  /**
   * Optional target
   */
  target?: '_blank' | '_self'
}

/**
 * Comment item for comment lists
 */
export interface CommentItem extends ListItem {
  /**
   * Comment author
   */
  author: string

  /**
   * Author avatar URL
   */
  avatar?: string

  /**
   * Comment content
   */
  content: string

  /**
   * Comment date
   */
  date: string

  /**
   * Number of likes/reactions
   */
  likes?: number
}

/**
 * Follower item for follower lists
 */
export interface FollowerItem extends ListItem {
  /**
   * Follower name
   */
  name: string

  /**
   * Profile image URL
   */
  image?: string

  /**
   * Username/handle
   */
  username?: string

  /**
   * Whether currently following
   */
  isFollowing?: boolean

  /**
   * Follower count
   */
  followers?: number
}

/**
 * League/ranking item
 */
export interface LeagueItem extends ListItem {
  /**
   * League/team name
   */
  name: string

  /**
   * Team/league logo
   */
  logo?: string

  /**
   * Current rank/position
   */
  rank: number

  /**
   * Points/score
   */
  points: number

  /**
   * Change from previous period
   */
  change?: number
}

/**
 * Ticket item for support/issue tracking
 */
export interface TicketItem extends ListItem {
  /**
   * Ticket title
   */
  title: string

  /**
   * Ticket status
   */
  status: 'open' | 'pending' | 'closed' | 'resolved'

  /**
   * Priority level
   */
  priority?: 'low' | 'medium' | 'high' | 'urgent'

  /**
   * Assigned user
   */
  assignedTo?: string

  /**
   * Creation date
   */
  createdAt: string

  /**
   * Last updated date
   */
  updatedAt?: string
}

/**
 * Cryptocurrency item
 */
export interface CryptoItem extends ListItem {
  /**
   * Currency symbol (e.g., BTC, ETH)
   */
  symbol: string

  /**
   * Current price
   */
  price: number

  /**
   * Price change percentage
   */
  change: number

  /**
   * Currency icon/logo
   */
  icon?: string

  /**
   * Market cap
   */
  marketCap?: number
}

/**
 * Project item for project lists
 */
export interface ProjectItem extends ListItem {
  /**
   * Project title
   */
  title: string

  /**
   * Project description
   */
  description?: string

  /**
   * Project status
   */
  status: 'active' | 'completed' | 'paused' | 'archived'

  /**
   * Progress percentage
   */
  progress?: number

  /**
   * Project team members
   */
  team?: string[]

  /**
   * Due date
   */
  dueDate?: string

  /**
   * Project thumbnail/image
   */
  thumbnail?: string
}

/**
 * Workspace item for workspace lists
 */
export interface WorkspaceItem extends ListItem {
  /**
   * Workspace name
   */
  name: string

  /**
   * Workspace slug/identifier
   */
  slug?: string

  /**
   * Workspace logo
   */
  logo?: string

  /**
   * Member count
   */
  memberCount?: number

  /**
   * User role in workspace
   */
  role?: 'owner' | 'admin' | 'member' | 'guest'

  /**
   * Whether this is the current workspace
   */
  isCurrent?: boolean
}
