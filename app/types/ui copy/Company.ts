/**
 * CompanyStats interface for company statistics data
 */
export interface CompanyStats {
  /**
   * Number of posts
   * @example 864
   */
  posts: number

  /**
   * Number of projects
   * @example 247
   */
  projects: number

  /**
   * Number of followers (can be formatted, e.g., "19k")
   * @example 19000 or "19k"
   */
  followers: number | string
}

/**
 * Company interface for organization data with statistics
 * Used in CompanyOverview.vue and similar components
 */
export interface Company {
  /**
   * Company name
   * @example "Slicer Learning"
   */
  name: string

  /**
   * Company logo/avatar URL
   * @example "/img/icons/logos/slicer.svg"
   */
  logo: string

  /**
   * Company industry/category
   * @example "Online courses"
   */
  industry: string

  /**
   * Company description
   * @example "Lorem ipsum dolor sit amet, consectetur adipiscing elit..."
   */
  description: string

  /**
   * Company statistics
   */
  stats: CompanyStats
}

/**
 * Props interface for CompanyOverview component
 */
export interface CompanyOverviewProps {
  /**
   * Company data to display
   */
  company?: Company

  /**
   * Avatar size for company logo
   * @default "xl"
   */
  avatarSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl'

  /**
   * Whether to show the add button
   * @default true
   */
  showAddButton?: boolean

  /**
   * Maximum width for description text
   * @default 320
   */
  maxDescriptionWidth?: number
}
