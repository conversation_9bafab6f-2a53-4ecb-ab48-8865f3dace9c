import { z } from 'zod'

// Billing intervals
export type BillingInterval = 'monthly' | 'yearly' | 'semestral'

// Plan pricing structure
export interface PlanPrice {
  monthly: number
  yearly: number
  currency?: string
}

// Billing plan interface
export interface BillingPlan {
  name: string
  description: string
  price: PlanPrice
  features: string[]
  benefits: string[]
}

// User seat data
export interface SeatInfo {
  name: string
  avatar?: string
  text?: string
  tooltipContent: string
}

// Card information
export interface CardInfo {
  name?: string
  number?: string
  expiryYear?: string
  expiryMonth?: string
  cvc?: string
}

// Billing notification setting
export interface BillingNotification {
  id: string
  label: string
  sublabel: string
  enabled: boolean
}

// Zod schemas for validation
export const PlanPriceSchema = z.object({
  monthly: z.number().nonnegative(),
  yearly: z.number().nonnegative(),
  currency: z.string().optional(),
})

export const BillingPlanSchema = z.object({
  name: z.string().min(1),
  description: z.string(),
  price: PlanPriceSchema,
  features: z.array(z.string()),
  benefits: z.array(z.string()),
})

export const SeatInfoSchema = z.object({
  name: z.string().min(1),
  avatar: z.string().optional(),
  text: z.string().optional(),
  tooltipContent: z.string().min(1),
})

export const CardInfoSchema = z.object({
  name: z.string().optional(),
  number: z.string().optional(),
  expiryYear: z.string().optional(),
  expiryMonth: z.string().optional(),
  cvc: z.string().optional(),
})

export const BillingNotificationSchema = z.object({
  id: z.string().min(1),
  label: z.string().min(1),
  sublabel: z.string().min(1),
  enabled: z.boolean(),
})
