// Tool Telemetry types (Story 4.5)
// Unified contract for tool run events across WS, logs, storage, and traces

export type ToolRunStatus = 'queued' | 'starting' | 'running' | 'success' | 'error' | 'cancelled'

export interface ToolRunIdentifiers {
  // Correlators
  correlationId: string
  traceId?: string
  spanId?: string

  // Scope
  sessionId: string
  conversationId: string
  messageId: string
  workspaceId: string
  userId?: string
  profileId?: string
  agentId?: string
}

export interface ToolRunMeta {
  toolId: string
  toolName: string
  provider?: string
  sensitivity?: 'low' | 'medium' | 'high'
  permission?: 'read' | 'write'
}

export interface ToolRunTimings {
  startedAt: string
  updatedAt?: string
  completedAt?: string
  durationMs?: number
}

export interface ToolRunResourceUsage {
  tokensIn?: number
  tokensOut?: number
  bytesIn?: number
  bytesOut?: number
  costUsd?: number
  // Retry metrics
  attempts?: number
  totalBackoffMs?: number
}

export interface ToolRunError {
  code: string
  message: string
  details?: any
}

export interface ToolRunRecord extends Tool<PERSON>unIdentifiers, ToolRunMeta, ToolRunTimings, ToolRunResourceUsage {
  status: ToolRunStatus
  // Store sanitized inputs/outputs
  args?: Record<string, any>
  resultSummary?: any
  error?: ToolRunError
  // Last known progress 0-100
  progress?: number
  // Streaming flag
  streamed?: boolean
  // Indexing helpers
  startedAtEpoch?: number
}

export interface ToolRunProgressEvent {
  correlationId: string
  status: Extract<ToolRunStatus, 'running'>
  progress: number
  statusMessage?: string
  partialResult?: any
  updatedAt: string
}
