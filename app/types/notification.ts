import { z } from 'zod'

/**
 * Notification Channel Schema
 * Defines how notifications are delivered to users
 */
export const notificationChannelSchema = z.object({
  email: z.boolean().default(true),
  inApp: z.boolean().default(true),
  push: z.boolean().default(false), // Default false for privacy
})

/**
 * Notification Content Schema
 * Defines what types of content trigger notifications
 */
export const notificationContentSchema = z.object({
  // System notifications (security, updates, etc.)
  system: z.boolean().default(true),

  // Workspace-level notifications (new members, workspace changes)
  workspace: z.boolean().default(true),

  // Direct mentions (@username)
  mentions: z.boolean().default(true),

  // Task/project assignments
  assignments: z.boolean().default(true),

  // Reminders and due dates
  reminders: z.boolean().default(true),

  // Comments and collaborative activities
  comments: z.boolean().default(true),

  // File shares and uploads
  fileActivity: z.boolean().default(false),
})

/**
 * Marketing Preferences Schema
 * External marketing and promotional communications
 */
export const marketingPrefsSchema = z.object({
  // Product updates and feature announcements
  productUpdates: z.boolean().default(false),

  // Marketing emails and promotions
  marketing: z.boolean().default(false),

  // Third-party partner offers
  partnerOffers: z.boolean().default(false),

  // Newsletter subscription
  newsletter: z.boolean().default(false),
})

/**
 * Notification Priority & Timing Schema
 * Controls how and when notifications are delivered
 */
export const notificationPrioritySchema = z.object({
  // Automatically dismiss low-priority notifications
  flushLowPriority: z.boolean().default(true),

  // Quiet hours configuration
  quietHours: z.object({
    enabled: z.boolean().default(false),
    startTime: z.string().default('22:00'), // 10 PM
    endTime: z.string().default('08:00'), // 8 AM
    timezone: z.string().default('UTC'),
  }).default({
    enabled: false,
    startTime: '22:00',
    endTime: '08:00',
    timezone: 'UTC',
  }),

  // Digest preferences (bundle notifications)
  digest: z.object({
    enabled: z.boolean().default(false),
    frequency: z.enum(['daily', 'weekly']).default('daily'),
    time: z.string().default('09:00'), // 9 AM
  }).default({
    enabled: false,
    frequency: 'daily',
    time: '09:00',
  }),
})

/**
 * Complete Notification Preferences Schema
 * Combines all notification preference categories
 */
export const notificationPreferencesSchema = z.object({
  // How notifications are delivered
  channels: notificationChannelSchema.default({
    email: true,
    inApp: true,
    push: false,
  }),

  // What triggers notifications
  content: notificationContentSchema.default({
    system: true,
    workspace: true,
    mentions: true,
    assignments: true,
    reminders: true,
    comments: true,
    fileActivity: false,
  }),

  // Marketing communications
  marketing: marketingPrefsSchema.default({
    productUpdates: false,
    marketing: false,
    partnerOffers: false,
    newsletter: false,
  }),

  // Timing and priority
  priority: notificationPrioritySchema.default({
    flushLowPriority: true,
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
      timezone: 'UTC',
    },
    digest: {
      enabled: false,
      frequency: 'daily',
      time: '09:00',
    },
  }),
})

// Type exports
export type NotificationChannel = z.infer<typeof notificationChannelSchema>
export type NotificationContent = z.infer<typeof notificationContentSchema>
export type MarketingPrefs = z.infer<typeof marketingPrefsSchema>
export type NotificationPriority = z.infer<typeof notificationPrioritySchema>
export type NotificationPreferences = z.infer<typeof notificationPreferencesSchema>

/**
 * Legacy Notification Mapping Utilities
 * For backward compatibility with existing boolean flags
 */
export interface LegacyNotificationFlags {
  incoming?: boolean
  outgoing?: boolean
  failed?: boolean
  uncashed?: boolean
  payments?: boolean
  low?: boolean
  features?: boolean
  offers?: boolean
  enabled?: boolean
  flushLowPriority?: boolean
  marketing?: boolean
  partners?: boolean
}

/**
 * Converts legacy notification booleans to new unified structure
 */
export function mapLegacyToNotificationPreferences(
  legacy: LegacyNotificationFlags,
): NotificationPreferences {
  return {
    channels: {
      email: legacy.enabled !== false,
      inApp: true,
      push: false,
    },
    content: {
      system: legacy.incoming !== false && legacy.failed !== false,
      workspace: legacy.outgoing !== false,
      mentions: true,
      assignments: legacy.payments !== false,
      reminders: legacy.low !== false,
      comments: true,
      fileActivity: false,
    },
    marketing: {
      productUpdates: legacy.features !== false,
      marketing: legacy.marketing || false,
      partnerOffers: legacy.partners || legacy.offers || false,
      newsletter: false,
    },
    priority: {
      flushLowPriority: legacy.flushLowPriority !== false,
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
        timezone: 'UTC',
      },
      digest: {
        enabled: false,
        frequency: 'daily',
        time: '09:00',
      },
    },
  }
}

/**
 * Converts new notification preferences to legacy boolean flags
 * For compatibility with existing UI components during migration
 */
export function mapNotificationPreferencesToLegacy(
  prefs: NotificationPreferences,
): LegacyNotificationFlags {
  return {
    enabled: prefs.channels.email,
    flushLowPriority: prefs.priority.flushLowPriority,
    marketing: prefs.marketing.marketing,
    partners: prefs.marketing.partnerOffers,
    incoming: prefs.content.system,
    outgoing: prefs.content.workspace,
    failed: prefs.content.system,
    uncashed: false, // Deprecated
    payments: prefs.content.assignments,
    low: prefs.content.reminders,
    features: prefs.marketing.productUpdates,
    offers: prefs.marketing.partnerOffers,
  }
}
