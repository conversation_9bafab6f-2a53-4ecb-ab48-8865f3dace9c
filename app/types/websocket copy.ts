/**
 * WebSocket Tool Streaming Types (Story 4.4)
 * Dedicated types for streaming tool execution and progress tracking
 */

import type { PlanStatus, PlanStepStatus } from '~/types/planner'

/**
 * Tool execution status for streaming updates
 */
export type ToolExecutionStatus = 'queued' | 'starting' | 'running' | 'success' | 'error' | 'cancelled'

/**
 * Tool streaming message payloads
 */
export interface ToolCallStartedPayload {
  toolId: string
  toolName: string
  sessionId: string
  conversationId: string
  messageId: string
  arguments: Record<string, any>
  startTime: string
  correlationId?: string
}

export interface ToolProgressPayload {
  toolId: string
  toolName: string
  sessionId: string
  conversationId: string
  messageId: string
  progress: number // 0-100
  status: ToolExecutionStatus
  statusMessage?: string
  partialResult?: any
  correlationId?: string
}

export interface ToolResultPayload {
  toolId: string
  toolName: string
  sessionId: string
  conversationId: string
  messageId: string
  status: 'success' | 'error'
  result?: any
  error?: {
    code: string
    message: string
    details?: any
  }
  duration: number // milliseconds
  endTime: string
  correlationId?: string
}

/**
 * Combined tool streaming data structure
 */
export interface ToolStreamData {
  toolId: string
  toolName: string
  status: ToolExecutionStatus
  progress: number
  startTime?: string
  endTime?: string
  duration?: number
  result?: any
  error?: {
    code: string
    message: string
    details?: any
  }
  partialResult?: any
  statusMessage?: string
}

/**
 * WebSocket message envelope for tool streaming
 */
export interface ToolStreamMessage {
  type: 'tool_call_started' | 'tool_progress' | 'tool_result' | 'tool_error'
  payload: ToolCallStartedPayload | ToolProgressPayload | ToolResultPayload
  timestamp: string
  sessionId: string
}

/**
 * Client-side tool streaming store state
 */
export interface ToolStreamingState {
  [messageId: string]: {
    [toolId: string]: ToolStreamData
  }
}

/**
 * Tool streaming configuration
 */
export interface ToolStreamingConfig {
  enableProgress: boolean
  progressInterval: number // milliseconds
  maxRetries: number
  timeout: number // milliseconds
}

/**
 * Tool streaming events for composables
 */
export interface ToolStreamingEvents {
  onToolStarted: (data: ToolStreamData) => void
  onToolProgress: (data: ToolStreamData) => void
  onToolCompleted: (data: ToolStreamData) => void
  onToolError: (data: ToolStreamData) => void
}

/**
 * Plan streaming message payloads (Story 4.9)
 */
export interface PlanStartedPayload {
  planId: string
  sessionId: string
  conversationId: string
  status: PlanStatus
  startedAt: string
  steps: Array<{
    id: string
    toolId: string
    toolName: string
    requiresApproval: boolean
    dependsOn: string[]
  }>
}

export interface PlanStepUpdatePayload {
  planId: string
  stepId: string
  toolId: string
  toolName: string
  status: PlanStepStatus
  correlationId?: string
  result?: any
  error?: { code: string, message: string }
}

export interface PlanCompletedPayload {
  planId: string
  status: PlanStatus
  completedAt: string
  successCount: number
  errorCount: number
  awaitingApprovalCount: number
}

export interface PlanApprovalPayload {
  planId: string
  stepIds: string[]
  approverId: string
  approvedAt: string
  context?: any
}

export interface PlanResumedPayload {
  planId: string
  resumedAt: string
  remainingStepsCount: number
}

export interface PlanStreamMessage {
  type: 'plan_started' | 'plan_step_update' | 'plan_completed' | 'plan_approval' | 'plan_resumed'
  payload: PlanStartedPayload | PlanStepUpdatePayload | PlanCompletedPayload | PlanApprovalPayload | PlanResumedPayload
  timestamp: string
  sessionId: string
}
