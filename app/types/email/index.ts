/**
 * Email message types and interfaces for inbox functionality
 */

import type { EmailProvider } from '~/types/ui'

/**
 * Email address interface
 */
export interface EmailAddress {
  /** Display name (e.g., "<PERSON>") */
  name?: string
  /** Email address (e.g., "<EMAIL>") */
  email: string
}

/**
 * Email attachment interface
 */
export interface EmailAttachment {
  /** Attachment unique identifier */
  id?: string
  /** Filename */
  name: string
  /** File size in bytes */
  size: number
  /** MIME type */
  mimeType: string
  /** Content type (used for UI display) */
  type: 'pdf' | 'zip' | 'doc' | 'docx' | 'xls' | 'xlsx' | 'ppt' | 'pptx' | 'image' | 'video' | 'audio' | 'other'
  /** Download URL or attachment ID for fetching */
  url?: string
  /** Inline content ID (for embedded images) */
  cid?: string
}

/**
 * Email message interface
 */
export interface EmailMessage {
  /** Unique message identifier */
  id: string

  /** Message subject */
  subject: string

  /** Message body/content */
  body: string

  /** Plain text version of body */
  textBody?: string

  /** HTML version of body */
  htmlBody?: string

  /** Message preview/snippet */
  preview?: string

  /** Sender information */
  from: EmailAddress

  /** Recipients */
  to: EmailAddress[]

  /** CC recipients */
  cc?: EmailAddress[]

  /** BCC recipients */
  bcc?: EmailAddress[]

  /** Reply-to address */
  replyTo?: EmailAddress

  /** Message timestamp */
  date: Date

  /** Message status */
  status: 'read' | 'unread'

  /** Message importance/priority */
  importance?: 'low' | 'normal' | 'high'

  /** Message flags */
  flags?: {
    flagged?: boolean
    starred?: boolean
    important?: boolean
    archived?: boolean
    deleted?: boolean
    spam?: boolean
  }

  /** Message attachments */
  attachments?: EmailAttachment[]

  /** Thread/conversation ID */
  threadId?: string

  /** Provider-specific metadata */
  provider: {
    /** Which email provider this message came from */
    type: EmailProvider
    /** Provider integration ID */
    integrationId: string
    /** Provider's internal message ID */
    providerId: string
    /** Provider-specific labels/folders */
    labels?: string[]
    /** Provider-specific metadata */
    metadata?: Record<string, any>
  }

  /** Workspace and user context */
  workspaceId: string
  profileId: string

  /** Timestamps */
  createdAt?: Date
  updatedAt?: Date
}

/**
 * Email folder/label interface
 */
export interface EmailFolder {
  /** Folder ID */
  id: string
  /** Folder name */
  name: string
  /** Display name */
  displayName: string
  /** Folder type */
  type: 'inbox' | 'sent' | 'drafts' | 'trash' | 'spam' | 'archive' | 'custom'
  /** Unread message count */
  unreadCount?: number
  /** Total message count */
  totalCount?: number
  /** Provider information */
  provider: {
    type: EmailProvider
    integrationId: string
  }
}

/**
 * Email fetch options
 */
export interface EmailFetchOptions {
  /** Email provider integration ID */
  integrationId: string
  /** Folder/label to fetch from */
  folder?: string
  /** Maximum number of emails to fetch */
  limit?: number
  /** Pagination token for next batch */
  pageToken?: string
  /** Only fetch unread emails */
  unreadOnly?: boolean
  /** Search query */
  query?: string
  /** Date range filter */
  dateRange?: {
    from?: Date
    to?: Date
  }
}

/**
 * Email fetch response
 */
export interface EmailFetchResponse {
  /** Fetched email messages */
  messages: EmailMessage[]
  /** Next page token for pagination */
  nextPageToken?: string
  /** Total count (if available) */
  totalCount?: number
  /** Has more messages */
  hasMore: boolean
  /** Provider information */
  provider: {
    type: EmailProvider
    integrationId: string
  }
}

/**
 * Email sync status
 */
export interface EmailSyncStatus {
  /** Integration ID */
  integrationId: string
  /** Provider type */
  provider: EmailProvider
  /** Last sync timestamp */
  lastSync?: Date
  /** Sync status */
  status: 'idle' | 'syncing' | 'error' | 'success'
  /** Sync progress */
  progress?: {
    current: number
    total: number
    message?: string
  }
  /** Error message if sync failed */
  error?: string
  /** Number of new messages found in last sync */
  newMessages?: number
}

/**
 * Email action options
 */
export interface EmailActionOptions {
  /** Message IDs to act on */
  messageIds: string[]
  /** Integration ID */
  integrationId: string
  /** Action type */
  action: 'mark_read' | 'mark_unread' | 'star' | 'unstar' | 'archive' | 'delete' | 'spam' | 'move'
  /** Target folder for move action */
  targetFolder?: string
  /** Additional action parameters */
  params?: Record<string, any>
}
