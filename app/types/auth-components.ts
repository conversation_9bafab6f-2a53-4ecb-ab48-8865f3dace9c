import type { Component } from 'vue'

// Navigation component interfaces
export interface AuthNavigationProps {
  /**
   * The URL to navigate back to
   */
  backUrl?: string
  /**
   * The text to display for the back button
   * @default "Back to Home"
   */
  backText?: string
  /**
   * Whether to show the theme toggle button
   * @default true
   */
  showThemeToggle?: boolean
  /**
   * Custom logo component to display
   */
  logo?: Component
  /**
   * Logo size class
   * @default "size-10"
   */
  logoSize?: string
}

// Social button interfaces
export interface SocialProvider {
  /**
   * The provider identifier (google, twitter, linkedin, etc.)
   */
  id: string
  /**
   * The display name for the provider
   */
  name: string
  /**
   * The icon name for the provider
   */
  icon: string
  /**
   * Whether this provider shows text alongside the icon
   * @default false
   */
  showText?: boolean
  /**
   * Custom text to display (overrides default "Login with {name}")
   */
  customText?: string
}

export interface AuthSocialButtonsProps {
  /**
   * Array of social providers to display
   */
  providers?: SocialProvider[]
  /**
   * Button variant style
   * @default "default"
   */
  variant?: 'default' | 'compact'
  /**
   * Whether the buttons are in a loading state
   * @default false
   */
  loading?: boolean
  /**
   * Whether the buttons are disabled
   * @default false
   */
  disabled?: boolean
  /**
   * Custom CSS classes for the container
   */
  class?: string
}

export interface AuthSocialButtonsEmits {
  /**
   * Emitted when a social login button is clicked
   */
  socialLogin: [provider: string]
}

// Divider component interfaces
export interface AuthDividerProps {
  /**
   * The text to display in the divider
   * @default "OR"
   */
  text?: string
  /**
   * Custom CSS classes for styling
   */
  class?: string
  /**
   * Divider variant
   * @default "default"
   */
  variant?: 'default' | 'compact'
}

// Default provider configurations
export const DEFAULT_PROVIDERS: SocialProvider[] = [
  {
    id: 'google',
    name: 'Google',
    icon: 'logos:google-icon',
    showText: true,
  },
  {
    id: 'twitter',
    name: 'Twitter',
    icon: 'fa6-brands:x-twitter',
    showText: false,
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: 'fa6-brands:linkedin-in',
    showText: false,
  },
]

// Layout component interfaces
export interface AuthLayoutSideBySideProps {
  /**
   * Which side to place the illustration
   * @default "right"
   */
  illustrationSide?: 'left' | 'right'
  /**
   * Path to the illustration image
   */
  illustrationSrc?: string
  /**
   * Alt text for the illustration
   * @default "Authentication illustration"
   */
  illustrationAlt?: string
  /**
   * Maximum width class for the container
   * @default "max-w-6xl"
   */
  maxWidth?: string
  /**
   * Spacing class between form and illustration
   * @default "gap-8"
   */
  spacing?: string
}

export interface AuthLayoutCenteredProps {
  /**
   * Background image path
   */
  backgroundImage?: string
  /**
   * Maximum width class for the form
   * @default "max-w-md"
   */
  maxWidth?: string
  /**
   * Spacing class for padding
   * @default "px-4"
   */
  spacing?: string
  /**
   * Whether to show background content
   * @default true
   */
  showBackground?: boolean
}

export interface AuthLayoutSplitProps {
  /**
   * Starting color for gradient
   * @default "primary-900"
   */
  gradientFrom?: string
  /**
   * Ending color for gradient
   * @default "primary-500"
   */
  gradientTo?: string
  /**
   * Whether to enable hover animations
   * @default true
   */
  enableAnimations?: boolean
  /**
   * Whether to show marketing content in gradient section
   * @default true
   */
  marketingContent?: boolean
}

// Utility types for component props validation
export type SocialButtonVariant = 'default' | 'compact'
export type DividerVariant = 'default' | 'compact'
export type IllustrationSide = 'left' | 'right'
