import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import ProgressTile from '../../../../app/components/widgets/stats/progress-tile.vue'

const stubs = {
  BaseCard: {
    template: '<div class="base-card"><slot /></div>',
  },
  BaseParagraph: {
    template: '<p class="base-paragraph"><slot /></p>',
  },
  BaseHeading: {
    template: '<h3 class="base-heading"><slot /></h3>',
  },
  BaseProgressCircle: {
    props: ['modelValue', 'size', 'max', 'thickness', 'variant'],
    template: '<div class="base-progress-circle" :data-variant="variant"><slot /></div>',
  },
  Icon: {
    props: ['name'],
    template: '<i :data-name="name"></i>',
  },
}

describe('widgets/stats/progress-tile', () => {
  it('renders defaults when no props provided', () => {
    const wrapper = mount(ProgressTile, {
      global: {
        stubs,
      },
    })

    expect(wrapper.find('[data-testid="progress-tile"]').exists()).toBe(true)
    expect(wrapper.text()).toContain('Stat Title')
    expect(wrapper.text()).toContain('0')
    // No change by default
    expect(wrapper.find('[data-testid="change"]').exists()).toBe(false)
  })

  it('renders provided stat and change', () => {
    const wrapper = mount(ProgressTile, {
      props: {
        stat: {
          title: 'Weekly Profit',
          value: '$926.31',
          change: '+2.3%',
          progress: 75,
          icon: 'solar:fire-minimalistic-bold-duotone',
          colorClass: 'text-orange-500',
          circleVariant: 'none',
        },
      },
      global: {
        stubs,
      },
    })

    expect(wrapper.text()).toContain('Weekly Profit')
    expect(wrapper.text()).toContain('$926.31')
    expect(wrapper.get('[data-testid="change"]').text()).toContain('+2.3%')

    // Ensure color class applied on the wrapper
    const wrap = wrapper.get('[data-testid="progress-circle-wrap"]')
    expect(wrap.attributes('class') || '').toContain('text-orange-500')

    // Ensure track color classes are added when variant is none
    const circle = wrapper.get('.base-progress-circle')
    const cls = circle.attributes('class') || ''
    expect(cls.includes('*:first:text-muted-200') || cls.includes('*:dark:first:text-muted-900')).toBe(true)
  })
})
